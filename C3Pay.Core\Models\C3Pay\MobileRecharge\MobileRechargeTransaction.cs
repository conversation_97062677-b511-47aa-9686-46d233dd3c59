﻿using Edenred.Common.Core;
using System;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models
{
    public class MobileRechargeTransaction : BaseModel
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public Guid? BeneficiaryId { get; set; }
        public string ProductCode { get; set; }
        public string ReferenceNumber { get; set; }
        public int ExternalUserId { get; set; }
        public int ExternalBeneficiaryId { get; set; }
        public string AccountNumber { get; set; }
        public MobileRechargeType? RechargeType { get; set; }
        public Status Status { get; set; }
        public string SendCurrency { get; set; }
        public decimal SendAmount { get; set; }
        public decimal? Fee { get; set; }
        public decimal TotalAmount { get; set; }
        public string ReceiveCurrency { get; set; }
        public decimal? ReceiveAmount { get; set; }
        public int? TriesCount { get; set; }
        public string Remarks { get; set; }
        public string ErrorContext { get; set; } 
        public bool IsRepeat { get; set; }
        public Guid? UserDiscountId { get; set; }
        public decimal? DiscountAmount { get; set; }
        public string DiscountCurrency { get; set; }
        public string ReferralCode { get; set; }
        public bool? IsAutoRenewalEnabled { get; set; }
        public decimal? TargetedDiscountAmount { get; set; }
        public bool? IsDeferredRecharge { get; set; }

        public User User { get; set; }
        public MobileRechargeBeneficiary Beneficiary { get; set; }
        public MobileRechargeProduct Product { get; set; }
        public Transaction Transaction { get; set; }
        public MobileRechargeExternalTransaction ExternalTransaction { get; set; }
        public MobileRechargeUserDiscount UserDiscount { get; set; } 
    }
}
