﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.UnEmpInsurance;
using C3Pay.Core.Repositories.C3Pay.UnEmpInsurance;
using C3Pay.Core.Repositories.C3Pay.UnemploymentInsurance;
using Edenred.Common.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace C3Pay.Data.Configurations
{
    public class UnEmpInsurancePaymentConfiguration : IEntityTypeConfiguration<UnEmpInsurancePayment>
    {
        public void Configure(EntityTypeBuilder<UnEmpInsurancePayment> builder)
        {
            builder.ToTable("UnEmpInsurancePayments");

            builder.HasKey(c => c.Id);

            builder.Property(c => c.Id).HasDefaultValueSql("NEWSEQUENTIALID()");

            builder.Property(c => c.AmountCurrency)
                .HasMaxLength(3);

            builder.Property(c => c.AmountVATCurrency)
                .HasMaxLength(3);

            builder.Property(c => c.FeeCurrency)
                .HasMaxLength(3);

            builder.Property(c => c.TotalAmountCurrency)
                .HasMaxLength(3);

            builder.Property(c => c.NextDueAmountCurrency)
                .HasMaxLength(3);

            builder.Property(c => c.Remarks)
                .HasMaxLength(500);

            builder.Property(c => c.ReferralCode)
                .HasMaxLength(50);
            builder.Property(c => c.InstallmentJson)
                           .HasMaxLength(5000);
            builder.Property(c => c.Category)
                .HasMaxLength(1);
            builder.HasIndex(x => x.CreatedDate);
            builder.HasIndex(x => x.NextDueDate);
        }
    }
}
