﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.Messages.User;
using C3Pay.Core.Models.Settings;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Repositories
{
    public interface IUserRepository : IRepository<User>
    {
        Task<UserProfile> GetProfile(ReferralProgramServiceSettings referralProgramSettings, Guid? id = null, string phoneNumber = null, GeneralSettings settings = null, CancellationToken cancellationToken = default(CancellationToken));
        Task<bool> UsernameMatchesUser(Guid id, string username);
        Task<Guid?> UsernameMatchesUserMoneyTransferBeneficiary(Guid beneficiaryId, string username);
        Task<Guid?> UsernameMatchesUserMobileRechargeBeneficiary(Guid beneficiaryId, string username);
        Task<Guid?> UsernameMatchesUserMoneyTransferTransaction(Guid transactionId, string username);
        Task<Guid?> UsernameMatchesUserMobileRechargeTransaction(Guid transactionId, string username);
        Task<User> Search(Guid userId);
        Task<Tuple<List<User>, int>> Search(List<Expression<Func<User, bool>>> searchUserParameters, int? page, int? size);
        Task<Tuple<List<VerificationUser>, int>> SearchVerificationUser(List<Expression<Func<User, bool>>> searchUserParameters, int? page, int? size, string registrationType);
        Task<List<string>> GetActiveUsersDeviceTokensByCorporateId(string corporateId);
        Task<User> SearchSalaryAdvance(List<Expression<Func<User, bool>>> salaryAdvanceParameter);
        Task<List<User>> SearchUsers(IList<Guid> userId);
        Task<Tuple<List<VerificationUser>, int>> SearchVerificationUsersFullKYC(SearchUserParameters searchUserParameters);
        Task<Tuple<List<VerificationUser>, int>> SearchVerificationUsersPartialKYC(SearchUserParameters searchUserParameters);
        Task<Tuple<List<VerificationUser>, int>> SearchVerificationUsersNoKYC(SearchUserParameters searchUserParameters);
        Task<User> GetUserById(Guid userId, CancellationToken cancellationToken = default(CancellationToken));
        Task<User> GetUserInfoWithDevicesAsync(string phoneNumber, CancellationToken cancellationToken);
        Task<DeviceBindingStatus> GetDeviceExistsStatusAsync(string phoneNumber, string deviceId, CancellationToken cancellationToken);
        Task<DeviceBindingStatus> GetDeviceExistsStatusAsyncV2(string phoneNumber, string deviceId, CancellationToken cancellationToken);
        Task<User> GetUserAsync(string phoneNumber, CancellationToken cancellationToken);
        Task<User> GetActiveUserAsync(string phoneNumber);
        Task<User> GetC3payActiveUserAsync(string phoneNumber);

        #region C3Pay+
        Task<User> GetUserForC3PayPlus(string phoneNumber, CancellationToken cancellationToken = default);
        Task<User> GetUserForC3PayPlus(Guid id, CancellationToken cancellationToken = default);
        Task<List<Identification>> GetUserIdentifications(Guid userId, CancellationToken cancellationToken = default);
        #endregion

        Task<User> GetUserByIdWithNoTrackingAsync(Guid id, CancellationToken cancellationToken);
        Task<Result> BlockUserAsync(string phoneNumber, UserBlockType blockType, CancellationToken cancellationToken);
    }
}
