﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.Settings;
using C3Pay.Core.Models.Settings.Membership;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Settings.Integration.ExchangeHouse;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace C3Pay.API.StartupExtensions
{
    public static class SettingsExtension
    {
        public static void InjectSettings(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<RAKSFTPConnectionSettings>(configuration.GetSection("RAKSFTPConnection"));
            services.Configure<TestingSettings>(configuration.GetSection("Testing"));
            services.Configure<GeneralSettings>(configuration.GetSection("General"));
            services.Configure<PasswordSettings>(configuration.GetSection("PasswordValidationRules"));
            services.Configure<MoneyTransferServiceSettings>(configuration.GetSection("MoneyTransferService"));
            services.Configure<MobileRechargeServiceSettings>(configuration.GetSection("MobileRechargeService"));
            services.Configure<CleverTapServiceSettings>(configuration.GetSection("CleverTapService"));
            services.Configure<ExchangeHouseSettings>(configuration.GetSection("ExchangeHouseSettings"));
            services.Configure<ReferralProgramServiceSettings>(configuration.GetSection("ReferralProgramService"));
            services.Configure<MultimediaSettings>(configuration.GetSection("Multimedia"));
            services.Configure<BillPaymentSettings>(configuration.GetSection("BillPayment"));
            services.Configure<LanguageSettings>(configuration.GetSection("Language"));
            services.Configure<RatingSettings>(configuration.GetSection("Rating")); 
            services.Configure<HRServiceSettings>(configuration.GetSection("HRService"));
            services.Configure<StoreSettings>(configuration.GetSection("Store"));
            services.Configure<UnEmpInsuranceSettings>(configuration.GetSection("UnEmploymentInsurance"));
            services.Configure<ExperimentSettings>(configuration.GetSection("Experiment"));
            services.Configure<KycExpirySettings>(configuration.GetSection("KycExpiry"));
            services.Configure<EncryptionSettings>(configuration.GetSection("EncryptionSettings"));
            services.Configure<C3PayPlusMembershipSettings>(configuration.GetSection("C3PayPlusMembership"));
            services.Configure<VpnMembershipSettings>(configuration.GetSection("VpnMembership"));
            services.Configure<LoginVideoSettings>(configuration.GetSection("LoginVideos"));
            services.Configure<KycUnblockByPassportSettings>(configuration.GetSection("KycUnblockByPassport"));
            services.Configure<InfobipVoiceCallSettings>(configuration.GetSection("InfobipVoiceCallSettings"));
        }
    }
}
