﻿using Edenred.Common.Core;
using System.Threading.Tasks;

namespace C3Pay.Core.Services
{
    public interface IPushNotificationSenderService
    {
        Task<ServiceResponse> SendRMTProfileCreatedNotification(string deviceToken);
        Task<ServiceResponse> SendRegistrationEmiratesIdApprovalNotification(string deviceToken);
        Task<ServiceResponse> SendRegistrationPassportApprovalNotification(string deviceToken);
        Task<ServiceResponse> SendRegistrationRejectionNotification(string deviceToken);
        Task<ServiceResponse> SendReferralCodeUsedNotification(string deviceToken, string name, int remainingCount, decimal amount, bool plural);
        Task<ServiceResponse> SendReferralRewardCreditedNotification(string deviceToken, decimal amount);
        Task<ServiceResponse> SendInstantDirectTransferPushNotification(string deviceToken, string name, decimal amount);
        Task<ServiceResponse> SendClaimedDirectTransferToSenderPushNotification(string deviceToken, string name, decimal amount);
        Task<ServiceResponse> SendClaimedDirectTransferToReceiverPushNotification(string deviceToken, string name, decimal amount);
        Task<ServiceResponse> SendBillerAmountDueNotification(string deviceToken, string billerNickName, bool isAmountDue, bool noBill, bool invalidDetails);
        Task<ServiceResponse> SendPaymentNotification(string deviceToken, string errorMessage, string billerNickName);
        Task<ServiceResponse> SendSuccessfulUnEmpInsuranceSubscription(string deviceToken, string phoneNumber, string policyNumber);
        Task<ServiceResponse> SendMobileRechargeResultNotification(string deviceToken, BaseEnums.Status rechargeStatus);
        Task<ServiceResponse> SendC3PayPlusBalanceEnquiryRefundNotification(string deviceToken);
        Task<ServiceResponse> SendC3PayPlusSmsRefundNotification(string deviceToken);
        Task<ServiceResponse> SendVpnRenewalNotification(string deviceToken, string code);
    }
}
