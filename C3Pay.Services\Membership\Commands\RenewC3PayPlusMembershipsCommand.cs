﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus.Logs;
using C3Pay.Core.Models.C3Pay.OutboxMessage;
using C3Pay.Services.IntegrationEvents.Out.Enums;
using Common.Core.Models.Messages.Integration.ESMO;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.ESMO;
using Edenred.Common.Core.Models.Messages.Integration.PPS;
using Edenred.Common.Core.Models.Messages.Integration.PPS.Transaction;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Services.Membership.Commands
{
    public class RenewC3PayPlusMembershipsCommand : IRequest<Result>
    {
    }

    public class RenewC3PayPlusMembershipsCommandHandler : IRequestHandler<RenewC3PayPlusMembershipsCommand, Result>
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IPPSWebAuthService _ppsWebAuthService;


        /// <summary>
        /// 
        /// </summary>
        private readonly IESMOWebService _esmoWebService;


        /// <summary>
        /// 
        /// </summary>
        private readonly IFeatureManager _featureManager;


        /// <summary>
        /// 
        /// </summary>
        private readonly IUnitOfWork _unitOfWork;


        /// <summary>
        /// 
        /// </summary>
        private readonly IPPSService _ppsService;


        /// <summary>
        /// 
        /// </summary>
        private readonly ILogger _logger;


        /// <summary>
        /// 
        /// </summary>
        private readonly decimal _membershipPrice = 10.5m;


        /// <summary>
        /// 
        /// </summary>
        public List<Subscription> Subscriptions;

        public RenewC3PayPlusMembershipsCommandHandler(IPPSWebAuthService ppsWebAuthService,
                                                       IFeatureManager featureManager,
                                                       IESMOWebService esmoWebService,
                                                       IPPSService ppsService,
                                                       IUnitOfWork unitOfWork,
                                                       ILogger<RenewC3PayPlusMembershipsCommandHandler> logger)
        {
            _ppsWebAuthService = ppsWebAuthService;
            _esmoWebService = esmoWebService;
            _featureManager = featureManager;
            _unitOfWork = unitOfWork;
            _ppsService = ppsService;
            _logger = logger;
        }

        public async Task<Result> Handle(RenewC3PayPlusMembershipsCommand command, CancellationToken ct)
        {
            // If C3Pay+ renewals is disabled, exit.
            var isEnabled = await _featureManager.IsEnabledAsync(C3PayPlusFeatures.RenewC3PayPlusMemberships);
            if (isEnabled == false)
            {
                this.LogError(C3PayPlusErrors.RenewalsFeatureDisabled);
                return Result.Failure(C3PayPlusErrors.RenewalsFeatureDisabled);
            }


            // Set timestamp to start job.
            var timestamp = DateTime.Now;

            var logEntry = new C3PayPlusMembershipRenewalsDailyLog()
            {
                RenewingFor = timestamp.Date,
                JobStartTime = timestamp,
                RunStatus = C3PayPlusRenewalsDailyLogStatus.Started
            };

            await this._unitOfWork.C3PayPlusMembershipRenewalsDailyLogs.AddAsync(logEntry);
            await this._unitOfWork.CommitAsync();

            try
            {
                // Log start details.
                logEntry.RunStatus = C3PayPlusRenewalsDailyLogStatus.FetchingSubscriptions;
                await this._unitOfWork.CommitAsync();

                var expiredMemberships = await this._unitOfWork.C3PayPlusMembershipUsers.FindAsync(x => x.IsActive == true && x.ValidUntill < logEntry.JobStartTime,
                                                                                                   withoutTraking: false,
                                                                                                   includeProperties: i => i.User.CardHolder);

                // Update log entry.
                logEntry.TotalRenewalsExpectedToProcess = expiredMemberships.Count;
                logEntry.RunStatus = C3PayPlusRenewalsDailyLogStatus.Renewing;
                await this._unitOfWork.CommitAsync();


                foreach (var subscriber in expiredMemberships)
                {
                    try
                    {
                        // Case 1: Find user.
                        var user = await this._unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == subscriber.UserId
                                                                                         && x.IsDeleted == false
                                                                                         && x.IsBlocked == false,
                                                                                         i => i.CardHolder);

                        // If the user is not found (e.g., deleted or blocked), end the membership.
                        if (user is null)
                        {
                            await this.EndMembership(C3PayPlusRenewalsCancelationReason.UserNotFoundOrBlockedOrDeleted, subscriber, logEntry);
                            continue;
                        }


                        // Case 2: Close the membership for users who have decided to cancel.
                        if (subscriber.UserHasCanceled.HasValue && subscriber.UserHasCanceled.Value == true)
                        {
                            await this.EndMembership(C3PayPlusRenewalsCancelationReason.UserUnsubscribed, subscriber, logEntry);
                            continue;
                        }



                        // Case 3: Close the membership if we cant get the user's DOB.
                        if (subscriber.User.CardHolder.Birthdate.HasValue == false)
                        {
                            await this.EndMembership(C3PayPlusRenewalsCancelationReason.CantGetAgeInformation, subscriber, logEntry);
                            continue;
                        }



                        // Case 4: Close membership if the user is not between 18 and 59;
                        var dob = subscriber.User.CardHolder.Birthdate.Value;
                        int age = timestamp.Year - dob.Year;
                        if (timestamp.Month < dob.Month || (timestamp.Month == dob.Month && timestamp.Day < dob.Day))
                        {
                            age--;
                        }

                        if (age < 18 || age > 59)
                        {
                            await this.EndMembership(C3PayPlusRenewalsCancelationReason.UserIsTooOldOrTooYoung, subscriber, logEntry);
                            continue;
                        }



                        // Case 5: Close the membership if user has an non active (dormant) card.
                        var tryCheckIsCardDormant = await this._esmoWebService.IsCardDormant(subscriber.User.CardHolder.C3RegistrationId);
                        if (tryCheckIsCardDormant.IsSuccessful == false)
                        {
                            logEntry.SkippedDueToDormantCardCheckFailure++;
                            await this._unitOfWork.CommitAsync();
                            continue;
                        }

                        // If the card is dormant, close membership.
                        if (tryCheckIsCardDormant.Data.IsCardDormant == true)
                        {
                            await this.EndMembership(C3PayPlusRenewalsCancelationReason.CardIsNotActive, subscriber, logEntry);
                            continue;
                        }



                        // Case 6: Close membership if we user did not get salary in the last 3 months.
                        var tryGetBalance = await this.GetBalance(user.CardHolder);
                        if (tryGetBalance.IsSuccessful == false)
                        {
                            if (tryGetBalance.ErrorMessage == "CARD BLOCKED")
                            {
                                await this.EndMembership(C3PayPlusRenewalsCancelationReason.CardIsBlocked, subscriber, logEntry);
                                continue;
                            }
                            else
                            {
                                logEntry.SkippedRenewalsDueToUnableToRetrieveBalance++;
                                await this._unitOfWork.CommitAsync();
                                continue;
                            }
                        }


                        if (tryGetBalance.Data < _membershipPrice)
                        {
                            var tryCheckingIfUserGotSalaryInLast3Months = await this._esmoWebService.GotSalaryInLast3Months(user.CardHolderId);
                            if (tryCheckingIfUserGotSalaryInLast3Months.IsSuccessful == false || tryCheckingIfUserGotSalaryInLast3Months.Data.IsSuccessful == false)
                            {
                                logEntry.SkippedRenewalsDueToUnableToConfirmSalaryReceived++;
                                await this._unitOfWork.CommitAsync();
                                continue;
                            }

                            if (tryCheckingIfUserGotSalaryInLast3Months.Data.GotSalary == false)
                            {
                                await this.EndMembership(C3PayPlusRenewalsCancelationReason.NoSalaryInTheLast3Months, subscriber, logEntry);
                                continue;
                            }
                        }


                        // Renew membership for Edenred cards.
                        if (user.CardHolder.CorporateId == "99999" || user.CardHolder.CorporateId == "99998")
                        {
                            subscriber.RenewMembership();
                            logEntry.RenewalsCompletedSuccessfully++;
                            continue;
                        }

                        // Perform billing.
                        var referenceNumber = GenerateBillingReferenceNumber();
                        var tryBillUserForC3PayPlus = await this.DecreaseBalance(subscriber, referenceNumber);
                        if (tryBillUserForC3PayPlus.IsSuccessful == false)
                        {
                            logEntry.SkippedRenewalsDueToUnableToDebitUser++;
                            await this._unitOfWork.CommitAsync();
                            continue;
                        }

                        // Renew membership.
                        subscriber.RenewMembership();
                        logEntry.RenewalsCompletedSuccessfully++;
                        await this._unitOfWork.CommitAsync();

                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex.ToString());

                        logEntry.SkippedRenewalsDueToException++;
                        await this._unitOfWork.CommitAsync();

                        continue;
                    }
                }


                // Renewal is completed.
                logEntry.JobEndTime = DateTime.Now;
                logEntry.JobDurationInMinutes = (logEntry.JobEndTime - logEntry.JobStartTime).Value.TotalMinutes;
                logEntry.RunStatus = C3PayPlusRenewalsDailyLogStatus.Finished;


                await this._unitOfWork.CommitAsync();
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.ToString());

                logEntry.Remarks = $"FATAL ERROR: {ex}";
                logEntry.RunStatus = C3PayPlusRenewalsDailyLogStatus.Interrupted;
                return Result.Failure(C3PayPlusErrors.GeneralException);
                throw;
            }
        }
        private async Task EndMembership(C3PayPlusRenewalsCancelationReason reason, C3PayPlusMembershipUser subscriber, C3PayPlusMembershipRenewalsDailyLog logEntry)
        {
            // We first need to rollback to the user's old subscriptions.
            var tryRollbackToOldSubscriptions = await this.RollbackToOldSubscriptions(subscriber);
            if (tryRollbackToOldSubscriptions.IsSuccessful == false)
            {
                // Update log entry.
                if (tryRollbackToOldSubscriptions.ErrorMessage == C3PayPlusErrors.BalanceEnquirySubscriptionNotFound.Code)
                {
                    logEntry.SkippedRenewalsDueToBalanceEnquirySubscriptionNotFound++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == C3PayPlusErrors.CantGetSecuritySmsSubscriptionCode.Code)
                {
                    logEntry.SkippedRenewalsDueToMissingSecuritySmsSubscriptionCode++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == C3PayPlusErrors.CantGetSecuritySmsSubscriptionFee.Code)
                {
                    logEntry.SkippedRenewalsDueToMissingSecuritySmsSubscriptionFee++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == C3PayPlusErrors.CantSubscribeBackToSecuritySms.Code)
                {
                    logEntry.SkippedRenewalsDueToIssueSubscribingBackToSecuritySms++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == C3PayPlusErrors.CantGetSalaryAlertSubscriptionCode.Code)
                {
                    logEntry.SkippedRenewalsDueToMissingSalaryAlertSubscriptionCode++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == C3PayPlusErrors.CantGetSalaryAlertSubscriptionFees.Code)
                {
                    logEntry.SkippedRenewalsDueToMissingSalaryAlertSubscriptionFee++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == C3PayPlusErrors.CantSubscribeBackToSalaryAlerts.Code)
                {
                    logEntry.SkippedRenewalsDueToIssueSubscribingBackToSalaryAlert++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == C3PayPlusErrors.CannotSubscribeBackToNone.Code)
                {
                    logEntry.SkippedRenewalsDueToIssueSubscribingBackToNone++;
                }

                logEntry.TotalSkippedRenewals++;

                // Save and continue.
                await this._unitOfWork.CommitAsync();
                return;
            }

            // Here, rollback was succesfull, so we can close the membership.
            switch (reason)
            {
                case C3PayPlusRenewalsCancelationReason.UserNotFoundOrBlockedOrDeleted:
                    subscriber.EndMembership(C3PayPlusConstantParams.C3PayPlus_RenewMembership_ClosedMembershipDueToUserNotFoundOrBlockedOrDeleted);
                    logEntry.UnsubscribedDueToDeletedUsers++;
                    break;
                case C3PayPlusRenewalsCancelationReason.UserUnsubscribed:
                    subscriber.EndMembership(C3PayPlusConstantParams.C3PayPlus_RenewMembership_CloseMembershipDueToUsersDecision);
                    logEntry.UnsubscribedDueToUserDecision++;
                    break;
                case C3PayPlusRenewalsCancelationReason.CantGetAgeInformation:
                    subscriber.EndMembership(C3PayPlusConstantParams.C3PayPlus_RenewMembership_CloseMembershipDueToAgeNotFound);
                    logEntry.UnsubscribedDueToMissingAgeInformation++;
                    break;
                case C3PayPlusRenewalsCancelationReason.UserIsTooOldOrTooYoung:
                    subscriber.EndMembership(C3PayPlusConstantParams.C3PayPlus_RenewMembership_CloseMembershipDueToAgeLimitExceeded);
                    logEntry.UnsubscribedDueToExceedingAgeLimit++;
                    break;
                case C3PayPlusRenewalsCancelationReason.NoSalaryInTheLast3Months:
                    subscriber.EndMembership(C3PayPlusConstantParams.C3PayPlus_RenewMembership_CloseMembershipDueToNoSalaryCreditedInLast3Months);
                    logEntry.UnsubscribedDueToNoSalaryCreditedInLast3Months++;
                    break;
                case C3PayPlusRenewalsCancelationReason.CardIsNotActive:
                    subscriber.EndMembership(C3PayPlusConstantParams.C3PayPlus_RenewMembership_CloseMembershipDueToInactiveCard);
                    logEntry.UnsubscribedDueToCardIsNotActive++;
                    break;
                case C3PayPlusRenewalsCancelationReason.CardIsBlocked:
                    subscriber.EndMembership(C3PayPlusConstantParams.C3PayPlus_RenewMembership_CloseMembershipDueToCardBeingBlocked);
                    logEntry.UnsubscribedDueToCardIsBlocked++;
                    break;
                case C3PayPlusRenewalsCancelationReason.Other:
                    subscriber.EndMembership(C3PayPlusConstantParams.C3PayPlus_RenewMembership_CloseMembershipDueToOtherReasons);
                    logEntry.UnsubscribedDueToOtherReasons++;
                    break;
                default:
                    subscriber.EndMembership(C3PayPlusConstantParams.C3PayPlus_RenewMembership_CloseMembershipDueToOtherReasons);
                    logEntry.UnsubscribedDueToOtherReasons++;
                    break;
            }

            // Update log entry.
            logEntry.SuccessfulUnsubscribes++;

            var outboxMessage = new OutboxMessage
            {
                Type = OutboxMessageTypeEnum.C3PayPlusUserUnsubscribedEvent.ToString(),
                Data = JsonConvert.SerializeObject(new
                {
                    C3EmployeeRegistrationId = subscriber.User.CardHolder.C3RegistrationId,
                    CitizenId = subscriber.User.CardHolderId,
                    Action = "UserSubscribedFromC3PayPlusMembership",
                    Timestamp = DateTime.Now
                }),
                CreatedDate = DateTime.Now
            };
            await _unitOfWork.OutboxMessages.AddAsync(outboxMessage);

            // Save and continue.
            await this._unitOfWork.CommitAsync();
        }
        private async Task<ServiceResponse> RollbackToOldSubscriptions(C3PayPlusMembershipUser subscriber)
        {
            try
            {
                // Get all cached subscrptions.
                if (this.Subscriptions is null || this.Subscriptions.Count == 0)
                {
                    this.Subscriptions = (List<Subscription>)await this._unitOfWork.Subscriptions.FindAsync(x => x.IsAvailable == true);
                }

                if (subscriber.UserHadBalanceEnquirySubscription == true)
                {
                    var balanceEnquirySubscriptionCode = this.Subscriptions.FirstOrDefault(x => x.Code == SMSSubscriptionType.BE.ToString());

                    // Find current BE subscription that is created because of C3Pay+.
                    var c3pBalanceEnquirySubscriptions = await this._unitOfWork.UserSubscriptions.FindAsync(x => x.Subscription.Code == SMSSubscriptionType.BE.ToString()
                                                                                                             && x.UserId == subscriber.UserId
                                                                                                             && x.C3PayPlusMembershipUserId == subscriber.Id,
                                                                                                             withoutTraking: false,
                                                                                                             includeProperties: null);

                    // TODO: Add a warning if more than one balance inquiry subscription is found.
                    if (c3pBalanceEnquirySubscriptions is null || c3pBalanceEnquirySubscriptions.Count == 0)
                    {
                        // Log this error. This case should not be possible and it means that the subscription was not created successfully.
                        this.LogError(C3PayPlusErrors.BalanceEnquirySubscriptionNotFound);
                        return new ServiceResponse(false, C3PayPlusErrors.BalanceEnquirySubscriptionNotFound);
                    }


                    // End the C3Pay+ BE subscription.
                    foreach (var c3pBalanceEnquirySubscription in c3pBalanceEnquirySubscriptions)
                    {
                        c3pBalanceEnquirySubscription.EndDate = DateTime.UtcNow;
                    }

                    // Create a new BE subscription.
                    var newBalanceEnquirySubscrption = new UserSubscription()
                    {
                        UserId = subscriber.UserId,
                        SubscriptionId = balanceEnquirySubscriptionCode.Id,
                        StartDate = DateTime.Now,
                    };

                    // Add the new BE subscription.
                    await this._unitOfWork.UserSubscriptions.AddAsync(newBalanceEnquirySubscrption);
                    await this._unitOfWork.CommitAsync();
                }

                if (subscriber.UserHadSecuritySmsSubscription == true)
                {
                    var securitySmsSubscription = this.Subscriptions.FirstOrDefault(x => x.Code == SMSSubscriptionType.T.ToString());
                    if (securitySmsSubscription is null)
                    {
                        this.LogError(C3PayPlusErrors.CantGetSecuritySmsSubscriptionCode);
                        return new ServiceResponse(false, C3PayPlusErrors.CantGetSecuritySmsSubscriptionCode);
                    }

                    var subscriptionFee = await this._unitOfWork.Subscriptions.GetSubscriptionFee(securitySmsSubscription.Id, subscriber.User.CardHolder.CorporateId);
                    if (subscriptionFee is null)
                    {
                        this.LogError(C3PayPlusErrors.CantGetSecuritySmsSubscriptionFee);
                        return new ServiceResponse(false, C3PayPlusErrors.CantGetSecuritySmsSubscriptionFee);
                    }

                    var tryToggleC3PayPlusSmsSubscription = await this._esmoWebService.RollbackC3PayPlusSmsSubscription(new RollbackC3PayPlusSmsSubscriptionRequestDto()
                    {
                        CardSerialNo = subscriber.User.CardHolder.CardSerialNumber,
                        CorporateId = int.Parse(subscriber.User.CardHolder.CorporateId),
                        NotificationType = securitySmsSubscription.Code,
                        MobileNo = subscriber.User.PhoneNumber,
                        SMSFee = subscriptionFee.Fee,
                        IsUnsubscribe = false
                    });


                    if (tryToggleC3PayPlusSmsSubscription.IsSuccessful == false)
                    {
                        this.LogError(C3PayPlusErrors.CantSubscribeBackToSecuritySms);
                        return new ServiceResponse(false, C3PayPlusErrors.CantSubscribeBackToSecuritySms);
                    }

                }
                else if (subscriber.UserHadSalaryAlertSmsSubscription == true)
                {
                    var salaryAlertSubscription = this.Subscriptions.FirstOrDefault(x => x.Code == SMSSubscriptionType.S.ToString());
                    if (salaryAlertSubscription is null)
                    {
                        this.LogError(C3PayPlusErrors.CantGetSalaryAlertSubscriptionCode);
                        return new ServiceResponse(false, C3PayPlusErrors.CantGetSalaryAlertSubscriptionCode);
                    }

                    var subscriptionFee = await this._unitOfWork.Subscriptions.GetSubscriptionFee(salaryAlertSubscription.Id, subscriber.User.CardHolder.CorporateId);
                    if (subscriptionFee is null)
                    {
                        this.LogError(C3PayPlusErrors.CantGetSalaryAlertSubscriptionFees);
                        return new ServiceResponse(false, C3PayPlusErrors.CantGetSalaryAlertSubscriptionFees);
                    }


                    var tryToggleC3PayPlusSmsSubscription = await this._esmoWebService.RollbackC3PayPlusSmsSubscription(new RollbackC3PayPlusSmsSubscriptionRequestDto()
                    {
                        CardSerialNo = subscriber.User.CardHolder.CardSerialNumber,
                        CorporateId = int.Parse(subscriber.User.CardHolder.CorporateId),
                        NotificationType = salaryAlertSubscription.Code,
                        MobileNo = subscriber.User.PhoneNumber,
                        SMSFee = subscriptionFee.Fee,
                        IsUnsubscribe = false
                    });

                    if (tryToggleC3PayPlusSmsSubscription.IsSuccessful == false)
                    {
                        this.LogError(C3PayPlusErrors.CantSubscribeBackToSalaryAlerts);
                        return new ServiceResponse(false, C3PayPlusErrors.CantSubscribeBackToSalaryAlerts);
                    }
                }
                else
                {
                    var tryToggleC3PayPlusSmsSubscription = await this._esmoWebService.RollbackC3PayPlusSmsSubscription(new RollbackC3PayPlusSmsSubscriptionRequestDto()
                    {
                        CardSerialNo = subscriber.User.CardHolder.CardSerialNumber,
                        CorporateId = int.Parse(subscriber.User.CardHolder.CorporateId),
                        NotificationType = "N",
                        MobileNo = subscriber.User.PhoneNumber,
                        SMSFee = 0,
                        IsUnsubscribe = true
                    });

                    if (tryToggleC3PayPlusSmsSubscription.IsSuccessful == false)
                    {
                        this.LogError(C3PayPlusErrors.CannotSubscribeBackToNone);
                        return new ServiceResponse(false, C3PayPlusErrors.CannotSubscribeBackToNone);
                    }
                }

                return new ServiceResponse();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.ToString());
                return new ServiceResponse(false, ex.ToString());
            }
        }
        public async Task<ServiceResponse<decimal>> GetBalance(CardHolder cardholder)
        {
            var cardNumber = cardholder.CardNumber;
            var cardSerialNumber = cardholder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var request = new BalanceRequest()
            {
                CardPanNumber = cardPanNumber,
                CardSerialNumber = cardSerialNumber,
                Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                ReferenceNumber = TypeUtility.GetReferenceNumber(TransactionPrefix.BAL.ToString(), 12)
            };

            var getCardBalanceResult = await _ppsWebAuthService.GetCardBalance(request);

            if (!getCardBalanceResult.IsSuccessful)
            {
                return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.PPSConnectionIssue.ToString());
            }

            if (getCardBalanceResult.Data.StatusCode != "00")
            {
                if (getCardBalanceResult.Data.Message == EnumUtility.GetDescriptionFromEnumValue(PPSResponseStatus.CARDNOTACTIVATED))
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.ActivateYourCard.ToString());
                }
                else if (getCardBalanceResult.Data.Message == EnumUtility.GetDescriptionFromEnumValue(PPSResponseStatus.MAXPINEXCEEDED))
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.UnblockYourCard.ToString());
                }
                else if (getCardBalanceResult.Data.Message == "CARD BLOCKED")
                {
                    return new ServiceResponse<decimal>(false, "CARD BLOCKED");
                }
                else
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.ErrorGettingBalance.ToString());
                }
            }

            var balance = TypeUtility.GetDecimalFromString(getCardBalanceResult.Data.EndBalanace.Amt) / 100;
            return new ServiceResponse<decimal>(balance);
        }
        private string GenerateBillingReferenceNumber()
        {
            var referencePrefix = TransactionPrefix.C3P.ToString();

            var dateStamp = DateTime.Now;

            var startYear = dateStamp.Year.ToString().Substring(2);
            var startMonth = dateStamp.Month.ToString();
            var startDay = dateStamp.Day.ToString();

            // We are doing this because we want the date in this format.
            // 1994/08/29 becomes: 940829
            var dateComponent = Convert.ToDecimal(startYear.PadLeft(2, '0') + startMonth.PadLeft(2, '0') + startDay.PadLeft(2, '0'));

            referencePrefix = $"{referencePrefix}{dateComponent}X";
            var referenceDigits = 12;
            var referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            return referenceNumber;
        }
        private async Task<ServiceResponse> DecreaseBalance(C3PayPlusMembershipUser membershipUser, string referenceNumber)
        {
            var cardNumber = membershipUser.User.CardHolder.CardNumber;
            var cardSerialNumber = membershipUser.User.CardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var decreaseBalanceRequest = new PPSDecreaseBalanceRequest()
            {
                accountField = new PPSAccountDetails()
                {
                    accountNoField = membershipUser.User.CardHolder.PpsAccountNumber,
                    cardSerialField = cardSerialNumber,
                    cardPanField = cardPanNumber
                },
                amountField = (long)(_membershipPrice * 100),
                reasonField = "C3Pay+ Monthly Billing",
                schemeRefField = referenceNumber,
                allowNegativeBalance = true
            };

            ServiceResponse tryDecreaseBalance;

            try
            {
                tryDecreaseBalance = await _ppsService.DecreaseBalance(decreaseBalanceRequest, true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.ToString());
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, C3PayPlusErrors.CantDebitUser);
            }

            if (tryDecreaseBalance.IsSuccessful == false)
            {
                this.LogError(C3PayPlusErrors.CantDebitUser);
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, C3PayPlusErrors.CantDebitUser);
            }

            return new ServiceResponse();
        }
        private void LogError(Error error)
        {
            _logger.LogError("{ErrorCode}. More Info: {ErrorMessage}", error?.Code, error?.Message);
        }
    }
}
