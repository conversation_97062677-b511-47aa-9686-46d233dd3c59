﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using C3Pay.Core.Models.DTOs.MoneyTransfer;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Portal;
using C3Pay.Core.Models.Structs;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Services
{
    public interface IMoneyTransferService
    {
        Task<ServiceResponse<BankDetailsRakModel>> GetBankBranchLists(Guid userId, string countryCode, string bankName, string bankBranchName);
        Task<ServiceResponse<BankDetailsRakModel>> GetBankBranchDetailsForIFSCCode(Guid userId, string ifscCode);
        Task<ServiceResponse<MoneyTransferBranch>> GetBranchDetailsByIfscCode(string ifscCode);
        Task<ServiceResponse<MoneyTransferFreeTransferEligiblity>> GetUserFreeTransferEigibilityLevel(Guid userId, CancellationToken cancellationToken = default(CancellationToken));
        Task<ServiceResponse<SendMoneyTransferResultDto>> SendMoneyTransfer(MoneyTransferTransaction moneyTransferTransaction);
        Task<ServiceResponse<Status>> AddExternalTransaction(Guid transactionId);
        Task<ServiceResponse<IEnumerable<MoneyTransferDetails>>> GetUserTransactions(Guid userId, MoneyTransferType? transferType, int? pageSize, int? pageNumber, string languageCode, CancellationToken cancellationToken = default(CancellationToken));
        Task<ServiceResponse<MoneyTransferTransaction>> GetTransactionReceipt(Guid transactionId, string languageCode = default);
        Task<ServiceResponse<MoneyTransferTransaction>> UpdateTransactionFromExternal(Guid transactionId, bool calledFromJob = false);
        Task<ServiceResponse<MoneyTransferTransaction>> ManualStatusUpdate(string referenceNumber, Status status);
        Task<ServiceResponse<MoneyTransferTransaction>> UpdateTransactionStatus(MoneyTransferTransaction moneyTransferTransaction, string referenceNumber, BaseEnums.Status recordStatus, string description, DateTime statusDate, string externalStatus, Guid? portalUserId = null, string portalEmailId = null);
        Task<ServiceResponse> BulkUpdateTransactionsStatuses(List<UpdateTransferStatusDetails> updates);
        Task<ServiceResponse<FxRateResponseRakModel>> GetFxRates(Guid userId, string toCurrency, decimal amount, string transferMethod, string beneficiaryId = null);
        Task<ServiceResponse<bool>> GetTransferLimitEligibility(Guid userId, string transferMethod, string countryCode, decimal transactionAmount);
        Task<ServiceResponse> RefreshRates();
        Task<ServiceResponse<Tuple<IList<MoneyTransferTransaction>, int>>> SearchUserMoneyTransferTransactionsReadOnly(Guid id, SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<ServiceResponse<Tuple<List<MoneyTransferTransactionStruct>, int>>> SearchMoneyTransferTransactionsReadOnly(SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<ServiceResponse<Tuple<IList<MoneyTransferTransaction>, int>>> SearchC3toC3Transactions(Guid id, SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<ServiceResponse> UpdatePendingTransactions();
        Task<ServiceResponse> BulkReverseTransactions(List<ReversalAmount> reversals);
        Task<ServiceResponse> ReverseFailedMoneyTransferTransactions();
        Task<ServiceResponse<bool>> CancelTransfer(User user, Guid transactionId);
        Task<ServiceResponse<MoneyTransferRateComparison>> GetRateComparisonDetails(Guid userId, string nationality);
        Task<ServiceResponse<bool>> ClaimDirectMoneyTransfers(string accountNumber);
        Task<ServiceResponse<bool>> IsTransferValid(MoneyTransferTransaction moneyTransferTransaction);
        Task<ServiceResponse> ReversePendingDirectMoneyTransfers();
        Task<ServiceResponse> ReverseFailedDirectMoneyTransfers();
        Task<ServiceResponse> ReverseOnHoldTransactions();
        Task<ServiceResponse<MoneyTransferSuspiciousInformation>> SaveSuspiciousInformation(MoneyTransferSuspiciousInformation data);
        Task<ServiceResponse<MoneyTransferSuspiciousInformation>> UpdateSuspiciousInformation(MoneyTransferSuspiciousInformation data);
        Task<ServiceResponse<bool>> ApproveSuspiciousInformation(Guid id);
        Task<bool> HasOldTransactions(string emiratesId, Guid excludedUserId);
        Task<ServiceResponse<bool>> EnableGuidedWalkthrough(User user, string languageCode);
        Task<ServiceResponse<List<RepeatTransfer>>> GetRepeatTransfers(Guid userId, decimal userBalance);
        Task<ServiceResponse<MoneyTransferSmartDefaultDto>> GetSmartDefault(MoneyTransferSmartDefaultRequestDto request);
        Task<ServiceResponse> ClearCache(string key);
        Task<ServiceResponse<IEnumerable<RemittanceDestinationDto>>> GetCorridors(string cardholderId);
        Task<ServiceResponse<IEnumerable<FieldGroupDto>>> GetFieldGroups(int deliveryMethodId, string languageCode, CardHolder cardholder);
        Task<ServiceResponse<ReviewBeneficiaryDto>> ValidateBeneficiary(PostBeneficiaryRequest request, string languageCode);
        Task<ServiceResponse<MoneyTransferBeneficiary>> AddBeneficiary(PostBeneficiaryRequest request);
        Task<ServiceResponse<ReviewTransferDto>> ValidateTransfer(PostTransferDto transferRequestDto, string languageCode, User user);
        Task<ServiceResponse<FetchRateDto>> FetchRate(string amount, string targetCurrency, string countryCode, string transferMethod);
        Task<ServiceResponse> SendFreeTransferExpiryNotification();
        Task<dynamic> GetRaffleTicket(Guid userId);
        Task<dynamic> GetSpinTheWheel(Guid userId);
        Task<ServiceResponse<string>> GetDirectTransferReceiverLimitEligibility(Guid beneficiaryId, decimal amount, User user);
        Task<ServiceResponse> ResendMoneyTransferUnassignedRewards();
        Task<Tuple<bool, MoneyTransferType?>> GetBeneficiaryDetailsToPromptOtp(Guid beneficiaryId);
    }
}
