﻿using C3Pay.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Text;

namespace C3Pay.Data.EntityConfigurations.BillPayments
{
    public class BillPaymentBillerConfiguration : IEntityTypeConfiguration<BillPaymentBiller>
    {
        public void Configure(EntityTypeBuilder<BillPaymentBiller> builder)
        {
            builder.ToTable("BillPaymentBillers");

            builder.HasKey(c => c.Id);

            builder.Property(c => c.Id).HasDefaultValueSql("NEWSEQUENTIALID()");

            builder.Property(c => c.Id).ValueGeneratedOnAdd();

            builder.Property(c => c.NickName)
                         .HasMaxLength(350);

            builder.Property(c => c.BillAmountCurrency)
                      .HasMaxLength(10); 

            builder
                .HasOne(c => c.Category)
                .WithMany(c => c.Bill<PERSON>)
                .HasForeignKey(c => c.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);
     

            builder
                 .HasOne(c => c.Provider)
                 .WithMany(c => c.Bill<PERSON>)
                 .HasForeignKey(c => c.ProviderId)
                 .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
