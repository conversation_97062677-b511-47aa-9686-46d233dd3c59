using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace C3Pay.Data.EntityConfigurations.Membership
{
    public class C3PayPlusTargetedDiscountOfferConfiguration : IEntityTypeConfiguration<C3PayPlusTargetedDiscountOffer>
    {
        public void Configure(EntityTypeBuilder<C3PayPlusTargetedDiscountOffer> builder)
        {
            builder.ToTable("C3PayPlusTargetedDiscountOffers");

            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id)
                   .IsRequired();

            builder.Property(x => x.UserId)
                   .IsRequired();

            builder.Property(x => x.CardholderId)
                   .IsRequired()
                   .HasMaxLength(50);

            builder.Property(x => x.OfferStartDate)
                   .IsRequired();

            builder.Property(x => x.OfferEndDate)
                   .IsRequired();

            builder.Property(x => x.IsActive)
                   .IsRequired();

            builder.Property(x => x.HasExpired)
                   .IsRequired();

            builder.Property(x => x.WasUsed)
                   .IsRequired();

            builder.Property(x => x.RecommendedMembershipType)
                   .IsRequired();

            builder.Property(x => x.CreatedAt)
                   .IsRequired();

            builder.Property(x => x.UpdatedAt);

            // Indexes for performance
            builder.HasIndex(x => x.UserId)
                   .HasDatabaseName("IX_C3PayPlusTargetedDiscountOffers_UserId");

            builder.HasIndex(x => x.CardholderId)
                   .HasDatabaseName("IX_C3PayPlusTargetedDiscountOffers_CardholderId");

            builder.HasIndex(x => new { x.UserId, x.IsActive })
                   .HasDatabaseName("IX_C3PayPlusTargetedDiscountOffers_UserId_IsActive");

            // Relationships
            builder.HasOne(x => x.User)
                   .WithMany()
                   .HasForeignKey(x => x.UserId)
                   .OnDelete(DeleteBehavior.Restrict);
        }
    }
}