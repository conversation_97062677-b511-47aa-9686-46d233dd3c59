﻿using C3Pay.Core;
using C3Pay.Core.Models;
using System;
using System.Collections.Generic;

namespace C3Pay.API.Resources.MoneyTransfer
{
    /// <summary>
    /// 
    /// </summary>
    public class MoneyTransferDto
    {
        public MoneyTransferDto()
        {
            CashPickupTransferDetailsDto = new CashPickupTransferDetailsDto();
        }

        /// <summary>
        /// 
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BeneficiaryFullName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BeneficiaryPhoneNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public AmountDto SendAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public AmountDto ReceiveAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string TransferMethod { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime DateSent { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ReferralCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public MoneyTransferDelayType DelayType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime? DelayStartDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime? DelayEndDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double? DelayTimeRemainingInHours
        {
            get
            {
                if (this.DelayEndDate is null)
                {
                    return null;
                }

                if (DateTime.Now >= this.DelayEndDate)
                {
                    return null;
                }

                var remainingTime = (this.DelayEndDate - DateTime.Now).Value.TotalHours;
                return remainingTime;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public string DelayCountryCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DelayCountryName { get; set; }

        /// <summary>
        /// Is Suspicious Action Required
        /// </summary>
        public bool IsSuspiciousActionRequired { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public CashPickupTransferDetailsDto CashPickupTransferDetailsDto { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Dictionary<string, string> TransferSummary { get; set; }
        public string StatusMessage { get; set; }
        public List<StatusVideoDto> StatusVideo { get; set; }
        public bool ShowRecentTransferStatus { get; set; }
    }

    public class StatusVideoDto
    {
        public string Url { get; set; }
        public string ThumbnailUrl { get; set; }
        public string Language { get; set; }
        public bool IsDefault { get; set; }
    }

    public class CashPickupTransferDetailsDto
    {
        public string CashPickupPin { get; set; }
        public string SharePinMessageContent { get; set; }
        public List<CashPickupLocationDto> CashPickupLocations { get; set; }
    }

    public class CashPickupLocationDto
    {
        public string Url { get; set; }
        public string Name { get; set; }
        public string City { get; set; }
    }
}
