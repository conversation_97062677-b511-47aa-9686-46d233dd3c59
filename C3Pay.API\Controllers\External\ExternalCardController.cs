﻿using C3Pay.API.Models;
using C3Pay.API.Resources.Card;
using C3Pay.Core;
using Edenred.Common.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using C3Pay.Core.Common;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Edenred.Common.Core.Services.Integration;

namespace C3Pay.API.Controllers.External
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    [Authorize(AuthenticationSchemes = AuthenticationScheme.DualAuthentication)]
    public class ExternalCardController : ControllerBase
    {
        private readonly IPPSService _ppsService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IESMOWebService _esmoWebService;
        private readonly IFeatureManager _featureManager;
        private readonly ISanctionScreeningService _sanctionScreeningService;

        public ExternalCardController(IPPSService ppsService, IUnitOfWork unitOfWork, IESMOWebService esmoWebService,
            IFeatureManager featureManager, ISanctionScreeningService sanctionScreeningService)
        {
            this._ppsService = ppsService;
            _unitOfWork = unitOfWork;
            _esmoWebService = esmoWebService;
            _featureManager = featureManager;
            _sanctionScreeningService = sanctionScreeningService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPut("card/activate/{cardSerialNumber}")]
        public async Task<ActionResult> ActivateCardByCardSerialNumber(string cardSerialNumber)
        {
            if (await _featureManager.IsEnabledAsync(FeatureFlags.SanctionScreeningValidateScreeningStatus))
            {
                var cardHolder = await this._unitOfWork.CardHolders.FirstOrDefaultAsync(record => record.CardSerialNumber == cardSerialNumber);
                var validateSanctionScreeningResult =
                    await _sanctionScreeningService.ValidateUserSanctionScreening(cardHolder.C3RegistrationId);
                if (validateSanctionScreeningResult.IsValid == false)
                {
                    return this.BadRequest(BaseEnums.IdentificationVerificationResult.SanctionScreeningInvalid.ToString());
                }
            }
            var activationResult = await this._ppsService.ActivateCard(cardSerialNumber);

            if (!activationResult.IsSuccessful)
            {
                return this.BadRequest(activationResult.ErrorMessage);
            }

            return this.Ok();
        }

        [HttpPost("card/kyc-unblock")]
        public async Task<ActionResult> CompleteKycUnblock(CompleteKycUnblockRequestDto requestDto)
        {
            var kycBlocked = await this._unitOfWork.MissingKycCardholders.FirstOrDefaultAsync(u => u.CitizenId == requestDto.CitizenId && 
                                                                                                   (u.Status == KycStatus.NotSubmittedKycBlocked || u.Status == KycStatus.Blocked));
            if (kycBlocked is null)
            {
                return this.Ok("Card is not blocked or already unblocked");
            }

            // Unblock card.
            var tryUnblockCard = await this._ppsService.UnblockCard(requestDto.CardSerialNumber);

            if (tryUnblockCard.IsSuccessful == false)
            {
                kycBlocked.Remarks += $"| Can't unblock card. Error: {tryUnblockCard.ErrorMessage}";
            }

            // Card was unblocked.
            kycBlocked.Status = KycStatus.Valid;
            kycBlocked.BlockedDate = null;
            kycBlocked.UnblockDate = DateTime.Now;
            kycBlocked.Remarks += $"| Card was unblocked";

            await this._unitOfWork.CommitAsync();

            // Add entry to ESMO.
            var tryAddEntry = await this._esmoWebService.MarkCardAsKycUnblocked(requestDto.CitizenId);

            if (tryAddEntry.IsSuccessful == false)
            {
                kycBlocked.Remarks += $"| Unable to add entry to ESMO. Error: {tryAddEntry.ErrorMessage}";
            }
            else
            {
                kycBlocked.Remarks += $"| Card was marked as unblocked on ESMO";
            }

            await this._unitOfWork.CommitAsync();

            return this.Ok();
        }
    }
}
