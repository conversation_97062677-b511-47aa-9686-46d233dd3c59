﻿using C3Pay.Core.Models;
using Edenred.Common.Core;
using System.Threading.Tasks;

namespace C3Pay.Core.Services
{
    public interface ITextMessageSenderService
    {
        Task<ServiceResponse> SendOTPMessage(SendOTPSMSRequest sendOTPSMSRequest);
        Task<ServiceResponse> SendRMTProfileCreatedMessage(string phoneNumber);
        Task<ServiceResponse> SendPhoneNumberChangedMessage(string phoneNumber);
        Task<ServiceResponse> SendMTBeneficiaryCreatedMessage(string phoneNumber, string beneficiaryName);
        Task<ServiceResponse> SendMTBeneficiaryApprovedMessage(string phoneNumber, string beneficiaryName);
        Task<ServiceResponse> SendResetPasswordMessage(string phoneNumber, string passsword);
        Task<ServiceResponse> SendPendingDirectTransferSMS(string phoneNumber, string firstName, string amount);
        Task<ServiceResponse> SendClaimedDirectTransferToReceiverSMS(string phoneNumber, string name, decimal amount);
        Task<ServiceResponse> SendMissingRakFileMessage(string phoneNumber);
        Task<ServiceResponse> SendDailyRmtProfileStatsMessage(string phoneNumber, DailyRmtProfileStat dailyProfileStat);
        Task<ServiceResponse> SendMissingProfilesAttachmentsUploadedStatsMessage(string phoneNumber, MissingProfilesAttachmentsUploadedStats missingAttachmentsUploadedStats);
        Task<ServiceResponse> SendEHBeneficiaryCreatedMessage(string phoneNumber, string firstName, string middleName, string lastName);
        Task<ServiceResponse> SendOrderPlacedSMSMessage(SendOrderPlacedSMSRequest request);
        Task<ServiceResponse> SendSuccessfulUnEmpInsuranceSubscription(string phoneNumber, string policyNumber, UnEmpInsuranceStatus previousStatus);
        Task<ServiceResponse> SendSuccessfulUnEmpInsuranceSubscription(string phoneNumber, string policyNumber, bool isSalaryDebit);
        Task<ServiceResponse> SendUnSuccessfulUnEmpInsuranceSubscription(string phoneNumber, string reason, UnEmpInsuranceStatus previousStatus);
        Task<ServiceResponse> SendUnEmpInsuranceCancellationRequest(string phoneNumber);
        Task<ServiceResponse> SendNewDeviceAddedMessage(string phoneNumber, bool userVerified);
        Task<ServiceResponse> SendKycGracePeriod(string phoneNumber, bool isNotSubmitted);
        Task<ServiceResponse> SendFreeExpiryNotification(string phoneNumber, double daysLeft);
        Task<ServiceResponse> SendAtmWithdrawalFeeReversalSms(string phoneNumber);
        Task<ServiceResponse> SendC3PayLifeInsuranceNomineeChangesToNominee(string phoneNumber, string cardHolderName, string nomineeName, string policyNumber);
        Task<ServiceResponse> SendC3PayLifeInsuranceNomineeChangesToMembershipUser(string phoneNumber, string cardHolderName, string nomineeName);
        Task<ServiceResponse> SendMRAutoRenewalDeactivationMessage (string phoneNumber, bool dueToLowBalance, string productName);
        Task<ServiceResponse> SendC3PayPlusSubscriptionSms(string phoneNumber);
        Task<ServiceResponse> SendMRLowBalanceStartSms(string phoneNumber, string firstName);
        Task<ServiceResponse> SendMRLowBalanceEndSms(string phoneNumber);
        Task<ServiceResponse> SendVpnSubscriptionSuccessMessage(string phoneNumber, string vpnCode);
        Task<ServiceResponse<string>> GetTextMessageTemplate(string path);
    }
}
 