﻿using AutoMapper;
using C3Pay.API.Resources;
using C3Pay.API.Resources.Subscriptions;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs.Dashboard.Base;
using C3Pay.Core.Models.DTOs.Dashboard.Popup.Responses;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using C3Pay.Core.Repositories;
using C3Pay.Core.Services;
using C3Pay.Services.Dashboard.Popup.Queries;
using C3Pay.Services.Membership.Queries;
using C3Pay.Services.Membership.BenefitsShell.Queries;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.ESMO;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// Dahsboard Controller
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [ApiController]
    [InputValidation]
    [Authorize]
    //[DeviceAuthorize]
    public class DashboardController : BaseController
    {

        /// <summary>
        /// 
        /// </summary>
        private readonly IMediator _mediator;


        private readonly ILookupService _lookupService;
        private readonly IESMOWebService _esmoWebService;
        private readonly IMoneyTransferService _moneyTransferService;
        private readonly IMapper _mapper;
        private readonly ISubscriptionService _subscriptionService;
        private readonly IExperimentService _experimentService;
        private readonly IMobileRechargeService _mobileRechargeService;
        private readonly IFeatureManager _featureManager;
        private readonly IUserRepository _userRepository;
        private readonly IUnitOfWork _unitOfWork;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="httpContextAccessor"></param>
        /// <param name="userService"></param>
        /// <param name="logger"></param>
        /// <param name="lookupService"></param>
        /// <param name="mapper"></param>
        /// <param name="esmoWebService"></param>
        /// <param name="subscriptionService"></param>
        /// <param name="moneyTransferService"></param>
        /// <param name="experimentService"></param>
        public DashboardController(IHttpContextAccessor httpContextAccessor,
        IUserService userService,
        ILogger<DashboardController> logger,
        ILookupService lookupService,
        IMapper mapper,
        IESMOWebService esmoWebService,
        ISubscriptionService subscriptionService,
        IMoneyTransferService moneyTransferService,
        IExperimentService experimentService, IMobileRechargeService mobileRechargeService, IFeatureManager featureManager, IUserRepository userRepository,
        IMediator mediator,
        IUnitOfWork unitOfWork)
        : base(httpContextAccessor, userService, logger)
        {
            _lookupService = lookupService;
            _mapper = mapper;
            _esmoWebService = esmoWebService;
            _subscriptionService = subscriptionService;
            _moneyTransferService = moneyTransferService;
            _experimentService = experimentService;
            _mobileRechargeService = mobileRechargeService;
            _featureManager = featureManager;
            _userRepository = userRepository;
            _mediator = mediator;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get All Dashboard Actions
        /// </summary>
        /// <returns> DashboardSectionResponseDto[] </returns>
        [HttpGet("sections")]
        public async Task<ActionResult<List<DashboardSectionResponseDto>>> GetAllDashboardASections()
        {
            string languageCode = Request.Headers["x-lang-code"];

            await GetLoggedInUser();

            if (_user == null) return this.Unauthorized("");

            var response = new DashboardSectionResponseDto();
            // Sections
            var sections = await _lookupService.GetSections();

            if (sections.IsSuccessful)
            {
                // Quick Actions 
                await GetQuickActionSection(languageCode, _user.CardHolder.Nationality, _user.CardHolder.BelongsToExchangeHouse, response, sections);

                // Subscriptions
                await GetSubscriptions(_user.CardHolder.CorporateId, languageCode, response);

                // If user subscribed then show ATM Pin else show the Security Alerts
                ShowHideQuickActsBasedOnSubscription(response);

                //Hide Quick Actions based on experiment
                await ShowHideQuickActsBasedOnExperiment(response);

                // Hide Quick Actions based on experiment (Money Transfer dashboard GIF)
                await ShowHideQuickActsBasedOnExperiment_MTGif(response);

                // Get MoneyTransfer Eligibility for tags 
                var allTags = await _lookupService.GetQuickActionTags(languageCode);

                var eligibility = await _moneyTransferService.GetUserFreeTransferEigibilityLevel(_user.Id);

                // Check for 50% off for Mobile Recharge Fixed users 
                bool haveMRDiscount = false;
                var haveMRDiscountResponse = await _mobileRechargeService.CheckForMobileRechargeDiscount(_user.Id);
                if (haveMRDiscountResponse.IsSuccessful)
                    haveMRDiscount = haveMRDiscountResponse.Data;

                // Track original icons before special icon handling
                var originalIconIds = response.QuickAction.Details.Select(x => x.Id).ToHashSet();

                // Here, we have the logic to display/hide the C3Pay+ icon.
                await HandleC3PayPlusIcon(response, _user.PhoneNumber);

                // VPN Quick Action logic
                await HandleVpnIcon(response);

                // Arrange final icon positions - C3Pay+ at 7th, VPN at 8th
                ArrangeFinalIconPositions(response, originalIconIds);

                var goldAndSpinExperiment = await _experimentService.FetchUserIsInRetentionGoldAndSpinWheel(_user.CardHolderId);
                var goldAndSpinExperimentDetails = goldAndSpinExperiment.IsSuccessful ? goldAndSpinExperiment.Data : null;

                var tryIsUserInSpinTheWheelExperimentData = await _experimentService.CheckUserIsInSpinTheWheelExperiment(_user.CardHolderId);
                var isUserInSpinTheWheelExperiment = tryIsUserInSpinTheWheelExperimentData.IsSuccessful && tryIsUserInSpinTheWheelExperimentData.Data;

                SetTag(response, allTags, _user.CardHolder.Nationality, eligibility.IsSuccessful ? eligibility.Data : null, haveMRDiscount, goldAndSpinExperimentDetails, isUserInSpinTheWheelExperiment);
            }

            return Ok(response);
        }





        /// <summary>
        /// Query: <see cref="GetDashboardPopupQuery" />
        /// Handler: <see cref="GetDashboardPopupQueryHandler" />
        /// </summary>
        /// <param name="lastPopupSeenDate"></param>
        /// <param name="lastSeenPopup"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("popup")]
        public async Task<ActionResult<DashboardBaseResponse<DashboardPopupResponseDto>>> GetDashboardPopupDetails(DateTime? lastPopupSeenDate, int? lastSeenPopup, CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            await GetLoggedInUser();

            var tryGetDashboardPopupQuery = await _mediator.Send(new GetDashboardPopupQuery()
            {
                LanguageCode = languageCode,

                LastPopupSeenDate = lastPopupSeenDate,
                LastSeenPopup = lastSeenPopup,

                User = this._user,
            }, ct);

            if (tryGetDashboardPopupQuery.IsFailure)
            {
                return BadRequest(new DashboardBaseResponse<DashboardPopupResponseDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryGetDashboardPopupQuery.Error.Code
                });
            }


            return Ok(tryGetDashboardPopupQuery.Value);
        }







        private async Task HandleC3PayPlusIcon(DashboardSectionResponseDto response, string phoneNumber)
        {
            // First, prepare icon list to be modified.
            // If C3Pay+ is enabled, we need to replace the last icon with C3Pay+ icon.
            response.QuickAction.Details = response.QuickAction.Details.ToList();



            // Next, we need to check if we have the icon loaded for C3Pay+.
            // This icon should always be returned, we will decide later if we want to show it or not.
            var c3PayIcon = response.QuickAction.Details.FirstOrDefault(x => x.Id == (int)DashboardElementItem.C3PayPlus);
            if (c3PayIcon == null)
            {
                // No icon is found, log an error since this should not happen and exit.
                _logger.LogError(Errors.C3PayPlus.DashboardIconNotLoaded.Code);
                return;
            }



            // Now, we need to check if C3Pay+ is globally enabled or not by checking feature flag 'GlobalC3PayPlus'.
            var isC3PayPlusEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.GlobalC3PayPlus);
            if (isC3PayPlusEnabled == false)
            {
                // C3Pay+ is not enabled, so we need to remove the icon and exit.
                response.QuickAction.Details = response.QuickAction.Details.Where(x => x.Id != (int)DashboardElementItem.C3PayPlus).ToList();
                return;
            }



            // At this point, C3Pay+ is enabled. So we need to check if the user can access C3Pay+ based on their profile.
            // We alreay have the user object loaded, we need to check if the user is blocked or not to match the checks we have in C3Pay+ APIs.
            if (this._user.IsBlocked == true)
            {
                // User is blocked, so we need to remove the icon and exit.
                response.QuickAction.Details = response.QuickAction.Details.Where(x => x.Id != (int)DashboardElementItem.C3PayPlus).ToList();
                return;
            }



            // To perform all C3Pay+ eligibility checks, we need to load the user's identifications.
            this._user.Identifications = await this._userRepository.GetUserIdentifications(this._user.Id);
            var isC3PayPlusEligible = this._user.IsC3PayPlusEligible();
            if (isC3PayPlusEligible.IsFailure)
            {
                // User is not eligible for C3Pay+, so we need to remove the icon and exit.
                response.QuickAction.Details = response.QuickAction.Details.Where(x => x.Id != (int)DashboardElementItem.C3PayPlus).ToList();
                return;
            }



            // At this point, C3Pay+ is enabled && the user can potentially view C3Pay+.
            // Set a flag to tell if we can show the icon so we skip any extra steps and exit.
            bool canViewC3PayPlus = false;


            // Check if this user's corporate is part of the rollout.
            var isCorporateDisabled = await this._lookupService.IsCorporateC3PayPlusEnabled(this._user.CardHolder.CorporateId);
            if (isCorporateDisabled.IsSuccessful == true && isCorporateDisabled.Data == true)
            {
                // User is eligible for C3Pay+, keep the icon in the list (positioning will be handled later)
                canViewC3PayPlus = true;
                return;
            }


            // At the end, check value of 'canViewC3PayPlus' and if the value was false => hide the icon.
            if (canViewC3PayPlus == false)
            {
                // If C3Pay+ is not enabled, we need to remove the icon.
                response.QuickAction.Details = response.QuickAction.Details.Where(x => x.Id != (int)DashboardElementItem.C3PayPlus).ToList();
            }
        }

        private async Task HandleVpnIcon(DashboardSectionResponseDto response)
        {
            try
            {
                // Silent return if prerequisites not met
                if (response?.QuickAction?.Details == null || _user?.PhoneNumber == null)
                    return;

                // Check if VPN feature is globally enabled
                var isVpnEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.Memberships_Vpn_Global);
                if (!isVpnEnabled)
                    return;

                // Ensure response.QuickAction.Details is a mutable list
                response.QuickAction.Details = response.QuickAction.Details.ToList();

                // Get VPN dashboard icon details using proper query
                var vpnIconResult = await _mediator.Send(new GetVpnDashboardIconQuery
                {
                    UserPhoneNumber = _user.PhoneNumber
                });

                // Only add VPN icon if user is entitled and has valid icon data
                if (vpnIconResult?.IsSuccess == true && vpnIconResult?.Value?.IsEntitled == true && vpnIconResult?.Value?.DashboardIconId != null)
                {
                    var iconData = vpnIconResult.Value;

                    // Check if icon not already present
                    if (!response.QuickAction.Details.Any(x => x.Id == iconData.DashboardIconId.Value))
                    {
                        var vpnQuickAction = new QuickActionResponseDto
                        {
                            Id = iconData.DashboardIconId.Value,
                            Description = iconData.DashboardIconTitle ?? "VPN",
                            IconUrl = iconData.DashboardIconUrl ?? "",
                            DeepLinkUrl = iconData.DashboardIconDeeplink ?? "",
                            Tag = iconData.DashboardIconSubtitle ?? ""
                        };

                        // Add VPN icon to the list (positioning will be handled later)
                        response.QuickAction.Details.Add(vpnQuickAction);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while handling VPN icon for user {PhoneNumber}", _user?.PhoneNumber ?? "unknown");
                // Don't rethrow - we don't want VPN icon issues to break the entire dashboard
            }
        }


        private void ArrangeFinalIconPositions(DashboardSectionResponseDto response, HashSet<int> originalIconIds)
        {
            if (response?.QuickAction?.Details == null)
                return;

            // Ensure we have a mutable list
            response.QuickAction.Details = response.QuickAction.Details.ToList();

            // Find C3Pay+, VPN, and BillPayment icons
            var c3PayIcon = response.QuickAction.Details.FirstOrDefault(x => x.Id == (int)DashboardElementItem.C3PayPlus);

            // VPN icons are any icons that weren't in the original list (added by HandleVpnIcon)
            var vpnIcon = response.QuickAction.Details.FirstOrDefault(x => !originalIconIds.Contains(x.Id) && x.Id != (int)DashboardElementItem.C3PayPlus);

            // BillPayment icon
            var billPaymentIcon = response.QuickAction.Details.FirstOrDefault(x => x.Id == (int)DashboardElementItem.BillPayments);

            // Remove special icons from current positions
            if (c3PayIcon != null)
                response.QuickAction.Details = response.QuickAction.Details.Where(x => x.Id != (int)DashboardElementItem.C3PayPlus).ToList();

            if (vpnIcon != null)
                response.QuickAction.Details = response.QuickAction.Details.Where(x => x.Id != vpnIcon.Id).ToList();

            if (billPaymentIcon != null)
                response.QuickAction.Details = response.QuickAction.Details.Where(x => x.Id != (int)DashboardElementItem.BillPayments).ToList();

            // Insert C3Pay+ at position 7 (index 6) if it exists
            if (c3PayIcon != null)
            {
                int c3PayPosition = Math.Min(6, response.QuickAction.Details.Count);
                response.QuickAction.Details.Insert(c3PayPosition, c3PayIcon);
            }

            // Insert VPN at position 8 (index 7) if it exists
            if (vpnIcon != null)
            {
                int vpnPosition = Math.Min(7, response.QuickAction.Details.Count);
                response.QuickAction.Details.Insert(vpnPosition, vpnIcon);
            }

            // Insert BillPayment at position 9 (index 8) ONLY if we have 9+ total icons
            if (billPaymentIcon != null)
            {
                if (response.QuickAction.Details.Count >= 8)
                {
                    // Position BillPayment at 9th position when there are 9+ icons
                    int billPaymentPosition = Math.Min(8, response.QuickAction.Details.Count);
                    response.QuickAction.Details.Insert(billPaymentPosition, billPaymentIcon);
                }
                else
                {
                    // If less than 9 icons total, add BillPayment back to its natural position
                    response.QuickAction.Details.Add(billPaymentIcon);
                }
            }
        }


        private static void SetTag(DashboardSectionResponseDto response,
        ServiceResponse<IList<DashboardQuickActionElementTag>> allTags,
        string nationality,
        MoneyTransferFreeTransferEligiblity eligibility,
        bool isPartOfMROffer,
        ExperimentUsers goldAndSpinExperiment,
        bool isUserInSpinTheWheelExperiment)
        {
            foreach (var _item in response.QuickAction.Details.ToList())
            {
                if (eligibility != null && (_item.Id == (int)DashboardElementItem.MoneyTransfer || _item.Id == (int)DashboardElementItem.MoneyTransfer_Bank || _item.Id == (int)DashboardElementItem.MoneyTransfer_CashPickup)) // Money Transfer 
                {
                    if (isUserInSpinTheWheelExperiment)
                    {
                        _item.Tag = allTags.Data.FirstOrDefault(a => a.Id == (int)DashboardQuickActionTag.MT_SpinTheWheel).TextContent.Translations.FirstOrDefault().Text;
                    }
                    else if (goldAndSpinExperiment != null &&
                    (goldAndSpinExperiment.GroupCode == ExperimentGroupCodeType.A.ToString() || goldAndSpinExperiment.GroupCode == ExperimentGroupCodeType.B.ToString()))
                    {
                        if (goldAndSpinExperiment.GroupCode == ExperimentGroupCodeType.A.ToString())
                            _item.Tag = allTags.Data.FirstOrDefault(a => a.Id == (int)DashboardQuickActionTag.MT_SpinTheWheel).TextContent.Translations.FirstOrDefault().Text;
                        else
                            _item.Tag = allTags.Data.FirstOrDefault(a => a.Id == (int)DashboardQuickActionTag.MT_GoldIncentive).TextContent.Translations.FirstOrDefault().Text;
                    }
                    else if (eligibility.IsFirstTransfer)
                    {
                        _item.Tag = allTags.Data.FirstOrDefault(a => a.Id == (int)DashboardQuickActionTag.MT_NoCharges).TextContent.Translations.FirstOrDefault().Text;
                    }
                    else if (eligibility.LoyaltyCount < 4)
                    {
                        if (nationality == "PAK")
                        {
                            _item.Tag = allTags.Data.FirstOrDefault(a => a.Id == (int)DashboardQuickActionTag.MT_NoCharges).TextContent.Translations.FirstOrDefault().Text;
                        }
                        else if (nationality == "BGD")
                        {
                            _item.Tag = allTags.Data.FirstOrDefault(a => a.Id == (int)DashboardQuickActionTag.MT_Get2Point5Percent).TextContent.Translations.FirstOrDefault().Text;
                        }
                        else if (nationality == "PHL")
                        {
                            _item.Tag = allTags.Data.FirstOrDefault(a => a.Id == (int)DashboardQuickActionTag.MT_ZeroDeduction).TextContent.Translations.FirstOrDefault().Text;
                        }
                    }
                    else if (eligibility.LoyaltyCount >= 4)
                    {
                        _item.Tag = allTags.Data.FirstOrDefault(a => a.Id == (int)DashboardQuickActionTag.MT_NoCharges).TextContent.Translations.FirstOrDefault().Text;
                    }
                }
                else if (_item.Id == (int)DashboardElementItem.UnemploymentInsurance) // for unemployment insurance
                {
                    _item.Tag = allTags.Data.FirstOrDefault(a => a.Id == (int)DashboardQuickActionTag.COMMON_New).TextContent.Translations.FirstOrDefault().Text;
                }
                else if (_item.Id == (int)DashboardElementItem.InternationalMobileRecharge && isPartOfMROffer) // For international Recharge
                {
                    _item.Tag = allTags.Data.FirstOrDefault(a => a.Id == (int)DashboardQuickActionTag.MR_50PercentOff).TextContent.Translations.FirstOrDefault().Text;
                }
            };
        }

        private static void ShowHideQuickActsBasedOnSubscription(DashboardSectionResponseDto response)
        {
            if (response.Subscription.Details.Any())
            {
                if (response.Subscription.Details.Find(subscription => subscription.Code == nameof(BaseEnums.SMSSubscriptionType.T)).IsActive)
                {
                    response.QuickAction.Details = response.QuickAction.Details.Where(a => a.Id != (int)DashboardElementItem.SecurityAlerts).ToImmutableList();
                }
                else
                {
                    response.QuickAction.Details = response.QuickAction.Details.Where(a => a.Id != (int)DashboardElementItem.Transactions).ToImmutableList();
                }
            }
            else
            {
                response.QuickAction.Details = response.QuickAction.Details.Where(a => a.Id != (int)DashboardElementItem.Transactions).ToImmutableList();
            }
        }

        private async Task ShowHideQuickActsBasedOnExperiment(DashboardSectionResponseDto response)
        {
            if (response.QuickAction.Details.Any())
            {
                var result = await _experimentService.CheckUserIsInCashPickupExperiment(_user.CardHolderId);
                if (result.IsSuccessful && result.Data)
                {
                    response.QuickAction.Details = response.QuickAction.Details.Where(a => a.Id != (int)DashboardElementItem.MoneyTransfer &&
                    a.Id != (int)DashboardElementItem.BillPayments).ToImmutableList();
                }
                else
                {
                    response.QuickAction.Details = response.QuickAction.Details.Where(a => a.Id != (int)DashboardElementItem.MoneyTransfer_Bank &&
                    a.Id != (int)DashboardElementItem.MoneyTransfer_CashPickup).ToImmutableList();
                }
            }
        }
        private async Task ShowHideQuickActsBasedOnExperiment_MTGif(DashboardSectionResponseDto response)
        {
            if (response.QuickAction.Details.Any())
            {
                var groupCode = await _experimentService.CheckUserIsInMTGifExperiment(_user.CardHolderId);
                if (groupCode.IsSuccessful && groupCode.Data != null)
                {
                    // Group A will be shown the bank logos + no tag + No recharge animation.
                    if (groupCode.Data.GroupCode == "A")
                    {
                        // Remove default MT item + roket icon.
                        // The response will have the new items.
                        response.QuickAction.Details = response.QuickAction.Details.Where(a => a.Id != (int)DashboardElementItem.MoneyTransfer
                        && a.Id != (int)DashboardElementItem.MoneyTransfer_Bank
                        && a.Id != (int)DashboardElementItem.InternationalMobileRecharge
                        && a.Id != (int)DashboardElementItem.MoneyTransfer_Gif_Rocket).ToImmutableList();


                    }
                    // Group B will be shown logo with plane from the back + no tag + No recharge animation.
                    else if (groupCode.Data.GroupCode == "B")
                    {
                        // Remove default MT item + bank logo icon.
                        // The response will have the new items.
                        response.QuickAction.Details = response.QuickAction.Details.Where(a => a.Id != (int)DashboardElementItem.MoneyTransfer
                        && a.Id != (int)DashboardElementItem.MoneyTransfer_Bank
                        && a.Id != (int)DashboardElementItem.InternationalMobileRecharge
                        && a.Id != (int)DashboardElementItem.MoneyTransfer_Gif_BankLogos).ToImmutableList();
                    }
                    // Group C is the default: Current production Experience + recharge animation.
                    else if (groupCode.Data.GroupCode == "C")
                    {
                        // Remove default MT item + bank logo icon.
                        // The response will have the new items.
                        // Not part of anything, remove the added stuff.
                        response.QuickAction.Details = response.QuickAction.Details.Where(a => a.Id != (int)DashboardElementItem.MoneyTransfer_Gif_BankLogos
                        && a.Id != (int)DashboardElementItem.MoneyTransfer_Gif_Rocket
                        && a.Id != (int)DashboardElementItem.MobileRecharge_No_Animation).ToImmutableList();
                    }
                }
                else
                {
                    // Not part of anything, remove the added stuff.
                    response.QuickAction.Details = response.QuickAction.Details.Where(a => a.Id != (int)DashboardElementItem.MoneyTransfer_Gif_BankLogos
                    && a.Id != (int)DashboardElementItem.MoneyTransfer_Gif_Rocket
                    && a.Id != (int)DashboardElementItem.MobileRecharge_No_Animation).ToImmutableList();
                }
            }
        }

        private async Task GetSubscriptions(string corporateId, string languageCode, DashboardSectionResponseDto response)
        {
            try
            {
                var userSubscribedToBalanceEnquiry = false;

                var subscriptionsResult = await this._subscriptionService.GetSubscripitonsList(corporateId, languageCode);

                if (!subscriptionsResult.IsSuccessful)
                    response.Subscription = new SubscriptionElementDto(new List<SubscriptionDto>(), false);

                var subscriptions = subscriptionsResult.Data;

                var mappedSubscriptions = this._mapper.Map<List<SubscriptionDto>>(subscriptions.ToList());

                mappedSubscriptions = mappedSubscriptions.OrderBy(subscription => subscription.DisplayOrder).ToList();

                //Balance Enquiry
                var balanceEnquirySubscription = mappedSubscriptions.Single(subscription => subscription.Code == BaseEnums.SMSSubscriptionType.BE.ToString());

                var userSubscribedToBalanceEnquiryResult = await this._subscriptionService.UserHasActiveSubscription(_user.Id, balanceEnquirySubscription.Id);

                if (!userSubscribedToBalanceEnquiryResult.IsSuccessful)
                    response.Subscription = new SubscriptionElementDto(new List<SubscriptionDto>(), false);


                userSubscribedToBalanceEnquiry = userSubscribedToBalanceEnquiryResult.Data;

                var request = new GetSMSSubscriptionModeRequest()
                {
                    CardSerialNo = _user.CardHolder.CardSerialNumber,
                    CorporateId = int.Parse(_user.CardHolder.CorporateId)
                };

                var subscriptionModeResult = await _esmoWebService.GetSMSSubscriptionMode(request);

                if (!subscriptionModeResult.IsSuccessful)
                {
                    response.Subscription = new SubscriptionElementDto(new List<SubscriptionDto>(), false);
                }

                var subscriptionMode = subscriptionModeResult.Data;

                mappedSubscriptions.Single(subscription => subscription.Code == BaseEnums.SMSSubscriptionType.BE.ToString()).IsActive = userSubscribedToBalanceEnquiry;
                mappedSubscriptions.Single(subscription => subscription.Code == BaseEnums.SMSSubscriptionType.S.ToString()).IsActive = subscriptionMode.SubscribedToSalaryAlert;
                mappedSubscriptions.Single(subscription => subscription.Code == BaseEnums.SMSSubscriptionType.T.ToString()).IsActive = subscriptionMode.SubscribedToSecurityAlerts;

                // Get Videos for SMS Security 
                mappedSubscriptions.Single(subscription => subscription.Code == nameof(BaseEnums.SMSSubscriptionType.T)).Video = await GetVideoUrls(_user);

                response.Subscription = new SubscriptionElementDto(mappedSubscriptions, true);
            }
            catch (Exception)
            {
                response.Subscription = new SubscriptionElementDto(new List<SubscriptionDto>(), false);
            }
        }

        private async Task GetQuickActionSection(string languageCode, string nationality, bool belongsToExchangeHouse, DashboardSectionResponseDto response, ServiceResponse<IList<DashboardSection>> sections)
        {
            var quickActionSection = sections.Data.FirstOrDefault(a => a.Id == (int)DashboardSectionType.QuickAction);
            if (quickActionSection != null)
            {
                var quickActionDtos = new List<QuickActionResponseDto>();
                if (quickActionSection.IsActive)
                {
                    var quickActions = await _lookupService.GetQuickActions(languageCode, nationality, belongsToExchangeHouse);
                    quickActionDtos = _mapper.Map<IList<DashboardElement>, List<QuickActionResponseDto>>(quickActions.Data);
                }
                response.QuickAction = new QuickActionElementDto(quickActionDtos, quickActionSection.IsActive);
            }
        }
        private async Task<List<SubscriptionMultimediaDto>> GetVideoUrls(User _user)
        {
            var hindiNationalities = new HashSet<string> { nameof(BaseEnums.MultimediaCountry.PAK), nameof(BaseEnums.MultimediaCountry.IND) };
            var userShouldSeeHindiVideo = hindiNationalities.Contains(_user.CardHolder.Nationality);

            var multimediaResources = await _lookupService.GetMultimediaResources(feature: (int)FeatureType.Subscription_SecuritySMS).ConfigureAwait(false);

            bool defaultAlreadySet = false;
            return multimediaResources.Data
            .Select(resource => new SubscriptionMultimediaDto
            {
                LanguageCode = resource.Language,
                Url = resource.Url,
                IsDefault = ((resource.Language == "hi" && userShouldSeeHindiVideo)
            || (resource.Language != "hi" && !userShouldSeeHindiVideo))
            && !defaultAlreadySet && (defaultAlreadySet = true)
            })
            .ToList();
        }
    }
}