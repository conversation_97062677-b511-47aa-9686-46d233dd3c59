﻿using System;


namespace Common.Core.Models
{
    
        public class ValidateSendTransferResponseDto
        {
            public bool ValidateSuccess { get; set; }// false
            public string ValidationMessage { get; set; }//provider 
            public ScreenContentDto ScreenContent { get; set; }
    }
        

        public class ScreenContentDto
        {
            public string Title { get; set; }
            public string SubTitle { get; set; }
            public ButtonDto PrimaryButton { get; set; }
            public ButtonDto SecondaryButton { get; set; }
        }

        public class ButtonDto
        {
            public string Text { get; set; }
        }

      
}
