﻿using Edenred.Common.Core;
using System;
using System.Threading.Tasks;

namespace C3Pay.Core.Services.Security
{
    public interface ISecurityService
    {
        Task<ServiceResponse<bool>> UsernameMatchesUser(Guid? id, string username = null, bool fromMock = true);
        Task<ServiceResponse<Guid?>> UsernameMatchesUserMoneyTransferBeneficiary(Guid beneficiaryId, bool fromMock = true);
        Task<ServiceResponse<Guid?>> UsernameMatchesUserMobileRechargeBeneficiary(Guid beneficiaryId, bool fromMock = true);
        Task<ServiceResponse<Guid?>> UsernameMatchesUserMoneyTransferTransaction(Guid transactionId, bool fromMock = true);
        Task<ServiceResponse<Guid?>> UsernameMatchesUserMobileRechargeTransaction(Guid transactionId, bool fromMock = true);
        Task<bool> IsUserOTPAuthorized();
        Task<bool> IsUserOTPAuthorizedV2();
        Task<bool> VerifyUserOTP();
    }
}
