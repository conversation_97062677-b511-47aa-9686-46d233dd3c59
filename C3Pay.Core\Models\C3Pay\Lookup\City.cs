﻿using System.Collections.Generic;

namespace C3Pay.Core.Models
{
    public class City
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string CountryCode { get; set; }
        public bool IsStoreEnabled { get; set; }
        public int? StateId { get; set; }

        public State State { get; set; }
        public Country Country { get; set; }
        public List<OrderAddress> OrderAddresses { get; set; }
        public List<MoneyTransferSuspiciousInformation> MoneyTransferSuspiciousInformations { get; set; }
    }
}
