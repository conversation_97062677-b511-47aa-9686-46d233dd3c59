﻿using C3Pay.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Text;

namespace C3Pay.Data.EntityConfigurations.Lookup
{
    public class CityConfiguration : IEntityTypeConfiguration<City>
    {
        public void Configure(EntityTypeBuilder<City> builder)
        {
            builder.ToTable("Cities");

            builder.<PERSON><PERSON><PERSON>(c => c.Id); 

            builder.Property(c => c.Id).ValueGeneratedOnAdd();

            builder.Property(c => c.Name)
                .IsRequired()
                .HasMaxLength(150);

            builder.Property(c => c.CountryCode)
                .HasMaxLength(2);

            builder.HasMany(c => c.OrderAddresses) 
                .WithOne(p => p.City)
                .HasForeignKey(c => c.CityId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
               .HasMany(c => c.MoneyTransferSuspiciousInformations)
               .WithOne(u => u.City)
               .HasForeignKey(u => u.CityId)
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasData(
                new City()
                {
                    Id = 1,
                    Name = "Dubai",
                    CountryCode = "AE",
                    IsStoreEnabled = true,
                },
                new City()
                {
                    Id = 2,
                    Name = "Abu Dhabi",
                    CountryCode = "AE",
                    IsStoreEnabled = true,
                },
                new City()
                {
                    Id = 3,
                    Name = "Sharjah",
                    CountryCode = "AE",
                    IsStoreEnabled = true,
                },
                new City()
                {
                    Id = 4,
                    Name = "Ajman",
                    CountryCode = "AE",
                    IsStoreEnabled = true,
                },
                new City()
                {
                    Id = 5,
                    Name = "Ras Al Khaimah",
                    CountryCode = "AE",
                    IsStoreEnabled = true,
                },
                new City()
                {
                    Id = 6,
                    Name = "Fujairah",
                    CountryCode = "AE",
                    IsStoreEnabled = true,
                },
                new City()
                {
                    Id = 7,
                    Name = "Umm Al-Quwain",
                    CountryCode = "AE",
                    IsStoreEnabled = true,
                }
            );
        }
    }
}
