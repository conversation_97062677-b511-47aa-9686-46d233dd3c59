﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.Portal;
using C3Pay.Core.Models.Portal.BlackList;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace C3Pay.Core.Services
{
    public interface IBlackListService
    {
        Task<ServiceResponse<Tuple<IList<BlackListedEntity>, int>>> SearchBlackList(SearchBlackListParameters searchBlackListParameters);
        Task<ServiceResponse<bool>> DeleteBlackList(Guid blackListId);
        Task<ServiceResponse<List<User>>> GetUsersByBeneficiary(SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<ServiceResponse> AddBlackList(SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<string> GetCountryCode(SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<ServiceResponse<bool>> CheckDuplicate(SearchBeneficiaryParameters searchBeneficiaryParameters);
    }
}
