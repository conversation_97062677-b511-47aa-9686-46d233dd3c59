﻿using C3Pay.Core;
using C3Pay.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace C3Pay.API.StartupExtensions
{
    /// <summary>
    /// 
    /// </summary>
    public static class DatabaseExtension
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void InjectDatabase(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IUnitOfWorkReadOnly, UnitOfWorkReadOnly>();

            int poolSize = 1000;

            int.TryParse(configuration["General:DBPoolSize"], out poolSize);

            services.AddDbContext<C3PayContextReadonly>(
                       options =>
                       options.UseSqlServer(configuration.GetConnectionString("C3PayConnectionReadOnly"),
                       x => x.MigrationsAssembly("C3Pay.Data"))
                   );

            services.AddDbContextPool<C3PayContext>(
                    options =>
                    options.UseSqlServer(configuration.GetConnectionString("C3PayConnection"),
                    x => x.MigrationsAssembly("C3Pay.Data")),
                    poolSize
                );
        }
    }
}
