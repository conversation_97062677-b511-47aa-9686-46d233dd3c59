using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus;
using Edenred.Common.Core;
using System.Threading;
using System.Threading.Tasks;

namespace C3Pay.Core.Services.C3Pay.Membership
{
    public interface IC3PayPlusTargetedDiscountService
    {
        Task<Result<C3PayPlusTargetedDiscountEligibility>> CheckEligibilityAsync(
            User user, 
            bool hasBalanceEnquiry,
            bool hasSecuritySms, 
            bool hasSalaryAlerts,
            int c3PayPlusVideoViewCount,
            CancellationToken ct);
        Task<Result<C3PayPlusTargetedDiscountPricing>> CalculateDiscountAsync(
            User user,
            C3PayPlusTargetedDiscountEligibility eligibility,
            C3PayPlusMembership membership,
            CancellationToken ct);
    }
}