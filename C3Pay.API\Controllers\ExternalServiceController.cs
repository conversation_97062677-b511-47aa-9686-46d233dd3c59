﻿using C3Pay.Core.Models.DTOs.ExternalService;
using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus;
using C3Pay.Core.Services.C3Pay.Membership;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using C3Pay.Core.Services;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// Api's are invloked from Intenal or External Application
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [InputValidation]
    public class ExternalServiceController: Controller
    {
        private readonly IWebhookAuthenticationService _callbackAuthService;
        private readonly IMediator _mediator;
        private readonly GeneralSettings _generalSettings;
        private readonly IExternalService _externalService;

        /// <summary>
        /// intitialize the dependecy
        /// </summary>
        /// <param name="callbackAuthService"></param>
        /// <param name="mediator"></param>
        /// <param name="generalSettings"></param>
        /// <param name="externalService"></param>
        public ExternalServiceController(IWebhookAuthenticationService callbackAuthService, IMediator mediator,
            IOptions<GeneralSettings> generalSettings,
            IExternalService externalService)
        {
            _callbackAuthService = callbackAuthService;
            _mediator = mediator;
            _generalSettings = generalSettings.Value;
            _externalService = externalService;

        }

        [AllowAnonymous]
        [HttpPost("infobip-callback/{token}")]
        public async Task<ActionResult> InfobipVoiceCallResultCallback([FromRoute] string token, [FromBody] InfobipCallbackRequestDto request)
        {

            var isValidAuthToken = _callbackAuthService.ValidateAuthToken(token);
            if (!isValidAuthToken)
                return Unauthorized();

            var infobipCallback = request.Results.FirstOrDefault();
            if (infobipCallback != null)
            {
                // Do processing by calling services
            }
                
            return Ok();
        }

        /// <summary>
        /// Get Card Holder Details
        /// </summary>
        /// <returns></returns>
        [HttpPost("cardholder-details")]
        public async Task<ActionResult<List<CardHoldersDto>>> GetCardHolderDetails([FromBody] List<string> cardHolderIds)
        {
            var apiKey = Request.Headers["x-api-key"].FirstOrDefault();

            if (!Request.Headers.ContainsKey("x-api-key") || apiKey != _generalSettings.C3PayAPIKeyForEsmoService)
            {
                return BadRequest("Invalid Api key");
            }

            var cardHolders = await _externalService.GetCardHolderDetails(cardHolderIds);
            if (cardHolders.IsSuccessful == false)
            {
                return BadRequest(cardHolders.ErrorMessage);
            }

            return Ok(cardHolders.Data);
        }

    }
}
