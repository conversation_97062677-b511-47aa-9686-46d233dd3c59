﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Models\C3Pay\Subscriptions\**" />
    <Compile Remove="Models\DTOs\MoneyTransfer\Beneficiary\**" />
    <Compile Remove="Models\MobileTopup\**" />
    <Compile Remove="Models\MoneyTransfer\**" />
    <Compile Remove="Repositories\C3Pay\MoneyTransfer\Lookups\**" />
    <EmbeddedResource Remove="Models\C3Pay\Subscriptions\**" />
    <EmbeddedResource Remove="Models\DTOs\MoneyTransfer\Beneficiary\**" />
    <EmbeddedResource Remove="Models\MobileTopup\**" />
    <EmbeddedResource Remove="Models\MoneyTransfer\**" />
    <EmbeddedResource Remove="Repositories\C3Pay\MoneyTransfer\Lookups\**" />
    <None Remove="Models\C3Pay\Subscriptions\**" />
    <None Remove="Models\DTOs\MoneyTransfer\Beneficiary\**" />
    <None Remove="Models\MobileTopup\**" />
    <None Remove="Models\MoneyTransfer\**" />
    <None Remove="Repositories\C3Pay\MoneyTransfer\Lookups\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Repositories\C3Pay\IRMTProfileFileRepository.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Azure.CognitiveServices.Vision.ComputerVision" Version="7.0.1" />
    <PackageReference Include="SendGrid" Version="9.29.3" />
    <PackageReference Include="System.Resources.ResourceManager" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="ResourceFile\UIResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>UIResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="ResourceFile\UIResource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>UIResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\DTOs\Dashboard\Popup\Requests\" />
  </ItemGroup>  

</Project>
