using C3Pay.Core.Models;
using System;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models.C3Pay.Membership.C3PayPlus
{
    public class C3PayPlusTargetedDiscountOffer
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string CardholderId { get; set; }
        public DateTime OfferStartDate { get; set; }
        public DateTime OfferEndDate { get; set; }
        public bool IsActive { get; set; }
        public bool HasExpired { get; set; }
        public bool WasUsed { get; set; }
        public C3PayPlusMembershipType RecommendedMembershipType { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public global::C3Pay.Core.Models.User User { get; set; }
    }
}