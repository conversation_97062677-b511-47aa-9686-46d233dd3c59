﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.Settings;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay;
using C3Pay.Data;
using C3Pay.Portal.API.Extensions;
using C3Pay.Portal.API.Filters;
using C3Pay.Portal.API.Middleware;
using C3Pay.Services;
using C3Pay.Services.Helper;
using C3Pay.Services.Mock;
using Edenred.Common.Core;
using Edenred.Common.Data;
using Edenred.Common.Services;
using Edenred.Common.Services.Azure.Extensions;
using Edenred.Common.Services.Extension;
using Edenred.Common.Services.Extensions;
using IdentityServer4.AccessTokenValidation;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Microsoft.OpenApi.Models;
using Serilog;
using System;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using C3Pay.Core.Services.C3Pay;
using System.Configuration;
using C3Pay.Core.Abstractions.Cache;
using C3Pay.Services.Cache;
using C3Pay.Services.Membership.Queries;
using Microsoft.Identity.Web;
using C3Pay.Portal.API.Mapping;
using Microsoft.AspNetCore.Authentication;
using MediatR;
using C3Pay.Services.Dashboard.Popup.Queries;
using C3Pay.Core.Services.C3Pay.Membership;
using C3Pay.Core.Repositories;
using C3Pay.Data.Repositories;
using C3Pay.Core.Services.LoginVideos;
using C3Pay.Services.LoginVideos;
using C3Pay.Services.LoginVideos.Validators;
using C3Pay.Services.BlobService;
using C3Pay.Services.Membership.BenefitsShell;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using OpenTelemetry.Metrics;
using System.Collections.Generic;
using Azure.Monitor.OpenTelemetry.AspNetCore;
using OpenTelemetry.Exporter;
using C3Pay.Services.Membership;
using C3Pay.Core.Services.Sms;
using MassTransit;

namespace C3Pay.Portal.API
{
    /// <summary>
    /// 
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="configuration"></param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// 
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// 
        public void ConfigureServices(IServiceCollection services)
        {
            // Configure logging with OpenTelemetry and Azure Monitor
            services.InjectLogging(Configuration);

            services.AddHttpContextAccessor();

            services.AddMassTransit(x =>
            {
                x.UsingAzureServiceBus((context, cfg) =>
                {
                    cfg.Host(Configuration.GetConnectionString("ServiceBusConnection"));

                    cfg.ConfigureEndpoints(context);
                });
            });

            services.AddCors(o => o.AddPolicy("CorsPolicy", builder =>
            {
                builder.AllowAnyOrigin()
                       .AllowAnyMethod()
                       .AllowAnyHeader();
            }));




            //Configure  Newtonsoft Json Serializer
            services
                .AddControllers(options =>
                {
                    options.Filters.Add(typeof(InputValidationAttribute));
                })
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
                });

            services.InjectValidators();

            //Register the portal services
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IUnitOfWorkReadOnly, UnitOfWorkReadOnly>();
            services.AddTransient<IUIElementRepository, UIElementRepository>();

            services.AddScoped<IIdentityService, IdentityService>();
            services.AddTransient<IPopupService, PopupService>();
            services.AddTransient<IPortalUserService, PortalUserService>();
            services.AddTransient<IUserService, UserService>();
            services.AddTransient<ICardHolderService, CardHolderService>();
            services.AddTransient<ISubscriptionService, SubscriptionService>();
            services.AddTransient<IIdentificationService, IdentificationService>();
            services.AddTransient<ITextMessageSenderService, TextMessageSenderService>();
            services.AddTransient<ISmsNotificationSender, SmsNotificationSender>();
            services.AddTransient<IEmailSenderService, EmailSenderService>();
            services.AddTransient<IPushNotificationSenderService, PushNotificationSenderService>();
            services.AddTransient<IUploadedDocumentService, UploadedDocumentService>();
            services.AddTransient<IMoneyTransferService, MoneyTransferService>();
            services.AddTransient<IReferrerCodeService, ReferrerCodeService>();
            services.AddTransient<ILookupService, LookupService>();
            services.AddTransient<IReferralProgramService, ReferralProgramService>();
            services.AddTransient<ISalaryAdvanceCashBackService, SalaryAdvanceCashBackService>();

            services.AddTransient<IUploadedDocumentService, UploadedDocumentService>();
            services.AddTransient<IJWEncryptionService, JWEncryptionService>();
            services.AddTransient<IMoneyTransferBeneficiaryService, MoneyTransferBeneficiaryService>();
            services.AddTransient<IDingService, DingService>();
            services.AddTransient<IMobileRechargeService, MobileRechargeService>();
            services.AddTransient<IBillerService, BillerService>();
            services.AddTransient<IBillerTransactionService, BillerTransactionService>();
            services.AddTransient<IBillerProductService, BillerProductService>();
            services.AddTransient<IBillerCategoryService, BillerCategoryService>();
            services.AddTransient<IBillerProviderService, BillerProviderService>();
            services.AddTransient<IPartnerCorporateService, PartnerCorporateService>();
            services.AddScoped<IExperimentService, ExperimentService>();
            services.AddTransient<ICsvConfigService, CsvConfigService>();
            services.AddTransient<IPDFService, PDFService>();
            services.AddTransient<ICacheService, CacheService>();

            //Register C3PayPlus Service
            services.AddTransient<IC3PayPlusMembershipTranslationService, C3PayPlusMembershipTranslationService>();
            services.AddTransient<IUserRepository, UserRepository>();
            services.AddTransient<IC3PayPlusMembershipLookupService, C3PayPlusMembershipLookupService>();
            services.AddTransient<ILoginVideoService, LoginVideoService>();
            services.AddScoped<IC3PayPlusTargetedDiscountService, C3PayPlusTargetedDiscountService>();
            services.AddTransient<IC3PayPlusMembershipNotificationService, C3PayPlusMembershipNotificationService>();
            services.AddTransient<IMoneyTransferIntegrationService, MoneyTransferIntegrationService>();

            services.Configure<MoneyTransferServiceSettings>(Configuration.GetSection("MoneyTransferService"));
            services.Configure<RewardServiceSettings>(Configuration.GetSection("RewardService"));
            services.Configure<CleverTapServiceSettings>(Configuration.GetSection("CleverTapService"));
            services.AddCleverTapService(Configuration);
            services.AddTransient<IAnalyticsPublisherService, AnalyticsPublisherService>();
            services.Configure<ReferralProgramServiceSettings>(Configuration.GetSection("ReferralProgramService"));
            services.Configure<KycExpirySettings>(Configuration.GetSection("KycExpiry"));
            services.Configure<KycUnblockByPassportSettings>(Configuration.GetSection("KycUnblockByPassport"));

            //RMT Profile service
            services.AddTransient<IRMTProfileService, RMTProfileService>();
            //AuditTrail
            services.AddTransient<IAuditTrailService, AuditTrailService>();
            //BlackList
            services.AddTransient<IBlackListService, BlackListService>();

            //Azure services
            services.AddKeyVault(Configuration);
            services.AddBlobStorageService(Configuration, "AzureBlobStorage");
            services.AddSendGridService(Configuration);
            services.AddServiceBusService(Configuration);

            //Firebase Cloud Messaging Service
            services.AddFirebaseCloudMessagingService(Configuration);

            services.AddTransient<C3PayPlusMembershipLoginVideoValidator>();
            services.AddTransient<VpnBenefitLoginVideoValidator>();
            services.AddScoped<SpinTheWheelLoginVideoValidator>();
            services.AddTransient<SmsPlusMigrationVideoValidator>();


            // Benefits Shell Services - Simplified Architecture
            services.AddTransient<IBenefitService, BenefitService>();

            //Configure the DB context
            services.AddDbContextPool<C3PayContext>(
                    options => options.UseSqlServer(Configuration.GetConnectionString("C3PayConnection"), x => x.MigrationsAssembly("C3Pay.Data"))
                );

            services.AddDbContext<C3PayContextReadonly>(
                   options => options.UseSqlServer(Configuration.GetConnectionString("C3PayConnectionReadOnly"), x => x.MigrationsAssembly("C3Pay.Data"))
               );

            services.AddDbContext<IdentityDbContext>(options => options.UseSqlServer(Configuration.GetConnectionString("IdentityConnection"), b => b.MigrationsAssembly("Identity")));

            var serviceProvider = services.BuildServiceProvider();

            // Add Azure AD authentication
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                    .AddMicrosoftIdentityWebApi(Configuration.GetSection("AzureAd"));

            // Add Authorization with policies
            services.AddAuthorization(options =>
            {
                // Policy for all authenticated users
                options.AddPolicy("AllUsers", policy =>
                    policy.RequireAuthenticatedUser());
            });

            // Mapping Roles Names
            services.AddScoped<IClaimsTransformation, RoleClaimsTransformation>();

            services.Configure<PasswordSettings>(Configuration.GetSection("PasswordValidationRules"));
            services.AddTransient<IPasswordService, PasswordService>();

            //Register Azure
            services.AddBlobStorageService(Configuration, "AzureBlobStorage");

            services.AddPPSService(Configuration);
            services.AddPPSWebAuthService(Configuration);

            services.AddKeyVault(Configuration);

            services.AddSignzyService(Configuration);

            services.AddSecondaryIndividualIdentificationService(Configuration);
            services.AddKYCService(Configuration);

            services.AddESMOWebService(Configuration);

            services.AddEtisalatSMSService(Configuration);

            services.AddInfobipSMSService(Configuration);

            services.AddServiceBusService(Configuration);
            services.AddTransactionsB2CService(Configuration);

            services.AddTransient<IResourceFileService, ResourceFileService>();

            //Register RAK Service
            IWebHostEnvironment env = serviceProvider.GetService<IWebHostEnvironment>();
            if (Convert.ToBoolean(Configuration["MoneyTransferService:EnableRakMock"]))
            {
                services.AddScoped<IRAKService, RakMockService>();
                services.Configure<RAKSettings>(Configuration.GetSection("RAKService"));
            }
            else
            {
                services.AddRakService(Configuration, env.IsDevelopment());
            }

            services.AddRakBankMoneyTransferService(Configuration);

            //Register Edenred Identity & Role services
            services.AddEdenredIdentityManagerAndRoleService<C3PayIdentityDbContext>(Configuration);

            //Register settings
            services.Configure<PortalUserSettings>(Configuration.GetSection("PortalUser"));

            //Configure Swager
            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "C3Pay MobileApp Portal", Version = "v1" });

                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                options.IncludeXmlComments(xmlPath);

                var securityScheme = new OpenApiSecurityScheme
                {
                    Name = "JWT Authentication",
                    Description = "Enter JWT Bearer token **_only_**",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.Http,
                    Scheme = "bearer", // must be lower case
                    BearerFormat = "JWT",
                    Reference = new OpenApiReference
                    {
                        Id = JwtBearerDefaults.AuthenticationScheme,
                        Type = ReferenceType.SecurityScheme
                    }
                };
                options.AddSecurityDefinition(securityScheme.Reference.Id, securityScheme);
                options.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {securityScheme, new string[] { }}
                });
            });

            services.AddTransient<RequestBodyLoggingMiddleware>();

            services.AddTransient<ResponseBodyLoggingMiddleware>();

            //Add caching
            if (Convert.ToBoolean(Configuration["General:EnableRedis"]))
            {
                services.AddRedisCaching(Configuration, "C3Pay");
            }
            else
            {
                services.AddDistributedMemoryCache();
            }

            //Regiseter auto mapper
            //services.AddAutoMapper(typeof(Startup), typeof(Edenred.Common.Services.MappingProfile));
            services.AddAutoMapper(typeof(Startup).Assembly, typeof(Edenred.Common.Services.MappingProfile).Assembly);

            services.AddOptions();

            services.AddControllers();

            services.AddHealthChecks();

            services.AddAzureAppConfiguration();

            services.AddFeatureManagement();

            services.AddSanctionScreeningService(Configuration);
            services.AddRewardService(Configuration);

            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(GetDashboardPopupQueryHandler).Assembly));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="logger"></param>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILogger<Startup> logger)
        {

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.ConfigureExceptionHandler(logger);

            app.UseHttpsRedirection();

            app.UseSerilogRequestLogging();

            app.UseRequestBodyLogging();

            //app.UseResponseBodyLogging();

            app.UseRouting();

            if (!string.IsNullOrEmpty(Configuration.GetConnectionString("AzureAppConfig")))
            {
                app.UseAzureAppConfiguration();
            }

            app.UseCors("CorsPolicy");

            app.UseAuthentication();

            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHealthChecks("/api/health");
            });

            app.UseMiddleware<CacheControlMiddleware>();

            if (env.IsDevelopment() || (Configuration["General:EnableSwagger"] != null && Configuration["General:EnableSwagger"] == "true"))
            {
                app.UseSwagger();
                app.UseSwaggerUI(configuration =>
                {
                    configuration.RoutePrefix = "";
                    configuration.SwaggerEndpoint("/swagger/v1/swagger.json", "C3Pay MobileApp Portal V1");
                    configuration.DisplayRequestDuration();
                });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="response"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        private static async Task BuildForbidenREsponse(HttpResponse response, string userName)
        {
            response.StatusCode = (int)HttpStatusCode.Forbidden;
            response.ContentType = "application/json";

            Log.Logger.Error($"{userName} is not identified as portal user.");

            await response.WriteAsync(new ErrorDetails()
            {
                StatusCode = response.StatusCode,
                Message = $"{userName} is not identified as portal user."
            }.ToString());
        }
    }
}
