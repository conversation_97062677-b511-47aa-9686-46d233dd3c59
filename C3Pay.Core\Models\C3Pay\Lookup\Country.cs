﻿using C3Pay.Core.Models.C3Pay;
using C3Pay.Core.Models.C3Pay.Lookup;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using System;
using System.Collections.Generic;

namespace C3Pay.Core.Models
{
    public class Country
    {
        public string Code { get; set; }
        public string Code3 { get; set; }
        public string STDCode { get; set; }

        public string Name { get; set; }
        public string LongName { get; set; }
        public string Currency { get; set; }

        public bool IsPapularCountry { get; set; }
        public int? DisplayOrder { get; set; }

        public bool MoneyTransferEnabled { get; set; }
        public bool MobileRechargeEnabled { get; set; }
        public bool MobileRechargeEnabledForPartner { get; set; }
        public bool BillPaymentEnabled { get; set; }

        public bool EligibleForBankTransfer { get; set; }
        public bool EligibleForCashPickUp { get; set; }

        public string CashPickUpProvider { get; set; }
        public string CashPickUpProviderLocationsURL { get; set; }

        public decimal BankTransferLatestRate { get; set; }
        public decimal CashPickUpLatestRate { get; set; }

        public DateTime? RatesLastUpdatedDate { get; set; }

        public DateTime? MobileRechargeLastSynchronizedDate { get; set; }

        public MoneyTransferLimit MoneyTransferLimit { get; set; }
        public List<Popup> Popups { get; set; }
        public List<BlackListedEntity> BlackListedEntities { get; set; }
        public List<MobileRechargeBeneficiary> MobileRechargeBeneficiaries { get; set; }
        public List<MoneyTransferBeneficiary> MoneyTransferBeneficiaries { get; set; }
        public List<BillPaymentProvider> BillPaymentProviders { get; set; }
        public List<BillPaymentCategoryFee> CategoryFees { get; set; }
        public List<MoneyTransferBank> Banks { get; set; }
        public List<Language> Languages { get; set; }
        public List<City> Cities { get; set; }
        public List<DashboardQuickActionElement> DashboardQuickActionElements { get; set; }
        public List<MoneyTransferDelay> MoneyTransferDelays { get; set; }
        public List<SocialProofing> SocialProofings { get; set; }
        public List<MoneyTransferSmartDefault> MoneyTransferSmartDefaults { get; set; }
        public List<State> States { get; set; }
        public List<Province> Provinces { get; set; }
        public List<District> Districts { get; set; }


        public static string DefaultProvince(string countryCode)
        {
            if (countryCode == "PK")
            {
                return "Lahore";
            }
            return null;
        }
    }
}
