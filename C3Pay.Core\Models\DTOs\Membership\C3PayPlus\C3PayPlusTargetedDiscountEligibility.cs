using System;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models.DTOs.Membership.C3PayPlus
{
    public class C3PayPlusTargetedDiscountEligibility
    {
        public bool IsEligible { get; set; }
        public C3PayPlusMembershipType RecommendedMembershipType { get; set; }
        public bool HasActiveOffer { get; set; }
        public DateTime? OfferStartDate { get; set; }
        public DateTime? OfferEndDate { get; set; }
        public bool HasOfferExpired { get; set; }
        public string EligibilityReason { get; set; }
        public bool HasBalanceEnquiry { get; set; }
        public bool HasSecuritySms { get; set; }
        public bool HasSalaryAlert { get; set; }
    }
}