﻿using AutoMapper;
using C3Pay.API.Resources;
using C3Pay.API.Resources.Balance;
using C3Pay.API.Resources.Base;
using C3Pay.API.Resources.Card;
using C3Pay.API.Resources.SignUp;
using C3Pay.API.Resources.Subscriptions;
using C3Pay.API.Resources.UserProfile;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs.LoginVideos;
using C3Pay.Core.Models.DTOs.UserDtos;
using C3Pay.Core.Services;
using C3Pay.Core.Services.Security;
using C3Pay.Services.Commands;
using C3Pay.Services.Handlers;
using C3Pay.Services.LoginVideos.Queries;
using C3Pay.Services.Queries;
using Common.Core.Models.Messages.Integration.Transactions;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.PPS.Card;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Microsoft.FeatureManagement.FeatureFilters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using static C3Pay.Core.Errors;
using static Edenred.Common.Core.Enums;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [InputValidation]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly IIdentificationService _identificationService;
        private readonly IPopupService _popupService;
        private readonly ILookupService _lookupService;
        private readonly IPPSService _ppsService;
        private readonly IESMOWebService _esmoWebService;
        private readonly IMapper _mapper;
        private readonly IIdentityService _identityService;
        private readonly IKYCService _kycService;
        private readonly ITextMessageSenderService _textMessageService;
        private readonly IStatementService _statementService;
        private readonly ISubscriptionService _subscriptionService;
        private readonly ISecurityService _securityService;
        private readonly ITransactionsB2CService _transactionsB2CService;
        private readonly IdentificationCommandHandler _identificationCommandHandler;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IPasswordService _passwordService;
        private readonly GeneralSettings _generalSettings;
        private readonly IUserInterfaceService _userInterfaceService;
        private readonly ILogger _logger;
        private readonly IMediator _mediator;
        private readonly IFeatureManager _featureManager;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userService"></param>
        /// <param name="identificationService"></param>
        /// <param name="identityService"></param>
        /// <param name="ppsService"></param>
        /// <param name="mapper"></param>
        /// <param name="kycService"></param>
        /// <param name="lookupService"></param>
        /// <param name="popupService"></param>
        /// <param name="textMessageService"></param>
        /// <param name="statementService"></param>
        /// <param name="subscriptionService"></param>
        /// <param name="esmoWebService"></param>
        /// <param name="securityService"></param>
        /// <param name="identificationCommandHandler"></param>
        /// <param name="transactionsB2CService"></param>
        /// <param name="httpContextAccessor"></param>
        public UserController(
            IUserService userService,
            IIdentificationService identificationService,
            IIdentityService identityService,
            IPPSService ppsService,
            IMapper mapper,
            IKYCService kycService,
            ILookupService lookupService,
            IPopupService popupService,
            ITextMessageSenderService textMessageService,
            IStatementService statementService,
            ISubscriptionService subscriptionService,
            IESMOWebService esmoWebService,
            ISecurityService securityService,
            IdentificationCommandHandler identificationCommandHandler,
            ITransactionsB2CService transactionsB2CService,
            IHttpContextAccessor httpContextAccessor,
            IPasswordService passwordService,
            IOptions<GeneralSettings> generalSettings,
            IUserInterfaceService userInterfaceService,
            ILogger<UserController> logger,
            IMediator mediator,
            IFeatureManager featureManager)
        {
            _userService = userService;
            _lookupService = lookupService;
            _identificationService = identificationService;
            _popupService = popupService;
            _lookupService = lookupService;
            _identityService = identityService;
            _ppsService = ppsService;
            _kycService = kycService;
            _mapper = mapper;
            _textMessageService = textMessageService;
            _statementService = statementService;
            _subscriptionService = subscriptionService;
            _esmoWebService = esmoWebService;
            _securityService = securityService;
            _generalSettings = generalSettings.Value;
            _identificationCommandHandler = identificationCommandHandler;
            _transactionsB2CService = transactionsB2CService;
            _httpContextAccessor = httpContextAccessor;
            _passwordService = passwordService;
            _userInterfaceService = userInterfaceService;
            _logger = logger;
            _mediator = mediator;
            _featureManager = featureManager;
        }

        /// <summary>
        /// RMT Profile Statuses: Created, Pending, Missing. EmiratesId Statuses: Missing, Expired, Validating, Valid, ValidUpdatable
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("{id}")]
        public async Task<ActionResult<UserDto>> GetUserById(Guid id)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(id, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetProfile(id, null);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            if (user.IsBlocked)
            {
                return BadRequest(ConstantParam.UserBlockedLogin);
            }

            var mappedUser = this._mapper.Map<UserDto>(user);

            return this.Ok(mappedUser);
        }

        /// <summary>
        /// RMT Profile Statuses: Created, Pending, Missing. EmiratesId Statuses: Missing, Expired, Validating, Valid, ValidUpdatable
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("by-phone-number/{phoneNumber}")]
        public async Task<ActionResult<UserDto>> GetUserByPhoneNumber(string phoneNumber, CancellationToken cancellationToken = default(CancellationToken))
        {
            var username = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username).Value;

            var usernameMatchesUser = phoneNumber.ToZeroPrefixedPhoneNumber() == username;
            if (!usernameMatchesUser)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetProfile(null, phoneNumber.ToZeroPrefixedPhoneNumber(), cancellationToken);
            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;
            if (user.IsBlocked)
            {
                return BadRequest(ConstantParam.UserBlockedLogin);
            }

            var mappedUser = this._mapper.Map<UserDto>(user);

            // Middle Nav Navigation 
            if (!this._generalSettings.IsMiddleNavExperimentActive)
                mappedUser.ShowDashboardNavigation = true;

            #region SecuritySMS Awareness, Device Binding User Interface Actions
            var isPartOfLoginVideoExperiment = user.ExperimentUsers == null ? false : user.ExperimentUsers.Any(x =>
                        x.ExperimentId == (int)ExperimentType.LoginVideo);
            var userInterfaceActions = await _userInterfaceService.GetUserInterfaceActions(user, isPartOfLoginVideoExperiment, cancellationToken);

            mappedUser.UserInterfaceActions = new List<UserInterfaceAction>();
            if (userInterfaceActions.Any())
            {
                mappedUser.UserInterfaceActions = userInterfaceActions.Select(a => new UserInterfaceAction()
                {
                    Feature = a.Item1.ToString(),
                    Flow = a.Item2.ToString()
                }).ToList();
            }

            #endregion
            return this.Ok(mappedUser);
        }

        /// <summary>
        /// Returns: 200, 400-NoPopupFound
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("{userId}/popup")]
        public async Task<ActionResult<PopupDto>> GetUserPopup(Guid userId)
        {
            var userResult = await this._userService.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return NotFound(userResult.ErrorMessage);
            }

            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var user = userResult.Data;

            var countryResult = await this._lookupService.GetCountryByCode(user.CardHolder.Nationality);

            if (!countryResult.IsSuccessful)
            {
                return BadRequest(countryResult.ErrorMessage);
            }

            var country = countryResult.Data;

            var popupResult = await this._popupService.GetCountryPopup(country.Code);

            if (!popupResult.IsSuccessful)
            {
                return BadRequest(popupResult.ErrorMessage);
            }

            var popup = popupResult.Data;

            var mappedPopup = this._mapper.Map<PopupDto>(popup);

            return this.Ok(mappedPopup);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("{userId}/balance-and-status")]
        public async Task<ActionResult<UserBalanceDto>> GetUserBalance(Guid userId, CancellationToken cancellationToken = default(CancellationToken))
        {
            string languageCode = Request.Headers["x-lang-code"];

            var username = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username).Value;

            var userResult = await this._userService.GetUserById(userId, cancellationToken);

            if (!userResult.IsSuccessful)
            {
                return this.BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var usernameMatchesUser = user.PhoneNumber == username;

            if (!usernameMatchesUser)
            {
                return this.Unauthorized();
            }

            string status;
            string hardBlockType = null;
            double balance = 0;

            var subscriptionsResult = await this._subscriptionService.GetSubscripitonsList(user.CardHolder.CorporateId, languageCode);

            if (!subscriptionsResult.IsSuccessful)
            {
                return this.BadRequest(subscriptionsResult.ErrorMessage);
            }

            var subscriptions = subscriptionsResult.Data;

            var balanceEnquirySubscription = subscriptions.Single(subscription => subscription.Code == BaseEnums.SMSSubscriptionType.BE.ToString());

            var userSubscribedToBalanceEnquiryResult = await this._subscriptionService.UserHasActiveSubscription(userId, balanceEnquirySubscription.Id, cancellationToken);

            if (!userSubscribedToBalanceEnquiryResult.IsSuccessful)
            {
                return this.BadRequest(userSubscribedToBalanceEnquiryResult.ErrorMessage);
            }

            var mappedSubscription = this._mapper.Map<SubscriptionDto>(balanceEnquirySubscription);

            mappedSubscription.IsActive = userSubscribedToBalanceEnquiryResult.Data;

            var getBalanceResult = await this._userService.GetUserCardBalance(user, cancellationToken);

            if (!getBalanceResult.IsSuccessful)
            {
                if (getBalanceResult.ErrorMessage == "CARD BLOCKED")
                {
                    var blockTypeResult = await this._esmoWebService.GetCardBlockType(new GetCardBlockTypeRequestDto()
                    {
                        CardSerialNumber = user.CardHolder.CardSerialNumber
                    });

                    if (!blockTypeResult.IsSuccessful)
                    {
                        return this.BadRequest(blockTypeResult.ErrorMessage);
                    }

                    var blockType = blockTypeResult.Data;

                    if (blockType == CardBlockType.Hard.ToString())
                    {
                        status = EnumUtility.GetDescriptionFromEnumValue(PPSCardStatus.HB);
                        try
                        {
                            var hardBlockTypeValue = await this._userService.GetUserHardBlockType(user.CardHolderId);
                            hardBlockType = hardBlockTypeValue.ToString();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning("Hard Block: Unable to detrmine hardblock type.");
                        }
                    }
                    else
                    {
                        status = EnumUtility.GetDescriptionFromEnumValue(PPSCardStatus.B);
                    }
                }
                else if (getBalanceResult.ErrorMessage == "CARD HAS NOT BEEN ACTIVATED")
                {
                    var userHasPendingIdentificationResponse = await this._userService.UserHasPendingIdentifications(userId);

                    if (!userHasPendingIdentificationResponse.IsSuccessful)
                    {
                        return this.BadRequest(userHasPendingIdentificationResponse.ErrorMessage);
                    }

                    var userHasPendingIdentification = userHasPendingIdentificationResponse.Data;

                    if (userHasPendingIdentification)
                    {
                        status = EnumUtility.GetDescriptionFromEnumValue(PPSCardStatus.V);
                    }
                    else
                    {
                        status = EnumUtility.GetDescriptionFromEnumValue(PPSCardStatus.I);
                    }
                }
                else
                {
                    return BadRequest(getBalanceResult.ErrorMessage);
                }
            }
            else
            {
                status = EnumUtility.GetDescriptionFromEnumValue(PPSCardStatus.A);

                balance = getBalanceResult.Data.BalanceAmount;

            }

            var result = new UserBalanceDto()
            {
                BalanceAmount = balance,
                CardStatus = status,
                HardBlockType = hardBlockType,
                Subscription = mappedSubscription
            };

            return Ok(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="page"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("{userId}/transactions/{page}")]
        public async Task<ActionResult<IEnumerable<CardTransactionDto>>> GetTransactions(Guid userId, int page)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var transactionsResult = await this._esmoWebService.GetCardTransactions(new GetCardTransactionsRequestDto()
            {
                CardSerialNumber = user.CardHolder.CardSerialNumber,
                CorporateId = int.Parse(user.CardHolder.CorporateId),
                PageNumber = page
            });

            if (!transactionsResult.IsSuccessful)
            {
                if (transactionsResult.ErrorMessage == EnumUtility.GetDescriptionFromEnumValue(GetLastTransactionsPerCardResponse.ESSOA011))
                {
                    return this.Ok(new List<CardTransactionDto>());
                }

                return BadRequest(transactionsResult.ErrorMessage);
            }

            var transactions = transactionsResult.Data.LastTransactions;

            var mappedTransactions = this._mapper.Map<IEnumerable<CardTransactionDto>>(transactions);

            foreach (var item in mappedTransactions)
            {
                if (item.MerchantName == "Money Transfer Fee Reversal")
                {
                    item.MerchantName = "Spin the Wheel Rewards";
                }
            }
            return this.Ok(mappedTransactions);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="page"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("{userId}/transactionsV2/{page}")]
        public async Task<ActionResult<IEnumerable<CardTransactionDto>>> GetTransactionsV2(Guid userId, int page, CancellationToken cancellationToken = default(CancellationToken))
        {
            var username = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username).Value;

            var userResult = await this._userService.GetUserById(userId, cancellationToken);

            if (!userResult.IsSuccessful)
            {
                return this.BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var usernameMatchesUser = user.PhoneNumber == username;

            if (!usernameMatchesUser)
            {
                return this.Unauthorized();
            }

            string languageCode = Request.Headers["x-lang-code"];

            var subscriptionsResult = await this._subscriptionService.GetSubscripitonsList(user.CardHolder.CorporateId, languageCode);

            if (!subscriptionsResult.IsSuccessful)
            {
                return this.BadRequest(subscriptionsResult.ErrorMessage);
            }

            var subscriptions = subscriptionsResult.Data;

            var balanceEnquirySubscription = subscriptions.Single(subscription => subscription.Code == BaseEnums.SMSSubscriptionType.BE.ToString());

            var userSubscribedToBalanceEnquiryResult = await this._subscriptionService.UserHasActiveSubscription(userId, balanceEnquirySubscription.Id, cancellationToken);

            if (!userSubscribedToBalanceEnquiryResult.IsSuccessful)
            {
                return this.BadRequest(userSubscribedToBalanceEnquiryResult.ErrorMessage);
            }

            if (userSubscribedToBalanceEnquiryResult.Data)
            {
                var targetingContext = new TargetingContext { UserId = user.CardHolderId };

                bool isOrianFetchEnabled = await _featureManager.IsEnabledAsync("FetchTransactionsFromOrianRollout", targetingContext);

                // Use a single service call with a dynamically constructed request
                var transactionsResult = isOrianFetchEnabled
                    ? await _transactionsB2CService.GetCardTransactionsV2(
                          new GetCardTransactionRequestV2Dto
                          {
                              PPSAccountNumber = user.CardHolder.PpsAccountNumber,
                              PageNo = page
                          },
                          cancellationToken
                      )
                    : await _transactionsB2CService.GetCardTransactions(
                          new GetCardTransactionRequestDto
                          {
                              CitizenId = user.CardHolderId,
                              PageNumber = page
                          },
                          cancellationToken
                      );

                if (!transactionsResult.IsSuccessful)
                {
                    if (transactionsResult.ErrorMessage == EnumUtility.GetDescriptionFromEnumValue(GetLastTransactionsPerCardResponse.ESSOA011))
                    {
                        return this.Ok(new List<CardTransactionDto>());
                    }

                    return BadRequest(transactionsResult.ErrorMessage);
                }

                var transactions = transactionsResult.Data.LastTransactions;

                var mappedTransactions = this._mapper.Map<IEnumerable<CardTransactionDto>>(transactions);

                foreach (var item in mappedTransactions)
                {
                    if (item.MerchantName == "Money Transfer Fee Reversal")
                    {
                        item.MerchantName = "Spin the Wheel Rewards";
                    }
                }
                return this.Ok(mappedTransactions);
            }

            //empty transactions if user is not subscribed
            return this.Ok(Enumerable.Empty<CardTransactionDto>());
        }

        /// <summary>
        /// Respones: 200, 400 (NoTransactionsFound), 400 (error)
        /// </summary>
        /// <param name="statementRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("statement")]
        public async Task<ActionResult> GenerateStatement(PostTransactionsStatementRequestDto statementRequest)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(statementRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var getUserResponse = await this._userService.GetUserById(statementRequest.UserId);

            if (!getUserResponse.IsSuccessful)
            {
                return this.BadRequest(getUserResponse.Data);
            }

            var user = getUserResponse.Data;

            var generateAndSendStatementResult = await this._statementService.GenerateAndSendStatement(user, statementRequest.StartDate, statementRequest.EndDate, statementRequest.Email);

            if (!generateAndSendStatementResult.IsSuccessful)
            {
                if (generateAndSendStatementResult.ErrorMessage == EnumUtility.GetDescriptionFromEnumValue(GetCardStatementResponse.ESSOA012))
                {
                    return this.BadRequest(StatementResult.NoTransactionsFound.ToString());
                }

                return this.BadRequest(generateAndSendStatementResult.ErrorMessage);
            }

            return this.Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("{userId}/security-questions")]
        public async Task<ActionResult<IEnumerable<SecurityQuestion>>> GetUserSecurityQuestions(Guid userId)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var secretAnswersResult = await this._userService.GetSecretAnswers(userId);

            if (!secretAnswersResult.IsSuccessful)
            {
                return BadRequest(secretAnswersResult.ErrorMessage);
            }

            var secretAnswers = secretAnswersResult.Data;

            var securityQuestionsIdList = secretAnswers.Select(record => record.SecurityQuestionId).ToList();

            var securityQuestionsResult = await this._lookupService.GetSecurityQuestionsByIdList(securityQuestionsIdList);

            if (!securityQuestionsResult.IsSuccessful)
            {
                return BadRequest(secretAnswersResult.ErrorMessage);
            }

            var securityQuestions = securityQuestionsResult.Data;

            return this.Ok(securityQuestions);
        }

        /// <summary>
        /// Verifies user card. possible returns: 400-InvalidCard, 400-InvalidUserCardCombination, 200-Verified
        /// </summary>
        /// <param name="verifyCardRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("verification/card")]
        public async Task<ActionResult> VerifyUserCardNumber(VerifyUserCardRequestDto verifyCardRequest)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(verifyCardRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(verifyCardRequest.UserId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            // Get card holder reference from PPS.
            var tryGetCardHolderReference = await this._esmoWebService.GetCardDetails(new GetCardDetailsRequest()
            {
                CardNumber = verifyCardRequest.CardNumber
            });

            if (tryGetCardHolderReference.IsSuccessful == false)
            {
                return BadRequest(tryGetCardHolderReference.ErrorMessage);
            }

            var cardholderReference = tryGetCardHolderReference.Data;
            if (cardholderReference.CardholderRef != user.CardHolderId)
            {
                return BadRequest(CardVerificationResult.InvalidUserCardCombination.ToString());
            }

            return this.Ok(CardVerificationResult.Verified.ToString());
        }

        /// <summary>
        /// Validates password. possible returns: 400-InvalidPassword, 200-ValidPassword
        /// </summary>
        /// <param name="verifyPasswordRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("verification/password")]
        public async Task<ActionResult> VerifyUserPassword(VerifyPasswordRequestDto verifyPasswordRequest)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(verifyPasswordRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(verifyPasswordRequest.UserId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var username = user.PhoneNumber.ToZeroPrefixedPhoneNumber();

            var validatePasswordResult = await this._identityService.ValidatePasswordAsync(username, verifyPasswordRequest.Password);

            if (!validatePasswordResult.IsSuccessful)
            {
                if (validatePasswordResult.ErrorMessage == Enums.EdenredIdentityManagerResponseMessage.InvalidCredentials.ToString())
                {
                    return this.BadRequest(Enums.PasswordValidationResult.InvalidPassword.ToString());
                }

                return this.BadRequest(validatePasswordResult.ErrorMessage);
            }

            return this.Ok(Enums.PasswordValidationResult.ValidPassword.ToString());
        }

        /// <summary>
        /// Verifies secret answers. possible returns: 400-WrongQuestion, 400-WrongAnswer, 200-Verified
        /// </summary>
        /// <param name="verifySecretAnswerRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("verification/secret-answer")]
        public async Task<ActionResult> VerifyUserSecretAnswer(VerifySecretAnswerRequestDto verifySecretAnswerRequest)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(verifySecretAnswerRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var secretAnswersResult = await this._userService.GetSecretAnswers(verifySecretAnswerRequest.UserId);

            if (!secretAnswersResult.IsSuccessful)
            {
                return BadRequest(secretAnswersResult.ErrorMessage);
            }

            var secretAnswers = secretAnswersResult.Data;

            var secretAnswer = secretAnswers.FirstOrDefault(record => record.SecurityQuestionId == verifySecretAnswerRequest.SecurityQuestionId);

            if (secretAnswer == null)
            {
                return this.BadRequest(BaseEnums.SecretAnswerVerificationResult.WrongQuestion.ToString());
            }

            if (secretAnswer.Answer.ToLower() != verifySecretAnswerRequest.SecretAnswer.ToLower())
            {
                return this.BadRequest(BaseEnums.SecretAnswerVerificationResult.WrongAnswer.ToString());
            }

            return this.Ok(BaseEnums.SecretAnswerVerificationResult.Verified.ToString());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="updateUserRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPut("email")]
        public async Task<ActionResult> UpdateEmail(UpdateUserRequestDto updateUserRequest)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(updateUserRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(updateUserRequest.UserId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;
            var emailConsent = updateUserRequest.EmailConsent.HasValue ? updateUserRequest.EmailConsent.Value : false;
            var updateEmailResult = await this._userService.UpdateUserEmail(user, updateUserRequest.Email, emailConsent);

            if (!updateEmailResult.IsSuccessful)
            {
                return BadRequest(updateEmailResult.ErrorMessage);
            }


            if (updateUserRequest.EmailConsent == true)
            {
                return Ok(updateEmailResult.Data);
            }
            else
            {
                return this.Ok();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="eligibilityRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("eligibility/phone-number")]
        public async Task<ActionResult> GetPhoneNumberUpdateEligibility(EligibilityRequestDto eligibilityRequest)
        {
            var eligibilityResult = await this._userService.GetPhoneNumberEligibility(eligibilityRequest.PhoneNumber.ToZeroPrefixedPhoneNumber(), true);

            var eligibility = eligibilityResult.Data;

            return this.Ok(new MobileAppEligibilityResponseDto(eligibility.ToString()));
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="updateUserRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPut("phone-number/v2")]
        public async Task<ActionResult> UpdatePhoneNumberV2(UpdateUserRequestDto updateUserRequest)
        {
            if (!await this._securityService.VerifyUserOTP())
            {
                return Unauthorized(MfaErrors.Invalid2fa.Message);
            }

            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(updateUserRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(updateUserRequest.UserId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var userCurrentPhoneNumber = user.PhoneNumber;

            var updatePhoneNumberResult = await this._kycService.UpdatePhoneNumber(new UpdatePhoneNumberRequestDto()
            {
                CitizenId = user.CardHolderId,
                Mobile = updateUserRequest.PhoneNumber.ToZeroPrefixedPhoneNumber(),
                PostUpdateToRAKBank = user.IsVerified
            });

            if (!updatePhoneNumberResult.IsSuccessful)
            {
                return BadRequest(updatePhoneNumberResult.ErrorMessage);
            }

            await this._textMessageService.SendPhoneNumberChangedMessage(userCurrentPhoneNumber.ToShortPhoneNumber());

            return this.Ok();
        }
        /// <summary>
        /// Returns: 200, 400-VerificationFailed
        /// </summary>
        /// <param name="updateEmiratesIdRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPut("emirates-id")]
        public async Task<ActionResult> UpdateEmiratesId(UpdateIdentificationRequestDto updateEmiratesIdRequest)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(updateEmiratesIdRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var identification = _mapper.Map<Identification>(updateEmiratesIdRequest);

            identification.FullName = updateEmiratesIdRequest.FullName;

            identification.NationalityCode = updateEmiratesIdRequest.NationalityCode;

            identification.Nationality = updateEmiratesIdRequest.NationalityCode;

            identification.Type = IdentificationType.EmiratesId;

            var addIdentificationResult = await this._identificationService.AddIdentification(identification);

            if (!addIdentificationResult.IsSuccessful)
            {
                return BadRequest(addIdentificationResult.ErrorMessage);
            }

            return this.Ok();
        }

        /// <summary>
        /// Returns:
        /// 200
        /// 400
        /// </summary>
        /// <param name="updateIdentificationDto"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPut("identification")]
        public async Task<ActionResult> UpdateIdentification(UpdateIdentificationDto updateIdentificationDto)
        {
            var doesMatch = await this._securityService.UsernameMatchesUser(updateIdentificationDto.UserId, null, false);
            if (doesMatch.Data == false)
            {
                return Unauthorized();
            }

            if (Enum.TryParse(updateIdentificationDto.IdentificationType, true, out IdentificationType identificationType) == false)
            {
                return BadRequest(ConstantParam.InvalidDocumentType);
            }

            switch (identificationType)
            {
                case IdentificationType.EmiratesId:
                    if (string.IsNullOrEmpty(updateIdentificationDto.BackScanFileName))
                    {
                        return BadRequest(string.Format(ConstantParam.DocumentNotFound, nameof(updateIdentificationDto.BackScanFileName)));
                    }

                    if (string.IsNullOrEmpty(updateIdentificationDto.DocumentNumber))
                    {
                        return BadRequest(ConstantParam.MissingIdNumber);
                    }
                    break;
                case IdentificationType.Passport:
                    if (string.IsNullOrEmpty(updateIdentificationDto.DocumentNumber))
                    {
                        return BadRequest(ConstantParam.MissingPassportNumber);
                    }
                    break;
                default:
                    break;
            }

            var identification = _mapper.Map<Identification>(updateIdentificationDto);

            identification.Nationality = updateIdentificationDto.NationalityCode;

            identification.Type = identificationType;

            var updateEmiratesIdResult = await this._identificationCommandHandler.Handle(new AddIdentificationCommand()
            {
                Identification = identification
            });

            if (!updateEmiratesIdResult.IsSuccessful)
            {
                return BadRequest(updateEmiratesIdResult.ErrorMessage);
            }

            return this.Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="updatePasswordRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPut("password")]
        public async Task<ActionResult<IEnumerable<SecurityQuestion>>> UpdatePassword(UpdateUserRequestDto updatePasswordRequest)
        {
            var validatePasswordResult = await this._passwordService.ValidatePassword(updatePasswordRequest.Password);

            if (!validatePasswordResult.IsSuccessful)
            {
                return BadRequest(validatePasswordResult.ErrorMessage);
            }

            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(updatePasswordRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(updatePasswordRequest.UserId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var resetCodeResult = await this._identityService.RequestResetPasswordAsync(user.PhoneNumber);

            if (!resetCodeResult.IsSuccessful)
            {
                return BadRequest(resetCodeResult.ErrorMessage);
            }

            var resetPasswordCode = resetCodeResult.Data;

            var updatePasswordResult = await this._identityService.ResetPasswordAsync(user.PhoneNumber, updatePasswordRequest.Password.ToLower(), resetPasswordCode);

            if (!updatePasswordResult.IsSuccessful)
            {
                return BadRequest(updatePasswordResult.ErrorMessage);
            }

            await this._userService.ResetPasswordResetFlag(user);

            return this.Ok();
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="updatePasswordRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPut("password/v2")]
        public async Task<ActionResult<IEnumerable<SecurityQuestion>>> UpdatePasswordV2(UpdateUserRequestDto updatePasswordRequest)
        {
            if (!await this._securityService.VerifyUserOTP())
            {
                return Unauthorized(MfaErrors.Invalid2fa.Message);
            }

            var validatePasswordResult = await this._passwordService.ValidatePassword(updatePasswordRequest.Password);

            if (!validatePasswordResult.IsSuccessful)
            {
                return BadRequest(validatePasswordResult.ErrorMessage);
            }

            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(updatePasswordRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(updatePasswordRequest.UserId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var resetCodeResult = await this._identityService.RequestResetPasswordAsync(user.PhoneNumber);

            if (!resetCodeResult.IsSuccessful)
            {
                return BadRequest(resetCodeResult.ErrorMessage);
            }

            var resetPasswordCode = resetCodeResult.Data;

            var updatePasswordResult = await this._identityService.ResetPasswordAsync(user.PhoneNumber, updatePasswordRequest.Password.ToLower(), resetPasswordCode);

            if (!updatePasswordResult.IsSuccessful)
            {
                return BadRequest(updatePasswordResult.ErrorMessage);
            }

            await this._userService.ResetPasswordResetFlag(user);

            return this.Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="updateDeviceTokenRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPut("device-token")]
        public async Task<ActionResult> UpdateDeviceToken(UpdateUserRequestDto updateDeviceTokenRequest)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(updateDeviceTokenRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(updateDeviceTokenRequest.UserId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var updateDeviceTokenResult = await this._userService.UpdateUserDeviceToken(user, updateDeviceTokenRequest.DeviceToken);

            if (!updateDeviceTokenResult.IsSuccessful)
            {
                return BadRequest(updateDeviceTokenResult.ErrorMessage);
            }

            return this.Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="updateDeviceTokenRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPut("device-id")]
        public async Task<ActionResult> UpdateDeviceId(UpdateDeviceIdRequestDto requestDto)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(requestDto.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var updateDeviceTokenResult = await this._userService.UpdateUserDeviceId(requestDto.UserId, requestDto.DeviceId, requestDto.Model);

            if (!updateDeviceTokenResult.IsSuccessful)
            {
                return BadRequest(updateDeviceTokenResult.ErrorMessage);
            }

            return this.Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="updateAnswersRequest"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPut("secret-answers")]
        public async Task<ActionResult> UpdateSecretAnswers(UpdateUserRequestDto updateAnswersRequest)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(updateAnswersRequest.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var secretAnswers = this._mapper.Map<List<SecretAnswer>>(updateAnswersRequest.SecurityAnswers);

            var updateSecretAnswersResult = await this._userService.UpdateSecretAnswers(updateAnswersRequest.UserId, secretAnswers);

            if (!updateSecretAnswersResult.IsSuccessful)
            {
                return BadRequest(updateSecretAnswersResult.ErrorMessage);
            }

            return this.Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("{userId}/card/block")]
        public async Task<ActionResult> BlockCard(Guid userId)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var blockCardResult = await this._esmoWebService.BlockCard(new BlockCardRequest()
            {
                SerialNumber = user.CardHolder.CardSerialNumber,
                Remarks = ConstantParam.UserBlockFromAppNarration,
                Username = ConstantParam.SenderUsername
            });

            if (!blockCardResult.IsSuccessful)
            {
                return BadRequest(blockCardResult.ErrorMessage);
            }

            return this.Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("{userId}/card/unblock")]
        public async Task<ActionResult> UnblockCard(Guid userId)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var unblockCardResult = await this._esmoWebService.UnblockCard(new UnblockCardRequest()
            {
                SerialNumber = user.CardHolder.CardSerialNumber,
                Remarks = ConstantParam.UserUnblockFromAppNarration,
                Username = ConstantParam.SenderUsername
            });

            if (!unblockCardResult.IsSuccessful)
            {
                return BadRequest(unblockCardResult.ErrorMessage);
            }

            return this.Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("{userId}/card/atm-pin-prompt")]
        public async Task<ActionResult> DisableAtmPinPrompt(Guid userId)
        {
            var userResult = await this._userService.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var disableAtmPinResult = await this._userService.DisableAtmPinPrompt(user);

            if (!disableAtmPinResult.IsSuccessful)
            {
                return BadRequest(disableAtmPinResult.ErrorMessage);
            }

            return this.Ok();
        }

        /// <summary>
        /// Possible results: Card Pin, or a bad request with the error message in case of an exception.
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="cardSerialNumber"></param>
        /// <param name="cvc2"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("{userId}/card/pin/{cardSerialNumber}/{cvc2}")]
        public async Task<ActionResult<CardPinDto>> GetCardPin(Guid userId, string cardSerialNumber, string cvc2)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            // Call PPS service.
            var getPinResult = await this._esmoWebService.GetCardPin(new GetCardPinRequest()
            {
                CardSerialNumber = cardSerialNumber,
                Cvc2 = cvc2
            });

            if (!getPinResult.IsSuccessful)
            {
                return BadRequest(getPinResult.ErrorMessage);
            }

            CardPinDto result = new CardPinDto()
            {
                Pin = getPinResult.Data.Pin
            };

            return this.Ok(result);
        }

        /// <summary>
        /// Possible results: Ok, BlockedTooManyFailedAttempts, MissingCredentials, InvalidCard, IncorrectCvc2, Error
        /// </summary>
        /// <param name="cardNumber"></param>
        /// <param name="cvc2"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("{userId}/card/verify/{cardNumber}/{cvc2}")]
        public async Task<ActionResult<CardDetailsDto>> VerifyCvv2(Guid userId, string cardNumber, string cvc2)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var userResult = await this._userService.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return BadRequest(userResult.ErrorMessage);
            }

            // Validate input.
            if (string.IsNullOrEmpty(cardNumber) || string.IsNullOrEmpty(cvc2))
            {
                return BadRequest(CardDetailsResults.MissingCredentials.ToString());
            }

            // Get card's details.
            var cardDetails = await this._esmoWebService.GetCardDetails(new GetCardDetailsRequest() { CardNumber = cardNumber });
            if (cardDetails.IsSuccessful == false || string.IsNullOrEmpty(cardDetails?.Data?.SerialNumber))
            {
                return BadRequest(CardDetailsResults.InvalidCard.ToString());
            }


            //Check if this card belongs to this user.
            var owned = await this._userService.CheckCardOwnership(userId, cardDetails.Data.CardholderRef);
            if (owned.IsSuccessful == false || owned.Data == false)
            {
                return BadRequest(CardDetailsResults.InvalidCard.ToString());
            }

            // Validate CVC2
            var isCvc2Valid = await this._esmoWebService.VerifyCvc2(new VerifyCvc2Request()
            {
                CardSerialNumber = cardDetails.Data.SerialNumber,
                Cvc2 = cvc2
            });


            if (isCvc2Valid.IsSuccessful == false)
            {
                Enum.TryParse(isCvc2Valid.ErrorMessage, out Enums.AtmPinErrors atmPinError);
                switch (atmPinError)
                {
                    case AtmPinErrors.MAX_PIN_TRIES_EXCEEDED:
                        await this._userService.SetATMPinBlockEndDate(userResult.Data, DateTime.Now.AddHours(24));
                        return BadRequest(CardDetailsResults.BlockedTooManyFailedAttempts.ToString());
                    case AtmPinErrors.INVALID_CVC2:
                        return BadRequest(CardDetailsResults.IncorrectCvc2.ToString());
                    case AtmPinErrors.GENERAL_ERROR:
                    default:
                        return BadRequest(CardDetailsResults.Error.ToString());
                }
            }

            var result = new CardDetailsDto()
            {
                Result = EnumUtility.GetDescriptionFromEnumValue(CardDetailsResults.Ok),
                SerialNumber = cardDetails.Data.SerialNumber
            };

            return this.Ok(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("{userId}/referral-code")]
        public async Task<ActionResult<string>> GetUserReferralCode(Guid userId)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var referralCodeResult = await this._userService.GetOrGenerateUserReferralCode(userId);

            if (!referralCodeResult.IsSuccessful)
            {
                return BadRequest();
            }

            return this.Ok(referralCodeResult.Data);
        }

        /// <summary>
        /// Possible results:  200 - PinNumber, 400 - InvalidCombination, 400 - BlockedTooManyFailedAttempts, or a bad request with the error message in case of an exception.
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("{userId}/card/pin")]
        public async Task<ActionResult<CardPinDto>> GetCardPin(Guid userId, GetCardPinRequestDto requestDto)
        {
            #region authenticate
            var username = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username).Value;

            var userResult = await this._userService.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return this.BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var usernameMatchesUser = user.PhoneNumber == username;

            if (!usernameMatchesUser)
            {
                return this.Unauthorized();
            }

            // Skip the verification if user is an international phone number user.
            if (user.IsInternationalPhoneNumberUser == null || user.IsInternationalPhoneNumberUser == false)
            {
                if (user != null && !user.IsVerified)
                {
                    return this.Unauthorized();
                }
            }


            #endregion

            #region verify it's the same card as the user's
            var tryGetCardDetails = await this._esmoWebService.GetCardDetails(new GetCardDetailsRequest()
            {
                CardNumber = requestDto.CardNumber
            });

            if (tryGetCardDetails.IsSuccessful == false || string.IsNullOrEmpty(tryGetCardDetails.Data?.CardholderRef))
            {
                return BadRequest(tryGetCardDetails.ErrorMessage);
            }

            var cardSerialNumber = tryGetCardDetails.Data.SerialNumber;

            if (cardSerialNumber != user.CardHolder.CardSerialNumber)
            {
                return BadRequest(CardEligibilityResult.InvalidCombination.ToString());
            }
            #endregion

            #region verify cvc2
            var getPinResult = await this._esmoWebService.GetCardPin(new GetCardPinRequest()
            {
                CardSerialNumber = cardSerialNumber,
                Cvc2 = requestDto.Cvc2
            });

            if (!getPinResult.IsSuccessful)
            {
                Enum.TryParse(getPinResult.ErrorMessage, out Enums.AtmPinErrors atmPinError);
                switch (atmPinError)
                {
                    case AtmPinErrors.MAX_PIN_TRIES_EXCEEDED:
                        await this._userService.SetATMPinBlockEndDate(userResult.Data, DateTime.Now.AddHours(24));
                        return BadRequest(CardDetailsResults.BlockedTooManyFailedAttempts.ToString());
                    case AtmPinErrors.INVALID_CVC2:
                        return BadRequest(CardDetailsResults.InvalidCombination.ToString());
                    case AtmPinErrors.GENERAL_ERROR:
                    default:
                        return BadRequest(CardDetailsResults.Error.ToString());
                }
            }

            if (!getPinResult.IsSuccessful)
            {
                return BadRequest(getPinResult.ErrorMessage);
            }
            #endregion

            #region return pin
            var result = new CardPinDto()
            {
                Pin = getPinResult.Data.Pin
            };

            return this.Ok(result);
            #endregion
        }

        /// <summary>
        /// Possible results: 200 - Verified, 400 - InvalidCombination, 400 - BlockedTooManyFailedAttempts or a bad request with the error message in case of an exception.
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("{userId}/card/verify")]
        public async Task<ActionResult<CardPinDto>> VerifyCardDetails(Guid userId, VerifyPinBaseRequestDto requestDto)
        {
            #region authenticate
            var username = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username).Value;

            var userResult = await this._userService.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return this.BadRequest(userResult.ErrorMessage);
            }

            var user = userResult.Data;

            var usernameMatchesUser = user.PhoneNumber == username;

            if (!usernameMatchesUser)
            {
                return this.Unauthorized();
            }
            #endregion

            #region verify it's the same card as the user's
            var tryGetCardDetails = await this._esmoWebService.GetCardDetails(new GetCardDetailsRequest()
            {
                CardNumber = requestDto.CardNumber
            });

            if (tryGetCardDetails.IsSuccessful == false || string.IsNullOrEmpty(tryGetCardDetails.Data?.CardholderRef))
            {
                return BadRequest(tryGetCardDetails.ErrorMessage);
            }

            var cardSerialNumber = tryGetCardDetails.Data.SerialNumber;

            if (cardSerialNumber != user.CardHolder.CardSerialNumber)
            {
                return BadRequest(CardEligibilityResult.InvalidCombination.ToString());
            }
            #endregion

            #region verify pin
            var getPinResult = await this._esmoWebService.GetCardPin(new GetCardPinRequest()
            {
                CardSerialNumber = cardSerialNumber,
                Cvc2 = requestDto.CVC2
            });

            if (!getPinResult.IsSuccessful)
            {
                Enum.TryParse(getPinResult.ErrorMessage, out Enums.AtmPinErrors atmPinError);
                switch (atmPinError)
                {
                    case AtmPinErrors.MAX_PIN_TRIES_EXCEEDED:
                        await this._userService.SetATMPinBlockEndDate(userResult.Data, DateTime.Now.AddHours(24));
                        return BadRequest(CardDetailsResults.BlockedTooManyFailedAttempts.ToString());
                    case AtmPinErrors.INVALID_CVC2:
                        return BadRequest(CardDetailsResults.InvalidCombination.ToString());
                    case AtmPinErrors.GENERAL_ERROR:
                    default:
                        return BadRequest(CardDetailsResults.Error.ToString());
                }
            }

            var pin = getPinResult.Data.Pin;

            if (pin != requestDto.Pin)
            {
                return BadRequest(CardEligibilityResult.InvalidCombination.ToString());
            }

            return this.Ok(CardVerificationResult.Verified.ToString());
            #endregion
        }

        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("login-videos")]
        public async Task<ActionResult<LoginVideoResponseDto>> GetLoginVideos([FromBody] LoginVideoRequestDto request, CancellationToken ct)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;


            // UAT ONLY
            if (userPhoneNumber == "00971524205129")
            {
                request.LoginCount = 4;
            }


            var tryGetLoginVideoes = await _mediator.Send(new GetLoginVideosQuery()
            {
                LanguageCode = languageCode,
                UserPhoneNumber = userPhoneNumber,
                LoginCount = request.LoginCount,
                CancellationToken = ct
            });

            if (tryGetLoginVideoes.IsFailure)
            {
                return BadRequest(tryGetLoginVideoes.Error.Code);
            }


            return Ok(tryGetLoginVideoes.Value);

        }

        [Authorize]
        [HttpPut("place-of-birth")]
        public async Task<ActionResult<LoginVideoResponseDto>> UpdatePlaceOfBirth([FromBody] PlaceOfBirthRequestDto request, CancellationToken ct)
        {
            var username = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username).Value;
            var userResult = await this._userService.GetUserByPhoneNumber(username);

            if (!userResult.IsSuccessful || userResult.Data == null)
            {
                return BadRequest("User not found!");
            }


            var updateCommand = new UpdateUserPlaceOfBirthCommand()
            {
                DistrictId = request.DistrictId,
                ProvinceId = request.ProvinceId,
                CardHolderId = userResult.Data?.CardHolderId
            };

            var validationResult = updateCommand.Validate();

            if (!validationResult.IsSuccessful)
                return BadRequest(validationResult.ErrorMessage);

            var updatePobResponse = await _mediator.Send(updateCommand);

            if (!updatePobResponse.IsSuccessful)
            {
                return Problem(updatePobResponse.ErrorMessage);
            }

            return Ok();

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [Authorize]
        [HttpGet("alert-preferences")]
        public async Task<ActionResult<UserBaseDto<AlertPreferenceDto>>> GetAlertPreferences(CancellationToken ct)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var tryGetUserAlertPreferences = await _mediator.Send(new GetUserAlertPreferencesQuery()
            {
                UserPhoneNumber = userPhoneNumber,
                LanguageCode = languageCode
            }, ct);


            if (tryGetUserAlertPreferences.IsFailure)
            {
                return BadRequest(new UserBaseDto<AlertPreferenceDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryGetUserAlertPreferences.Error.Code
                });
            }


            return Ok(tryGetUserAlertPreferences.Value);
        }

        /// <summary>
        /// Updates the User Alert Preferences
        /// </summary>
        /// <param name="channelType"></param>
        /// <param name="ct"></param>
        /// <returns>true, if successfully updated.</returns>
        [Authorize]
        [HttpPut("alert-preferences")]
        public async Task<ActionResult<UserBaseDto<bool>>> UpdateAlertPreferences([FromQuery] int channelType, CancellationToken ct)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var updateAlertPreferenceResponse = await _mediator.Send(new UpdateAlertPreferenceCommand()
            {
                UserPhoneNumber = userPhoneNumber,
                LanguageCode = languageCode,
                ChannelType = channelType
            }, ct);


            if (updateAlertPreferenceResponse.IsFailure)
            {
                return BadRequest(new UserBaseDto<bool>()
                {
                    IsSuccessful = false,
                    ErrorMessage = updateAlertPreferenceResponse.Error.Code
                });
            }

            return Ok(updateAlertPreferenceResponse.Value);

        }

    }
}
