﻿using C3Pay.Core.Models;
using Edenred.Common.Core;
using System.Threading.Tasks;

namespace C3Pay.Core.Services
{
    public interface IExperimentService
    {
        Task<ServiceResponse<bool>> CheckUserIsInCashPickupExperiment(string userId);
        Task<ServiceResponse<bool>> AddUserToNoLoyaltyExperiment(Identification identification);
        Task<ServiceResponse<ExperimentUsers>> CheckUserIsInMTGifExperiment(string userId);
        Task<ServiceResponse<ExperimentUsers>> CheckUserIsInC3PayPlus(string cardholderId);
        Task<ServiceResponse<ExperimentUsers>> FetchUserIsInRetentionGoldAndSpinWheel(string cardHolderId);
        Task<ServiceResponse<bool>> CheckUserIsInSpinTheWheelExperiment(string cardHolderId);
    }
}
