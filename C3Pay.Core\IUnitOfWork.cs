﻿using C3Pay.Core.MoneyTransfer;
using C3Pay.Core.Repositories;
using C3Pay.Core.Repositories.C3Pay;
using C3Pay.Core.Repositories.C3Pay.BillPayment;
using C3Pay.Core.Repositories.C3Pay.Dashboard;
using C3Pay.Core.Repositories.C3Pay.LoginVideos;
using C3Pay.Core.Repositories.C3Pay.Lookup;
using C3Pay.Core.Repositories.C3Pay.Membership;
using C3Pay.Core.Repositories.C3Pay.Membership.Logs;
using C3Pay.Core.Repositories.C3Pay.Membership.VPN;
using C3Pay.Core.Repositories.C3Pay.MoneyTransfer;
using C3Pay.Core.Repositories.C3Pay.SignUp;
using C3Pay.Core.Repositories.C3Pay.UnEmpInsurance;
using C3Pay.Core.Repositories.C3Pay.UnemploymentInsurance;
using C3Pay.Core.Repositories.C3Pay.User;
using System;
using System.Data;
using System.Threading.Tasks;

namespace C3Pay.Core
{
    public interface IUnitOfWork : IDisposable
    {
        //CardHolder
        ICardHolderRepository CardHolders { get; }

        //User
        IUserRepository Users { get; }
        ISecretAnswerRepository SecretAnswers { get; }
        IUploadedDocumentRepository UploadedDocuments { get; }
        IVerificationCommentRepository VerificationComments { get; }
        IUserSubscriptionRepository UserSubscriptions { get; }
        IUserBlockReasonRepository UserBlockReasons { get; }
        IMissingKycCardholderRepository MissingKycCardholders { get; }
        IMissingKycExclusionRepository MissingKycExclusions { get; }
        IRmtKycRefinementsRepository RmtKycRefinement { get; }


        //Emirates Id Update
        IIdentificationRepository Identifications { get; }
        INonRegisteredIdentificationRepository NonRegisteredIdentifications { get; }
        IUserRegistrationRejectionReasonRepository UserRegistrationRejectionReasons { get; }


        //Mobile Recharge
        IMobileRechargeSupportedCountryRepository MobileRechargeSupportedCountries { get; }
        IMobileRechargeProviderRepository MobileRechargeProviders { get; }
        IMobileRechargeProductRepository MobileRechargeProducts { get; }
        IMobileRechargeBeneficiaryRepository MobileRechargeBeneficiaries { get; }
        IMobileRechargeBeneficiaryProviderRepository MobileRechargeBeneficiaryProviders { get; }
        IMobileRechargeTransactionRepository MobileRechargeTransactions { get; }
        IMobileRechargeExternalTransactionRepository MobileRechargeExternalTransactions { get; }
        IMobileRechargeUserDiscountRepository MobileRechargeUserDiscounts { get; }
        IMobileRechargeRenewalRepository MobileRechargeRenewals { get; }
        IMobileRechargeTargetedDiscountRepository MobileRechargeTargetedDiscounts { get; }

        //Money Transfer
        IMoneyTransferLimitRepository MoneyTransferLimits { get; }
        IMoneyTransferProfileRepository MoneyTransferProfiles { get; }
        IMoneyTransferBeneficiaryRepository MoneyTransferBeneficiaries { get; }
        IMoneyTransferExternalBeneficiaryRepository MoneyTransferExternalBeneficiaries { get; }
        IMoneyTransferTransactionRepository MoneyTransferTransactions { get; }
        IMoneyTransferExternalTransactionRepository MoneyTransferExternalTransactions { get; }
        IMoneyTransferBankRepository MoneyTransferBanks { get; }
        IMoneyTransferBranchRepository MoneyTransferBranches { get; }
        IMoneyTransferFeesRepository MoneyTransferFees { get; }
        IMoneyTransferSmartDefaultRepository MoneyTransferSmartDefaults { get; }
        IMoneyTransferSuspiciousInformationRepository MoneyTransferSuspiciousInformations { get; }
        IMoneyTransferProfileTrackerRepository MoneyTransferProfileTracker { get; }
        IMoneyTransferUserRewardRepository MoneyTransferUserRewards { get; }
        ITempRmtProfileForResendingRepository TempRmtProfileForResending { get; }

        //Bill Payments
        IBillPaymentProviderRepository BillPaymentProviders { get; }
        IBillPaymentProductRepository BillPaymentProducts { get; }
        IBillPaymentProductIORepository BillPaymentProductIOs { get; }
        IBillPaymentCategoryRepository BillPaymentCategories { get; }
        IBillPaymentSubCategoryRepository BillPaymentSubCategories { get; }
        IBillPaymentCategoryProviderRepository BillPaymentCategoryProviders { get; }
        IBillPaymentBillerRepository BillPaymentBillers { get; }
        IBillPaymentBillerIORepository BillPaymentBillerIOs { get; }
        IBillPaymentProviderFeeRepository BillPaymentProviderFees { get; }
        IBillPaymentDailyFxRateRepository BillPaymentDailyFxRates { get; }
        IBillPaymentCategoryFeeRepository BillPaymentCategoryFees { get; }
        IBillPaymentTransactionRepository BillPaymentTransactions { get; }
        IBillPaymentFeeRepository BillPaymentFees { get; }
        IBillPaymentExternalTransactionRepository BillPaymentExternalTransactions { get; }

        //Experiment
        IExperimentRepository Experiments { get; }
        IExperimentUsersRepository ExperimentUsers { get; }
        IExperimentGroupMultimediaRepository ExperimentGroupMultimedias { get; }
        IExperimentCtaRepository ExperimentCtas { get; }

        //Transaction
        ITransactionRepository Transactions { get; }

        //BlackListed Entity
        IBlackListedEntityRepository BlackListedEntities { get; }

        //Referrer Code
        IReferrerCodeRepository ReferrerCodes { get; }

        //Lookup
        ICountryRepository Countries { get; }
        ISubscriptionRepository Subscriptions { get; }
        ISubscriptionFeatureRepository SubscriptionFeatures { get; }
        ISecurityQuestionRepository SecurityQuestions { get; }
        IMoneyTransferReasonRepository MoneyTransferReasons { get; }
        IMoneyTransferRewardExperimentRepository MoneyTransferRewardExperiment { get; }


        //Portal
        IPortalUserRepository PortalUsers { get; }

        //Popups
        IPopupRepository Popups { get; }

        //AuditTrail
        IAuditTrailRepository AuditTrails { get; }

        //Language
        ILanguageRepository Languages { get; }
        ITextContentRepository TextContents { get; }
        ITranslationRepository Translations { get; }
        IMoneyTransferPartnerReasonRepository MoneyTransferPartnerReasons { get; }
        IMoneyTransferPartnerRepository MoneyTransferPartners { get; }
        IMoneyTransferCorridorRepository MoneyTransferCorridors { get; }

        //Ratings
        IRatingRepository Ratings { get; }
        //Features
        IFeatureRepository Features { get; }
        //outboxMessages
        IOutboxMessageRepository OutboxMessages { get; }

        //Store
        IProductRepository Products { get; }
        IOrderRepository Orders { get; }
        IOrderAddressRepository OrderAddresses { get; }
        IStoreExperimentUserRepository StoreExperimentUsers { get; }
        ICityRepository Cities { get; }
        IStateRepository States { get; }

        // Dashboard 
        IDashboardElementRepository DashboardElements { get; }
        IDashboardSectionRepository DashboardSections { get; }

        // ExchangeHouse 
        IPartnerRepository Partners { get; }
        IPartnerCorporateRepository PartnerCorporates { get; }

        // SubscriptionFee
        ISubscriptionFeeRepository SubscriptionFees { get; }

        //Multimedia
        IMultimediaResourceRepository Multimedias { get; }

        //UnemploymentInsurance
        IUnemploymentInsuranceRepository UnemploymentInsurance { get; }
        IUnEmpInsuranceRepository UnEmpInsurance { get; }

        IPassportLogRepository PassportLogs { get; }
        IEmiratesIdLogRepository EmiratesIdLogs { get; }

        IStatementFeeRepository StatementFees { get; }
        #region Money Transfer

        IMoneyTransferDelayRepository MoneyTransferDelays { get; }
        IMoneyTransferDynamicScreenRepository MoneyTransferDynamicScreens { get; }

        #endregion

        //SocialProofing
        ISocialProofingRepository SocialProofings { get; }

        IRemittanceDestinationRepository RemittanceDestinations { get; }
        IMoneyTransferMethodRepository MoneyTransferMethods { get; }
        IMoneyTransferProviderRepository MoneyTransferProviders { get; }
        IFieldGroupRepository FieldGroups { get; }
        IFieldRepository Fields { get; }
        IBannerRepository Banners { get; }
        IFieldValidationRuleRepository FieldValidationRules { get; }
        INavigationFieldRepository NavigationFields { get; }
        IAllowedCharacterTypeRepository AllowedCharacterTypes { get; }
        IDropdownItemRepository DropdownItems { get; }
        IAdditionalFieldMappingRepository AdditionalFieldMappings { get; }
        IBeneficiaryAdditionalFieldRepository BeneficiaryAdditionalFields { get; }
        IMoneyTransferTextLocalization MoneyTransferTextLocalizations { get; }


        #region C3PayPlus
        IC3PayPlusMembershipRepository C3PayPlusMemberships { get; }
        IC3PayPlusMembershipBenefitRepository C3PayPlusMembershipBenefits { get; }
        IC3PayPlusMembershipUserRepository C3PayPlusMembershipUsers { get; }
        IC3PayPlusTargetedDiscountOfferRepository C3PayPlusTargetedDiscountOffers { get; }
        IC3PayPlusMembershipVideoRepository C3PayPlusMembershipVideos { get; }
        IC3PayPlusMembershipTransactionRepository C3PayPlusMembershipTransactions { get; }
        IC3PayPlusMembershipLuckyDrawWinnerRepository C3PayPlusMembershipLuckyDrawWinners { get; }
        IC3PayPlusMembershipLifeInsuranceNomineeRepository C3PayPlusLifeInsuranceNominees { get; }
        IC3PayPlusLifeInsuranceNomineeRelationshipTypeRepository C3PayPlusLifeInsuranceNomineesRelationshipTypes { get; }
        IC3PayPlusMembershipSupportedCountryRepository C3PayPlusMembershipSupportedCountries { get; }
        IC3PayPlusMembershipFreeMoneyTransferClaimRepository C3PayPlusMembershipFreeMoneyTransferClaims { get; }
        IC3PayPlusMembershipCashbackClaimRepository C3PayPlusMembershipCashbackClaims { get; }
        IC3PayPlusMembershipTranslationRepository C3PayPlusMembershipTranslationRepository { get; }
        IC3PayPlusMembershipEnabledCorporateRepository C3PayPlusMembershipEnabledCorporates { get; }
        IC3PayPlusMembershipExperimentRepository C3PayPlusMembershipExperiments { get; }
        IC3PayPlusMembershipExperimentUserRepository C3PayPlusMembershipExperimentUsers { get; }
        ILog_C3PayPlusMembershipRenewalsRepository Logs_C3PayPlusMembershipRenewals { get; }
        IC3PayPlusMembershipRenewalBillingRepository C3PayPlusMembershipRenewalBillings { get; }
        IC3PayPlusMembershipRenewalsDailyLogRepository C3PayPlusMembershipRenewalsDailyLogs { get; }

        #endregion

        ILoginVideoSlotRepository LoginVideoSlotRepository { get; }
        ILoginVideoRepository LoginVideoRepository { get; }
        ILoginVideoResourceRepository LoginVideoResourceRepository { get; }
        IUserLoginVideoDetailRepository UserLoginVideoDetailRepository { get; }

        IC3PayPlusMembershipAutomatedCallsExclusionRepository C3PayPlusMembershipAutomatedCallsExclusions { get; }


        #region Dashboard
        IDashboardPopupRepository DashboardPopups { get; }
        #endregion

        IDistrictRepository DistrictRepository { get; }
        IProvinceRepository ProvinceRepository { get; }


        #region Sign-up
        ISignUpCountryRepository SignUpCountries { get; }

        #endregion


        #region VPN
        IVpnMembershipUserRepository VpnMembershipUsers { get; }
        IVpnMembershipRepository VpnMemberships { get; }
        IVpnMembershipCodeRepository VpnMembershipCodes { get; }
        ILog_VpnMembershipRenewalsRepository Logs_VpnMembershipRenewals { get; }
        IVpnMembershipRenewalBillingRepository VpnMembershipRenewalBillings { get; }
        #endregion

        #region Benefit
        IBenefitAllowedUserRepository BenefitAllowedUsers { get; }
        IBenefitRepository Benefits { get; }
        #endregion

        IDbTransaction BeginTransaction();
        Task<int> CommitAsync();
        Task<int> CommitWithRetryAsync();
    }
}
