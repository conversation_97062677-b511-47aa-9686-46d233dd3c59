using System;
using System.IO;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using C3Pay.Core;
using C3Pay.Core.Common;
using Edenred.Common.Core;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using static C3Pay.Core.Errors;

namespace C3Pay.API.Filters
{
    /// <summary>
    /// Idempotency attribute to prevent duplicate requests
    /// <param name="tableName">The name of the table to store the idempotency key</param>
    /// <param name="partitionKey">The partition key to store the idempotency key</param>
    /// <param name="headerName">The name of the header to store the idempotency key. Default is "X-Idempotency-Key".</param>
    /// </summary>
    [AttributeUsage(AttributeTargets.Method)]
    public class IdempotencyAttribute : ActionFilterAttribute
    {
        private readonly string _tableName;
        private readonly string _partitionKey;
        private readonly string _headerName;

        /// <summary>
        /// Constructor for the IdempotencyAttribute
        /// </summary>
        /// <param name="tableName">The name of the table to store the idempotency key</param>
        /// <param name="partitionKey">The partition key to store the idempotency key</param>
        /// <param name="headerName">The name of the header to store the idempotency key. Default is "X-Idempotency-Key".</param>
        public IdempotencyAttribute(string tableName, string partitionKey, string headerName = "X-Idempotency-Key")
        {
            _tableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
            _partitionKey = partitionKey ?? throw new ArgumentNullException(nameof(partitionKey));
            _headerName = headerName;
        }

        /// <summary>
        /// OnActionExecutionAsync method to execute the action
        /// </summary>
        /// <param name="context">The action executing context</param>
        /// <param name="next">The action execution delegate</param>
        public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var httpContext = context.HttpContext;
            var logger = httpContext.RequestServices.GetRequiredService<ILogger<IdempotencyAttribute>>();
            var tableStorageService = httpContext.RequestServices.GetRequiredService<ITableStorageService>();
            var featureManager = httpContext.RequestServices.GetRequiredService<IFeatureManager>();
            var idempotencyKey = httpContext.Request.Headers.TryGetValue("X-Idempotency-Key", out var idempotencyKeyHeader) ? idempotencyKeyHeader.ToString() : null;
            if (string.IsNullOrWhiteSpace(idempotencyKey))
            {
                await next();
                return;
            }

            if (!await featureManager.IsEnabledAsync(FeatureFlags.EnableIdempotency))
            {
                await next();
                return;
            }

            var key = idempotencyKey.ToString();
            if (string.IsNullOrWhiteSpace(key))
            {
                await next();
                return;
            }

            try
            {
                var existingRequest = await tableStorageService.GetEntityAsync(_tableName, _partitionKey, key);
                if (existingRequest.IsSuccessful && existingRequest.Data != null)
                {
                    if (existingRequest.Data.Value == "REQUEST_INITIATED")
                    {
                        context.Result = new BadRequestObjectResult(IdempotencyErrors.RequestInProgress.Message);
                        return;
                    }
                    else
                    {
                        var cachedResponse = ParseCachedResponse(existingRequest.Data.Value, logger);
                        if (cachedResponse != null)
                        {
                            context.Result = cachedResponse;
                            return;
                        }
                    }
                }
                var initialEntity = new ValueEntity
                {
                    PartitionKey = _partitionKey,
                    RowKey = key,
                    Value = "REQUEST_INITIATED"
                };

                var createResult = await tableStorageService.InsertEntityAsync(_tableName, initialEntity);
                if (!createResult.IsSuccessful)
                {
                    logger.LogError("Failed to create idempotency record for request {IdempotencyKey}", key);

                    if (createResult.ErrorMessage != null && createResult.ErrorMessage.Equals(SystemMessages.TableStorageConflictException))
                    {
                        context.Result = new BadRequestObjectResult(IdempotencyErrors.RequestInProgress.Message);
                        return;
                    }
                }
                var executedContext = await next();
                await StoreResponse(executedContext, key, tableStorageService, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in IdempotencyAttribute for key {IdempotencyKey}", key);
                // Continue with normal execution on error
                await next();
            }
        }

        /// <summary>
        /// Parse the cached response from the table storage
        /// </summary>
        /// <param name="cachedValue">The cached value from the table storage</param>
        /// <param name="logger">The logger</param>
        /// <returns>The parsed response</returns>
        private IActionResult ParseCachedResponse(string cachedValue, ILogger logger)
        {
            try
            {
                var parts = cachedValue.Split("|||");
                if (parts.Length >= 2)
                {
                    var responseCode = parts[0];
                    var responseData = parts[1];
                    var storedResult = JsonConvert.DeserializeObject<object>(responseData);
                    return responseCode switch
                    {
                        "200" => new OkObjectResult(storedResult),
                        "201" => new CreatedResult(string.Empty, storedResult),
                        "400" => new BadRequestObjectResult(storedResult),
                        "401" => new UnauthorizedObjectResult(storedResult),
                        "403" => new ForbidResult(),
                        "404" => new NotFoundObjectResult(storedResult),
                        "409" => new ConflictObjectResult(storedResult),
                        "422" => new UnprocessableEntityObjectResult(storedResult),
                        "500" => new ObjectResult(storedResult) { StatusCode = 500 },
                        "502" => new ObjectResult(storedResult) { StatusCode = 502 },
                        "503" => new ObjectResult(storedResult) { StatusCode = 503 },
                        _ => new OkObjectResult(storedResult) // Default to 200
                    };
                }
            }
            catch (JsonException ex)
            {
                logger.LogError(ex, "Failed to deserialize cached response: {CachedValue}", cachedValue);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error parsing cached response: {CachedValue}", cachedValue);
            }
            return null;
        }

        /// <summary>
        /// Store the response in the table storage
        /// </summary>
        /// <param name="context">The action executed context</param>
        /// <param name="idempotencyKey">The idempotency key</param>
        /// <param name="tableStorageService">The table storage service</param>
        private async Task StoreResponse(ActionExecutedContext context, string idempotencyKey, ITableStorageService tableStorageService, ILogger logger)
        {
            try
            {
                string responseToStore;
                string responseCode;

                if (context.Result is ObjectResult objectResult)
                {
                    responseCode = objectResult.StatusCode?.ToString() ?? "200";
                    responseToStore = JsonConvert.SerializeObject(objectResult.Value);
                }
                else if (context.Result is BadRequestObjectResult badRequestResult)
                {
                    responseCode = "400";
                    responseToStore = JsonConvert.SerializeObject(badRequestResult.Value);
                }
                else if (context.Result is UnauthorizedObjectResult unauthorizedResult)
                {
                    responseCode = "401";
                    responseToStore = JsonConvert.SerializeObject(unauthorizedResult.Value);
                }
                else if (context.Result is NotFoundObjectResult notFoundResult)
                {
                    responseCode = "404";
                    responseToStore = JsonConvert.SerializeObject(notFoundResult.Value);
                }
                else if (context.Result is ConflictObjectResult conflictResult)
                {
                    responseCode = "409";
                    responseToStore = JsonConvert.SerializeObject(conflictResult.Value);
                }
                else if (context.Result is UnprocessableEntityObjectResult unprocessableResult)
                {
                    responseCode = "422";
                    responseToStore = JsonConvert.SerializeObject(unprocessableResult.Value);
                }
                else if (context.Result is CreatedResult createdResult)
                {
                    responseCode = "201";
                    responseToStore = JsonConvert.SerializeObject(createdResult.Value);
                }
                else if (context.Result is ForbidResult)
                {
                    responseCode = "403";
                    responseToStore = JsonConvert.SerializeObject(new { message = "Forbidden" });
                }
                else if (context.Exception != null)
                {
                    responseCode = "500";
                    responseToStore = JsonConvert.SerializeObject(new { message = "Internal Server Error" });
                }
                else
                {
                    responseCode = "200";
                    responseToStore = JsonConvert.SerializeObject(context.Result);
                }

                var valueEntity = new ValueEntity
                {
                    PartitionKey = _partitionKey,
                    RowKey = idempotencyKey,
                    Value = $"{responseCode}|||{responseToStore}"
                };

                var updateResult = await tableStorageService.SetEntityAsync(_tableName, valueEntity);
                if (!updateResult.IsSuccessful)
                {
                    logger.LogError("Failed to update idempotency record for request {IdempotencyKey}", idempotencyKey);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error storing response for idempotency key {IdempotencyKey}", idempotencyKey);
            }
        }
    }
}
