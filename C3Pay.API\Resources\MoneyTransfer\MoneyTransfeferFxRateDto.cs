﻿using System;
using System.Collections.Generic;

namespace C3Pay.API.Resources.MoneyTransfer
{
    /// <summary>
    /// 
    /// </summary>
    public class MoneyTransfeferFxRateDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string TransferType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FromCurrency { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string ToCurrency { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double Amount { get; set; }
            
        /// <summary>
        /// 
        /// </summary>
        public double FxRate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double TotalFeeAmount { get; set; }        

        /// <summary>
        /// 
        /// </summary>
        public double TotalCreditAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double totalDebitAmount { get; set; }        

        /// <summary>
        /// 
        /// </summary>
        public DateTime ConversionDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime Expiry { get; set; } = DateTime.Now.AddMinutes(35);

    }    
}