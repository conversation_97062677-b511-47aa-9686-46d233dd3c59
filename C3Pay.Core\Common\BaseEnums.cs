﻿using System;
using System.ComponentModel;

namespace C3Pay.Core
{
    public static class BaseEnums
    {
        #region Database

        //User
        public enum UserResult
        {
            UserNotFound
        }

        public enum Gender
        {
            Male = 1,
            Female
        }

        public enum UserBlockType
        {
            Locked = 1,
            BlockedByAdmin,
            MoneyTransferBeneficiaryBlackListed,
            MobileRechargeBeneficiaryBlackListed
        }

        public enum HardBlockType
        {
            Admin,
            KYC
        }

        public enum MoneyTransferProfileStatus
        {
            Missing = 1,
            Pending,
            Created
        }

        public enum MobileApplicationId
        {
            C3Pay = 1,
            MySalary
        }

        public enum DocumentType
        {
            EmiratesIdFrontScan = 1,
            EmiratesIdBackScan,
            Selfie,
            Passport
        }

        public enum BlackListedEntityType
        {
            CardSerialNumber = 1,
            MobileRechargePhoneNumber,
            MoneyTransferAccountNumber,
        }

        public enum SubscriptionPaymentFrequency
        {
            PerMonth = 1,
            PerAlert
        }

        public enum MobileRechargeType
        {
            [Description("Local Recharge")]
            LOCAL = 1,
            [Description("International Recharge")]
            INTERNATIONAL,
            [Description("International Calling Card")]
            CALLINGCARDS,
            [Description("Local")]
            MYNUMBER,
            [Description("Local Calling Card")]
            LOCALCALLINGCARDS,
        }

        public enum Status
        {
            [Description("Pending")]
            PENDING = 1,
            [Description("Completed")]
            COMPLETED,
            [Description("Deleted")]
            DELETED,
            [Description("Failed")]
            FAILED,
            [Description("Approved")]
            APPROVED,
            [Description("Successful")]
            SUCCESSFUL,
            [Description("Rejected")]
            REJECTED,
            [Description("Canceled")]
            CANCELED,
            [Description("Auto Canceled")]
            AUTOCANCELED,
            [Description("PendingReverse")]
            PENDINGREVERSE,
            [Description("Reversed")]
            REVERSED,
            [Description("FailedToReverse")]
            FAILEDTOREVERSE,
            [Description("NeedsManualReversal")]
            NEEDSMANUALREVERSAL,
            [Description("PendingFeeReverse")]
            PENDINGFEEREVERSE,
            [Description("Started")]
            STARTED,
            [Description("OnHold")]
            ONHOLD
        }

        public enum BeneficiarySubStatus
        {
            CheckingBeneficiary = 1,
            WaitingForRMTProfileCreation,
        }

        public enum MoneyTransferType
        {
            [Description("Cash Pick-Up")]
            RAKMoneyCashPayout = 1,
            [Description("Bank Transfer")]
            OutsideUAE = 2,
            [Description("Direct Transfer")]
            DirectTransfer = 3,
            [Description("Wallet")]
            Wallet = 4
        }

        public enum DirectTransferEligbility
        {
            ReceiverLimitReached,
            SendAmountLimitReached,
            ValidAmount
        }

        public enum WaiveType
        {
            NotWaived = 1,
            FirstTransaction,
            Loyalty,
            StayStrongIndia,
            CorporateExempted
        }

        public enum PPSBillPayType
        {
            [Description("04")]
            RMT = 1,
            [Description("05")]
            MOB,
            [Description("06")]
            STMNT,
            [Description("14")]
            EMT,
            [Description("07")]
            RFRL,
            [Description("08")]
            DMT,
            [Description("09")]
            BP,
            [Description("11")]
            MTC,
            [Description("12")]
            ORD,
            [Description("13")]
            UEIN,
            [Description("15")]
            C3P
        }

        public enum IdentificationUpdateType
        {
            Add = 1,
            Update
        }

        #endregion

        public enum PopupMessages
        {
            NoPopupFound
        }

        public enum LoggedOutTokenType
        {
            OTP,
            PASSWORD
        }
        public enum CardPaymentServiceProvider
        {
            Euronet,
            PPS
        }

        public enum IdentificationVerificationResult
        {
            VerificationFailed,
            SanctionScreeningInvalid
        }

        public enum TransferStatusValidationMessage
        {
            PartnerMinAmountReached,
            PartnerMaxAmountReached,
            NoOfTransactionsLimitReached,
            MonthlyAmountLimitReached,
            UserNoOfTransactionsLimitReached,
            UserMonthlyAmountLimitReached,
            BeneficiaryAlreadyExists,
            BeneficiaryNotExists,
            CountryNotExists,
            BankNameNotExists,
            IFSCCodeNotExists,
            BankNotExists,
            TransferMethodNotExists,
            TransferTypeNotExists,
            UserNotExists,
            InvalidCardNumber,
            FirstNameNotExists,
            LastNameNotExists,
            InvalidCardSerialNumber,
            InvalidTransaction,
            PPSConnectionIssue,
            RAKConnectionIssue,
            InsufficientBalance,
            ExceedBeneficiaryCountLimit,
            InvalidReasonId,
            ReasonNotExists,
            CountryNotSupported,
            FxRateNotFoundFromRak,
            TransactionIsInProgress,
            ActivateYourCard,
            UnblockYourCard,
            InvalidToCurrency,
            EmiratesIdNotExists,
            Successful,
            UserBlocked,
            BankCodeNotExists,
            BankBranchCodeNotExists,
            AccountNumberNotExists,
            AmountNotExists,
            SendCurrencyNotExists,
            ReceiveCurrencyNotExists,
            SendAmountNotExists,
            ReceiveAmountNotExists,
            ConversionRateNotExists,
            ReferralCodeNotExists,
            TransactionIdNotExists,
            PhoneNumberIsRequired,
            AccountNumberIsRequired,
            ApiCallFailed,
            AmountTooLowOrTooHigh,
            SamePhoneNumberAsUserUsed,
            AmountTooHigh,
            AmountTooLow,
            ErrorGettingBalance,
            ErrorDebitingCard,
            TransactionNotExists,
            CantBeCanceledOrHasBeenClaimed,
            ErrorCreditingCard,
            TransferError,
            InvalidBranchId,
            NoSuccessfulTransferFound,
            BeneficiaryUpdateFailed,
            FetchingExternalBeneficiaryFailed,
            ExceedAmountLimit,
            ExceedFirstNameLength,
            ExceedMiddleNameLength,
            ExceedLastNameLength,
            ExceedMaxAmountLimit,
            ExceptionOccurred,
            AccountNumberAlreadyExists,
            ExchangeHouseUser,
            MinFirstNameLength,
            MinLastNameLength,
            FullNameNotFound,
            InvalidRequestInput,
            FieldsNotFound,
            AddressLine1NotFound,
            CityNotFound,
            DistrictNotFound,
            ProfilePending,
            PostalCodeNotFound,
            EidExpired
        }

        #region UnemploymentInsurance 

        public enum UnemploymentInsurancePaymentFrequency
        {
            PerMonth,
            PerYear
        }
        #endregion

        public enum RechargeStatusValidationMessage
        {
            BeneficiaryNotExists,
            ProductsNotExistsForBeneficiary,
            InvalidProductCode,
            TransactionIsInProgress,
            InvalidBeneficiaryForCallingCards,
            ExceedNicknameLength,
            BeneficiaryAlreadyExists,
            InvalidPhoneNumber,
            NickNameNotExists,
            CountryNotSupported,
            UserBlocked,
            ProviderNotExistsforPhoneNumber,
            UserNotExists,
            ProductNotExists,
            FullNameNotExists,
            phoneNumberNotExists,
            CountryNotExists,
            InvalidCardNumber,
            InvalidCardSerialNumber,
            RechargeAmountMonthlyLimitReached,
            InvalidRechargeTransaction,
            ActivateYourCard,
            UnblockYourCard,
            InsufficientBalance,
            PPSConnectionIssue,
            DingConnectionIssue,
            DingResponseFailed,
            EmiratesIdNotExists,
            EmiratesIdExpired,
            RechargeAmountLimitReachedWithoutEmiratesId,
            ProductCodeNotExists,
            OperatorNotExists,
            RechargeTypeNotExists,
            SendCurrencyNotExists,
            ReceiveCurrencyNotExists,
            SendAmountNotExists,
            ReceiveAmountNotExists,
            TransactionIdNotExists,
            AutoRenewalDisabled,
            AutoRenewalDisabledForUser
        }

        public enum MobileProductCategory
        {
            [Description("Mobile,Minutes")]
            Credit = 1,
            [Description("Mobile,Data")]
            Data,
            [Description("Mobile,Minutes,Data")]
            CreditData
        }

        public enum MobileRechargeProducts
        {
            CREDIT = 1,
            DATA,
            CALLINGCARDS
        }

        public enum MobileRechargeDiscountType
        {
            Flat = 1,
            Percentage
        }

        public enum TransactionType
        {
            [Description("RAK Money")]
            RAKMoney = 1,
            [Description("Index Money")]
            IndexMoney = 2,
            [Description("Direct")]
            Direct = 3
        }

        public enum TransactionPrefix
        {
            [Description("Balance")]
            BAL,
            [Description("RAK Money Transfer")]
            RMT,
            [Description("EH Money Transfer")]
            EMT,
            [Description("Mobile Transfer")]
            MOB,
            [Description("Statement")]
            STMNT,
            [Description("Referral")]
            RFRL,
            [Description("C3 To C3 Debit")]
            DMTD,
            [Description("C3 To C3 Credit")]
            DMTC,
            [Description("Bill Payment")]
            BP,
            [Description("Money Transfer Cashback")]
            MTC,
            [Description("Store Orders")]
            ORD,
            [Description("Unemployment Insurance")]
            UEIN,
            [Description("RAK Money Transfer Experiment")]
            RMTEXP,
            [Description("Mobile Transfer")]
            MOBMK,
            [Description("Reversed")]
            REV,
            [Description("C3Pay+")]
            C3P,
            [Description("C3Pay+ ATM Withdrawal Fee Reversal")]
            C3PAR,
            [Description("C3Pay+ ATM Withdrawal Zero Fee Reversal")]
            C3PARZ,

            //[Description("C3Pay+ BE Subscription Fee Reversal")]
            [Description("C3Pay+ : Balance Enquiry Fee Reversal")]
            C3PBER,
            //[Description("C3Pay+ SMS Subscription Fee Reversal")]
            [Description("C3Pay+ : Security SMS Fee Reversal")]
            C3PSMSR,
            [Description("C3Pay+ Membership Refund")]
            C3PR,
            [Description("C3Pay+ Money Transfer Refund")]
            C3PMTR,

            [Description("MenaVPN")]
            MENAVPN,
            [Description("MenaVPN Reversal")]
            MENAVPNRF,
        }

        public enum PPSMerchantType
        {
            RMT = 1,
            MOB = 2,
        }

        public enum ExternalStatus
        {
            [Description("SUCCESS")]
            S,
            [Description("REJECTED")]
            REJ,
            [Description("FAILED")]
            F,
            [Description("PENDING")]
            P,
            [Description("REVERSED")]
            REV
        }

        public enum TransactionEnvironment
        {
            [Description("Testing")]
            UAT,
            [Description("Production")]
            LIVE,
        }

        public enum ExternalFileStatus
        {
            [Description("Sucess")]
            SUC,
            [Description("Waiting for Bank Approval")]
            UPR,
            [Description("Failure")]
            FAL,
            [Description("Reveral")]
            REV,
        }

        public enum PPSResponseStatus
        {
            [Description("CARD HAS NOT BEEN ACTIVATED")]
            CARDNOTACTIVATED,
            [Description("MAX PIN TRIES EXCEEDED")]
            MAXPINEXCEEDED,
        }

        public enum PPSAccountIdPrefix
        {
            RMT = 50,
            MOB = 80,
        }

        public enum MobileRechargeReference
        {
            [Description("MyC3Card")]
            myc3card
        }

        public enum TransferMethod
        {
            [Description("CashPickup")]
            CASHPICKUP = 1,
            [Description("BankTransfer")]
            BANKTRANSFER,
            [Description("DirectTransfer")]
            DIRECTTRANSFER,
            [Description("Wallet")]
            WALLET
        }

        public enum CountryEnable
        {
            [Description("00971")]
            AE,
            [Description("0091")]
            IN,
            [Description("0092")]
            PK,
            [Description("00977")]
            NP,
            [Description("00880")]
            BD,
            [Description("0094")]
            LK,
            [Description("0063")]
            PH,
            [Description("00212")]
            MA
        }

        public enum EmiratesIdDocumentType
        {
            emirates_id_front_scan = 1,
            emirates_id_back_scan,
            selfie
        }

        public enum CallingCardOperator
        {
            [Description("ETISALAT")]
            FUAE,
            [Description("DU")]
            HAAE,
            [Description("VIRGIN")]
            VRAE
        }

        public enum LocalOperator
        {
            [Description("ETISALAT")]
            ETAE,
            [Description("DU")]
            E6AE
        }

        public enum MessageAction
        {
            Create,
            Update,
            Delete,
            CreateWu
        }

        public enum IdentificationStatus
        {
            Missing,
            Expired,
            Validating,
            Valid,
            ValidUpdatable,
            InGrace
        }

        public enum EmiratesIdEligibilityResult
        {
            Eligible,
            AssociatedWithAnotherUser,
            Mismatch
        }

        public enum CardEligibilityResult
        {
            Eligible,
            AlreadyRegistered,
            InvalidCard,
            InEligible,
            Blocked,
            InvalidCombination
        }

        public enum StatementResult
        {
            NoTransactionsFound
        }

        public enum PhoneEligibilityResult
        {
            Eligible,
            AlreadyRegistered,
            MatchesUserPhone,
            InvalidPhoneNumber,
            UserBlocked
        }

        public enum AutoUnblockResult
        {
            WrongCombination,
            MaxAttemptsReached
        }

        public enum ResetPasswordFlowEligibilityResult
        {
            EligibleForForgotPassword,
            EligibleForAutoUnblock,
            InEligible
        }

        public enum OTPVerificationResult
        {
            Verified,
            InvalidPhoneNumber,
            InvalidOTP
        }

        public enum CardVerificationResult
        {
            Verified,
            InvalidCard,
            InvalidUserCardCombination,
            UserBlocked
        }

        public enum SecretAnswerVerificationResult
        {
            Verified,
            WrongQuestion,
            WrongAnswer
        }

        public enum OTPVerificationReason
        {
            [Description("C3Pay app Sign up")]
            SignUp,
            [Description("Forgot Password")]
            ForgotPassword,
            [Description("Change Password")]
            ChangePassword,
            [Description("Forgot Phone Number")]
            ForgotPhoneNumber,
            [Description("Change Phone Number")]
            ChangePhoneNumber,
            [Description("Mobile Recharge beneficiary")]
            MobileRechargeBeneficiary,
            [Description("Money Transfer beneficiary")]
            CashPickupBeneficiary,
            [Description("Money Transfer beneficiary")]
            BankTransferBeneficiary,
            [Description("Money Transfer beneficiary")]
            MoneyTransferBeneficiary,
            [Description("Money Transfer Cashpickup")]
            MoneyTransfer,
            [Description("Unblock")]
            AutoUnblock,
            [Description("Direct Transfer Beneficiary")]
            DirectTransferBeneficiary,
            [Description("Bill Payment")]
            BillPayment,
            [Description("Salary Advance")]
            SalaryAdvance,
            [Description("C3Pay app unblock card")]
            UnblockCard,
            [Description("Earned Wage Access")]
            EarnedWageAccess,
            [Description("Device Binding")]
            DeviceBinding,
            [Description("MoneyTransfer Wallet")]
            MoneyTransferWallet,
            [Description("MoneyTransfer Bank")]
            MoneyTransferBank
        }

        public enum IdentificationVerificationStatus
        {
            Pending = 1,
            Approved,
            Rejected
        }

        public enum IdentificationServiceVendor
        {
            OldService = 1,
            Signzy,
            Azure
        }

        public enum FileMediaType
        {
            [Description("application/pdf")]
            PDF,
            [Description("application/mp4")]
            MP4,
            [Description("application/png")]
            PNG
        }

        public enum SMSSubscriptionType
        {
            [Description("Balance Enquiry")]
            BE,
            [Description("Salary Alert")]
            S,
            [Description("SMS Security")]
            T,
            [Description("C3Pay+")]
            C3P,
        }

        public enum RMTProfileStatus
        {
            Created,
            Pending,
            Missing,
            Unavailable
        }

        public enum RMTProfileActions
        {
            A,
            U,
            R,
            D
        }

        public enum RegistrationType
        {
            [Description("Full KYC")]
            FullKYC,
            [Description("Partial KYC")]
            PartialKYC,
            [Description("No KYC")]
            NoKYC,
        }

        public enum UserStatus
        {
            [Description("Active")]
            Active,
            [Description("Verified")]
            Verified,
            [Description("Blocked")]
            Blocked,
        }

        public enum SearchType
        {
            User,
            Registration,
            Rejected,
            Updated,
            Verification
        }

        public enum TransactionNarration
        {
            [Description("Balance Enquiry")]
            BalanceEnquiry,
            [Description("Local Mobile Top up")]
            LocalMobileRecharge,
            [Description("Intl Mobile Top up")]
            InternationalMobileRecharge,
            [Description("Money Transfer to {0}")]
            MoneyTransfer,
            [Description("ACCOUNT STATEMENT FEE - SOFT COPY (INCL. VAT)")]
            Statement,
            [Description("Referral Reward")]
            ReferralReward,

            [Description("Sent to {0}")]
            C3PayToC3PayTransferDebit,
            [Description("Received from {0}")]
            C3PayToC3PayTransferCredit,
            [Description("Cancelled Transfer to {0}")]
            C3PayToC3PayTransferCancellation,
            [Description("C3Pay to C3Pay Transfer Fee")]
            C3PayToC3PayTransferFee,

            [Description("Sent to {0}")]
            MySalaryToMySalaryTransferDebit,
            [Description("Received from {0}")]
            MySalaryToMySalaryTransferCredit,
            [Description("Cancelled Transfer to {0}")]
            MySalaryToMySalaryTransferCancellation,
            [Description("MySalary to MySalary Transfer Fee")]
            MySalaryToMySalaryTransferFee,

            [Description("Exchange House Money Transfer")]
            EHMoneyTransfer,
            [Description("Bill Payment")]
            BillPayment,

            [Description("Money Transfer Cashback")]
            MoneyTransferCashback,
            [Description("Money Transfer Reversal")]
            MoneyTransferReversal,

            [Description("Store Order Payment")]
            StoreOrder,

            [Description("C3Pay+")]
            C3PayPlus,
            [Description("C3Pay+ ATM Withdrawal Fee Reversal")]
            C3PayPlusAtmWithdrawalFeeReversal,

            [Description("C3Pay+ : Balance Enquiry Fee Reversal")]
            C3PayPlusBESubscriptionFeeReversal,
            [Description("C3Pay+ : Security SMS Fee Reversal")]
            C3PayPlusSMSSubscriptionFeeReversal,
            [Description("C3Pay+ Membership Refund")]
            C3PayPlusMembershipRefund,
            [Description("C3Pay+ Money Transfer Refund")]
            C3PayPlusMoneyTransferRefund
        }

        public enum TransactionMerchantCode
        {
            [Description("500001")]
            MobileRecharge,
            [Description("500003")]
            MoneyTransfer,
            [Description("500005")]
            Statement,
            [Description("500002")]
            Referral,
            [Description("500007")]
            AppToAppTransfer,
            [Description("500007")]
            AppToAppTransferFee,
            [Description("500008")]
            AppToAppTransferCredit,
            [Description("500009")]
            BillPayment,
            [Description("500004")]
            MoneyTransferCashbackCredit,
            [Description("500011")]
            StoreOrder,
            [Description("500013")]
            UnemploymentInsurance,
            [Description("500016")]
            C3PayPlusAtmWithdrawalFeeReversal,
            [Description("500017")]
            C3PayPlusMembershipDebit,

            [Description("500022")]
            C3PayPlusBESubscriptionFeeReversal,
            [Description("500024")]
            C3PayPlusSMSSubscriptionFeeReversal,

            [Description("500028")]
            C3PayPlusMoneyTransferRefund,
        }
        public enum MySalaryTransactionMerchantCode
        {
            [Description("600001")]
            MobileRecharge,
            [Description("600013")]
            MoneyTransfer,
            [Description("600007")]
            AppToAppTransfer,
            [Description("600007")]
            AppToAppTransferFee,
            [Description("600008")]
            AppToAppTransferCredit,
        }
        public enum EHTransactionMerchantCode
        {
            [Description("700001")]
            MobileRecharge,
            [Description("700003")]
            MoneyTransfer,
            [Description("700005")]
            Statement,
            [Description("500002")]
            Referral,
            [Description("700007")]
            AppToAppTransfer,
            [Description("700007")]
            AppToAppTransferFee,
            [Description("700008")]
            AppToAppTransferCredit,
            [Description("700009")]
            BillPayment,
            [Description("700004")]
            MoneyTransferCashbackCredit,
            [Description("700011")]
            StoreOrder,
            [Description("700013")]
            UnemploymentInsurance,
            [Description("700016")]
            C3PayPlusAtmWithdrawalFeeReversal,
            [Description("700017")]
            C3PayPlusMembershipDebit,

            [Description("700022")]
            C3PayPlusBESubscriptionFeeReversal,
            [Description("700024")]
            C3PayPlusSMSSubscriptionFeeReversal,

            [Description("700028")]
            C3PayPlusMoneyTransferRefund,
        }
        public enum TransactionMerchantCodeFeature
        {
            MobileRecharge,
            MoneyTransfer,
            Statement,
            Referral,
            AppToAppTransfer,
            AppToAppTransferFee,
            AppToAppTransferCredit,
            BillPayment,
            MoneyTransferCashbackCredit,
            StoreOrder,
            UnemploymentInsurance,
            C3PayPlusMembershipDebit,
            C3PayPlusAtmWithdrawalFeeReversal,
            C3PayPlusBESubscriptionFeeReversal,
            C3PayPlusSMSSubscriptionFeeReversal,
            C3PayPlusMembershipRefund,
            C3PayPlusMoneyTransferRefund
        }
        public enum CardDetailsResults
        {
            [Description("Ok")]
            Ok = 1,
            [Description("Blocked Too Many Failed Attempts")]
            BlockedTooManyFailedAttempts,
            [Description("Missing Credentials")]
            MissingCredentials,
            [Description("Invalid Card")]
            InvalidCard,
            [Description("Incorrect CVC2")]
            IncorrectCvc2,
            [Description("Error")]
            Error,
            [Description("InvalidCombination")]
            InvalidCombination
        }

        public enum MultimediaCountry
        {
            IND,
            PAK,
            NPL,
            PHL,
            LKA,
            BGD
        }

        public enum CardPlasticType
        {
            Blue = 1,
            BlackV1,
            BlackV2
        }

        public enum MoneyTransferPartnerType
        {
            DirectClient = 1,
            ExchangeHouse
        }

        public enum EHMoneyTransferApiResponses
        {
            EligibleForMoneyTransfer = 1,
            UpdatedCardHolderPhoneNumber,
            InvalidCountryCode,
            [Description("The selected money transfer reason was not found.")]
            MoneyTransferReasonNotFound,
            [Description("Beneficiary was added successfully.")]
            BeneficiaryAdded,
            BeneficiaryNotApproved,
            BeneficiaryNotFound,
            BeneficiaryDeleted,
            BeneficiaryWaiting,
            [Description("User was either not found or was blocked.")]
            UserNotFoundOrBlocked,
            [Description("Account number is required.")]
            AccountNumberIsRequired,
            [Description("Beneficiaries count limit was exceeded.")]
            ExceededBeneficiaryCountLimit,
            [Description("The beneficiary you are trying to add was already added.")]
            BeneficiaryAlreadyExists,
            [Description("The selected country is not supported.")]
            CountryNotSupported,
            UserNoOfTransactionsLimitReached,
            UserMonthlyAmountLimitReached,
            AmountTooHigh,
            AmountTooLow,
            UserNotEligible,
            TransferTypeNotFound,
            UserCardDetailsNotFound,
            [Description("User was not found.")]
            UserNotFound,
            PPSConnectionIssue,
            CardNotActive,
            CardBlocked,
            [Description("Error getting user's balance.")]
            ErrorGettingBalance,
            InsufficientBalance,
            ApiCallToEHFailed,
            [Description("User is blocked.")]
            UserBlocked,
            CantAddMoneyTransferBeneficiary,
            TransferNotFound,
            NoBanksFound,
            FailedToGetRates,
            [Description("Last name is required.")]
            LastNameIsRequired,
            [Description("Nationality code is required.")]
            NationalityCodeIsRequired,
            [Description("Country code is required.")]
            CountryCodeIsRequired,
            [Description("Money transfer reason is required.")]
            MoneyTransferReasonIsRequired,
            [Description("The beneficiary's phone number cannot be the same as the current user's phone number.")]
            SamePhoneNumberAsUserUsed,
            [Description("The selected bank was not found.")]
            BankNotFound,
            [Description("The selected branch was not found.")]
            BranchNotFound,
            [Description("An error occurred while adding a beneficiary. Please try again later.")]
            ErrorAddingBeneficiary,
            TransactionAmountExceeds,
            MonthlyTransactionLimitReached,
            AnuualTransactionLimitReached,
            RemitterTransactionAmountExceeded,
            AccountNumberShouldXCharactersLong,
            BeneficiaryAccountNumberShouldXCharactersLong,
            BeneficiaryAccountNumberLengthBetweenXY,
            RemitterDOBMinor,
            AnnualActivityVolumeLimitCheckFailed,
            AnnualActivityAmountLimitCheckFailed,
            RemitterAlreadyTransferedAmount
        }

        public enum EHMoneyTransferClients
        {
            Index = 1
        }

        public enum BillPaymentsIOType
        {
            Input = 1,
            Output = 2
        }

        public enum BillFetchStatus
        {
            [Description("NoBill")]
            NOBILL = 1,
            [Description("Invalid")]
            INVALID,
            [Description("Fetching")]
            FETCHING,
            [Description("Ready")]
            READY,
            [Description("Expired")]
            EXPIRED,
            [Description("Not Applicable")]
            NOTAPPLICABLE
        }

        public enum BillPaymentsIOOperation
        {
            Inquiry = 0,
            Payment = 1,
            Both = 2
        }
        public enum BillPaymentsIODataType
        {
            [Description("Numeric")]
            Numeric,
            [Description("Alphanumeric")]
            Alphanumeric,
        }

        public enum BillPaymentProductDisplayType
        {
            [Description("Package")]
            Package,
            [Description("Topup")]
            Topup,
        }

        public enum UserValidationMessage
        {
            UserNotExists
        }
        public enum BillPaymentValidationMessage
        {
            InvalidFieldId,
            NeedSubProvider,
            InvalidSubProvider,
            InvalidProvider,
            [Description("Bill details you entered are incorrect. Please delete the bill and try again.")]
            InvalidFieldValue,
            InvalidCategory,
            [Description("Sorry we are unable to process your request right now. Please try again.")]
            FieldMinValueDoesnotMet,
            [Description("Sorry we are unable to process your request right now. Please try again.")]
            FieldMaxValueExceeded,
            FieldInvalidDataType,
            ProviderNotFound,
            ProductNotFound,
            FieldNotFound,
            [Description("Sorry we are unable to process your request right now. Please try again.")]
            InvalidUserBiller,
            [Description("Sorry we are unable to process your request right now. Please try again.")]
            InvalidProduct,
            AmountOrProductNotNeeded,
            NeedAmountOrProduct,
            [Description("Sorry, the amount you entered is incorrect, please enter the bill amount given in your bill and try again. We have sent the money back into your account.")]
            InvalidAmount,
            [Description("Sorry we are unable to process your request right now. Please try again.")]
            InvalidCurrency,
            [Description("Sorry we are unable to process your request right now. Please try again.")]
            InvalidInput,
            UserBillerExistsAlready,
            InquiryNotAvailable,
            [Description("There are no bills due for this biller.")]
            NoAmountDue,
            InvalidTransaction,
            [Description("Sorry we are unable to process your request right now. Please try again.")]
            UnableToGetCardBalance,
            [Description("Sorry we are unable to process your request. Please try again.")]
            PaymentFailed,
            [Description("Sorry we are unable to process your request right now. Please try again.")]
            NoTransactionsFound,
            MaximumBillersLimitReached,
            [Description("Sorry we are unable to process your request right now as maximum allowed amount per transaction exceeded.")]
            MaximumAllowedAmountPerTransactionExceeded,
            [Description("Sorry we are unable to process your request right now as maximum allowed amount for a month exceeded.")]
            MaximumAllowedAmountPerMonthExceeded,
            [Description("Sorry we are unable to process your request right now as you do not have sufficient balance.")]
            InsufficientBalance,
            [Description("Sorry we are unable to process your request right now because your card has not been activated. Please activate your card and try again.")]
            ActivateYourCard,
            [Description("Sorry we are unable to process your request as your card is blocked. Please unblock your card or contact us for assistance.")]
            UnblockYourCard,
            [Description("Payment is in Progress.")]
            PaymentInProgress,
            [Description("Sorry we are unable to process your request right now. Please try again.")]
            UnableToDebitUserBalance,
            [Description("Successful Payment.")]
            SuccessfulPayment,
            [Description("Sorry we are unable to process your request right now. Please try again.")]
            WaitFewMinsForNextTransaction,
            [Description("This account is expired/invalid. Please use valid account details and try again, we have sent the money back to your account.")]
            AccountExpiredOrInvalid,
            [Description("The amount you entered is not in the allowed range. Please enter the amount within the given range and try again. We have sent the money back into your account.")]
            AmountNotInAllowedRange,
            [Description("Sorry, we are experiencing some issues with this biller. Please try again later, we have sent the money back into your account.")]
            BillerNotAvailable,
        }

        public enum DirectTransferDebitKind
        {
            [Description("F")]
            Fee = 1,
            [Description("A")]
            Amount = 2
        }

        public enum Version
        {
            V1 = 1,
            V2
        }

        public enum ReversalMode
        {
            None = 1,
            EdenredCorporateIds,
            All
        }
    }

    public enum CurrentEnvironment
    {
        [Description("Development")]
        Development,
        [Description("UAT")]
        UAT,
        [Description("Production")]
        Production
    }
    public enum MySalaryTransferType
    {
        ByExchangeHouse = 1,
        Direct
    }

    public enum SkuCatelog
    {
        [Description("Country")]
        Country,
        [Description("BillerType")]
        BillerType,
        [Description("BillerName")]
        BillerName,
        [Description("BillerID")]
        BillerID,
        [Description("Sku")]
        Sku
    }

    public enum MobileRechargeFeeMode
    {
        NoFee,
        OnlyOnSelectedCorporates,
        All
    }

    public enum SmsProvider
    {
        Etisalat = 1,
        Infobip
    }

    public enum LanguageContentType
    {
        sa_criteria,
        da_qck_act,  // Dashboard Quick action (Default)
        da_qck_act_ind,// Dashboard Quick action India
        da_qck_act_pak, // Dashboard Quick action Pakistan
        da_qck_act_lka,// Dashboard Quick action Srilanka
        da_qck_act_phl,// Dashboard Quick action Phillipines
        da_qck_act_bgd,// Dashboard Quick action Bangladesh
        da_qck_act_npl, // Dashboard Quick action Nepal
        da_mt_tag, // Dashboard Money Transfer Tags (Short Form)
        da_mr_tag, // Dashboard Mobile Recharge Tags (Short Form)
        dev_bind_msg, //Device Binding Message Type
    }

    public enum NomineeRelationshipType
    {
        Wife = 1,
        Husband,
        Child,
        Parent,
        Sibling,
        Friend,
        Father,
        Mother,
        Son,
        Daughter,
        Brother,
        Sister,
    }
    public enum VerifyAtmPinResult
    {
        PinVerified,
        WrongCombination,
        BlockedTooManyFailedAttempts
    }

    public enum EHAgentBankName
    {
        [Description("ICICI Bank")]
        IN = 1,
        [Description("United Bank Limited")]
        PK,
        [Description("Siddartha Bank")]
        NP,
        [Description("Commercial Bank of Ceylon")]
        LK,
        [Description("Metro Ban")]
        PH,
        [Description("DBBL")]
        BD
    }

    public enum FeatureType
    {
        MobileRecharge = 1,
        MoneyTransfer,
        C3PayToC3Pay,
        BillPayments,
        SalaryAdvance,
        ReferralProgram,
        UnEmpInsurance,
        UnEmpInsurance_MySalary,
        Subscription_SecuritySMS,
        MobileRecharge_Promotion,
        EhRatesAreBetter,
        Login,
        ForgotPassword,
        BillPaymentNol,
    }

    public enum MultimediaType
    {
        Video = 1,
        Audio,
        Text,
        Image
    }

    public enum MoneyTransferSmartDefaultType
    {
        Send = 1,
        Receive
    }

    public enum ExperimentType
    {
        MoneyBackGuarantee = 1,
        LoginVideo = 3,
        CashPickup = 4,
        NoLoyalty = 5,
        MTGif = 10,
        PersonalDetailsRedesign = 12,
        FreeTransferExpiry = 18,
        C3PayPlus = 19,
        RetentionGoldAndSpinWheel = 20,
        RatesExperimentControl = 21,
        RatesExperimentMid = 22,
        RatesExperimentBest = 23,
        GoldIncentiveAcquisition = 24,
        SpinWheelRetention = 28,
        MRBestValue = 29,
        MRBestValueControl = 30,
        PersonalDetails_FullName = 31,
        VPN = 32
    }

    public enum ExperimentMode
    {
        Default = 1,
        OddEven = 2,
        Custom = 3
    }

    public enum ExperimentGroupCodeType
    {
        A,
        B,
        C,
        D
    }

    public enum AccountNumberLengthLimitType
    {
        [Description("UseDefault")]
        UseDefault,
        [Description("Exact")]
        Exact,
        [Description("Range")]
        Range
    }

    #region  Store

    public enum OrderStatus
    {
        Placed = 0,
        Failed
    }


    public enum StoreOrderPhoneField
    {
        [Description("Brand")]
        Brand,
        [Description("Model")]
        Model,
        [Description("Back Camera")]
        BackCamera,
        [Description("Front Camera")]
        FrontCamera,
        [Description("Screen Size")]
        ScreenSize,
        [Description("Sim")]
        Sim,
        [Description("Memory")]
        Memory,
        [Description("Warranty")]
        Warranty,
        [Description("Colour")]
        Colour,
        [Description("ImageUrl")]
        ImageUrl,
        [Description("Price")]
        Price
    }

    public enum StoreOrderValidationMessage
    {
        OrderAlreadyPlaced,
        ActivateYourCard,
        InvalidProduct,
        InsufficientBalance,
        UnblockYourCard,
        UnableToGetCardBalance,
        UnableToDebitUserBalance,
        [Description("Successful Payment.")]
        OrderPlaced,
        InvalidOrder,
        InvalidCity,
        OrderAddressAlreadyAvailable,
        NoActiveOrderFound,
    }

    public enum ProductType
    {
        Phone = 1,
    }

    #endregion 

    #region Dashboard 

    public enum DashboardSectionType
    {
        QuickAction = 1,
        Subscriptions = 2
    }

    public enum DashboardElementType
    {
        QuickActionButton = 1
    }

    public enum DashboardQuickActionTag
    {
        MT_NoCharges = 1, // MT = money transfer
        MT_FromAED5 = 2,
        MT_Get2Point5Percent = 3,
        MT_AEDTenOnly = 4,
        MT_ZeroDeduction = 5,
        COMMON_New = 6,
        MR_50PercentOff = 7,
        MT_SpinTheWheel = 8,
        MT_GoldIncentive = 9,
    }

    public enum DashboardElementItem
    {
        MoneyTransfer = 1,
        LocalMobileRecharge = 2,
        InternationalMobileRecharge = 3,
        BillPayments = 6,
        Transactions = 8,
        SecurityAlerts = 10,
        UnemploymentInsurance = 11,
        MoneyTransfer_Bank = 13,
        MoneyTransfer_CashPickup = 14,
        MoneyTransfer_Gif_BankLogos = 15,
        MoneyTransfer_Gif_Rocket = 16,
        MobileRecharge_No_Animation = 17,
        C3PayPlus = 18,
        VPN = 19
    }

    public enum DashboardPopupDynamicContentType
    {
        None = 0,
        C3PayPlusWinnersV1,
        C3PayPlusLifeInsuranceV1,
        C3PayPlusFreeAtmWithdrawalV1,

    }

    public enum DashboardPopupMainContentDisplayMode
    {
        None = 0,
        Circle,
        Square
    }

    #endregion


    #region UnEmp Insurance


    public enum UnemploymentInsuranceValidationMessage
    {
        InvalidPaymentOption,
        UserAlreadySubscribed,

        [Description("Invalid Request")]
        InvalidRequest,

        [Description("ExternalApiFailed")]
        ExternalApiFailed,

        [Description("WorkerNotFound")]
        WorkerDetailsNotFound,
        [Description("Sorry we are unable to process your request right now. Please try again.")]
        UnableToGetCardBalance,
        ActivateYourCard,
        UnblockYourCard,
        EntryNotFound,
        InvalidUserFreeZoneEmployee,
        UnableToFetchInstallments
    }

    public enum UnEmpInsuranceStatus
    {
        [Description("InProgress")]
        InProgress = 1,
        [Description("Subscribed")]
        Subscribed,
        [Description("UnSubscribed")]
        UnSubscribed,
        [Description("Failed")]
        Failed,
        [Description("PendingPayment - In B2B Salary Queue")]
        PendingPayment,
        [Description("Retrying -  In B2B Retry Queue")]
        Retrying
    }

    public enum UnEmpInsuranceInstallmentStatus
    {
        [Description("Paid")]
        Paid = 1,
        [Description("Upcoming")]
        Upcoming,
        [Description("OverDue")]
        OverDue,
        [Description("")]
        InFuture
    }

    public enum UnEmpInsuranceSource
    {
        C3Pay = 1,
        MySalary = 2,

        Corporate = 3,
        External = 4
    }

    public enum UnEmpInsuranceCategory
    {
        A = 1,
        B = 2
    }

    public enum UnEmpInsurancePaymentOptionFromExternal
    {
        [Description("Monthly")]
        Monthly = 01,
        [Description("Quarterly")]
        Quarterly = 02,
        [Description("Semi Annual")]
        SemiAnnual = 03,
        [Description("Full/Annual")]
        Annual = 04,
    }

    public enum InsurancePaymentOption
    {
        Monthly,
        Full,
        SemiAnnual,
        Quarterly
    }
    #endregion


    public enum UnEmpInsurancePartnerType
    {
        DirectClient = 1,
        ExchangeHouse
    }

    public enum PartnerType
    {
        [Description("Direct and Bank Referral")]
        DirectClient = 1,
        [Description("Exchange Houses")]
        ExchangeHouse
    }

    public enum StatementOption
    {
        HardCopy = 1,
        SoftCopy
    }

    public enum UserRole
    {
        ExchangeHouseUser = 1,
        DirectUser
    }

    public enum MoneyTransferDelayType
    {
        None = 0,
        BankHoliday,
        ImpsDowntime,
        ScheduledDowntime
    }
    public enum TransactionFailureReasons
    {
        None = 0,
        InvalidBeneficiaryAccountNumber,
        InvalidBeneficiaryName,
        InvalidIfsCode
    }

    public enum ExchangeHouseCode
    {
        [Description("ORIENT")]
        ORIENT
    }

    public enum EmployeeStatus
    {
        Active,
        Dormant,
        Deleted
    }

    public enum MoneyTransferMethodType
    {
        BankTransfer = 1,
        CashPickup,
        Direct,
        Wallet
    }

    public enum BannerDisplayType
    {
        Top = 1,
        Middle,
        Bottom
    }

    public enum BannerContentType
    {
        Image = 1,
        Video,
        Text
    }

    public enum FieldType
    {
        Input = 1,
        Selection,
        Mixed,
        Dropdown
    }

    public enum FieldCode
    {
        IFSC = 1,
        FirstName,
        MiddleName,
        LastName,
        AccountNumber,
        IBAN,
        PhoneNumber,
        AddressLine1,
        CountryCode,
        CardNumber,
        City,
        State,
        PostalCode,
        BankId,
        District,
        CurrencyCode,
        BankName,
        ResidentCountry,
        ServiceProvider,
        AccountNumberOrIBAN,
        FullName,
        BranchId
    }

    public enum CharacterType
    {
        Any = 1,
        Digit,
        Latin,
        Space
    }

    public enum CharacterCaseType
    {

        None = 1,
        Lower,
        Upper,
    }

    public enum AllowedCharsType
    {
        Any = 1,
        Alpha,
        Numeric,
        NumericMask,
        AlphaNumeric,
        AlphaNumericMask,
        AlphaLatin,

    }

    public enum NavigationFieldType
    {
        CTA = 1,
        Selection
    }

    public enum DeviceBindingStatus
    {
        VALID = 1,
        INVALID,
        NODEVICE
    }

    public enum KycStatus
    {
        Valid,
        NotSubmittedKycGraceFirstReminder,
        NotSubmittedKycGraceSecondReminder,
        NotSubmittedKycBlocked,
        ExpiringSoon,
        Expired,
        ExpiredAndInGrace,
        Blocked,
    }

    public enum SftpReportType
    {
        TransactionReport = 1,
        StatementReport,
        SubmissionReport,
        AcknowledgementReport
    }

    public enum GwTooltipPosition
    {
        Top = 1,
        Bottom
    }

    public enum UserInterfaceFeature
    {
        DeviceBinding,
        SecuritySMSAwareness,
        SecuritySMSMigration
    }

    public enum UserInterfaceFlow
    {
        OTP,
        ATMPin,
        Dashboard,
        SecuritySMSAwareness,
        SecuritySMSMigration
    }

    public enum ResponseStatus
    {
        Success,
        Failure
    }

    public enum MobileRecharge_Provider
    {
        // Airtel India
        AIIN
    }

    public enum MobileRecharge_AutoRenewalInactiveCode
    {
        CancelledDueToLowBalance = 1,
        DeactivatedByUser = 2,
        CancelledDueToPackUnavailability = 3,
        CancelledDueToExceededNumberOfLowBalanceChecks = 4
    }
    public enum MobileRecharge_TargetedDiscountOfferStatus
    {
        Initiated = 1,
        Started = 2,
        Claimed = 3,
        Expired = 4
    }

    #region C3Pay+
    public enum C3PayPlusMembershipType
    {
        None = 0,
        UsersWithNoSubscription,
        UsersWithBalanceEnquiryOnly,
        UsersWithBalanceEnquiryAndSecuritySms,
        UsersWithBalanceEnquiryAndSalaryAlert,
        UsersWithSecuritySmsOnly,
        UsersWithSalaryAlertsOnly
    }

    public enum C3PayPlusMembershipBenefitType
    {
        None = 0,
        LifeInsurance,
        FirstAtmWithdrawalFree,
        SecuritySms,
        BalanceEnquiry,
        LuckyDraw,
        FreeMoneyTransfer,
        Cashback,
        AccidentalInsurance
    }

    public enum C3PayPlusMembershipTransactionType
    {
        MonthlyBilling = 1,
        AtmWithdrawalFeeReversal,
        BalanceEnquiryFeeReversal,
        SmsFeeReversal
    }

    public enum C3PayPlusMembershipVideoType
    {
        Login = 1,
        Onboarding,
        Cancelation,
        LifeInsurance,
        LuckyDraw
    }

    public enum C3PayPlusMembershipPrizeType
    {
        GoldCoin = 1,
        CashPrizeAed10K
    }

    public enum C3PayPlusMembershipBannerBenefit
    {
        NoNomineeAtmAvailable = 1,
        PickNewDrawTicket,
        OneFreeAtm,
        WinnersAreOut,
        HasLifeInsurance,
        FreeMoneyTransfer,
        Cashback
    }

    public enum C3PayPlusMembershipHeadline
    {
        ExpiresSoon = 1,
        UpdateNominee,
        ClaimFreeAtm,
        Active,
        ChangeLuckyDrawTicket,
        SendMoneyFree,
        GetAed10
    }

    public enum C3PayPlusMembershipSavingType
    {
        FreeAtmWithdrawal = 1,
        SecuritySms,
        BalanceEnquiry,
        LuckyDrawGoldCoinWinner,
        LuckyDrawGrandPrizeWinner,
        FreeMoneyTransfer,
        Cashback
    }
    public enum C3PayPlusMembershipHeadlineUrgencyLevel
    {
        Normal = 1,     // Green
        Warning,        // Yellow
        Danger          // Red
    }

    public enum C3PayPlusMembershipBannerBenefitsStyle
    {
        Normal = 1,
        Gold,
        Yellow,
        Green
    }

    public enum C3PayPlusMembershipTitleStyle
    {
        Normal = 1,
        Yellow,
        Green
    }

    public enum C3PayPlusVoiceMessageType
    {
        [Description("WelcomeCall")]
        WelcomeCall = 1,
        [Description("PreRenewalCall")]
        PreRenewalCall,
        [Description("PreLuckyDrawcall")]
        PreLuckyDrawcall
    }

    public enum C3PayPlusFreeMoneyTransferClaimTrigger
    {
        UpdatePendingMoneyTransferJob = 1,
        SftpWorker,
        InlineWesternUnion,
        DailyWorker
    }

    public enum C3PayPlusBenefitType
    {
        LifeInsurance = 1,
        LuckyDraw,
        SaveMoney,
        MoneyTransfer,
        Cashback
    }

    public enum C3PayPlusMembershipTranslationGroupCode
    {
        RelationShipType,
        RelationShipTypeQuestion,
        CancellationDetails
    }

    public enum C3PayPlusMembershipTranslationTextCode
    {
        // Nominee RelationshipType translation code
        RelationShipTypeWife,
        RelationShipTypeHusband,
        RelationShipTypeChild,
        RelationShipTypeParent,
        RelationShipTypeSibling,
        RelationShipTypeFriend,
        RelationShipTypeFather,
        RelationShipTypeMother,
        RelationShipTypeSon,
        RelationShipTypeDaughter,
        RelationShipTypeBrother,
        RelationShipTypeSister,

        // Nominee RelationshipType Question translation code
        RelationShipTypeWifeQuestion,
        RelationShipTypeHusbandQuestion,
        RelationShipTypeChildQuestion,
        RelationShipTypeParentQuestion,
        RelationShipTypeSiblingQuestion,
        RelationShipTypeFriendQuestion,
        RelationShipTypeFatherQuestion,
        RelationShipTypeMotherQuestion,
        RelationShipTypeSonQuestion,
        RelationShipTypeDaughterQuestion,
        RelationShipTypeBrotherQuestion,
        RelationShipTypeSisterQuestion,

        //Cancellation details
        MoneyTransferBenefitTitle,
        MoneyTransferBenefitValuePrefix,
        CashbackBenefitTitle,
        CashbackBenefitValuePrefix,
        InsuranceBenefitTitle,
        InsuranceBenefitValueSuffix,
        InsuranceBenefitValueTextFamily,
        InsuranceBenefitValuePrefixTextYour,
        LuckyDrawBenefitTitle,
        LuckyDrawBenefitValuePrefix,
        SaveMoneyBenefitTitle,
        SaveMoneyBenefitValuePrefixAbove15,
        SaveMoneyBenefitValuePrefixBelow15,
        AED15Monthly,

        // Membership details.
        SavingAed15Monthly,
        SavingAed40Monthly,
        SavingAed25Monthly,
        SavingFreeMoneyTransfer,
        SavingFreeAtmWithdrawal,
        SavingSecuritySms,
        SavingBalanceEnquiry,
        SavingLuckyDraw,
        SavingTextFreeWithMembership,
        SavingFreeAtmWithdrawalAny,
        WinAndSaveMoney,

        BenefitsSendMoneyHomeFree,
        BenefitsSendMoneyHomeFreeSubtitle,
        BenefitsOneAndHalfPercentCashback,
        BenefitsOneAndHalfPercentCashbackSubtitle,
        BenefitsAddInsuranceNominee,
        BenefitsAddInsuranceNomineeSubtitle,
        BenefitsPickNewDrawTicket,
        BenefitsPickNewDrawTicketSubtitle,
        BenefitsOneFreeAtmAvailable,
        BenefitsOneFreeAtmAvailableSubtitle,
        BenefitsWinnerOutForTheWeek,
        BenefitsWinnerOutForTheWeekSubtitle,
        BenefitsSecuredWithLifeInsurance,
        BenefitsSecuredWithLifeInsuranceSubtitle,
        BenefitsTextAmount10KAed,
        BenefitsTextAmountTwoAndHalfAed,
        BenefitsTextAmountThreeAed,
        BenefitsTextAmountOneAed,
        BenefitsOneFreeMoneyTransferMonth,
        BenefitsTextAmountTwentyFiveAed,
        BenefitsAcidentalInsurance,
        BenefitsTextUpto,
        BenefitsTextAmount5KAed,
        AccidentalInsuranceClaim,

        HeadlinesExpiresSoon,
        HeadlinesUpdateNominee,
        HeadlinesStatusActive,
        HeadlinesSendMoneyFree,
        HeadlinesGetAed10,
        HeadlinesSelectLuckyDraw,

        InsurnacePolicyActive,
        InsurnacePolicyViewYourCertificate,
        InsurnacePolicySharedWithNominee,

        SavingTextOneFreeAtmAvailable,
        SavingTextRenewsOn,
        SavingTextAlreadySavedTwoAndHalfAed,
        SavingTextTotalFreeAtmUsed,
        SavingTextYouSaveAedThreeForMonth,
        SavingTextSmsForSalary,
        SavingTextSmsWhenUseCard,
        SavingTextYourAccountIsSafe,
        SavingTextBalanceEnquiry,
        SavingTextYouSaveAedOneForMonth,
        SavingTextViewYourBalanceAnytime,
        SavingTextFirstFreeTransferAvailableUseAndSave,
        SavingTextTotalFreeTransferUsed,
        SavingTextMinimumSpendTenAed,
        SavingTextTotalSavingAed,
        SavingTextUseCardCashback
    }

    public enum C3PayPlusMonitaryBenefits
    {
        AtmWWithdrawalFeeRefund,
        MoneyTransferRefund,
        Cashback
    }


    public enum C3PayPlusMembershipExperiments
    {
        TargetedDiscount = 1
    }

    public enum C3PayPlusRenewalRunStatus
    {
        Started = 1,
        FetchingSubscriptions,
        Renewing,
        FinalChecks,
        Finished,
        Interrupted
    }


    public enum C3PayPlusRenewalBillingStatus
    {
        BillingStarted = 1,
        BillingSubmitedButNotConfirmed,
        BillingConfirmed,
    }

    public enum C3PayPlusRenewalsDailyLogStatus
    {
        Started = 1,
        FetchingSubscriptions,
        Renewing,
        FinalChecks,
        Finished,
        Interrupted
    }


    public enum C3PayPlusRenewalsCancelationReason
    {
        UserNotFoundOrBlockedOrDeleted = 1,
        UserUnsubscribed,
        CantGetAgeInformation,
        UserIsTooOldOrTooYoung,
        CardIsNotActive,
        NoSalaryInTheLast3Months,
        CardIsBlocked,
        Other,
    }

    #endregion

    #region VPN
    public enum VpnMembershipProvider
    {
        Mena = 1
    }

    public enum VpnRenewalRunStatus
    {
        Started = 1,
        FetchingSubscriptions,
        Renewing,
        FinalChecks,
        Finished,
        Interrupted
    }

    public enum VpnRenewalBillingStatus
    {
        BillingStarted = 1,
        BillingSubmitedButNotConfirmed,
        BillingConfirmed,
    }
    #endregion
    public enum PaymentAuthRequestStatus
    {
        PENDING_USER_APPROVAL = 1,
        USER_APPROVED,
        USER_DECLINED,
        MERCHANT_CANCELLED,
        PAYMENT_APPROVAL_ERROR
    }

    public enum PaymentAuthDecision
    {
        CANCEL = 1,
        APPROVE
    }

    public enum RewardAction
    {
        [Description("MoneyTransfer SpinTheWheel")]
        MT_SP_RT
    }

    public enum UserSegment
    {
        [Description("Money Transfer Unclustered")]
        MT_UC,
        [Description("Money Transfer General")]
        MT_GE
    }

    #region MT Dynamic Screens
    public enum ScreenType
    {
        Form = 1,
        List = 2,
        SearchableList = 3,
        Video = 4
    }

    public enum ElementType
    {
        TextBox = 1,
        PhoneNumber = 2,
        InfoBar = 3,
        Button = 4,
        ListItem = 5,
        SearchTextBox = 6,
        LogoWithLabel = 7,
        List = 8
    }
    public enum NavigationType
    {
        Normal = 1,
        Video = 2
    }

    public enum HttpMethod
    {
        GET,
        POST,
        PUT,
        DELETE
    }

    #endregion

    #region User Alerts
    public enum AlertChannelType
    {
        Email = 1,
        SMS
    }
    #endregion

    public enum SMVActionType
    {
        Beneficiary,
        Transaction,
        Technical
    }
}

