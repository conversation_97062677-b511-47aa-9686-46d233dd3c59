﻿{
  "ConnectionStrings": {
    "C3PayConnection": "Data Source=(LocalDb)\\MSSQLLocalDB;Initial Catalog=C3Pay;Integrated Security=SSPI",
    "C3PayConnectionReadOnly": "Data Source=(LocalDb)\\MSSQLLocalDB;Initial Catalog=C3PayReadOnly;Integrated Security=SSPI",
    "IdentityConnection": "Data Source=(LocalDb)\\MSSQLLocalDB;Initial Catalog=C3PayIdentity;Integrated Security=SSPI",
    "AzureBlobStorage": "UseDevelopmentStorage=true",
    "AzureAppConfig": "",
    "RedisConnection": "",
    "ServiceBusConnection": ""
  },

  "General": {
    "EnableSwagger": ""
  },
  "PortalEDConnect": {
    "Authority": "",
    "ApiName": "",
    "ApiSecret": "",
    "EnableCaching": true,
    "CacheDurationInMinutes": 60
  },
  "EdenredIdentityManager": {
    "BaseAddress": "",
    "Tenant": "AE",
    "Authority": "",
    "ResourceId": "",
    "ClientId": "",
    "ClientSecret": ""
  },
  "PasswordValidationRules": {
    "RequiredLength": 6,
    "RequiredUniqueChars": 0,
    "RequireNonAlphanumeric": false,
    "RequireLowercase": false,
    "RequireUppercase": false,
    "RequireDigit": true,
    "RequireLetter": true
  },
  "SendGrid": {
    "SenderEmail": "",
    "APIKey": "",
    "Templates": [
      {
        "TemplateId": "",
        "Name": "CardHolderRegistrationRejected"
      },
      {
        "TemplateId": "",
        "Name": "PortalUserCreated"
      },
      {
        "Name": "PortalAdminPasswordReset",
        "TemplateId": ""
      }
    ]
  },
  "EtisalatSMS": {
    "BaseAddress": "",
    "SenderName": "C3Pay",
    "ContentType": "application/json",
    "Username": "",
    "Password": "",
    "Timeout": 0,
    "RetryCount": 0
  },

  "InfobipSMS": {
    "BaseAddress": "",
    "SenderName": "C3Pay",
    "ContentType": "application/json",
    "Username": "",
    "Password": "",
    "Timeout": 0,
    "RetryCount": 0
  },

  "CleverTapService": {
    "BaseAddress": "",
    "PassCode": "",
    "ProjectId": "",
    "EventsNames": {
      "MoneyTransferStarted": "RMT_status_started",
      "MoneyTransferPending": "RMT_status_pending",
      "MoneyTransferSuccessful": "v2_transfer_make_transfer_success",
      "MoneyTransferFailed": "v2_transfer_make_transfer_failed",
      "EmiratesIdVerified": "EmiratesIDVerified",
      "EmiratesIDRejected": "EmiratesIDRejected",
      "MoneyTransferProfileCreated": "RmtProfileCreated",
      "SalaryProccessed": "GotPaid",
      "MobileRechargeSuccessRatesExp": "Ratexp_recharge_successful",
      "MobileRechargeFailedRatesExp": "Rateexp_recharge_failed"
    }
  },
  "AzureOCR": {
    "Endpoint": "",
    "Key": "",
    "WaitTimeInMilliSeconds": 1000

  },
  "AzureFace": {
    "Endpoint": "",
    "Key": ""
  },

  "AzureIdentificationService": {
    "TwoDigitYearMaxLimit": 12,
    "IdealFaceMatchPercentage": 60,
    "IdealNameMatchPercentage": 75,
    "MinimumAllowedEmiratesIdLines": 8,
    "MinimumAllowedPassportLines": 8,
    "TempStorageContainerName": "temp-identifications",
    "EmirateIdStorageContainerName": "emirates-id",
    "PassportStorageContainerName": "passport",
    "SelfieStorageContainerName": "selfies",
    "UseAzureOCR": false,
    "UseAzureFace": false
  },
  "ReferralProgramService": {
    "MoneyTransferCountThreshold": null,
    "MoneyTransferAmountThreshold": null,
    "MoneyTransferRewardAmount": null,
    "MoneyTransferReferralProgramStartDate": null,
    "QueueConnectionString": "",
    "QueueName": ""
  },

  "EmiratesIdService": {
    "BaseAddress": "",
    "FileExchangeBaseAddress": "",
    "Id": "",
    "UserId": "",
    "AcceptedSelfieScore": 0.5,
    "AcceptedEIDScore": 0.5,
    "IdealFaceMatchPercentage": 60,
    "IdealNameMatchPercentage": 75,
    "TTL": "2 hrs",
    "MimeType": "image/png",
    "ContentType": "application/json"
  },

  "KYCService": {
    "BaseAddress": "",
    "MethodName": "ApproveKYC",
    "Username": "",
    "Password": "",
    "SponsorCode": "",
    "UniqueRef": "",
    "SharedSecret": "",
    "QueueConnectionString": "",
    "QueueName": ""
  },

  "ESMOService": {
    "EndpointAddress": "",
    "Username": "",
    "Password": "",
    "SponsorCode": "",
    "SharedSecret": "",
    "QueueConnectionString": "",
    "QueueName": ""
  },

  "PPSService": {
    "WebAuthBaseURL": "",
    "WebAuthClientId": "",
    "WebAuthClientSecretkey": "",
    "WebAuthContentType": "",
    "EndpointAddress": "",
    "Username": "",
    "Password": "",
    "SponsorCode": "",
    "CustomerCode": "",
    "SharedSecret": ""
  },
  "RAKService": {
    "BeneficiaryQueueConnectionString": "",
    "BeneficiaryQueueName": "",
    "MoneyTransferQueueConnectionString": "",
    "MoneyTransferQueueName": "",
    "MoneyTransferBeneficiaryCount": 0,
    "BaseURL": "",
    "URLPath": "",
    "ClientId": "",
    "clientSecretkey": "",
    "ContentType": "",
    "TokenContentType": "",
    "TokenScope": "",
    "TokenGrantType": "",
    "SSLCertificateName": "",
    "SSLCertificatePassword": "",
    "PaylaodPrivateKey": "",
    "PaylaodPublicKey": "",
    "BanksMaxRecords": "",
    "MaxTransactionTriesCount": 0,
    "MessageProcessInDelay": 0,
    "MoneyTransferBeneficiaryDelayInMins": 0,
    "LoyaltyImplementDate": "",
    "LoyaltyLimitCount": 0,
    "LoyaltyLimitAmount": 0
  },
  "FirebaseCloudMessaging": {
    "BaseAddress": "",
    "Key": "",
    "SenderId": ""
  },
  "PortalUser": {
    "AllowReuseExistentIdentity": "false"
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "Using": [
      "Serilog.Sinks.OpenTelemetry"
    ],
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "OpenTelemetry",
        "Args": {
          "restrictedToMinimumLevel": "Information"
        }
      }
    ]
  },
  "ApplicationInsights": {
    "ConnectionString": ""
  },
  "OpenTelemetry": {
    "ServiceName": "C3Pay.Portal.API",
    "ServiceVersion": "1.0.0"
  },
  "Jaeger": {
    "AgentHost": "localhost",
    "AgentPort": 6831,
    "Endpoint": "http://localhost:14268/api/traces"
  },
  "KycUnblockByPassport": {
    "QueueConnectionString": "",
    "QueueName": ""
  }
}
