﻿using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models
{
    public class MoneyTransferBeneficiary : BaseModel
    {

        #region Constants
        public const int MAX_FIRST_NAME_LENGTH = 26;
        public const int MAX_MIDDLE_NAME_LENGTH = 26;
        public const int MAX_LAST_NAME_LENGTH = 26;
        public const string WALLET_INDIA_ADDRESS_LINE1 = "CM 1102 LAS LAJAS";
        public const string WALLET_INDIA_CITY = "Pune";
        public const string WALLET_INDIA_STATE = "MAHARASHTRA";
        public const string WALLET_INDIA_POSTAL_CODE = "455551";
        public readonly string[] WU_CORRIDORS = new[] { "MA", "NG", "UG", "KE", "ET", "GH", "EG", "ID" };
        private static readonly string[] retryMessages = new string[]
        {
            "404001-Validate REST",
            "500-Sorry, we are unable to process your transaction. Please try again.",
            "Error reading JObject from JsonReader",
            "Rak Access token error",
            "The SSL connection could not be established",
            "Internal Server Error",
            "Failed to initialize RAK Bank service",
            "A connection attempt failed"
        };
        #endregion

        #region Properties
        public Guid Id { get; set; }

        public Guid UserId { get; set; }

        public string CountryCode { get; set; }

        public int? MoneyTransferReasonId { get; set; }

        public int ExternalUserId { get; set; }

        public string FirstName { get; set; }

        public string MiddleName { get; set; }

        public string LastName { get; set; }

        public MoneyTransferType TransferType { get; set; }

        public string BankName { get; set; }

        public string BankBranchName { get; set; }

        public string Address1 { get; set; }

        public string Address2 { get; set; }

        public string IdentifierCode1 { get; set; }

        public string IdentifierCode2 { get; set; }

        public string AccountNumber { get; set; }

        public string DocumentType { get; set; }

        public string DocumentNumber { get; set; }

        public Status Status { get; set; }

        public string StatusDescription { get; set; }

        public string Remarks { get; set; }

        public bool Posted { get; set; }

        public bool RequiresApproval { get; set; }

        public DateTime? PostDate { get; set; }

        public DateTime? ApprovalDate { get; set; }

        public string LastTransferReferenceNumber { get; set; }

        public string PhoneNumber { get; set; }

        public string NationalityCode { get; set; }

        public bool? IsBranchSelected { get; set; }

        public Guid? LinkedUserId { get; set; }

        public int RetryCount { get; set; }
        public bool IsCrossTransfer { get; set; }

        public string WUMsgId { get; set; }

        #endregion

        #region Not Mapped
        public int BankId { get; set; }
        public int? BranchId { get; set; }

        public string SelectedIfscCode { get; set; }
        public int SelectedBranchId { get; set; }
        public string FullName
        {
            get
            {
                var fullName = this.FirstName;

                if (!string.IsNullOrEmpty(this.MiddleName))
                {
                    fullName = string.Join(" ", fullName, this.MiddleName);
                }

                fullName = string.Join(" ", fullName, this.LastName);

                return fullName;
            }
        }

        public string WalletProvider { get; set; }
        #endregion

        #region Navigation Properties
        public User User { get; set; }
        public Country Country { get; set; }
        public MoneyTransferReason MoneyTransferReason { get; set; }
        public MoneyTransferExternalBeneficiary ExternalBeneficiary { get; set; }
        public List<MoneyTransferTransaction> MoneyTransferTransactions { get; set; }
        public List<BeneficiaryAdditionalField> BeneficiaryAdditionalFields { get; set; }
        public User LinkedUser { get; set; }
        public MoneyTransferSuspiciousInformation MoneyTransferSuspiciousInformation { get; set; }
        #endregion

        public static MoneyTransferBeneficiary Create(User user)
        {
            return new MoneyTransferBeneficiary()
            {
                Status = Status.PENDING,
                UserId = user.Id,
                DocumentNumber = user.CardHolder.EmiratesId,
                DocumentType = "Emirates_ID",
                BeneficiaryAdditionalFields = new List<BeneficiaryAdditionalField>(),
                MoneyTransferReasonId = MoneyTransferReason.GetDefaultReasonId(),
            };
        }


        public async Task<Result<Guid>> ValidateAndPopulate(User user, MoneyTransferMethod moneyTransferMethod, int moneyTransferReasonId,
                                                Dictionary<string, string> submittedFields, IUnitOfWork unitOfWork, IIdentityService identityService,
                                                RAKSettings rakSettings, CancellationToken cancellationToken)
        {
            // Beneficiary Count Check
            var existingBeneficiaryCount = await unitOfWork.MoneyTransferBeneficiaries.CountAsync(user.Id);
            if (existingBeneficiaryCount >= rakSettings.MoneyTransferBeneficiaryCount)
                return Result.Failure<Guid>(Errors.MoneyTransfer.ExceedBeneficiaryCountLimit);

            // Transfer Reason Check
            var reasonId = moneyTransferReasonId == 0 ? MoneyTransferReason.GetDefaultReasonId() : moneyTransferReasonId;
            if (reasonId != MoneyTransferReason.GetDefaultReasonId())
            {
                var moneyTransferReason = await unitOfWork.MoneyTransferReasons.GetByIdAsync(reasonId);
                if (moneyTransferReason.IsFailure)
                    return Result.Failure<Guid>(moneyTransferReason.Error);
                reasonId = moneyTransferReason.Value.Id;
            }

            // Submitted Field Check 
            // Check for FieldCode.FullName in submittedFields dictionary
            SetCountryCode(moneyTransferMethod.RemittanceDestination.Code2);
            List<BeneficiaryAdditionalField> _beneficiaryAdditionalFields = new();

            var fullNameDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.FullName.ToString()).FirstOrDefault();

            // If FullName is found, process it
            if (fullNameDictionaryEntry.Key is not null)
            {
                if (fullNameDictionaryEntry.Value.Length > 50)
                    return Result.Failure<Guid>(Errors.MoneyTransfer.FieldFullNameNotFound);
                var (firstPart, lastPart) = ProcessFullName(fullNameDictionaryEntry.Value);
                SetFirstName(firstPart);
                SetLastName(lastPart);
            }
            else
            {
                // FirstName  
                var firstNameDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.FirstName.ToString()).FirstOrDefault();
                if (firstNameDictionaryEntry.Key is null || string.IsNullOrWhiteSpace(firstNameDictionaryEntry.Value))
                    return Result.Failure<Guid>(Errors.MoneyTransfer.FirstNameNotExists);
                if (firstNameDictionaryEntry.Value.Length > MAX_FIRST_NAME_LENGTH)
                    return Result.Failure<Guid>(Errors.MoneyTransfer.ExceedFirstNameLength);
                SetFirstName(firstNameDictionaryEntry.Value);

                // MiddleName
                var middleNameDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.MiddleName.ToString()).FirstOrDefault();
                if (middleNameDictionaryEntry.Key is not null)
                {
                    if (middleNameDictionaryEntry.Value.Length > MAX_MIDDLE_NAME_LENGTH)
                        return Result.Failure<Guid>(Errors.MoneyTransfer.ExceedMiddleNameLength);
                    SetMiddleName(middleNameDictionaryEntry.Value);
                }

                // LastName
                var lastNameDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.LastName.ToString()).FirstOrDefault();
                if (lastNameDictionaryEntry.Key is null || string.IsNullOrWhiteSpace(lastNameDictionaryEntry.Value))
                    return Result.Failure<Guid>(Errors.MoneyTransfer.LastNameNotExists);
                if (lastNameDictionaryEntry.Value.Length > MAX_LAST_NAME_LENGTH)
                    return Result.Failure<Guid>(Errors.MoneyTransfer.ExceedLastNameLength);
                SetLastName(lastNameDictionaryEntry.Value);
            }

            //***** Check with Transfer Type ******/
            var thisTransferType = moneyTransferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer ? MoneyTransferType.OutsideUAE
                    : moneyTransferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup ? MoneyTransferType.RAKMoneyCashPayout
                    : MoneyTransferType.Wallet;

            // Bank Transfer
            if (thisTransferType == MoneyTransferType.OutsideUAE)
            {
                var bankTransferResult = await ProcessForBankTransfer(submittedFields, user, unitOfWork);
                if (bankTransferResult.IsWarning)
                    return Result.Warning(Guid.Empty, bankTransferResult.Error);
                else if (bankTransferResult.IsFailure)
                    return Result.Failure<Guid>(bankTransferResult.Error);
            }
            // Cash Pickup
            else if (thisTransferType == MoneyTransferType.RAKMoneyCashPayout)
            {
                var cashPickupResult = await ProcessForCashPickup(user, unitOfWork);
                if (cashPickupResult.IsWarning)
                    return Result.Warning(Guid.Empty, cashPickupResult.Error);
                else if (cashPickupResult.IsFailure)
                    return Result.Failure<Guid>(cashPickupResult.Error);
            }
            // Wallet
            else if (thisTransferType == MoneyTransferType.Wallet)
            {
                var walletResult = await ProcessForWallet(moneyTransferMethod, submittedFields, user, unitOfWork);
                if (walletResult.IsWarning)
                    return Result.Warning(Guid.Empty, walletResult.Error);
                else if (walletResult.IsFailure)
                    return Result.Failure<Guid>(walletResult.Error);
            }

            // Check User is Blacklisted
            var isBlacklisted = await unitOfWork.BlackListedEntities.IsBlacklistedForMoneyTransferAsync(CountryCode, AccountNumber, cancellationToken);
            if (isBlacklisted.IsFailure)
                return Result.Failure<Guid>(isBlacklisted.Error);
            if (isBlacklisted.Value)
            {
                var blockUserResult = await unitOfWork.Users.BlockUserAsync(user.PhoneNumber, UserBlockType.MoneyTransferBeneficiaryBlackListed, cancellationToken);
                if (blockUserResult.IsFailure)
                    return Result.Failure<Guid>(blockUserResult.Error);
                await identityService.LockUserAccountAsync(user.PhoneNumber);
                return Result.Failure<Guid>(Errors.MoneyTransfer.UserBlocked);
            }

            return Result.Success(Guid.Empty);
        }

        private (string firstName, string lastName) ProcessFullName(string fullName)
        {
            // Remove titles like Mr., Mrs., etc from the start of the full name if followed by a space
            var titlesToRemove = new[] { "Mr.", "Mr", "Mrs.", "Mrs", "Ms.", "Ms", "Miss.", "Miss", "Dr.", "Dr" };
            var cleanFullName = fullName.TrimStart();
            foreach (var title in titlesToRemove)
            {
                if (cleanFullName.StartsWith(title, StringComparison.OrdinalIgnoreCase) &&
                    cleanFullName.Length > title.Length &&
                    char.IsWhiteSpace(cleanFullName[title.Length]))
                {
                    cleanFullName = cleanFullName.Substring(title.Length).TrimStart();
                    break;
                }
            }

            // Split into name parts
            var nameParts = cleanFullName.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

            // Handle first name - must be 3-25 chars
            var firstName = nameParts[0];
            if (firstName.Length < 3 && nameParts.Length > 1)
            {
                // If first name too short, include next part
                firstName = $"{firstName} {nameParts[1]}";
                // Remove the used part from name parts
                nameParts = nameParts.Skip(2).ToArray();
            }
            else
            {
                nameParts = nameParts.Skip(1).ToArray();
            }

            // Combine remaining parts for last name
            var lastName = string.Join(" ", nameParts);

            // Validate and trim lengths
            firstName = firstName.Length > 25 ? firstName.Substring(0, 25) : firstName;
            lastName = lastName.Length > 50 ? lastName.Substring(0, 50) : lastName;

            return (firstName, lastName);
        }

        private bool IsValidPakistanIban(string accountNumber)
        {
            // Pakistan IBAN should start with PK.
            if (accountNumber.StartsWith("PK", StringComparison.OrdinalIgnoreCase) == false) return false;

            // Pakistan IBAN should have a length of 24.
            if (accountNumber?.Length != 24) return false;

            for (int i = 0; i < accountNumber.Length; i++)
            {
                var @char = accountNumber[i];
                if (i == 0 && @char != 'P') return false;
                if (i == 1 && @char != 'K') return false;

                if (i == 2 && char.IsDigit(@char) == false) return false;
                if (i == 3 && char.IsDigit(@char) == false) return false;

                if (i > 3 && i <= 7 && char.IsLetter(@char) == false) return false;
            }

            return true;
        }

        #region Bank Transfer
        public async Task<Result<Guid>> ProcessForBankTransfer(Dictionary<string, string> submittedFields, User user, IUnitOfWork unitOfWork)
        {
            var accountNumberDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.AccountNumber.ToString() || x.Key == FieldCode.IBAN.ToString()
                                                                                      || x.Key == FieldCode.AccountNumberOrIBAN.ToString()).FirstOrDefault();
            if (accountNumberDictionaryEntry.Key is null || string.IsNullOrWhiteSpace(accountNumberDictionaryEntry.Value))
                return Result.Failure<Guid>(Errors.MoneyTransfer.AccountNumberNotExists);
            SetAccountNumber(accountNumberDictionaryEntry.Value);

            // For Pakistan, we need to know if the provided account number is an IBAN or not.
            if (CountryCode == "PK")
            {
                if (IsValidPakistanIban(accountNumberDictionaryEntry.Value.Trim()))
                    RemoveAccountNumberAndSetIban(accountNumberDictionaryEntry.Value);
            }

            // Duplicate Beneficiary Check
            var duplicateBeneficiary = await unitOfWork.MoneyTransferBeneficiaries.GetByBankAccountNumberAsync(user.Id, AccountNumber);
            if (duplicateBeneficiary is not null)
                return Result.Warning(duplicateBeneficiary.Id, Errors.MoneyTransfer.BeneficiaryAlreadyExists);

            // Bank Related Logic (Bank Name, Branch Name, Identifier Code 1 & 2, Bank Code)
            MoneyTransferBank _selectedBank = null;
            MoneyTransferBranch _selectedBranch = null;
            var bankIdDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.BankId.ToString()).FirstOrDefault();
            if (bankIdDictionaryEntry.Key is null || string.IsNullOrWhiteSpace(bankIdDictionaryEntry.Value))
                return Result.Failure<Guid>(Errors.MoneyTransfer.BankCodeNotExists);

            //***** Bank *****//
            if (int.TryParse(bankIdDictionaryEntry.Value.Trim(), out var bankId))
            {
                _selectedBank = await unitOfWork.MoneyTransferBanks.GetByIdAsync(bankId);
                if (_selectedBank is null)
                    return Result.Failure<Guid>(Errors.MoneyTransfer.BankNotExists);

                // In case the bank does not require a branch selection from the FE, find the last successful transfer made to the selected bank and use that branch.
                if (!_selectedBank.RequiresBranch)
                {
                    // No need for WU corridors
                    if (!WU_CORRIDORS.Contains(CountryCode))
                    {
                        var lastSuccessfulTransfer = await unitOfWork.MoneyTransferTransactions.GetBankTransferLastSuccessfulTransferAsync(user.Id,
                                                                     _selectedBank.Name, CountryCode);
                        if (lastSuccessfulTransfer is not null && lastSuccessfulTransfer.MoneyTransferBeneficiary is not null)
                        {
                            SetIdentifierCode1(lastSuccessfulTransfer.MoneyTransferBeneficiary.IdentifierCode1);
                            SetIdentifierCode2(lastSuccessfulTransfer.MoneyTransferBeneficiary.IdentifierCode2);
                            SetBankName(lastSuccessfulTransfer.MoneyTransferBeneficiary.BankName);
                            SetBankBranchName(lastSuccessfulTransfer.MoneyTransferBeneficiary.BankBranchName);
                        }
                    }
                }
            }

            //****** Bank Branch ******//
            // By IFSC Code (If exists)
            var ifscCodeDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.IFSC.ToString()).FirstOrDefault();
            if (ifscCodeDictionaryEntry.Key is not null && !string.IsNullOrWhiteSpace(ifscCodeDictionaryEntry.Value))
            {
                _selectedBranch = await unitOfWork.MoneyTransferBranches.GetByIfscCodeAsync(ifscCodeDictionaryEntry.Value.Trim());
                if (_selectedBranch is not null)
                {
                    SetBankBranchName(_selectedBranch.Name);
                    SetIdentifierCode1(_selectedBranch.PrimaryIdentifierCode);
                    SetIdentifierCode2(_selectedBranch.SecondaryIdentifierCode);
                    SetBankName(_selectedBranch.Bank.Name);
                    SetSelectedIfscCode(ifscCodeDictionaryEntry.Value.Trim());
                }
            }
            if (_selectedBranch is null)
            {
                // Branch ID
                var branchIdDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.BranchId.ToString()).FirstOrDefault();
                if (branchIdDictionaryEntry.Key is not null && !string.IsNullOrWhiteSpace(branchIdDictionaryEntry.Value))
                {
                    if (int.TryParse(branchIdDictionaryEntry.Value.Trim(), out var branchId))
                    {
                        _selectedBranch = await unitOfWork.MoneyTransferBranches.GetByIdAsync(branchId);
                        if (_selectedBranch is not null)
                        {
                            SetBankBranchName(_selectedBranch.Name);
                            SetIdentifierCode1(_selectedBranch.PrimaryIdentifierCode);
                            SetIdentifierCode2(_selectedBranch.SecondaryIdentifierCode);
                            SetBankName(_selectedBranch.Bank.Name);
                            SetSelectedBranchId(branchId);
                        }
                    }
                }
            }

            if (_selectedBranch is null)
            {
                // For Philippines, Get the first branch by bank ID.
                if (CountryCode == "PH")
                {
                    _selectedBranch = await unitOfWork.MoneyTransferBranches.GetFirstBranchByBankIdAsync(BankId);
                    if (_selectedBranch is not null)
                    {
                        SetBankBranchName(_selectedBranch.Name);
                        SetIdentifierCode1(_selectedBranch.PrimaryIdentifierCode);
                        SetIdentifierCode2(_selectedBranch.SecondaryIdentifierCode);
                        SetBankName(_selectedBranch.Bank.Name);
                    }
                }
            }

            if (string.IsNullOrWhiteSpace(IdentifierCode1))
            {
                if (!_selectedBank.RequiresBranch && !string.IsNullOrWhiteSpace(_selectedBranch.PrimaryIdentifierCode))
                {
                    SetIdentifierCode1(_selectedBranch.PrimaryIdentifierCode);
                    SetBankName(_selectedBank.Name);
                }
                else
                    return Result.Failure<Guid>(Errors.MoneyTransfer.BankNotExists);
            }

            // For Ghana, Need AddressLine1 & City
            if (CountryCode == "GH")
            {
                // AddressLine1
                var addressLine1DictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.AddressLine1.ToString()).FirstOrDefault();
                if (addressLine1DictionaryEntry.Key is null || string.IsNullOrWhiteSpace(addressLine1DictionaryEntry.Value))
                    return Result.Failure<Guid>(Errors.MoneyTransfer.AddressLine1NotFound);
                SetAddress1(addressLine1DictionaryEntry.Value);

                // City
                var cityDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.City.ToString()).FirstOrDefault();
                if (cityDictionaryEntry.Key is null || string.IsNullOrWhiteSpace(cityDictionaryEntry.Value))
                    return Result.Failure<Guid>(Errors.MoneyTransfer.CityNotFound);
                SetCity(cityDictionaryEntry.Value);
            }

            // For Ethiopia, Need Postal Code
            if (CountryCode == "ET")
            {
                // PostalCode
                var postalCodeDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.PostalCode.ToString()).FirstOrDefault();
                if (postalCodeDictionaryEntry.Key is null || string.IsNullOrWhiteSpace(postalCodeDictionaryEntry.Value))
                    return Result.Failure<Guid>(Errors.MoneyTransfer.PostalCodeNotFound);
                SetPostalCode(postalCodeDictionaryEntry.Value);
            }

            return Result.Success(Guid.Empty);
        }

        public async Task<Result<Guid>> ProcessForCashPickup(User user, IUnitOfWork unitOfWork)
        {
            // Duplicate Beneficiary Check
            var duplicateBeneficiary = await unitOfWork.MoneyTransferBeneficiaries.GetForCashPickupByNamesAsync(user.Id, FirstName, LastName);
            if (duplicateBeneficiary is not null)
                return Result.Warning(duplicateBeneficiary.Id, Errors.MoneyTransfer.BeneficiaryAlreadyExists);
            IsBranchSelected = false;

            return Result.Success(Guid.Empty);
        }
        public async Task<Result<Guid>> ProcessForWallet(MoneyTransferMethod moneyTransferMethod,
            Dictionary<string, string> submittedFields, User user, IUnitOfWork unitOfWork)
        {
            // PhoneNumber Fields
            var phoneNumberDictionaryEntry = submittedFields.Where(x => x.Key == FieldCode.PhoneNumber.ToString()).FirstOrDefault();
            if (phoneNumberDictionaryEntry.Key is null || string.IsNullOrWhiteSpace(phoneNumberDictionaryEntry.Value))
                return Result.Failure<Guid>(Errors.MoneyTransfer.PhoneNumberIsRequired);

            SetPhoneNumber(phoneNumberDictionaryEntry.Value);

            // Duplicate Beneficiary Check
            var duplicateBeneficiary = await unitOfWork.MoneyTransferBeneficiaries.GetForWalletByPhoneNumberAsync(user.Id, PhoneNumber);
            if (duplicateBeneficiary is not null)
                return Result.Warning(duplicateBeneficiary.Id, Errors.MoneyTransfer.BeneficiaryAlreadyExists);

            // Service Provider
            SetServiceProvider(moneyTransferMethod.MoneyTransferProvider.ProviderCode);
            // Country ISD Code
            SetCountryISDCode(moneyTransferMethod.RemittanceDestination.IsdCode);
            // If Country is India, Set Address Line 1, City, State & Postal Code
            if (CountryCode == "IN")
            {
                SetAddress1(WALLET_INDIA_ADDRESS_LINE1);
                SetCity(WALLET_INDIA_CITY);
                SetState(WALLET_INDIA_STATE);
                SetPostalCode(WALLET_INDIA_POSTAL_CODE);
            }

            return Result.Success(Guid.Empty);
        }
        #endregion

        #region Fields Setters
        private void SetFirstName(string firstName)
        {
            FirstName = firstName;
            // Remove if any and add new one
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "BENE_FIRST_NAME");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "BENE_FIRST_NAME",
                FieldValue = firstName.Trim()
            });
        }

        private void SetMiddleName(string middleName)
        {
            MiddleName = middleName;
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "BENE_MIDDLE_NAME");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "BENE_MIDDLE_NAME",
                FieldValue = middleName.Trim()
            });
        }
        private void SetLastName(string lastName)
        {
            LastName = lastName;
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "BENE_LAST_NAME");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "BENE_LAST_NAME",
                FieldValue = lastName.Trim()
            });
        }

        // Account Number Field
        private void SetAccountNumber(string accountNumber)
        {
            AccountNumber = Regex.Replace(accountNumber, @"\s+", "");
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "ACCOUNT_NUMBER");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "ACCOUNT_NUMBER",
                FieldValue = accountNumber.Trim()
            });
        }

        private void RemoveAccountNumberAndSetIban(string iban)
        {
            IdentifierCode1 = iban;
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "ACCOUNT_NUMBER");
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "IBAN");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "IBAN",
                FieldValue = iban.Trim()
            });
        }

        // Identifier Code 1
        private void SetIdentifierCode1(string identifierCode1) => IdentifierCode1 = identifierCode1;

        // Identifier Code 2
        private void SetIdentifierCode2(string identifierCode2) => IdentifierCode2 = identifierCode2;

        // Bank Name
        private void SetBankName(string bankName)
        {
            BankName = bankName;
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "BANK_NAME");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "BANK_NAME",
                FieldValue = bankName.Trim()
            });
        }

        // Bank Branch Name
        private void SetBankBranchName(string bankBranchName)
        {
            BankBranchName = bankBranchName;
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "BRANCH_NAME");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "BRANCH_NAME",
                FieldValue = bankBranchName.Trim()
            });
        }

        //SelectedIFSCCode
        private void SetSelectedIfscCode(string ifscCode) => SelectedIfscCode = ifscCode;

        // Selected Branch ID
        private void SetSelectedBranchId(int branchId) => SelectedBranchId = branchId;
        private void SetCountryCode(string countryCode) => CountryCode = countryCode;

        // Address Line 1
        private void SetAddress1(string addressLine1)
        {
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "ADDRESS_LINE1");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "ADDRESS_LINE1",
                FieldValue = addressLine1.Trim()
            });
        }

        // City
        private void SetCity(string city)
        {
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "CITY");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "CITY",
                FieldValue = city.Trim()
            });
        }
        // Postal Code
        private void SetPostalCode(string postalCode)
        {
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "POSTAL_CODE");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "POSTAL_CODE",
                FieldValue = postalCode.Trim()
            });
        }

        // Phone Number
        private void SetPhoneNumber(string phoneNumber)
        {
            if (phoneNumber.StartsWith("0", StringComparison.OrdinalIgnoreCase))
                phoneNumber = phoneNumber.Substring(1);
            // If Country is Indonesia then add Zero in front if it's not already there.
            if (CountryCode == "ID")
            {
                if (!phoneNumber.StartsWith("0", StringComparison.OrdinalIgnoreCase))
                    phoneNumber = "0" + phoneNumber;
            }

            PhoneNumber = phoneNumber.Trim();
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "MOBILE_NO");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "MOBILE_NO",
                FieldValue = phoneNumber.Trim()
            });
        }

        // Service Provider
        private void SetServiceProvider(string serviceProvider)
        {
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "SERVICE_PROVIDER");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "SERVICE_PROVIDER",
                FieldValue = serviceProvider.Trim()
            });
        }

        // Country ISD Code
        private void SetCountryISDCode(string countryISDCode)
        {
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "COUNTRY_CODE");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "COUNTRY_CODE",
                FieldValue = countryISDCode.Trim()
            });
        }

        // State
        private void SetState(string state)
        {
            BeneficiaryAdditionalFields.RemoveAll(x => x.FieldCode == "STATE");
            BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "STATE",
                FieldValue = state.Trim()
            });
        }
        #endregion

        public void FailedWithException(string msgId, Exception exception, MoneyTransferServiceSettings moneyTransferServiceSettings, bool isWesternUnion = false)
        {
            if (isWesternUnion)
            {
                WUMsgId = string.IsNullOrWhiteSpace(msgId) ? WUMsgId : msgId;
                if (RetryCount < moneyTransferServiceSettings.MaxBeneficiaryRetryLimit && CheckValidRetryRemark(Remarks))
                    Status = Status.PENDING;
                else
                    Status = Status.FAILED;
            }
            else
                Status = Status.FAILED;
            Remarks = string.Join(" : ", "Exception", exception.Message);
            UpdatedDate = DateTime.Now;
        }

        public void Success(string msgId, string externalBeneficiaryId, string externalCreationMessageId)
        {
            Status = Status.APPROVED;
            WUMsgId = string.IsNullOrWhiteSpace(msgId) ? WUMsgId : msgId;
            UpdatedDate = DateTime.Now;
            // External Beneficiary
            // If exists already
            if (ExternalBeneficiary is not null)
            {
                ExternalBeneficiary.ExternalId = externalBeneficiaryId;
                ExternalBeneficiary.UpdatedDate = DateTime.Now;
                if (!string.IsNullOrEmpty(externalCreationMessageId))
                {
                    ExternalBeneficiary.CreationMessageId = new Guid(externalCreationMessageId);
                }
            }
            else
            {
                ExternalBeneficiary = new MoneyTransferExternalBeneficiary()
                {
                    ExternalId = externalBeneficiaryId,
                    CreationMessageId = new Guid(externalCreationMessageId)
                };
            }
        }

        public void Failed(string msgId, string message, MoneyTransferServiceSettings moneyTransferServiceSettings, bool isWesternUnion = false)
        {
            if (isWesternUnion)
            {
                WUMsgId = string.IsNullOrWhiteSpace(msgId) ? WUMsgId : msgId;
                if (RetryCount < moneyTransferServiceSettings.MaxBeneficiaryRetryLimit && CheckValidRetryRemark(Remarks))
                    Status = Status.PENDING;
                else
                    Status = Status.FAILED;
            }
            else
                Status = Status.FAILED;
            WUMsgId = string.IsNullOrWhiteSpace(msgId) ? WUMsgId : msgId;
            Remarks = message;
            UpdatedDate = DateTime.Now;
        }

        public void Post()
        {
            Posted = true;
            PostDate = DateTime.Now;
        }

        private static bool CheckValidRetryRemark(string remarks)
        {
            return string.IsNullOrEmpty(remarks) || retryMessages.Any(remarks.Contains);
        }

        public bool ShouldRetry(int retryLimit, string remarks)
        {
            return RetryCount < retryLimit && CheckValidRetryRemark(Remarks);
        }

        public string GetFullName()
        {
            var names = new List<string> { this.FirstName };
            if (!string.IsNullOrEmpty(this.MiddleName)) names.Add(this.MiddleName);
            names.Add(this.LastName);
            return string.Join(" ", names);
        }
    }
}
