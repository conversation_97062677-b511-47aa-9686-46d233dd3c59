﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.Messages.MoneyTransfer;
using System;
using System.Collections.Generic;

namespace C3Pay.API.Resources.MoneyTransfer
{
    /// <summary>
    /// 
    /// </summary>
    public class MoneyTransferReceiptDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string BeneficiaryFullName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double ConversionRate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public AmountDto SendAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public AmountDto ReceiveAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public AmountDto Fee { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public BankDetailsDto BankDetails { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public MoneyTransferBeneficiaryBaseDto BeneficiaryDetails { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string MoneyTransferPurpose { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string TransferMethod { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime DateSent { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ReferralCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string CashPickUpProvider { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<MoneyTransferStatusStepDto> MoneyTransferStatusSteps { get; set; }

        public string ReceiptUri { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public CashPickupTransferDetailsDto CashPickupTransferDetailsDto { get; set; }
        public string StatusMessage { get; set; }
    }
}
