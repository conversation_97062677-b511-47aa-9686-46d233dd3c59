﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Portal;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Beneficiaries;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using static Edenred.Common.Core.Enums;

namespace C3Pay.Core.Services
{
    public interface IMoneyTransferBeneficiaryService
    {
        Task<ServiceResponse<bool>> GetEligibilityOfBeneficiaryDetails(Guid userId, string countryCode, string firstName, string lastName, MoneyTransferType transferType, string accountNumber);
        Task<ServiceResponse<List<MoneyTransferBeneficiaryDetails>>> GetUserMoneyTransferBeneficiaries(Guid userId, string transferType);
        Task<ServiceResponse<MoneyTransferBeneficiary>> AddMoneyTransferBeneficiary(MoneyTransferBeneficiary newbeneficiary, BaseEnums.Version version = BaseEnums.Version.V1);
        Task<ServiceResponse<MoneyTransferBeneficiary>> DeleteMoneyTransferBeneficiary(Guid beneficiaryId, Guid? portalUserId = null, string portalEmailId = null);
        Task<ServiceResponse> AddExternalBeneficiary(Guid beneficiaryId);
        Task<ServiceResponse> DeleteExternalBeneficiary(Guid beneficiaryId);
        Task<ServiceResponse<Tuple<IList<MoneyTransferBeneficiary>, int>>> SearchUserMoneyTransferBeneficiariesReadOnly(Guid id, SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<ServiceResponse<Tuple<IList<MoneyTransferBeneficiary>, int>>> SearchUserMoneyC3toC3TransferBeneficiaries(Guid id, SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<ServiceResponse<MoneyTransferBeneficiary>> ApproveMoneyTransferBeneficiary(Guid beneficiaryId, Guid? portalUserId = null, string portalEmailId = null);
        Task<ServiceResponse<Tuple<IList<MoneyTransferBeneficiary>, int>>> GetAllMoneyTransferBeneficiaryReadOnly(SearchMoneyTransferBeneficiary searchRAKParameters);
        Task<ServiceResponse<IEnumerable<MoneyTransferBeneficiary>>> GetMoneyTransferBeneficiariesByBeneficiaryIdReadOnly(Guid id, MoneyTransferType? transferType);
        Task<ServiceResponse<bool>> ValidateBeneficiary(MoneyTransferBeneficiary beneficiary);
        ServiceResponse<Dictionary<string, string>> GetSettings(MoneyTransferType moneyTransferType, string corporateId);
        Task<IList<Guid>> GetUserIdsByBeneficiary(SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<string> GetCountryCode(string accountNumber);
        Task<ServiceResponse<int>> Count(Guid userId, bool nonDirect);
        Task<ServiceResponse> RetryPendingBeneficiaries();
        Task<ServiceResponse> AddExternalProviderBeneficiary(Guid beneficiaryId);
        Task<bool> IsOtpValid(MoneyTransferBeneficiary beneficiary, string appVersion, string otp, string phoneNumber, bool skipOtpForUat);
    }
}