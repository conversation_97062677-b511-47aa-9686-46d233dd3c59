﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Structs;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Repositories
{
    public interface IMoneyTransferTransactionRepository : IRepository<MoneyTransferTransaction>
    {
        Task<FreeTransferDetails> GetFreeTransferDetails(Guid userId, decimal loyaltyAmount, DateTime loyaltyImplementationDate, int loyaltyLimitCount, bool IsLoyaltyWaiveDisabled, CancellationToken cancellationToken = default(CancellationToken));
        Task<List<ValidFreeTransferExpiryUserDetails>> GetValidFreeTransferExpiryUsersDetails();
        Task<ReferrerCodeDetails> GetReferrerCodeDetails(Guid userId, DateTime transactionDate);
        Task<MoneyTransferLimitDetails> GetTransferLimitDetails(Guid userId, MoneyTransferType transferType, string countryCode, decimal amount);
        Task<List<MoneyTransferDetails>> GetUserTransactionsDetails(Guid userId, MoneyTransferType? transferType, int? skipValue, int? pageSize, DateTime checkMinSuspiciousDate, CancellationToken cancellationToken = default(CancellationToken));
        Task<UserMonthlyLimitDetails> GetUserMonthlyTransferLimitDetails(Guid userId, decimal amount, int countLimit, decimal amountLimit);
        Task<Tuple<List<MoneyTransferTransactionStruct>, int>> Search(List<Expression<Func<MoneyTransferTransaction, bool>>> searchMoneyTransferTransactionParameters, int? page, int? size);
        Task<MoneyTransferTransaction> GetBankLastSuccessfulTransaction(string bankName, string countryCode, TransactionType transactionType);
        Task<List<MoneyTransferTransaction>> GetReversableTransactions(ReversalMode reversalMode);
        Task<List<Guid>> GetRecentSentBeneficiaries(Guid userId, int count, decimal minSendAmount);
        Task<List<MoneyTransferSuspiciousBeneficiaryDto>> GetSuspiciousBeneficiaries(Guid userId, DateTime checkMinSuspiciousDate);
        Task<MoneyTransferTransaction> GetBankTransferLastSuccessfulTransferAsync(Guid userId, string bankName, string countryCode);
        Task UpdateStatusCheckRetryCount(Guid transactionId);
    }
}
