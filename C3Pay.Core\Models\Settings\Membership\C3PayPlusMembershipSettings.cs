﻿namespace C3Pay.Core.Models.Settings.Membership
{
    public class C3PayPlusMembershipSettings
    {
        public string QueueConnectionString { get; set; }
        public string QueueName { get; set; }

        public string LuckyDrawWinnersCount { get; set; }
        public string CdnUrl { get; set; }
        public string LuckyDrawSchedule { get; set; }
        public string GenerateTicketsMaxCount { get; set; }
        public string OverrideLuckyDrawDate { get; set; }
        public string OverrideLuckyDrawTime { get; set; }
        public string RenewalSchedule { get; set; }
        public string FreeMoneyTransferRefundsSchedule { get; set; }


        // ATM Withdrawal Refunds.
        public string ATMWithdrawalRefundsQueueName { get; set; }
        public string ATMWithdrawalRefundsQueueConnectionString { get; set; }

        // C3P+ Post subscription
        public string PostSubscriptionQueueName { get; set; }
        public string PostSubscriptionQueueConnectionString { get; set; }
        public int DashboardPopupDisplayIntervalInDays { get; set; }

        public int LuckyDrawWinnersDashboardPopupDisplayCount { get; set; }
        public int TargetedDiscountCooldownDays { get; set; } = 30;
        public string AllowedPhoneNumbers { get; set; } = string.Empty;
    }
}
