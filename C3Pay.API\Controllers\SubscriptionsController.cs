﻿using AutoMapper;
using C3Pay.API.Resources;
using C3Pay.API.Resources.Subscriptions;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.BenefitsShell;
using C3Pay.Core.Services;
using C3Pay.Core.Services.Security;
using C3Pay.Services.Membership.Queries;
using C3Pay.Services.Membership.VPN.Queries;
using Edenred.Common.Core;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    [Authorize]
    //[DeviceAuthorize]
    public class SubscriptionsController : ControllerBase
    {
        private readonly IESMOWebService _esmoWebService;
        private readonly IUserService _userService;
        private readonly ISubscriptionService _subscriptionService;
        private readonly IMapper _mapper;
        private readonly ISecurityService _securityService;
        private IHttpContextAccessor _httpContextAccessor;
        private readonly ILookupService _lookupService;
        private readonly IMediator _mediator;
        private readonly ILogger<SubscriptionsController> _logger;
        private readonly IFeatureManager _featureManager;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBenefitService _benefitService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="esmoWebService"></param>
        /// <param name="userService"></param>
        /// <param name="subscriptionService"></param>
        /// <param name="mapper"></param>
        public SubscriptionsController(IESMOWebService esmoWebService, IUserService userService, ISubscriptionService subscriptionService, IMapper mapper, ISecurityService securityService, IHttpContextAccessor httpContextAccessor, ILookupService lookupService, IMediator mediator, ILogger<SubscriptionsController> logger, IFeatureManager featureManager, IUnitOfWork unitOfWork, IBenefitService benefitService)
        {
            this._esmoWebService = esmoWebService;
            this._userService = userService;
            this._subscriptionService = subscriptionService;
            this._mapper = mapper;
            _securityService = securityService;
            _httpContextAccessor = httpContextAccessor;
            _lookupService = lookupService;
            _mediator = mediator;
            _logger = logger;
            _featureManager = featureManager;
            _unitOfWork = unitOfWork;
            _benefitService = benefitService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet("{userId}")]
        public async Task<ActionResult<List<SubscriptionDto>>> GetUserSubscriptions(Guid userId, CancellationToken cancellationToken = default(CancellationToken))
        {
            string languageCode = Request.Headers["x-lang-code"];
            var username = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username).Value;
            var userResult = await this._userService.GetUserById(userId, cancellationToken);
            if (!userResult.IsSuccessful)
            {
                return this.BadRequest(userResult.ErrorMessage);
            }
            var user = userResult.Data;
            var usernameMatchesUser = user.PhoneNumber == username;
            if (!usernameMatchesUser)
            {
                return this.Unauthorized();
            }
            var subscriptionsResult = await this._subscriptionService.GetUserSubscriptionList(user, languageCode ?? "en");
            if (!subscriptionsResult.IsSuccessful)
            {
                return BadRequest(subscriptionsResult.ErrorMessage);
            }
            var userSubscriptions = this._mapper.Map<List<SubscriptionDto>>(subscriptionsResult.Data.ToList());
            // Get Videos for SMS Security 
            userSubscriptions.Single(subscription => subscription.Code == nameof(BaseEnums.SMSSubscriptionType.T)).Video = await GetVideoUrls(user);

            // --- VPN Subscription logic ---
            // Check if VPN is globally enabled and app version supports VPN
            if (await _featureManager.IsEnabledAsync(FeatureFlags.Memberships_Vpn_Global))
            {
                try
                {
                    // Use Benefits Shell integration for VPN data
                    await ProcessVpnWithBenefitsShell(user, userSubscriptions, languageCode, cancellationToken);
                }
                catch (Exception ex)
                {
                    // Log the error but don't break the entire subscription list
                    // VPN subscription errors should not prevent other subscriptions from loading
                    _logger.LogError(ex, "Error occurred while processing VPN subscription for user {UserPhoneNumber}. VPN subscription will be excluded from results.", user.PhoneNumber);
                }
            }
            // If VPN global flag is off, skip VPN entirely
            // --- End VPN Subscription logic ---

            return this.Ok(userSubscriptions);
        }

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        [HttpGet("{userId}/subscribe/{subscriptionId}")]
        public async Task<ActionResult> Subscribe(Guid userId, Guid subscriptionId)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            // If the user is a C3Pay+ subscriber, do not allow them to subscribe to any other subscription.
            var isC3PayPlusSubscriber = await this._mediator.Send(new GetC3PayPlusMembershipUserDetailsQuery()
            {
                UserId = userId
            });

            if (isC3PayPlusSubscriber.IsFailure)
            {
                return this.BadRequest(isC3PayPlusSubscriber.Error.Code);
            }

            var subscriptionResult = await this._subscriptionService.GetSubscripitonsById(subscriptionId);

            if (!subscriptionResult.IsSuccessful)
            {
                return this.BadRequest(subscriptionResult.ErrorMessage);
            }

            var subscription = subscriptionResult.Data;

            if (subscription.Code == BaseEnums.SMSSubscriptionType.BE.ToString())
            {
                var subscribeResult = await this._subscriptionService.Subscribe(userId, subscriptionId, null, true);

                if (!subscribeResult.IsSuccessful)
                {
                    return BadRequest(subscribeResult.ErrorMessage);
                }

                return this.Ok();
            }
            else
            {
                var userResult = await this._userService.GetUserById(userId);

                if (!userResult.IsSuccessful)
                {
                    return this.BadRequest(userResult.ErrorMessage);
                }

                var user = userResult.Data;
                var subscriptionFee = await _subscriptionService.GetSubscriptionFee(subscription.Id, user.CardHolder.CorporateId);
                if (!subscriptionFee.IsSuccessful)
                {
                    return this.BadRequest(subscriptionFee.ErrorMessage);
                }

                var subscribeResult = await this._esmoWebService.SubscribeToSMSNotification(new SubscribeToSMSNotificationRequestDto()
                {
                    CardSerialNumber = user.CardHolder.CardSerialNumber,
                    CorporateId = int.Parse(user.CardHolder.CorporateId),
                    NotificationType = subscription.Code,
                    PhoneNumber = user.PhoneNumber,
                    SMSFee = subscriptionFee.Data.Fee
                });

                if (!subscribeResult.IsSuccessful)
                {
                    return this.BadRequest(subscribeResult.ErrorMessage);
                }

                string languageCode = string.IsNullOrEmpty(Request.Headers["x-lang-code"]) ? "en" : Request.Headers["x-lang-code"].ToString();
                return this.Ok(new BaseResponseDto()
                {
                    Title = Messages.SMSSubscription.GetTitleOnSuccess(languageCode),
                    Description = Messages.SMSSubscription.GetDescriptionOnSuccess(languageCode),
                    Status = ResponseStatus.Success.ToString()
                });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("{userId}/unsubscribe/{subscriptionId}")]
        public async Task<ActionResult> Unsubscribe(Guid userId, Guid subscriptionId)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            // If the user is a C3Pay+ subscriber, do not allow them to unsubscribe from any other subscription.
            var isC3PayPlusSubscriber = await this._mediator.Send(new GetC3PayPlusMembershipUserDetailsQuery()
            {
                UserId = userId
            });

            if (isC3PayPlusSubscriber.IsFailure)
            {
                return this.BadRequest(isC3PayPlusSubscriber.Error.Code);
            }

            var subscriptionResult = await this._subscriptionService.GetSubscripitonsById(subscriptionId);

            if (!subscriptionResult.IsSuccessful)
            {
                return this.BadRequest(subscriptionResult.ErrorMessage);
            }

            var subscription = subscriptionResult.Data;

            if (subscription.Code == BaseEnums.SMSSubscriptionType.BE.ToString())
            {
                var unsubscribeResult = await this._subscriptionService.Unsubscribe(userId, subscriptionId, null, true);

                if (!unsubscribeResult.IsSuccessful)
                {
                    return BadRequest(unsubscribeResult.ErrorMessage);
                }

                return this.Ok();
            }
            else
            {
                var userResult = await this._userService.GetUserById(userId);

                if (!userResult.IsSuccessful)
                {
                    return this.BadRequest(userResult.ErrorMessage);
                }

                var user = userResult.Data;

                var unsubscribeResult = await this._esmoWebService.UnsubscribeFromSMSNotification(new UnsubscribeFromSMSNotificationRequestDto()
                {
                    CardSerialNumber = user.CardHolder.CardSerialNumber,
                    CorporateId = int.Parse(user.CardHolder.CorporateId),
                    NotificationType = subscription.Code
                });

                if (!unsubscribeResult.IsSuccessful)
                {
                    return this.BadRequest(unsubscribeResult.ErrorMessage);
                }

                return this.Ok();
            }
        }

        #region Private Methods

        private async Task ProcessVpnWithBenefitsShell(User user, List<SubscriptionDto> userSubscriptions, string languageCode, CancellationToken cancellationToken)
        {
            // Step 1: Check if user is allowed for the benefit
            var isEligibleForBenefitResult = await _benefitService.GetBenefitEligibilityByCardHolderId(user.CardHolderId, ConstantParam.VpnBenefitId);
            if (!isEligibleForBenefitResult.IsSuccess)
            {
                _logger.LogDebug("User {UserPhoneNumber} not entitled to VPN benefit", user.PhoneNumber);
                return;
            }

            // Step 2: Get cached VPN benefit details (avoid expensive GetBenefitDetailsQuery)
            var vpnBenefitResult = await _benefitService.GetBenefitDetails(ConstantParam.VpnBenefitId, cancellationToken);
            if (!vpnBenefitResult.IsSuccess || vpnBenefitResult.Value == null)
            {
                //_logger.LogWarning("Could not retrieve cached VPN benefit details for user {UserPhoneNumber}", user.PhoneNumber);
                return;
            }
            var vpnBenefit = vpnBenefitResult.Value;

            // Step 3: Check if user has active VPN subscription (for IsActive property)
            var vpnMembershipResult = await _mediator.Send(new GetVpnMembershipDetailsQuery
            {
                UserPhoneNumber = user.PhoneNumber,
                LanguageCode = languageCode ?? "en"
            }, cancellationToken);

            bool isActiveSubscription = vpnMembershipResult.IsSuccess &&
                                       vpnMembershipResult.Value?.IsSuccessful == true &&
                                       vpnMembershipResult.Value.Data != null;

            // Step 5: Generate deterministic ID using user ID and benefit ID (processor-less solution)
            //var vpnSubscriptionId = new Guid($"{user.Id:N}".Substring(0, 8) + $"{ConstantParam.VpnBenefitId:D8}" + "0000" + "0000" + "000000000000");
            var guidString = $"{user.Id:N}".Substring(0, 8) +
                 $"-{ConstantParam.VpnBenefitId:D4}-{ConstantParam.VpnBenefitId:D4}-" +
                 "0000-000000000000";
            var vpnSubscriptionId = new Guid(guidString);

            // Step 4: Create VPN subscription DTO using cached benefit data
            var vpnSubscription = new SubscriptionDto
            {
                Id = vpnSubscriptionId,
                Title = vpnBenefit.ProductTitle ?? vpnBenefit.Name,
                ShortDescription = vpnBenefit.Description,
                FullDescription = vpnBenefit.ProductTitle,
                Code = vpnBenefit.CustomName,
                Fee = vpnBenefit.Price,
                FeeIteration = vpnBenefit.FeeIteration,
                IsEnabled = true, // User is allowed since we checked BenefitAllowedUsers
                DisplayOrder = vpnBenefit.DashboardIconOrder ?? 10,
                Video = ApplyNationalityBasedVideoDefaults(vpnBenefit.Videos, user.CardHolder.Nationality),
                Features = vpnBenefit.ProductDefinitions?.Select(f => new SubscriptionFeatureDto
                {
                    Description = f.Description,
                    IsAvailable = f.IsAvailable,
                    IconUrl = f.IconUrl
                }).ToList() ?? new List<SubscriptionFeatureDto>(),
                BenefitId = vpnBenefit.Id
            };

            // temp:
            var vpnMembershipUser = await this._unitOfWork.VpnMembershipUsers.FirstOrDefaultAsync(x => x.UserId == user.Id && x.IsActive);
            if (vpnMembershipUser is null || isActiveSubscription == false)
            {
                vpnSubscription.IsActive = false;
            }
            else
            {
                // Here, membership is still active, but the user could have cancelled.
                vpnSubscription.IsActive = !vpnMembershipUser.UserHasCancelled;
            }
            
            
            // Step 5: Add VPN subscription to the list (regardless of subscription status, as long as user is allowed)
            userSubscriptions.Add(vpnSubscription);
        }

        /// <summary>
        /// Apply nationality-based IsDefault logic to benefit videos, similar to GetVideoUrls method
        /// </summary>
        private List<SubscriptionMultimediaDto> ApplyNationalityBasedVideoDefaults(List<BenefitVideoDto> videos, string userNationality)
        {
            if (videos == null || !videos.Any())
                return new List<SubscriptionMultimediaDto>();

            // Same logic as GetVideoUrls method
            var hindiNationalities = new HashSet<string> { nameof(BaseEnums.MultimediaCountry.PAK), nameof(BaseEnums.MultimediaCountry.IND) };
            var userShouldSeeHindiVideo = hindiNationalities.Contains(userNationality);
            bool defaultAlreadySet = false;

            return videos.Select(video => new SubscriptionMultimediaDto
            {
                LanguageCode = video.LanguageCode,
                Url = video.Url,
                // Apply nationality-based default logic: Hindi default for PAK/IND users, English for others
                IsDefault = ((video.LanguageCode == "hi" && userShouldSeeHindiVideo)
                          || (video.LanguageCode != "hi" && !userShouldSeeHindiVideo))
                          && !defaultAlreadySet && (defaultAlreadySet = true)
            }).ToList();
        }

        private async Task<List<SubscriptionMultimediaDto>> GetVideoUrls(User _user)
        {
            var hindiNationalities = new HashSet<string> { nameof(BaseEnums.MultimediaCountry.PAK), nameof(BaseEnums.MultimediaCountry.IND) };
            var userShouldSeeHindiVideo = hindiNationalities.Contains(_user.CardHolder.Nationality);
            bool defaultAlreadySet = false;

            // Temporarily added this for rollout plan
            var IsSmsPlusEabled = await _lookupService.IsSmsPlusEnabled(_user.CardHolder.CorporateId);

            if (IsSmsPlusEabled)
            {
                var multimediaResources = await _lookupService.GetMultimediaResources(feature: (int)FeatureType.Subscription_SecuritySMS).ConfigureAwait(false);

                return multimediaResources.Data
                    .Select(resource => new SubscriptionMultimediaDto
                    {
                        LanguageCode = resource.Language,
                        Url = resource.Url,
                        IsDefault = ((resource.Language == "hi" && userShouldSeeHindiVideo)
                                        || (resource.Language != "hi" && !userShouldSeeHindiVideo))
                                    && !defaultAlreadySet && (defaultAlreadySet = true)
                    })
                    .ToList();
            }
            else
            {
                var multimediaResources = new List<MultimediaResource>();
                multimediaResources.Add(new MultimediaResource() { Language = "hi", Url = "https://cdn.edenred.ae/money-transfer-videos/hindi_s.mp4" });
                multimediaResources.Add(new MultimediaResource() { Language = "en", Url = "https://cdn.edenred.ae/money-transfer-videos/English_S.mp4" });

                return multimediaResources
                    .Select(resource => new SubscriptionMultimediaDto
                    {
                        LanguageCode = resource.Language,
                        Url = resource.Url,
                        IsDefault = ((resource.Language == "hi" && userShouldSeeHindiVideo)
                                        || (resource.Language != "hi" && !userShouldSeeHindiVideo))
                                    && !defaultAlreadySet && (defaultAlreadySet = true)
                    })
                    .ToList();
            }
        }
        #endregion    
    }
}
