﻿using C3Pay.Core.Models;
using System;
using System.Collections.Generic;

namespace C3Pay.Core
{
    /// <summary>
    /// 
    /// </summary>
    public class BeneficiaryMessageDto
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public BaseEnums.MessageAction Action { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class MoneyTransferMessageDto
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public BaseEnums.MessageAction Action { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class MobileRechargeMessageDto
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public BaseEnums.MessageAction Action { get; set; }
    }

    /// <summary>
    /// Bill Payment New Biller MessageDto
    /// </summary>
    public class BillPaymentNewBillerMessageDto
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Action
        /// </summary>
        public BaseEnums.MessageAction Action { get; set; }
    }

    public class BillPaymentTransactionMessageDto
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        public int ProductId { get; set; }
        public decimal? InputAmount { get; set; }
        public string InputAmountCurrency { get; set; } 

        /// <summary>
        /// Action
        /// </summary>
        public BaseEnums.MessageAction Action { get; set; }
    }


    /// <summary>
    /// 
    /// </summary>
    public class MarkCardHolderAsAppRegisteredMessageDto
    {
        public string CardSerialNo { get; set; }
        public long CorporateId { get; set; }
        public bool AppStatus { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class CompleteUserRegistrationDto
    {
        public string CardSerialNo { get; set; }
        public long CorporateId { get; set; }
        public bool AppStatus { get; set; }
        public string CitizenId { get; set; }
        public string Mobile { get; set; }
        public string PpsAccountNumber { get; set; }
        public bool? IsInternationalPhoneNumberUser { get; set; }

    }

    /// <summary>
    /// 
    /// </summary>
    public class ReplacementCardUpdateDto
    {
        public string CitizenId { get; set; }
        public string CardNumber { get; set; }
        public string ExpiryDate { get; set; }
        public string CardSerialNUmber { get; set; }
        public string C3EmpRegId { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class DeletedCardUpdateDto
    {
        public string CitizenId { get; set; }
        public string C3EmpRegId { get; set; }
    }

    public class PushSalaryProcessedEventsDto
    {
        public List<SalaryProcessedDto> SalariesProcessed { get; set; }
    }

    public class SalaryProcessedDto
    {
        public string CitizenId { get; set; }
        public decimal Amount { get; set; }
        public DateTime ProcessedDate { get; set; }
    }

    public class RewardUserForReferralDto
    {
        public string ReferralCode { get; set; }
    }


    public class ClaimPendingDirectTransfersDto
    {
        public string AccountNumber { get; set; }
    }

    public class BranchIdUpdateDto
    {
        public string BranchId { get; set; }
        public List<string> C3RegistrationIds { get; set; }
    }
    public class EHBankBranch
    {
        public string BankId { get; set; }
        public string BankName { get; set; }
        public string BranchId { get; set; }
        public string BranchName { get; set; }

    }


    #region Unemployment Insurance 
    public class UnEmploymentInsuranceCompletedMessageDto
    {
        public string CitizenId { get; set; }
        public decimal Amount { get; set; }
        public string AmountCurrency { get; set; }
        public decimal VatAmount { get; set; }
        public string VatAmountCurrency { get; set; }
        public decimal FeeAmount { get; set; }
        public string FeeAmountCurrency { get; set; }
        public decimal TotalAmount { get; set; }
        public string TotalAmountCurrency { get; set; }
        public string PolicyNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public DateTime? InceptionDate { get; set; }
        public int TotalInstallment { get; set; }
        public int PaidInstallment { get; set; }
        public DateTime? NextDueDate { get; set; }
        public decimal NextDueAmount { get; set; }
        public string NextDueAmountCurrency { get; set; }
        public bool IsSubscribedExternally { get; set; }
        public string Source { get; set; } 
        public string PaymentOption { get; set; }
        public string Category { get; set; }
        public bool IsAutoPayment { get; set; } 
        public bool IsSalaryDebit { get; set; }
    }
    
    public class UnEmpInsuranceErrorResponseDto
    {
        public string Error { get; set; }
        public string CitizenId { get; set; }
    }

    public class UnEmpInsuranceCancellationDto
    {
        public List<string> CitizenIds { get; set; }
    }

    public class UnEmpInsuranceInstallmentFailedFromB2BDto
    {
        public List<string> CitizenIds { get; set; }
    }

    public class UnEmploymentInsuranceAddedInQueueMessageDto
    {
        public string CitizenId { get; set; }
        public decimal Amount { get; set; }
        public decimal VatAmount { get; set; }
        public decimal FeeAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public string PaymentOption { get; set; }
        public string Remarks { get; set; }
        public string Source { get; set; }
        public string Category { get; set; }
        public bool IsAutoPayment { get; set; }
        public bool IsWorkerDetailsNotFoundError { get; set; }
    }

    public class UnempInsuranceStatusChangeMessageDto
    {
        public string CitizenId { get; set; }
        public string CorporateId { get; set; }
        public string EmployeeId { get; set; }
        public bool IsWorkerDetailsNotFoundError { get; set; }
    }
    #endregion

    public class ServiceBusTopicMessageDto
    { 
        public string Data {get;set;}
        public string Type {get;set;}
    }

    public class MobileRechargeFailedTransactionMessageDto
    {
        public Guid TransactionId { get; set; }
        public Guid BeneficiaryId { get; set; }
        public string BeneficiaryCountryCode { get; set; }
        public string TransactionErrorContext { get; set; }
    }

    public class RenewalCardUpdateDto 
    {
        public string CitizenId { get; set; }
        public string C3EmpRegId { get; set; }
        public string NewCardserialNumber { get; set; } 
        public string NewCardNumber { get; set; }
        public string Source { get; set; } 
    }

    public class RenewalCardSyncDto
    {
        public string CitizenId { get; set; }
        public string C3EmpRegId { get; set; }
        public string NewCardserialNumber { get; set; }
        public string NewCardNumber { get; set; }
    }

    public class FinalizeSubscribingToC3PayPlusActionDto
    {
        public Guid UserId { get; set; }
        public int C3PayPlusMembershipId { get; set; }
    }

    public class UnblockKycMessageDto
    {
        public Guid Id { get; set; }
        public string CardholderId { get; set; }
        public string PassportId { get; set; }
        public string EmiratesId { get; set; }
    }

    public class SalaryGotPaidMessageDto
    {
        public int C3EmpRegId { get; set; }
        public string CitizenId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public int CorporateId { get; set; }
        public int SalMonth { get; set; }
        public int SalYear { get; set; }
        public decimal SalaryAmount { get; set; }

        public DateTime? ProcessedDate { get; set; }
    }


}
