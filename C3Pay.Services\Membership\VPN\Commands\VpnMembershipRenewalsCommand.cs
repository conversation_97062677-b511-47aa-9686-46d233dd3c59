﻿using Application.Messages;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.VPN.Billings;
using C3Pay.Core.Models.C3Pay.Membership.VPN.Logging;
using C3Pay.Core.Services;
using C3Pay.Core.Services.Sms;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.PPS;
using Edenred.Common.Core.Models.Messages.Integration.PPS.Transaction;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using Result = C3Pay.Core.Result;

namespace C3Pay.Services.Membership.VPN.Commands
{
    public class VpnMembershipRenewalsCommand : IRequest<Result>
    {
    }

    public class VpnMembershipRenewalsCommandHandler : IRequestHandler<VpnMembershipRenewalsCommand, Result>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPPSWebAuthService _ppsWebAuthService;
        private readonly IESMOWebService _esmoWebService;
        private readonly IFeatureManager _featureManager;
        private readonly IPPSService _ppsService;
        private readonly ILogger _logger;
        private readonly ITextMessageSenderService _textMessageSenderService;
        private readonly ISmsNotificationSender _smsNotificationSender;
        private readonly IPushNotificationSenderService _pushNotificationSenderService;

        private readonly decimal _vpnPrice = 3.15m;

        private enum VpnRenewalCancelationReasons
        {
            UserNotFoundOrBlockedOrDeleted = 1,
            UserUnsubscribed,
            NonActiveCard,
            NoSalaryInTheLast3Months,
            Other
        }

        public VpnMembershipRenewalsCommandHandler(
            ILogger<VpnMembershipRenewalsCommandHandler> logger,
            IUnitOfWork unitOfWork,
            IPPSWebAuthService ppsWebAuthService,
            IESMOWebService esmoWebService,
            IFeatureManager featureManager,
            IPPSService ppsService, 
            ISmsNotificationSender smsNotificationSender,
            ITextMessageSenderService textMessageSenderService,
            IPushNotificationSenderService pushNotificationSenderService)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _ppsWebAuthService = ppsWebAuthService;
            _esmoWebService = esmoWebService;
            _featureManager = featureManager;
            _ppsService = ppsService;
            _textMessageSenderService = textMessageSenderService;
            _smsNotificationSender = smsNotificationSender;
            _pushNotificationSenderService = pushNotificationSenderService;
        }

        public async Task<Result> Handle(VpnMembershipRenewalsCommand command, CancellationToken ct)
        {
            // Check if VPN renewals feature is enabled
            var isEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.Memberships_Vpn_Renewals);
            if (!isEnabled)
            {
                _logger.LogError("VPN renewals feature is disabled");
                return Result.Failure(Errors.VpnMembershipErrors.FeatureDisabled);
            }

            var currentDate = DateTime.Now;

            // Create log entry for the renewal job
            var logEntry = new Log_VpnMembershipRenewals()
            {
                RenewingFor = currentDate.Date,
                JobInvokedAt = currentDate,
                RunStatus = VpnRenewalRunStatus.Started
            };

            await _unitOfWork.Logs_VpnMembershipRenewals.AddAsync(logEntry);
            await _unitOfWork.CommitAsync();

            var completedRenewals = new List<VpnMembershipUser>();

            try
            {
                logEntry.JobStartTime = DateTime.Now;
                logEntry.RunStatus = VpnRenewalRunStatus.FetchingSubscriptions;
                await _unitOfWork.CommitAsync();

                //Handle end-of-month renewals for users who subscribed on days that don't exist in current month
                var currentDay = currentDate.Day;
                var daysInCurrentMonth = DateTime.DaysInMonth(currentDate.Year, currentDate.Month);
                var isLastDayOfMonth = currentDay == daysInCurrentMonth;

                //Exclude today's date to prevent same-day renewals
                var todayStart = currentDate.Date;

                // Get all VPN subscriptions that should renew today, but not those created today
                var renewals = await _unitOfWork.VpnMembershipUsers.FindAsync(
                    x => x.IsActive == true && 
                         (x.UserSubscribedOn.Day == currentDay || 
                          (isLastDayOfMonth && x.UserSubscribedOn.Day > daysInCurrentMonth)) &&
                         x.UserSubscribedOn < todayStart, // Prevent same-day renewals
                    withoutTraking: false,
                    includeProperties: i => i.User.CardHolder);

                if (isLastDayOfMonth)
                {
                    var endOfMonthRenewals = renewals.Count(x => x.UserSubscribedOn.Day > daysInCurrentMonth);
                    if (endOfMonthRenewals > 0)
                    {
                        _logger.LogInformation("Processing {endOfMonthRenewals} end of month renewals for users who subscribed on days > {daysInCurrentMonth} (last day of {Month}/{Year})",
                            endOfMonthRenewals, daysInCurrentMonth, currentDate.Month, currentDate.Year);
                    }
                }

                logEntry.TotalRenewalsExpectedToProcess = renewals.Count;
                logEntry.RunStatus = VpnRenewalRunStatus.Renewing;
                await _unitOfWork.CommitAsync();

                foreach (var subscriber in renewals)
                {
                    // Find user
                    var user = await _unitOfWork.Users.FirstOrDefaultAsync(x =>
                        x.Id == subscriber.UserId &&
                        x.IsDeleted == false &&
                        x.IsBlocked == false &&
                        x.ApplicationId == MobileApplicationId.C3Pay,
                        i => i.CardHolder);

                    if (user == null)
                    {
                        await EndMembership(VpnRenewalCancelationReasons.UserNotFoundOrBlockedOrDeleted, subscriber, logEntry);
                        continue;
                    }

                    // Check if user has cancelled
                    if (subscriber.UserHasCancelled)
                    {
                        await EndMembership(VpnRenewalCancelationReasons.UserUnsubscribed, subscriber, logEntry);
                        continue;
                    }

                    // Check if card is dormant
                    var cardDormantCheck = await _esmoWebService.IsCardDormant(subscriber.User.CardHolder.C3RegistrationId);
                    if (!cardDormantCheck.IsSuccessful)
                    {
                        logEntry.SkippedRenewalsDueToDormantCardCheckFailure++;
                        await _unitOfWork.CommitAsync();
                        continue;
                    }

                    if (cardDormantCheck.Data.IsCardDormant)
                    {
                        await EndMembership(VpnRenewalCancelationReasons.NonActiveCard, subscriber, logEntry);
                        continue;
                    }

                    // Check balance
                    var balanceResult = await GetBalance(user.CardHolder);
                    if (!balanceResult.IsSuccessful)
                    {
                        logEntry.SkippedRenewalsDueToUnableToRetrieveBalance++;
                        await _unitOfWork.CommitAsync();
                        continue;
                    }

                    if (balanceResult.Data < _vpnPrice)
                    {
                        var salaryCheck = await _esmoWebService.GotSalaryInLast3Months(user.CardHolderId);
                        if (!salaryCheck.IsSuccessful || !salaryCheck.Data.IsSuccessful)
                        {
                            logEntry.SkippedRenewalsDueToUnableToConfirmSalaryReceived++;
                            await _unitOfWork.CommitAsync();
                            continue;
                        }

                        if (!salaryCheck.Data.GotSalary)
                        {
                            await EndMembership(VpnRenewalCancelationReasons.NoSalaryInTheLast3Months, subscriber, logEntry);
                            continue;
                        }
                    }

                    // Declare variables outside try-catch for exception handling
                    string referenceNumber = null;
                    bool billingSucceeded = false;

                    try
                    {
                        // Create billing record
                        referenceNumber = await GenerateBillingReferenceNumber();
                        var renewalBilling = new VpnMembershipRenewalBilling()
                        {
                            ReferenceNumber = referenceNumber,
                            Status = VpnRenewalBillingStatus.BillingStarted,
                            VpnMembershipUserId = subscriber.Id,
                            RenewingFor = currentDate.Date,
                            BillingDate = DateTime.Now,
                            BillingAmount = _vpnPrice
                        };

                        await _unitOfWork.VpnMembershipRenewalBillings.AddAsync(renewalBilling);
                        await _unitOfWork.CommitAsync();

                        // Perform billing
                        var billingResult = await DecreaseBalance(subscriber);
                        if (!billingResult.IsSuccessful)
                        {
                            logEntry.SkippedRenewalsDueToUnableToDebitUser++;
                            await _unitOfWork.CommitAsync();
                            continue;
                        }

                        // Mark billing as successful only after DecreaseBalance succeeds
                        billingSucceeded = true;

                        // Assign new VPN code after successful billing
                        var codeAssignmentResult = await AssignNewVpnCode(subscriber, user, logEntry);
                        if (!codeAssignmentResult.IsSuccess)
                        {
                            // Code assignment failed after successful billing - attempt refund
                            _logger.LogWarning("VPN renewal billing succeeded but code assignment failed for user {UserPhoneNumber}. Attempting refund.", user.PhoneNumber);
                            
                            logEntry.RefundsAttemptedDueToCodeAssignmentFailure++;
                            
                            try
                            {
                                var refundResult = await RefundVpnRenewal(subscriber, referenceNumber, _vpnPrice, "VPN renewal code assignment failed after billing");
                                if (refundResult.IsSuccessful)
                                {
                                    _logger.LogInformation("Successfully refunded VPN renewal for user {UserPhoneNumber}, reference {ReferenceNumber}", user.PhoneNumber, referenceNumber);
                                    logEntry.RefundsSucceededDueToCodeAssignmentFailure++;
                                }
                                else
                                {
                                    _logger.LogError("Critical: VPN renewal billing succeeded but refund failed for user {UserPhoneNumber}, reference {ReferenceNumber}. Manual intervention required.", user.PhoneNumber, referenceNumber);
                                }
                            }
                            catch (Exception refundEx)
                            {
                                _logger.LogError("Exception during VPN renewal refund attempt for user {UserPhoneNumber}, reference {ReferenceNumber}: {ErrorMessage}", user.PhoneNumber, referenceNumber, refundEx.Message);
                            }
                            
                            logEntry.SkippedRenewalsDueToVpnCodeAssignmentFailure++;
                            await _unitOfWork.CommitAsync();
                            continue;
                        }

                        // Update billing status and renew membership
                        renewalBilling.Status = VpnRenewalBillingStatus.BillingConfirmed;
                        subscriber.RenewMembership(currentDate);

                        // Send renewal success SMS
                        try
                        {
                            var result = await _textMessageSenderService.GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.VpnRenewal.txt");

                            var template = result.Data;

                            var message = string.Format(template, subscriber.Code);
                            var sendSmsCommand = new SendSmsNotificationCommand
                            {
                                CorrelationId = Guid.NewGuid(),
                                Content = message,
                                PhoneNumber = user.PhoneNumber
                            };

                            await _smsNotificationSender.SendAsync(sendSmsCommand);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to send VPN renewal success SMS to user {UserPhoneNumber}", user.PhoneNumber);
                        }

                        // Send renewal success push notification
                        try
                        {
                            await _pushNotificationSenderService.SendVpnRenewalNotification(user.DeviceToken, subscriber.Code);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to send VPN renewal success push notification to user {UserPhoneNumber}", user.PhoneNumber);
                        }

                        completedRenewals.Add(subscriber);
                        logEntry.RenewalsCompletedSuccessfully++;
                        await _unitOfWork.CommitAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Exception during VPN renewal processing for user {UserPhoneNumber}: {ErrorMessage}", user.PhoneNumber, ex.Message);

                        // If billing was successful but an exception occurred, attempt refund
                        if (billingSucceeded && referenceNumber != null)
                        {
                            logEntry.RefundsAttemptedDueToCodeAssignmentFailure++;
                            
                            try
                            {
                                var refundResult = await RefundVpnRenewal(subscriber, referenceNumber, _vpnPrice, "VPN renewal exception after billing");
                                if (refundResult.IsSuccessful)
                                {
                                    _logger.LogInformation("Successfully refunded VPN renewal after exception for user {UserPhoneNumber}, reference {ReferenceNumber}", user.PhoneNumber, referenceNumber);
                                    logEntry.RefundsSucceededDueToCodeAssignmentFailure++;
                                }
                                else
                                {
                                    _logger.LogError("Critical: VPN renewal billing succeeded but refund failed after exception for user {UserPhoneNumber}, reference {ReferenceNumber}. Manual intervention required.", user.PhoneNumber, referenceNumber);
                                }
                            }
                            catch (Exception refundEx)
                            {
                                _logger.LogError("Exception during VPN renewal refund attempt for user {UserPhoneNumber}, reference {ReferenceNumber}: {ErrorMessage}", user.PhoneNumber, referenceNumber, refundEx.Message);
                            }
                        }

                        logEntry.SkippedRenewalsDueToVpnCodeAssignmentFailure++;
                        await _unitOfWork.CommitAsync();
                        continue;
                    }
                }

                // Job completed successfully
                logEntry.JobEndTime = DateTime.Now;
                logEntry.JobDurationInSeconds = (int)(logEntry.JobEndTime.Value - logEntry.JobStartTime.Value).TotalSeconds;
                logEntry.RunStatus = VpnRenewalRunStatus.Finished;
                await _unitOfWork.CommitAsync();

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fatal error in VPN renewals job: {Error}", ex.Message);
                logEntry.Remarks = $"FATAL ERROR: {ex}";
                logEntry.RunStatus = VpnRenewalRunStatus.Interrupted;
                await _unitOfWork.CommitAsync();
                return Result.Failure(Errors.VpnMembershipErrors.RenewalJobFailed);
            }
        }

        private async Task EndMembership(VpnRenewalCancelationReasons reason, VpnMembershipUser subscriber, Log_VpnMembershipRenewals logEntry)
        {
            switch (reason)
            {
                case VpnRenewalCancelationReasons.UserNotFoundOrBlockedOrDeleted:
                    subscriber.EndMembership("VPN membership closed due to user not found, blocked, or deleted");
                    logEntry.UnsubscribedDueToDeletedUsers++;
                    break;
                case VpnRenewalCancelationReasons.UserUnsubscribed:
                    subscriber.EndMembership("VPN membership closed due to user cancellation");
                    logEntry.UnsubscribedDueToUserDecision++;
                    break;
                case VpnRenewalCancelationReasons.NonActiveCard:
                    subscriber.EndMembership("VPN membership closed due to inactive card");
                    logEntry.UnsubscribedDueToDormantCard++;
                    break;
                case VpnRenewalCancelationReasons.NoSalaryInTheLast3Months:
                    subscriber.EndMembership("VPN membership closed due to no salary credited in last 3 months");
                    logEntry.UnsubscribedDueToNoSalaryCreditedInLast3Months++;
                    break;
                default:
                    subscriber.EndMembership("VPN membership closed due to other reasons");
                    logEntry.UnsubscribedDueToOtherReasons++;
                    break;
            }

            logEntry.SuccessfulUnsubscribes++;
            await _unitOfWork.CommitAsync();
        }

        private async Task<string> GenerateBillingReferenceNumber()
        {
            var referencePrefix = TransactionPrefix.MENAVPN.ToString();
            var dateStamp = DateTime.Now;
            var startYear = dateStamp.Year.ToString().Substring(2);
            var startMonth = dateStamp.Month.ToString();
            var startDay = dateStamp.Day.ToString();

            var dateComponent = Convert.ToDecimal(startYear.PadLeft(2, '0') + startMonth.PadLeft(2, '0') + startDay.PadLeft(2, '0'));
            referencePrefix = $"{referencePrefix}{dateComponent}X";
            var referenceDigits = 10;
            var referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);

            while (await _unitOfWork.VpnMembershipRenewalBillings.Any(t => t.ReferenceNumber == referenceNumber))
            {
                referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            }

            return referenceNumber;
        }

        private async Task<ServiceResponse> DecreaseBalance(VpnMembershipUser membershipUser)
        {
            var cardNumber = membershipUser.User.CardHolder.CardNumber;
            var cardSerialNumber = membershipUser.User.CardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var decreaseBalanceRequest = new PPSDecreaseBalanceRequest()
            {
                accountField = new PPSAccountDetails()
                {
                    accountNoField = membershipUser.User.CardHolder.PpsAccountNumber,
                    cardSerialField = cardSerialNumber,
                    cardPanField = cardPanNumber
                },
                amountField = (long)(_vpnPrice * 100),
                reasonField = "VPN Monthly Billing",
                schemeRefField = GenerateSchemeReference(),
                allowNegativeBalance = true
            };

            try
            {
                var result = await _ppsService.DecreaseBalance(decreaseBalanceRequest, true);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error decreasing balance for VPN renewal");
                return new ServiceResponse(false, "Failed to debit user account");
            }
        }

        private async Task<ServiceResponse<decimal>> GetBalance(CardHolder cardholder)
        {
            var cardNumber = cardholder.CardNumber;
            var cardSerialNumber = cardholder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var request = new BalanceRequest()
            {
                CardPanNumber = cardPanNumber,
                CardSerialNumber = cardSerialNumber,
                Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                ReferenceNumber = TypeUtility.GetReferenceNumber(TransactionPrefix.BAL.ToString(), 12)
            };

            var getCardBalanceResult = await _ppsWebAuthService.GetCardBalance(request);

            if (!getCardBalanceResult.IsSuccessful)
            {
                return new ServiceResponse<decimal>(false, "PPS connection issue");
            }

            if (getCardBalanceResult.Data.StatusCode != "00")
            {
                return new ServiceResponse<decimal>(false, getCardBalanceResult.Data.Message);
            }

            var balance = TypeUtility.GetDecimalFromString(getCardBalanceResult.Data.EndBalanace.Amt) / 100;
            return new ServiceResponse<decimal>(balance);
        }

        private string GenerateSchemeReference()
        {
            return TypeUtility.GetReferenceNumber("VPN", 8);
        }

        private string GenerateRefundReferenceNumber(string originalReferenceNumber)
        {
            var referencePrefix = TransactionPrefix.MENAVPNRF.ToString();
            var dateStamp = DateTime.Now;
            var startYear = dateStamp.Year.ToString().Substring(2);
            var startMonth = dateStamp.Month.ToString();
            var startDay = dateStamp.Day.ToString();

            var dateComponent = Convert.ToDecimal(startYear.PadLeft(2, '0') + startMonth.PadLeft(2, '0') + startDay.PadLeft(2, '0'));
            referencePrefix = $"{referencePrefix}{dateComponent}X";
            var referenceDigits = 10;
            var referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);

            _logger.LogInformation($"Generated refund reference {referenceNumber} for original renewal transaction {originalReferenceNumber}");

            return referenceNumber;
        }

        private async Task<ServiceResponse> RefundVpnRenewal(VpnMembershipUser membershipUser, string originalReferenceNumber, decimal amount, string reason)
        {
            var loadCardRequest = new PPSCardLoadRequest()
            {
                acceptorNameField = TransactionPrefix.MENAVPNRF.ToString(),
                accountField = new PPSAccountDetails()
                {
                    accountNoField = membershipUser.User.CardHolder.PpsAccountNumber
                },
                schemeRefField = GenerateRefundReferenceNumber(originalReferenceNumber),
                topupAmountField = (long)(amount * 100), // Convert to cents
                commentsField = reason
            };

            try
            {
                return await _ppsService.LoadCard(loadCardRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during VPN renewal refund for user {membershipUser.User.PhoneNumber}, reference {originalReferenceNumber}: {ex.Message}");
                return new ServiceResponse(false, $"Refund failed: {ex.Message}");
            }
        }

        private async Task<Result> AssignNewVpnCode(VpnMembershipUser subscriber, User user, Log_VpnMembershipRenewals logEntry)
        {
            try
            {
                VpnMembershipCode newCode = null;
                int maxRetries = 3;
                int retryCount = 0;

                // Find and assign available code with retry logic
                while (retryCount < maxRetries && newCode == null)
                {
                    // Find an available voucher code
                    var candidateCode = await _unitOfWork.VpnMembershipCodes
                        .FirstOrDefaultAsync(x => x.IsAssigned == false);

                    if (candidateCode == null)
                    {
                        _logger.LogError("No available VPN codes for renewal - code pool exhausted");
                        logEntry.SkippedRenewalsDueToNoAvailableVpnCodes++;
                        return Result.Failure(Errors.VpnMembershipErrors.NoAvailableCodesForRenewal);
                    }

                    // Assign the voucher code (following subscription pattern)
                    candidateCode.IsAssigned = true;
                    candidateCode.CardholderId = user.CardHolderId;
                    candidateCode.SubscriptionDate = DateTime.Now;
                    candidateCode.RenewalDate = DateTime.Now.AddMonths(1);
                    candidateCode.IsUsed = false;

                    // Store the assigned code
                    newCode = candidateCode;
                    break; // Exit retry loop - main method will handle the commit
                }

                if (newCode == null)
                {
                    return Result.Failure(Errors.VpnMembershipErrors.NoAvailableCodesForRenewal);
                }

                // Update VpnMembershipUser with new code
                subscriber.Code = newCode.VoucherCode;

                _logger.LogInformation("Successfully assigned new VPN code {VpnCode} to {UserPhoneNumber} during renewal", newCode.VoucherCode, user.PhoneNumber);

                return Result.Success();

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred during VPN code assignment for renewal: {ErrorMessage}", ex.Message);
                return Result.Failure(Errors.VpnMembershipErrors.CodeAssignmentFailed);
            }
        }
    }
}