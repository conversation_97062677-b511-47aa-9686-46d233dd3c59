﻿namespace C3Pay.Core.Models
{
    public class GeneralSettings
    {
        public string MobileAppHashKey { get; set; }
        public int CachedEntityExpiryInMinutes { get; set; }
        public int MaxFailedCardVerificationAttempts { get; set; }
        public int AutoUnblockMaxAttemptCount { get; set; }
        public string FirstBlackV1PlasticCardId { get; set; }
        public string FirstBlackV2PlasticCardId { get; set; }
        public bool AutoUnblockEnabled { get; set; }
        public bool SkipOtp { get; set; }
        public string Environment { get; set; }
        public string QAUserPhoneNumbers { get; set; }
        public int HRCacheExpiryInMinutes { get; set; }
        public int PrimarySmsProvider { get; set; }
        public string UnemploymentInsuranceVideoURL { get; set; }
        public string UnemploymentInsuranceVideoURLHindi { get; set; }
        public string EmiratesIdStorageURL { get; set; }
        public string QAAutomationPhoneNumbers { get; set; }
        public bool IsMiddleNavExperimentActive { get; set; }
        public bool EnableRedis { get; set; }
        public int MaxDevicesAllowedForBinding { get; set; } 
        public string DeviceIdBindingPrefix { get; set; } 
        public bool IsSecuritySMSAwarenessActive { get; set; } 
        public bool IsSecuritySMSMigrationActive { get; set; }
        public bool IsDbSaveRetryEnabled { get; set; } 
        public string AuthenticationTokenSecretKey { get; set; }
        public string UATPentestPhoneNumbers { get; set; }
        public int DeviceTokenExpirySeconds { get; set; }
        public string C3PayAPIKeyForEsmoService { get; set; }
    }
}
