﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs.UserDtos;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Messages.Signup;
using C3Pay.Core.Models.Messages.User;
using C3Pay.Core.Models.Portal.SalaryAdvance;
using C3Pay.Core.Models.Portal.Users;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Services
{
    public interface IUserService
    {
        Task<ServiceResponse<User>> CreateUser(User newUser, BaseEnums.Version version, string cardNumber, string cvv);
        Task<ServiceResponse<User>> CreateExternalUser(User newUser);
        Task<ServiceResponse<User>> UpdateExternalUser(User user, string phoneNumber, bool isVerified);
        Task<ServiceResponse<Tuple<List<User>, int>>> SearchUsers(SearchUserParameters searchUserParameters, SearchType searchType);
        Task<ServiceResponse<Tuple<List<VerificationUser>, int>>> SearchVerificationUsers(SearchUserParameters searchUserParameters);
        Task<ServiceResponse> BlockUser(User userToBeBlocked, Guid? portalUserId = null, string portalEmailId = null, int? blockReasonId = null);
        Task<ServiceResponse> UnblockUser(User userToBeUnblocked, string comment, Guid? portalUserId = null, string portalEmailId = null);
        Task<ServiceResponse> RemoveUserAccount(Guid userIdToRemove, Guid? portalUserId = null, bool checkIfRmtIsPending = false, bool deactivateRmt = false, bool setCardHolderAsDeleted = false, string portalEmailId = null);
        Task<ServiceResponse> ResetPassword(User userAccount);
        Task<ServiceResponse<bool>> GetUserKYCStatus(Guid id);
        Task<ServiceResponse<User>> GetUserById(Guid id, CancellationToken cancellationToken = default(CancellationToken));
        Task<ServiceResponse<User>> GetUserByIdWithIdentifications(Guid id);
        Task<ServiceResponse<User>> GetUserByExternalId(int externalId);
        Task<ServiceResponse<User>> GetUserByPhoneNumber(string phoneNumber);
        Task<ServiceResponse<UserProfile>> GetProfile(Guid? id = null, string phoneNumber = null, CancellationToken cancellationToken = default(CancellationToken));
        Task<ServiceResponse<User>> GetUserByCitizenId(string citizenId);
        Task<ServiceResponse<User>> GetUserByCardNumber(string cardNumber);
        Task<ServiceResponse<User>> GetUserByC3RegistrationId(string c3RegistrationId);
        Task<ServiceResponse<User>> GetUserByEmiratesId(string emiratesId);
        Task<ServiceResponse<CardNumberEligibilityResult>> GetCardNumberEligibility(string cardNumber);
        Task<ServiceResponse<PhoneEligibilityResult>> GetPhoneNumberEligibility(string cardNumber, bool checkIdentity);
        Task<ServiceResponse<string>> GetPhoneNumberUpdateEligibility(string citizenId, string phoneNumber);
        Task<ServiceResponse<UserCardBalance>> GetUserCardBalance(User user, CancellationToken cancellationToken = default(CancellationToken));
        Task<ServiceResponse<IEnumerable<SecretAnswer>>> GetSecretAnswers(Guid userId);
        Task<ServiceResponse<bool>> UserHasPendingIdentifications(Guid userId);
        Task<ServiceResponse> UpdateUserDeviceToken(User user, string deviceToken);
        Task<ServiceResponse> UpdateUserDeviceId(Guid userId, string deviceId, string deviceModel);
        Task<ServiceResponse> ResetPasswordResetFlag(User user);
        Task<UserBaseDto<EmailUpdateDto>> UpdateUserEmail(User user, string email, bool emailConsent = false);
        Task<ServiceResponse> UpdateSecretAnswers(Guid userId, List<SecretAnswer> secretAnswers);
        Task<ServiceResponse> UpdatePhoneNumber(User user, string phoneNumber);
        Task<ServiceResponse> UpdatePhoneNumberV2(User user, string oldPhoneNumber, string newPhoneNumber, string source);
        Task<ServiceResponse<IEnumerable<VerificationComment>>> GetUserComments(Guid id);
        Task<ServiceResponse> AddUserComment(AddUserComment addUserComment);
        Task<ServiceResponse<string>> GetUserKYCRemarks(Guid id);
        Task<ServiceResponse<bool>> CheckCardOwnership(Guid userId, string cardHolderRef);
        Task<ServiceResponse> DisableAtmPinPrompt(User user);
        Task<ServiceResponse> SetATMPinBlockEndDate(User user, DateTime date);
        Task<ServiceResponse> ProcessUsersSalaryEvents(PushSalaryProcessedEventsDto salaryProcessedEvents);
        Task<ServiceResponse<bool>> IncrementAutoUnblockWrongAttemptsCount(User user);
        Task<ServiceResponse> ResetAutoUnblockWrongAttemptsCount(User user);
        Task<ServiceResponse<Guid>> GetUserIdByPhoneNumber(string phoneNumber);
        Task<ServiceResponse> ActivateCard(Guid userId, Guid? portalUserId = null, string portalEmailId = null);
        Task<ServiceResponse<string>> GetOrGenerateUserReferralCode(Guid userId);
        Task<ServiceResponse> RemoveIdenityIfUserDoesntExist(string phoneNumber);
        Task<ServiceResponse<User>> GetUserDetails(SalaryAdvanceParameter salaryAdvanceParameter);
        Task<ServiceResponse<List<string>>> GetActiveUsersDeviceTokensByCorporateId(string corporateId);
        Task<ServiceResponse<User>> FindUser(Guid userId);
        Task<ServiceResponse<List<User>>> GetUsersByIds(IList<Guid> userId);
        Task<ServiceResponse<Tuple<List<VerificationUser>, int>>> SearchVerificationUsersFullKYC(SearchUserParameters searchUserParameters);
        Task<ServiceResponse<Tuple<List<VerificationUser>, int>>> SearchVerificationUsersPartialKYC(SearchUserParameters searchUserParameters);
        Task<ServiceResponse<Tuple<List<VerificationUser>, int>>> SearchVerificationUsersNoKYC(SearchUserParameters searchUserParameters);
        Task AddOutboxMessage(User user, string type);
        Task<ServiceResponse<CardNumberEligibilityResult>> GetCardAndCvc2Eligibility(string cardNumber, string cvv);
        ServiceResponse<bool> ValidateEmiratesId(string emiratesId);
        Task<ServiceResponse<User>> GetUserByCitizenIdForAllApplications(string citizenId);
        Task<ServiceResponse<bool>> IsVerifiedWithEmiratesId(Guid userId);
        Task<ServiceResponse<List<User>>> GetUsersByCitizenIds(List<string> citizenIds);
        Task<ServiceResponse> UpdateUserDevice(string phoneNumber, string model, string uniqueDeviceId, CancellationToken cancellationToken);
        Task<ServiceResponse<(DeviceBindingStatus, string)>> CheckDeviceIsValidOrNot(string phoneNumber, string uniqueDeviceId, CancellationToken cancellationToken);
        Task<bool> ValidateDeviceToken(string phoneNumber, string passedToken);
        Task UpdateUserCacheForDeviceBinding(string phoneNumber);
        Task<ServiceResponse> ValidateUpdationKYCExpiry();
        Task<ServiceResponse> ValidateAdditionKYCExpiry();
        Task DeactivateRmt(string cardHolderId, string emiratesId, Guid userId, bool belongsToExchangeHouse);
        Task<HardBlockType> GetUserHardBlockType(string cardHolderId);
        Task<ServiceResponse> UpdateEidNumber(UpdateEidNumberDto updateEidNumberDto);



        #region International Phone Number Sign Up
        Task<ServiceResponse<PhoneEligibilityResult>> GetPhoneNumberEligibilityV2(string phoneNumber, bool checkIdentity);
        Task<ServiceResponse<User>> CreateUserV2(User newUser, BaseEnums.Version version, string cardNumber, string cvv);
        #endregion

        Task<ServiceResponse> SetAsLocalUat(User user);
        Task<ServiceResponse> ProcessSalaryGotPaidEvents(IEnumerable<SalaryGotPaidMessageDto> events);
    }
}
