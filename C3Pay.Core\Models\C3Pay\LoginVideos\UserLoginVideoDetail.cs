﻿using System;

namespace C3Pay.Core.Models
{
    public class UserLoginVideoDetail
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public int VideoId { get; set; }
        public DateTime LastSeenDate { get; set; }
        public int ViewCount { get; set; } = 1;
    }

    public class UserVideoDetails
    {
        public int ViewCount { get; set; }
        public bool HasSeenWithinThreshold { get; set; }
        public DateTime? LastSeenDate { get; set; }
    }
}
