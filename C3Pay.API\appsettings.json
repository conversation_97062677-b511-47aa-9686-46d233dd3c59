﻿{
  "ConnectionStrings": {
    "C3PayConnection": "Data Source=(LocalDb)\\MSSQLLocalDB;Initial Catalog=C3Pay;Integrated Security=SSPI",
    "C3PayConnectionReadOnly": "Data Source=(LocalDb)\\MSSQLLocalDB;Initial Catalog=C3PayReadOnly;Integrated Security=SSPI",
    "AzureBlobStorage": "UseDevelopmentStorage=true",
    "AzureAppConfig": "",
    "RedisConnection": "",
    "ServiceBusConnection": ""
  },
  "PasswordValidationRules": {
    "RequiredLength": 6,
    "RequiredUniqueChars": 0,
    "RequireNonAlphanumeric": false,
    "RequireLowercase": false,
    "RequireUppercase": false,
    "RequireDigit": true,
    "RequireLetter": true
  },
  "IpRateLimiting": {
    "EnableEndpointRateLimiting": true,
    "StackBlockedRequests": false,
    "RealIPHeader": "X-Real-IP",
    "ClientIdHeader": "X-ClientId",
    "HttpStatusCode": 429,
    "GeneralRules": [
      {
        "Endpoint": "*:/api/Test/test-ip-limiting",
        "Period": "1h",
        "Limit": 3
      }
    ]
  },
  "Swagger": {
    "Username": "",
    "Password": ""
  },
  "EncryptionSettings": {
    "IsActive": true,
    "PrivateKey": "",
    "PublicKey": ""
  },
  "General": {
    "MobileAppHashKey": "",
    "EnableSwagger": "",
    "DBPoolSize": 1500,
    "CachedEntityExpiryInMinutes": 60,
    "AutoUnblockMaxAttemptCount": 5,
    "AutoUnblockEnabled": true,
    "QAUserPhoneNumbers": "",
    "TestKey": "",
    "PrimarySmsProvider": 2,
    "QAAutomationPhoneNumbers": "",
    "IsMiddleNavExperimentActive": false,
    "MaxDevicesAllowedForBinding ": 2,
    "DeviceIdBindingPrefix": "",
    "IsSecuritySMSAwarenessActive": false,
    "IsSecuritySMSMigrationActive": false,
    "IsDbSaveRetryEnabled": true,
    "AuthenticationTokenSecretKey": "",
    "UATPentestPhoneNumbers": "",
    "DeviceTokenExpirySeconds": 300
  },
  "KeyVault": {
    "Authority": "",
    "KeyIdentifier": ""
  },
  "EDConnect": {
    "Authority": "",
    "ApiName": "",
    "ApiSecret": "",
    "EnableCaching": true,
    "CacheDurationInMinutes": 5
  },
  "EdenredIdentityManager": {
    "BaseAddress": "",
    "Tenant": "AE",
    "Authority": "",
    "ResourceId": "",
    "ClientId": "",
    "ClientSecret": ""
  },
  "AADSecurity": {
    "Authority": "",
    "Audience": "",
    "AllowedClientIds": ""
  },
  "SendGrid": {
    "SenderEmail": "",
    "APIKey": "",
    "Templates": [
      {
        "Name": "BankStatement",
        "TemplateId": ""
      },
      {
        "Name": "RMTProfileCreated",
        "TemplateId": ""
      },
      {
        "Name": "StoreOrderPlaced",
        "TemplateId": ""
      }
    ]
  },
  "EtisalatSMS": {
    "BaseAddress": "",
    "SenderName": "C3Pay",
    "ContentType": "application/json",
    "Username": "",
    "Password": "",
    "Timeout": 0,
    "RetryCount": 0
  },
  "InfobipSMS": {
    "BaseAddress": "",
    "SenderName": "",
    "ContentType": "",
    "Username": "",
    "Password": "",
    "AuthKey": "",
    "AuthKeyBaseUrl": "",
    "SmsMode": "",
    "Timeout": 0,
    "RetryCount": 0
  },
  "CleverTapService": {
    "BaseAddress": "",
    "PassCode": "",
    "ProjectId": "",
    "EventsNames": {
      "MoneyTransferStarted": "RMT_status_started",
      "MoneyTransferPending": "RMT_status_pending",
      "MoneyTransferSuccessful": "v2_transfer_make_transfer_success",
      "AutoReversedBankTransfer": "transfer_reversed",
      "MoneyTransferFailed": "v2_transfer_make_transfer_failed",
      "EmiratesIdVerified": "EmiratesIDVerified",
      "EmiratesIDRejected": "EmiratesIDRejected",
      "MoneyTransferProfileCreated": "RmtProfileCreated",
      "SalaryProccessed": "GotPaid",
      "UnemploymentInsuranceSubscribed": "unemployment_insurance_subscribed",
      "UnEmpInsuranceExternalServerError": "unemployment_ins_status_fail_serverproblems",
      "UnEmpInsuranceExternalWorkerNotFound": "unemployment_ins_status_fail_workerdetailsnotfound",
      "UnEmpInsuranceInsufficientBalanceStatus": "unemployment_ins_insufficient_balance_status",
      "UnEmpInsuranceDisabledAutoPaymentWithDue": "unemployment_ins_toggle_off_installment_status_duedate",
      "UnEmpInsuranceEnabledAutoPaymentWithDue": "unemployment_ins_toggle_on_installment_status_duedate",
      "UnEmpInsuranceFailedAutoPayment": "unemployment_ins_installment_status_fail",
      "UnEmpInsuranceSuceessfulAutoPayment": "unemployment_ins_installment_status_success",
      "KycNotSubmitted": "eid_addition",
      "KycExpiry": "eid_expiry",
      "C3PayPlusAtmWithdrawalFeeReversal": "ATM_reversal_done",
      "MobileRechargeSuccessRatesExp": "Ratexp_recharge_successful",
      "MobileRechargeFailedRatesExp": "Rateexp_recharge_failed",
      "MobileRechargeTargetedOfferSent": "targeted_discount_offer_sent",
      "DirectTransferSendLimitReached": "DirectTransfer_SendLimitReached",
      "MRAutoRenewalFailureEvent": "Auto_renewal_failure",
      "C3PayPlusVoiceMessageEvent": "C3PayPlus_Voice_Message_Status",
      "MobileRechargeFailureEvent": "Mobile_recharge_failure"
    }
  },
  "FirebaseCloudMessaging": {
    "BaseAddress": "https://fcm.googleapis.com/fcm/",
    "SenderId": "",
    "Key": "",
    "RetryCount": 0,
    "Timeout": 0
  },
  "SignzyService": {
    "BaseAddress": "",
    "FileExchangeBaseAddress": "",
    "Id": "",
    "UserId": "",
    "AcceptedSelfieScore": 0.5,
    "AcceptedEIDScore": 0.5,
    "IdealFaceMatchPercentage": 60,
    "IdealNameMatchPercentage": 75,
    "TTL": "1 month",
    "MimeType": "image/png",
    "ContentType": "application/json",
    "ImageQualityTimeout": 5000,
    "FaceMatchTimeout": 5000,
    "ReadDocumentTimeout": 10000
  },
  "AzureOCR": {
    "Endpoint": "",
    "Key": "",
    "WaitTimeInMilliSeconds": 1000
  },
  "AzureFace": {
    "Endpoint": "",
    "Key": ""
  },
  "AzureIdentificationService": {
    "TwoDigitYearMaxLimit": 12,
    "IdealFaceMatchPercentage": 60,
    "IdealNameMatchPercentage": 75,
    "MinimumAllowedEmiratesIdLines": 8,
    "MinimumAllowedPassportLines": 8,
    "TempStorageContainerName": "temp-identifications",
    "EmirateIdStorageContainerName": "emirates-id",
    "PassportStorageContainerName": "passport",
    "SelfieStorageContainerName": "selfies",
    "UseAzureOCR": false,
    "UseAzureFace": false
  },
  "KYCService": {
    "BaseAddress": "",
    "MethodName": "ApproveKYC",
    "Username": "",
    "Password": "",
    "SponsorCode": "",
    "UniqueRef": "",
    "SharedSecret": "",
    "QueueConnectionString": "",
    "QueueName": ""
  },
  "ESMOService": {
    "BaseAddress": "",
    "Authority": "",
    "ClientId": "",
    "ClientSecret": "",
    "Scope": "",
    "QueueConnectionString": "",
    "QueueName": "",
    "RegistrationStatusConnectionString": "",
    "RegistrationStatusQueueName": "",
    "Timeout": 2
  },
  "PPSService": {
    "WebAuthBaseURL": "",
    "WebAuthClientId": "",
    "WebAuthClientSecretkey": "",
    "WebAuthContentType": "",
    "EndpointAddress": "",
    "Username": "",
    "Password": "",
    "SponsorCode": "",
    "CustomerCode": "",
    "SharedSecret": "",
    "Timeout": 2
  },
  "MobileRechargeService": {
    "QueueConnectionString": "",
    "QueueName": "",
    "SynchronizeWithDingSchedule": "",
    "UpdateStatusSchedule": "",
    "MonthlyAmountLimitVerified": 0,
    "MonthlyAmountLimitNotVerified": 0,
    "NickNameLength": 0,
    "TransactionEnvironment": "",
    "CallingCardAccountNumberLive": "",
    "CustomCallingCardName": "Hello and Five",
    "CustomCallingCardCode": "",
    "CustomCallingCardLogoUrl": "",
    "CustomCallingCardValidationRegex": "",
    "DynamicPackageSeperator": "",
    "ServiceBusTopicName": "",
    "ServiceBusSubscriptionName": "",
    "ServiceBusConnectionString": ""
  },
  "DingService": {
    "BaseURL": "",
    "ClientApiKey": "",
    "ContentType": "",
    "RetryCount": 0,
    "SleepDuration": 0,
    "IsRetryEnabled": true
  },
  "MoneyTransferService": {
    "UserMonthlyTransactionAmountLimit": null,
    "UserMonthlyTransactionCountLimit": null,
    "EnableBeneficiarySameCountryDelay": false,
    "MultimediaURL": null,
    "FreeDirectTransferBranches": "",
    "EnableRakMock": true,
    "WUTransactionMinLimitValidationEnabled": "false",
    "NonWUEmiratesIdExpiryGracePeriodDays": 0,
    "WUEmiratesIdExpiryGracePeriodDays": 0,
    "NonWUCorridors": "",
    "RMTStatusFromCreatedToPendingEnabled": "false",
    "LastRaffleWinnerName": "",
    "LastRaffleWinnerTicketNumber": "",
    "RaffleDateString": "",
    "DirectTransferReceiverMaxAmountPerMonth": 10000
  },
  "RAKService": {
    "BeneficiaryQueueConnectionString": "",
    "BeneficiaryQueueName": "",
    "MoneyTransferQueueConnectionString": "",
    "MoneyTransferQueueName": "",
    "MoneyTransferBeneficiaryCount": 0,
    "BaseURL": "",
    "URLPath": "",
    "ClientId": "",
    "clientSecretkey": "",
    "ContentType": "",
    "TokenContentType": "",
    "TokenScope": "",
    "TokenGrantType": "",
    "SSLCertificateName": "",
    "SSLCertificatePassword": "",
    "PaylaodPrivateKey": "",
    "PaylaodPublicKey": "",
    "BanksMaxRecords": "",
    "MaxTransactionTriesCount": 0,
    "MessageProcessInDelay": 0,
    "MoneyTransferBeneficiaryDelayInMins": 0,
    "LoyaltyImplementDate": "",
    "LoyaltyLimitCount": 0,
    "LoyaltyLimitAmount": 0
  },
  "RAKSFTPConnection": {
    "Endpoint": "",
    "Port": 0,
    "Username": "",
    "Password": "",
    "InputRootDirectory": "",
    "OutputRootDirectory": "",
    "TransactionStatusDirectory": "",
    "TransactionBlobContainerName": "",
    "ProfileStatusDirectory": ""
  },
  "ReferralProgramService": {
    "MoneyTransferCountThreshold": null,
    "MoneyTransferAmountThreshold": null,
    "MoneyTransferRewardAmount": null,
    "MoneyTransferReferralProgramStartDate": null,
    "QueueConnectionString": "",
    "QueueName": ""
  },
  "Testing": {
    "PhoneNumbers": "",
    "MRDynamicPackageTestNepalNumbers": "",
    "MRInlineFeeCalculationTestNumbers": ""
  },
  "Multimedia": {
    "AutoPlayMoneyTransferVideoCorporateIds": "",
    "AutoPlayMoneyTransferVideoForAll": false
  },
  "PaykiiService": {
    "BaseAddress": "",
    "APIKey": "",
    "Token": "",
    "CashierId": "",
    "CustomerId": "",
    "Urls": {
      "BillerCatalogUrl": "",
      "SKUCatalogUrl": "",
      "IOCatalogUrl": "",
      "AmountDueUrl": "",
      "ProcessPaymentUrl": "",
      "VerifyPaymentStatusUrl": "",
      "BalanceUrl": "",
      "BillNotificationUrl": "",
      "MobileCarrierLookupUrl": "",
      "DailyFXRatePerBillerTypeUrl": "",
      "BillerFeesCatalogUrl": ""
    },
    "RefreshDataIntervalCronExpression": "",
    "LocationId": 0,
    "PointOfSaleId": 0
  },
  "BillPayment": {
    "IconBaseUrl": "",
    "TransactionEnvironment": "UAT",
    "FxRateRefreshIntervalCronExpression": "",
    "PendingBillsRefreshIntervalCronExpression": " ",
    "GridViewDisplayProviderIds": "",
    "MaximumLocalBillersLimit": 0,
    "MaximumInternationalBillersLimit": 0,
    "MaximumAllowedBillAmountPerTransaction": 0,
    "MaximumAllowedBillAmountPerMonth": 0,
    "NolProviderCode": "",
    "AllowedNolAmountForNotVerified": "",
    "NolRechargeMonthlyLimitForVerified": 0,
    "NolRechargeMonthlyLimitForNotVerified": 0,
    "IconContainerName": "",
    "SalikProviderCode": "",
    "MockUserId": "",
    "AddBillerQueueConnectionString": "",
    "ProcessPaymentQueueConnectionString": "",
    "AddBillerQueueName": "",
    "ProcessPaymentQueueName": "",
    "AmountDueExpireIntervalCronExpression": " ",
    "AmountDueExpiryHours": 0,
    "RemoveCountrywiseFilter": false
  },
  "InfobipVoiceCallSettings": {
    "BaseUrl": "",
    "AppKey": "",
    "FromNumber": "",
    "CallbackUrl": "",
    "CallbackSecret": ""
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "Using": [
      "Serilog.Sinks.OpenTelemetry"
    ],
    "WriteTo": [
      {
        "Name": "OpenTelemetry",
        "Args": {
          "restrictedToMinimumLevel": "Information"
        }
      }
    ]
  },
  "ApplicationInsights": {
    "ConnectionString": ""
  },
  "OpenTelemetry": {
    "ServiceName": "C3Pay.API",
    "ServiceVersion": "1.0.0"
  },
  "Jaeger": {
    "AgentHost": "localhost",
    "AgentPort": 6831,
    "Endpoint": "http://localhost:14268/api/traces"
  },
  "TransactionsB2CService": {
    "Authority": "",
    "ClientId": "",
    "Scope": "",
    "ClientSecret": "",
    "BaseAddress": "",
    "GrantType": "",
    "APIVersion": "",
    "Timeout": 2
  },
  "PayrollService": {
    "BaseAddress": "",
    "Authority": "",
    "ClientId": "",
    "ClientSecret": "",
    "Scope": ""
  },
  "HRService": {
    "Authority": "",
    "BaseAddress": "",
    "ClientId": "",
    "ClientSecret": "",
    "Scope": "",
    "CacheInMinutes": 0
  },
  "Language": {
    "AudioLinks": [
      {
        "Nationality": "",
        "URL": ""
      },
      {
        "Nationality": "",
        "URL": ""
      }
    ]
  },
  "SalaryAdvanceCashbackService": {
    "CashbackAmount": null
  },
  "Rating": {
    "MinimumDaysToShowInApp": 0,
    "MinimumDaysToShowStore": 0
  },
  "Store": {
    "EmailRecepients": ""
  },
  "UnEmploymentInsurance": {
    "ServiceBusTopicName": "",
    "ServiceBusSubscriptionName": "",
    "ServiceBusConnectionString": "",
    "ServiceBusUserTopicName": "users-topic",
    "ServiceBusUserTopicConnectionString": ""
  },
  "DubaiInsuranceService": {
    "BaseUrl": "https://google.com",
    "UserName": "",
    "Password": "",
    "Channel": "",
    "Branch": "",
    "Sector": ""
  },
  "Experiment": {
    "ExperimentNoLoyaltyMaxCountIND": 0,
    "ExperimentNoLoyaltyMaxCountPHL": 0,
    "ExperimentNoLoyaltyMaxCountNPL": 0
  },
  "RenewalCardUpdate": {
    "ServiceBusTopicName": "",
    "ServiceBusSubscriptionName": "",
    "ServiceBusConnectionString": ""
  },
  "LoginVideos": {
    "FrequestUserSlotInterval": 5,
    "NonFrequentUserSlotInterval": 3
  },
  "KycUnblockByPassport": {
    "QueueConnectionString": "",
    "QueueName": ""
  },
  "SanctionScreeningApi": {
    "Address": ""
  },
  "C3PayPlusMembership": {
    "TargetedDiscountCooldownDays": 30,
    "AllowedPhoneNumbers": ""
  },
  "Queues": {
    "SendSmsNotification": "queue:send-sms-notification"
  }
}