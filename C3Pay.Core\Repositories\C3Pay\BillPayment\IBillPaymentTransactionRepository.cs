﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.Structs;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace C3Pay.Core.Repositories.C3Pay
{
    public interface IBillPaymentTransactionRepository : IRepository<BillPaymentTransaction>
    {
        Task<List<BillPaymentTransaction>> FindTransactionsByUserIdAsync(Guid userId, string providerCode, int? skipValue, int? pageSize);
        Task<BillPaymentTransaction> FindTransactionByIdAsync(Guid transactionId);
        Task<BillPaymentTransaction> FindTransactionByIdAndUserAsync(Guid transactionId, Guid userId);
        Task<Tuple<List<BillPaymentTransactionStruct>, int>> Search(List<Expression<Func<BillPaymentTransaction, bool>>> searchBillPaymentParameters, int? page, int? size);
        Task<List<BillPaymentTransaction>> FindPendingTransactionsForTodayAsync(int statusId);
        Task<List<BillPaymentTransaction>> GetMonthTransactions(Guid userId, DateTime monthStart, DateTime monthEnd);

        Task<BillPaymentTransaction> FindLastTransactionWithinFewMinutes(Guid billerId, int minutes);
    }
}
