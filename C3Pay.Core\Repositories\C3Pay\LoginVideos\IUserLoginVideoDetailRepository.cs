﻿using C3Pay.Core.Models;
using Edenred.Common.Core;
using System;
using System.Threading.Tasks;

namespace C3Pay.Core.Repositories.C3Pay.LoginVideos
{
    public interface IUserLoginVideoDetailRepository : IRepository<UserLoginVideoDetail>
    {
        Task<bool> HasSeenLoginVideoWithinDays(Guid userId, int videoId, int days);
        Task AddOrUpdateUserLoginVideosLastSeenDate(Guid userId, int videoId);
        Task<int> GetUserVideoViewCount(Guid userId, int videoId);
        Task<UserVideoDetails> GetUserVideoDetails(Guid userId, int videoId, int thresholdDays);
    }
}
