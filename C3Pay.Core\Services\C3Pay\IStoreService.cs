﻿using C3Pay.Core.Models;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace C3Pay.Core.Services
{
    public interface IStoreService
    {
        Task<ServiceResponse<Order>> CreateNewOrder(Guid productId, User user);
        Task<ServiceResponse<Order>> CreateOrderAddress(OrderAddress orderAddress, Guid orderId);
        Task<ServiceResponse<Order>> GetActiveOrder(Guid userId); 
        Task<ServiceResponse> ClearUserOrders(string userPhoneNumber); 
    }
}
