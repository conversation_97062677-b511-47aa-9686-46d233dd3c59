﻿using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.Linq;

namespace C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses
{
    public class C3PayPlusMembershipDto
    {
        public C3PayPlusMembershipType C3PayPlusTypeId { get; set; }

        public bool IsActive { get; set; }
        public bool? UserHasCanceled { get; set; }

        public string ValidFrom { get; set; }
        public string UserCanceledOn { get; set; }
        public string ValidUntill { get; set; }

        public string Price { get; set; }
        public string PriceVatInclusive { get; set; }
        public string NextBilling { get; set; }
        public string TotalSavings { get; set; }
        public string MembershipEstimatedSavings { get; set; }

        public string FirstName { get; set; }
        public string ProfileImageUrl { get; set; }
        public string MembershipDuration { get; set; }


        public bool DiscountAvailable { get; set; }
        public C3PayPlusMembershipDiscountDto TargetedDiscount { get; set; }
        public C3PayPlusMembershipHeadlineDto Headline { get; set; }
        public List<C3PayPlusMembershipSavingsDto> Savings { get; set; }

        #region Benefits 
        public List<C3PayPlusMembershipBenefitDto> Benefits { get; set; }
        public List<C3PayPlusMembershipBannerBenefitDto> BannerBenefits { get; set; }
        #endregion

        #region Videos
        public List<C3PayPlusMembershipVideoDto> LoginVideos { get; set; }
        public List<C3PayPlusMembershipVideoDto> OnboardingVideos { get; set; }
        public List<C3PayPlusMembershipVideoDto> CancelationVideos { get; set; }
        public List<C3PayPlusMembershipVideoDto> LifeInsuranceOnboardingVideos { get; set; }
        public List<C3PayPlusMembershipVideoDto> LuckyDrawOnboardingVideos { get; set; }
        #endregion

        public C3PayPlusMembershipLifeInsurancePolicyDetailsDto LifeInsurancePolicyDetails { get; set; }
        public C3PayPlusMembershipLuckyDrawDetailsDto LuckyDrawDetailsDto { get; set; }

        public static C3PayPlusMembershipDto Map(C3PayPlusMembership source)
        {
            // Temporary mapping for now.
            var dto = new C3PayPlusMembershipDto()
            {
                C3PayPlusTypeId = source.C3PayPlusTypeId,

                // This will convert from 10.00 to 10.
                Price = source.Price.ToString("0"),

                // This will convert from 10.40 to 10.4.
                PriceVatInclusive = source.PriceVatInclusive.ToString("0.##"),

                Benefits = new List<C3PayPlusMembershipBenefitDto>(),
                BannerBenefits = new List<C3PayPlusMembershipBannerBenefitDto>(),

                Headline = new C3PayPlusMembershipHeadlineDto(),
                Savings = new List<C3PayPlusMembershipSavingsDto>(),

                LoginVideos = new List<C3PayPlusMembershipVideoDto>(),
                OnboardingVideos = new List<C3PayPlusMembershipVideoDto>(),
                CancelationVideos = new List<C3PayPlusMembershipVideoDto>(),
                LifeInsuranceOnboardingVideos = new List<C3PayPlusMembershipVideoDto>(),
                LuckyDrawOnboardingVideos = new List<C3PayPlusMembershipVideoDto>()
            };


            foreach (var benefit in source.Benefits)
            {
                var benefitDto = new C3PayPlusMembershipBenefitDto()
                {
                    Title = benefit.Title,
                    BenefitId = benefit.BenefitId,
                    Subtitle = benefit.Subtitle,

                    SummaryTitle = benefit.SummaryTitle,
                    SummarySubtitle = benefit.SummarySubtitle,

                    SubtitleText1 = benefit.SubtitleText1,
                    SubtitleText2 = benefit.SubtitleText2,
                    SubtitleText3 = benefit.SubtitleText3,

                    IconUrl = benefit.IconUrl,

                    Price = benefit.PriceLabel,
                    DisplayOrder = benefit.DisplayOrder
                };

                if (benefitDto.BenefitId == C3PayPlusMembershipBenefitType.FirstAtmWithdrawalFree)
                {
                    benefitDto.SavingsId = C3PayPlusMembershipSavingType.FreeAtmWithdrawal;
                }
                else if (benefitDto.BenefitId == C3PayPlusMembershipBenefitType.SecuritySms)
                {
                    benefitDto.SavingsId = C3PayPlusMembershipSavingType.SecuritySms;
                }
                else if (benefitDto.BenefitId == C3PayPlusMembershipBenefitType.BalanceEnquiry)
                {
                    benefitDto.SavingsId = C3PayPlusMembershipSavingType.BalanceEnquiry;
                }
                else if (benefitDto.BenefitId == C3PayPlusMembershipBenefitType.FreeMoneyTransfer)
                {
                    benefitDto.SavingsId = C3PayPlusMembershipSavingType.FreeMoneyTransfer;
                }
                else if (benefitDto.BenefitId == C3PayPlusMembershipBenefitType.Cashback)
                {
                    benefitDto.SavingsId = C3PayPlusMembershipSavingType.Cashback;
                }

                dto.Benefits.Add(benefitDto);
            }

            dto.Benefits = dto.Benefits.OrderBy(x => x.DisplayOrder).ToList();


            // Login videos.
            foreach (var video in source.Videos.Where(x => x.VideoType == C3PayPlusMembershipVideoType.Login))
            {
                dto.LoginVideos.Add(new C3PayPlusMembershipVideoDto()
                {
                    Url = video.Url,
                    LanguageCode = video.LanguageCode,
                    IsDefault = video.IsDefault
                });
            }

            // Onboarding videos.
            foreach (var video in source.Videos.Where(x => x.VideoType == C3PayPlusMembershipVideoType.Onboarding))
            {
                dto.OnboardingVideos.Add(new C3PayPlusMembershipVideoDto()
                {
                    Url = video.Url,
                    LanguageCode = video.LanguageCode,
                    IsDefault = video.IsDefault
                });
            }

            // Cancelation videos.
            foreach (var video in source.Videos.Where(x => x.VideoType == C3PayPlusMembershipVideoType.Cancelation))
            {
                dto.CancelationVideos.Add(new C3PayPlusMembershipVideoDto()
                {
                    Url = video.Url,
                    LanguageCode = video.LanguageCode,
                    IsDefault = video.IsDefault
                });
            }

            // Life insurance videos.
            foreach (var video in source.Videos.Where(x => x.VideoType == C3PayPlusMembershipVideoType.LifeInsurance))
            {
                dto.LifeInsuranceOnboardingVideos.Add(new C3PayPlusMembershipVideoDto()
                {
                    Url = video.Url,
                    LanguageCode = video.LanguageCode,
                    IsDefault = video.IsDefault
                });
            }

            // Lucky draw videos.
            foreach (var video in source.Videos.Where(x => x.VideoType == C3PayPlusMembershipVideoType.LuckyDraw))
            {
                dto.LuckyDrawOnboardingVideos.Add(new C3PayPlusMembershipVideoDto()
                {
                    Url = video.Url,
                    LanguageCode = video.LanguageCode,
                    IsDefault = video.IsDefault
                });
            }

            return dto;
        }

        public C3PayPlusMembershipLifeInsurancePolicyDetailsDto GetLifeInsurancePolicyDetails(string policyHolderName, string policyNumber, C3PayPlusMembershipLifeInsuranceNomineeResponseDto nomineeDetails)
        {
            return new C3PayPlusMembershipLifeInsurancePolicyDetailsDto(policyHolderName, policyNumber, nomineeDetails);
        }

        public C3PayPlusMembershipLuckyDrawDetailsDto GetLuckyDrawDetails(string name, string ticketNumber, string luckyDrawDate, string luckyDrawTime)
        {
            var details = new C3PayPlusMembershipLuckyDrawDetailsDto(name, ticketNumber);

            if (string.IsNullOrEmpty(luckyDrawDate) || DateTime.TryParse(luckyDrawDate, out var result) == false)
            {
                // Check if today is a Sunday and before 7PM.
                if (DateTime.Now.DayOfWeek == DayOfWeek.Sunday && DateTime.Now.Hour < 19)
                {
                    // Format the date to show today (Sunday), and 7PM.
                    details.NextWeeklyDrawAt = DateTime.Now.ToString("ddd dd MMM, ") + "7 PM";
                }
                else
                {
                    // Calculate the number of days until the next Sunday.
                    int daysUntilSunday = ((int)DayOfWeek.Sunday - (int)DateTime.Now.DayOfWeek + 7) % 7;
                    var nextSunday = DateTime.Now.AddDays(daysUntilSunday);

                    // Format the date to show today (Sunday), and 7PM.
                    details.NextWeeklyDrawAt = nextSunday.ToString("ddd dd MMM, ") + "7 PM";
                }
            }
            else
            {
                details.NextWeeklyDrawAt = result.ToString("ddd dd MMM, ") + luckyDrawTime;
            }

            return details;
        }
    }
}
