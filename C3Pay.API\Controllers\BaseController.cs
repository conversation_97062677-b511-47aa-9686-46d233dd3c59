﻿using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace C3Pay.API.Controllers
{

    /// <summary>
    /// Base Controller
    /// </summary>
    public class BaseController : ControllerBase
    {
        /// <summary>
        /// 
        /// </summary>
        private IHttpContextAccessor _httpContextAccessor;
        private IUserService _userService;
        internal readonly ILogger _logger;

        /// <summary>
        /// 
        /// </summary>
        public Guid _loggedInUserId;

        /// <summary>
        /// 
        /// </summary>
        public User _user { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="httpContextAccessor"></param>
        /// <param name="userService"></param>
        /// <param name="logger"></param>
        public BaseController(
            IHttpContextAccessor httpContextAccessor,
            IUserService userService,
            ILogger logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// Get Logged In UserId
        /// </summary>
        /// <returns></returns>
        [NonAction]
        public async Task<ActionResult> GetLoggedInUserId()
        {
            var username = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username).Value;

            var userResponse = await _userService.GetUserIdByPhoneNumber(username);

            if (!userResponse.IsSuccessful)
                return this.Unauthorized();

            _loggedInUserId = userResponse.Data;

            _logger.LogInformation(ConstantParam.LoggedInUserIdInformation, _loggedInUserId);

            return null;
        }

        /// <summary>
        /// Get Logged In UserId
        /// </summary>
        /// <returns></returns>
        [NonAction]
        public async Task<ActionResult> GetLoggedInUser()
        {
            var usernameClaim = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username);
            if (usernameClaim == null)
                return this.Unauthorized();

            var username = usernameClaim.Value;
            var userResponse = await _userService.GetUserByPhoneNumber(username);
            if (!userResponse.IsSuccessful)
                return this.Unauthorized();

            _user = userResponse.Data;
            if (_user.ApplicationId != BaseEnums.MobileApplicationId.C3Pay)
                return this.Unauthorized();

            _loggedInUserId = userResponse.Data.Id;
            _logger.LogInformation(ConstantParam.LoggedInUserIdInformation, _loggedInUserId);
            return Ok();
        }
    }
}
