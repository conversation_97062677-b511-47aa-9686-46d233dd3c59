﻿using C3Pay.API.Models;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using C3Pay.Core.Services.Security;
using C3Pay.Services;
using C3Pay.Services.Security;
using Edenred.Common.Core;
using IdentityServer4.AccessTokenValidation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Security.Claims;

namespace C3Pay.API.StartupExtensions
{
    public static class AuthenticationExtension
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void InjectAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<ISecurityService, SecurityService>();

            services.AddAuthentication(IdentityServerAuthenticationDefaults.AuthenticationScheme)
                .AddIdentityServerAuthentication(options =>
                {
                    options.Authority = configuration["EDConnect:Authority"];
                    options.ApiName = configuration["EDConnect:ApiName"];
                    options.ApiSecret = configuration["EDConnect:ApiSecret"];
                    options.EnableCaching = bool.Parse(configuration["EDConnect:EnableCaching"]);
                    options.CacheDuration = TimeSpan.FromMinutes(int.Parse(configuration["EDConnect:CacheDurationInMinutes"]));
                })
                .AddJwtBearer(AuthenticationScheme.AzureActiveDirectory, options =>
                {
                    options.Authority = configuration["AADSecurity:Authority"];

                    options.TokenValidationParameters = new TokenValidationParameters()
                    {
                        ValidAudiences = new List<string> { configuration["AADSecurity:Audience"] },
                    };
                })
                .AddPolicyScheme(AuthenticationScheme.DualAuthentication, "Edenred connect or Azure AD", options =>
                {
                    options.ForwardDefaultSelector = context =>
                    {
                        var authorizationHeader = context.Request.Headers["Authorization"].FirstOrDefault();

                        if (authorizationHeader == null || !authorizationHeader.StartsWith(JwtBearerDefaults.AuthenticationScheme, StringComparison.InvariantCultureIgnoreCase))
                        {
                            return AuthenticationScheme.AzureActiveDirectory;
                        }

                        var jwt = authorizationHeader[7..];
                        var handler = new JwtSecurityTokenHandler();
                        var isAADToken = handler.CanReadToken(jwt);

                        if (!isAADToken)
                        {
                            return IdentityServerAuthenticationDefaults.AuthenticationScheme;
                        }

                        return AuthenticationScheme.AzureActiveDirectory;
                    };
                });

            services.AddAuthorization(options =>
            {
                options.AddPolicy("ApplicationIdEntry", policy =>
                {
                    var allowedClientIds = configuration["AADSecurity:AllowedClientIds"].Split(";");
                    policy.AddAuthenticationSchemes(AuthenticationScheme.AzureActiveDirectory);
                    policy.RequireAssertion(context => context.User.HasClaim(claim =>
                          claim.Type.ToLower() == "appid" &&
                          allowedClientIds.Contains(claim.Value)
                    ));
                });


            });
        }
    }
}
