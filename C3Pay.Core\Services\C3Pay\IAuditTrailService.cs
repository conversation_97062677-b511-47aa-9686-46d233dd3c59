﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.Portal.AuditTrail;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace C3Pay.Core.Services
{
    public interface IAuditTrailService
    {
        Task<ServiceResponse> AddAuditTrail(Guid? portalUserId, string portalEmailId, Guid userId, string action, string actionOn, string iPAddress = null);
        Task<ServiceResponse<Tuple<IList<AuditTrail>, int>>> SearchAuditTrails(AuditTrailParameter auditTrailParameter);
    }
}
