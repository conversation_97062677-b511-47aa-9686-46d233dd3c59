﻿using AutoMapper;
using C3Pay.API.Resources.MoneyTransfer;
using C3Pay.API.Filters;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs.MoneyTransfer;
using C3Pay.Core.Models.DTOs.MoneyTransfer.Lookups;
using C3Pay.Core.Services;
using C3Pay.Core.Services.Security;
using C3Pay.Services.MoneyTransfer;
using Edenred.Common.Core;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using C3Pay.Services.MoneyTransfer.Commands;
using C3Pay.Services.MoneyTransfer.Queries;
using Microsoft.Extensions.Options;
using static C3Pay.Core.Errors;
using User = C3Pay.Core.Models.User;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    public class MoneyTransferController : BaseController
    {
        private readonly IMoneyTransferBeneficiaryService _beneficiaryService;
        private readonly IMoneyTransferService _moneyTransferService;
        private readonly ISecurityService _securityService;
        private readonly ILookupService _lookupService;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IMediator _mediator;
        private readonly IFeatureManager _featureManager;
        private readonly ILogger<MoneyTransferController> _logger;
        private readonly IIdentityService _identityService;
        private readonly GeneralSettings _generalSettings;

        private readonly IConfiguration _configuration;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="moneyTransferService"></param>
        /// <param name="mapper"></param>
        /// <param name="beneficiaryBeneficiaryService"></param>
        /// <param name="lookupService"></param>
        /// <param name="securityService"></param>
        /// <param name="httpContextAccessor"></param>
        /// <param name="userService"></param>
        /// <param name="logger"></param>
        /// <param name="mediator"></param>
        /// <param name="featureManager"></param>
        /// <param name="identityService"></param>
        /// <param name="generalSettings"></param>
        public MoneyTransferController(IMoneyTransferBeneficiaryService beneficiaryBeneficiaryService,
                                       IMoneyTransferService moneyTransferService,
                                       ISecurityService securityService,
                                       ILookupService lookupService,
                                       IMapper mapper,
                                       IHttpContextAccessor httpContextAccessor,
                                       IUserService userService,
                                       ILogger<MoneyTransferController> logger,
                                       IMediator mediator,
                                       IFeatureManager featureManager, IIdentityService identityService, IOptions<GeneralSettings> generalSettings,
                                       IConfiguration configuration) : base(httpContextAccessor, userService, logger)
        {
            _beneficiaryService = beneficiaryBeneficiaryService;
            _moneyTransferService = moneyTransferService;
            _lookupService = lookupService;
            _securityService = securityService;
            _mapper = mapper;
            _userService = userService;
            _mediator = mediator;
            _featureManager = featureManager ?? throw new ArgumentNullException(nameof(featureManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _identityService = identityService;
            _generalSettings = generalSettings.Value;
            _configuration = configuration;
        }

        #region Money Transfer Module

        /// <summary>
        /// Retrieves the list of countries available for money transfer.
        /// </summary>
        /// <returns>A list of CountryDto objects representing available countries for money transfer.</returns>
        /// <response code="200">Returns the list of countries</response>
        [ProducesResponseType(typeof(IEnumerable<CountryDto>), StatusCodes.Status200OK)]
        [HttpGet("countries")]
        public async Task<ActionResult<IEnumerable<CountryDto>>> GetCountriesAsync()
        {
            IEnumerable<Country> countries;
            try
            {
                bool isEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.UseNewGetCountriesQuery);
                countries = isEnabled
                    ? (await _mediator.Send(new GetMoneyTransferCountriesQuery())).Value
                    : (await _lookupService.GetMoneyTransferCountries()).Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while retrieving money transfer countries.");
                countries = Enumerable.Empty<Country>();
            }

            return Ok(_mapper.Map<IEnumerable<CountryDto>>(countries ?? Enumerable.Empty<Country>()));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("reasons")]
        public async Task<ActionResult<IEnumerable<MoneyTransferReasonDto>>> GetMoneyTransferPurpose()
        {
            string language = Request.Headers["x-lang-code"];
            if (await _featureManager.IsEnabledAsync(FeatureFlags.UseNewGetMoneyTransferReasonsQuery))
            {
                var res = await _mediator.Send(new MoneyTransferReasonsQuery(language));
                if ((res.IsSuccess && (res.Value == null || !res.Value.Any())) || res.IsFailure)
                {
                    return Ok(Enumerable.Empty<string>());
                }

                var purposes = _mapper.Map<IEnumerable<MoneyTransferReason>, IEnumerable<MoneyTransferReasonDto>>(res.Value);
                return Ok(purposes);
            }
            else
            {

                var moneytransferpurpose = await this._lookupService.GetMoneyTransferReasons(language);

                if (moneytransferpurpose.Data == null || !moneytransferpurpose.Data.Any())
                {
                    return Ok(Enumerable.Empty<string>());
                }

                var purposeResources = _mapper.Map<IEnumerable<MoneyTransferReason>, IEnumerable<MoneyTransferReasonDto>>(moneytransferpurpose.Data);

                return Ok(purposeResources);
            }
        }

        /// <summary>
        /// Retrieves bank branch lists based on provided parameters
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <param name="country">Country code</param>
        /// <param name="bankName">Bank name</param>
        /// <param name="bankBranchName">Optional bank branch name for filtering</param>
        /// <returns>List of bank branches or appropriate error response</returns>
        /// <response code="200">Returns the list of bank branches</response>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("banks/{userId}/{country}/{bankName}")]
        [ProducesResponseType(typeof(IEnumerable<BankBranchDto>), StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<BankBranchDto>>> GetBankBranchLists(
            [FromRoute] Guid userId,
            [FromRoute] string country,
            [FromRoute] string bankName,
            [FromQuery] string? bankBranchName = null)
        {
            try
            {
                // Verify user authorization
                var doesMatch = await _securityService.UsernameMatchesUser(userId, null, false);
                if (!doesMatch.Data)
                    return Unauthorized(Errors.Common.UserNotExists);

                // Check feature flag
                bool isEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.UseNewBankBranchQuery);
                if (isEnabled)
                {
                    var queryResult = GetBankBranchListsQuery.Create(userId, country, bankName, bankBranchName);
                    if (queryResult.IsFailure)
                        return Ok(Enumerable.Empty<BankBranchDto>());

                    var result = await _mediator.Send(queryResult.Value);
                    if (result.IsFailure || !result.Value.Any())
                        return Ok(Enumerable.Empty<BankBranchDto>());
                    return Ok(result.Value);
                }

                // Old Code
                var bankResponse = await _moneyTransferService.GetBankBranchLists(
                    userId, country, bankName, bankBranchName);
                if (!bankResponse.IsSuccessful && bankResponse.ErrorMessage != ConstantParam.BankListNotFound)
                    return BadRequest(bankResponse.ErrorMessage);
                if (bankResponse.Data?.BankDetails == null || !bankResponse.Data.BankDetails.Any())
                    return Ok(Enumerable.Empty<BankBranchDto>());
                var banksResources = _mapper.Map<IEnumerable<BankBranchDetailsRakModel>, IEnumerable<BankBranchDto>>(
                    bankResponse.Data.BankDetails);
                return Ok(banksResources);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    "Error occurred while retrieving bank branch lists. UserId: {UserId}, Country: {Country}, BankName: {BankName}",
                    userId, country, bankName);
                return Ok(Enumerable.Empty<BankBranchDto>());
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="countryCode"></param>
        /// <returns></returns>
        [HttpGet("{countryCode}/banks")]
        public async Task<ActionResult<IEnumerable<MoneyTransferBankDto>>> GetBanksByCountry(string countryCode)
        {
            if (await _featureManager.IsEnabledAsync(FeatureFlags.UseNewGetBanksByCountryCodeQuery))
            {
                var res = await _mediator.Send(new BanksByCountryCodeQuery(countryCode));
                if (res.IsFailure)
                    return BadRequest(res.Error.Message);
                var banks = _mapper.Map<IEnumerable<MoneyTransferBankDto>>(res.Value);
                return Ok(banks);
            }

            var banksResult = await _lookupService.GetMoneyTransferBanksByCountryCode(countryCode);
            if (banksResult.IsSuccessful == false)
                return BadRequest(banksResult.ErrorMessage);
            var mappedBanks = _mapper.Map<IEnumerable<MoneyTransferBankDto>>(banksResult.Data);
            return Ok(mappedBanks);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("banks/{id}/branches")]
        public async Task<ActionResult<IEnumerable<MoneyTransferBranchDto>>> GetBranchesByBankId(int id)
        {
            if (await _featureManager.IsEnabledAsync(FeatureFlags.UseNewGetBranchesByBankQuery))
            {
                var res = await _mediator.Send(new BranchesByBankQuery(id));
                if (!res.IsSuccess)
                    return BadRequest(res.Error.Message);
                var branches = _mapper.Map<IEnumerable<MoneyTransferBranchDto>>(res.Value);
                return Ok(branches);
            }
            var branchesResult = await _lookupService.GetMoneyTransferBranchesByBankId(id);
            if (branchesResult.IsSuccessful == false)
                return BadRequest(branchesResult.ErrorMessage);
            var mappedBranches = _mapper.Map<IEnumerable<MoneyTransferBranchDto>>(branchesResult.Data);
            return Ok(mappedBranches);
        }

        /// <summary>
        /// Possible results: UserNotExists,CountryNotExists,CountryNotSupported,IFSCCodeNotExists,BankNotExists
        /// </summary>
        /// <param name="userId"></param>        
        /// <param name="ifscCode"></param>        
        /// <returns></returns>               
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("bank/{userId}/{ifscCode}")]
        public async Task<ActionResult<BankBranchDto>> GetBankBranchForIFSCCode(Guid userId, string ifscCode)
        {
            if (string.IsNullOrEmpty(ifscCode))
            {
                return BadRequest(BaseEnums.TransferStatusValidationMessage.IFSCCodeNotExists.ToString());
            }

            var bankresponse = await _moneyTransferService.GetBankBranchDetailsForIFSCCode(userId, ifscCode);

            //If no data found
            if ((bankresponse.IsSuccessful == false) ||
                    (bankresponse.IsSuccessful == true && (bankresponse.Data == null || !bankresponse.Data.BankDetails.Any()))
                )
            {
                return BadRequest(BaseEnums.TransferStatusValidationMessage.IFSCCodeNotExists.ToString());
            }
            var banksResources = _mapper.Map<BankBranchDetailsRakModel, BankBranchDto>(bankresponse.Data.BankDetails[0]);
            return Ok(banksResources);

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ifscCode"></param>
        /// <returns></returns>
        [HttpGet("bank/{ifscCode}/details")]
        public async Task<ActionResult<BankBranchDto>> GetBranchDetailsByIfscCode(string ifscCode)
        {
            if (await _featureManager.IsEnabledAsync(FeatureFlags.UseNewGetBranchDetailsByIfscCodeQuery))
            {
                var res = await _mediator.Send(new GetBranchDetailsByIfscCodeQuery(ifscCode));
                if (res.IsFailure)
                    return BadRequest(res.Error.Message);
                var bankBranchDetails = _mapper.Map<BankBranchDto>(res.Value);
                return Ok(bankBranchDetails);
            }

            var getBranchResult = await _moneyTransferService.GetBranchDetailsByIfscCode(ifscCode);
            if (!getBranchResult.IsSuccessful)
                return BadRequest(getBranchResult.ErrorMessage);
            var branchDetails = _mapper.Map<BankBranchDto>(getBranchResult.Data);
            return Ok(branchDetails);
        }

        /// <summary>
        /// Possible results:
        /// BeneficiaryAlreadyExists
        /// FirstNameNotExists
        /// LastNameNotExists
        /// CountryNotExists
        /// UserNotExists
        /// AccountNumberNotExists
        /// TransferMethodNotExists
        /// ExceedBeneficiaryCountLimit
        /// CountryNotSupported
        /// UserBlocked
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="countryCode"></param>
        /// <param name="firstName"></param>
        /// <param name="lastName"></param>
        /// <param name="transferMethod"></param>
        /// <param name="accountNumber"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("beneficiary-details-iseligible/{userId}/{countryCode}/{firstName}/{lastName}/{transferMethod}")]
        public async Task<ActionResult<bool>> GetEligibilityOfBeneficiaryDetails(Guid userId, string countryCode, string firstName, string lastName, string transferMethod, string accountNumber = null)
        {

            if (string.IsNullOrEmpty(transferMethod))
            {
                return BadRequest(TransferStatusValidationMessage.TransferMethodNotExists.ToString());
            }

            MoneyTransferType? TransferMethod = null;

            if (transferMethod.ToUpper() == BaseEnums.TransferMethod.BANKTRANSFER.ToString())
            {
                TransferMethod = MoneyTransferType.OutsideUAE;
            }
            else if (transferMethod.ToUpper() == BaseEnums.TransferMethod.CASHPICKUP.ToString())
            {
                TransferMethod = MoneyTransferType.RAKMoneyCashPayout;
            }
            else if (transferMethod.ToUpper() == BaseEnums.TransferMethod.DIRECTTRANSFER.ToString())
            {
                TransferMethod = MoneyTransferType.DirectTransfer;
            }

            if (TransferMethod == MoneyTransferType.DirectTransfer)
            {
                var beneficiary = new MoneyTransferBeneficiary()
                {
                    UserId = userId,
                    CountryCode = countryCode,
                    FirstName = firstName,
                    TransferType = MoneyTransferType.DirectTransfer,
                    AccountNumber = accountNumber
                };

                // Validate beneficiary.
                var isValid = await this._beneficiaryService.ValidateBeneficiary(beneficiary);
                if (isValid.IsSuccessful == false)
                {
                    return BadRequest(isValid.ErrorMessage);
                }

                return Ok();
            }

            if (string.IsNullOrEmpty(countryCode))
            {
                return BadRequest(RechargeStatusValidationMessage.CountryNotExists.ToString());
            }

            if (string.IsNullOrEmpty(firstName))
            {
                return BadRequest(TransferStatusValidationMessage.FirstNameNotExists.ToString());
            }

            if (string.IsNullOrEmpty(lastName))
            {
                return BadRequest(TransferStatusValidationMessage.LastNameNotExists.ToString());
            }

            if (!string.IsNullOrEmpty(transferMethod) && transferMethod.ToUpper()
                != BaseEnums.TransferMethod.BANKTRANSFER.ToString() && transferMethod.ToUpper() != BaseEnums.TransferMethod.CASHPICKUP.ToString())
            {
                return BadRequest(TransferStatusValidationMessage.TransferMethodNotExists.ToString());
            }

            if (transferMethod.ToUpper() == BaseEnums.TransferMethod.BANKTRANSFER.ToString() && string.IsNullOrEmpty(accountNumber))
            {
                return BadRequest(TransferStatusValidationMessage.AccountNumberNotExists.ToString());
            }

            var isEligible = await _beneficiaryService.GetEligibilityOfBeneficiaryDetails(userId, countryCode, firstName, lastName, (MoneyTransferType)TransferMethod, accountNumber);
            if (isEligible.IsSuccessful == false)
            {
                return BadRequest(isEligible.ErrorMessage);
            }

            return Ok(isEligible.Data);
        }

        /// <summary>
        /// Possible Results:
        /// BeneficiaryAlreadyExists,
        /// BeneficiaryNotExists
        /// FirstNameNotExists
        /// LastNameNotExists
        /// CountryNotExists
        /// UserNotExists
        /// BankNameNotExists
        /// BankCodeNotExists
        /// AccountNumberNotExists
        /// BankBranchCodeNotExists
        /// TransferMethodNotExists
        /// ExceedBeneficiaryCountLimit
        /// ReasonNotExists
        /// CountryNotSupported
        /// UserBlocked
        /// </summary>
        /// <param name="beneficiaryDto"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("beneficiary")]
        public async Task<ActionResult<PostMoneyTransferBeneficiaryResponseDto>> AddMoneyTransferBeneficiary(PostMoneyTransferBeneficiaryRequestDto beneficiaryDto)
        {

            // Try to match username with the current user.
            var doesMatch = await this._securityService.UsernameMatchesUser(beneficiaryDto.UserId, null, false);
            if (doesMatch.Data == false)
            {
                return Unauthorized();
            }

            var beneficiary = _mapper.Map<PostMoneyTransferBeneficiaryRequestDto, MoneyTransferBeneficiary>(beneficiaryDto);

            await this.GetLoggedInUser();

            var version = Request.Headers["x-source-client-version"];
            var otp = Request.Headers["x-2fa-otp"];

            var isOtpValid = await _beneficiaryService.IsOtpValid(beneficiary, version, otp, _user.PhoneNumber, this._generalSettings.SkipOtp);
            if (isOtpValid == false)
            {
                return Unauthorized(MfaErrors.Invalid2fa.Message);
            }


            var tryAdding = await _beneficiaryService.AddMoneyTransferBeneficiary(beneficiary);
            if (!tryAdding.IsSuccessful)
            {
                return BadRequest(tryAdding.ErrorMessage);
            }

            var result = _mapper.Map<BankTransferBeneficiaryDto>(tryAdding.Data);
            return Ok(result);
        }

        /// <summary>
        /// Possible Results:
        /// BeneficiaryAlreadyExists,
        /// BeneficiaryNotExists
        /// FirstNameNotExists
        /// LastNameNotExists
        /// CountryNotExists
        /// UserNotExists
        /// BankNameNotExists
        /// BankCodeNotExists
        /// AccountNumberNotExists
        /// BankBranchCodeNotExists
        /// TransferMethodNotExists
        /// ExceedBeneficiaryCountLimit
        /// ReasonNotExists
        /// CountryNotSupported
        /// UserBlocked
        /// </summary>
        /// <param name="beneficiaryDto"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("v2/beneficiary")]
        public async Task<ActionResult<PostMoneyTransferBeneficiaryResponseDto>> AddMoneyTransferBeneficiaryV2(PostMoneyTransferBeneficiaryV2RequestDto beneficiaryDto)
        {

            //Try to match username with the current user.
            var doesMatch = await this._securityService.UsernameMatchesUser(beneficiaryDto.UserId, null, false);
            if (doesMatch.Data == false)
            {
                return Unauthorized();
            }

            var beneficiary = _mapper.Map<PostMoneyTransferBeneficiaryV2RequestDto, MoneyTransferBeneficiary>(beneficiaryDto);

            var tryAdding = await _beneficiaryService.AddMoneyTransferBeneficiary(beneficiary, BaseEnums.Version.V2);

            if (!tryAdding.IsSuccessful)
            {
                if (tryAdding.ErrorMessage == ConstantParam.Unauthorized)
                {
                    return Unauthorized();
                }
                return BadRequest(tryAdding.ErrorMessage);
            }

            var result = _mapper.Map<BankTransferBeneficiaryDto>(tryAdding.Data);
            return Ok(result);
        }

        /// <summary>
        /// Possible Resuls: 
        /// TransferMethodNotExists
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="transferMethod"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("beneficiary/{userId}")]
        public async Task<ActionResult<IEnumerable<BankTransferBeneficiaryDto>>> GetUserBeneficiaries(Guid userId, string transferMethod = null)
        {
            // Try to match username with the current user.
            var doesMatch = await this._securityService.UsernameMatchesUser(userId, null, false);
            if (doesMatch.Data == false)
                return Unauthorized();

            if (await _featureManager.IsEnabledAsync(FeatureFlags.UseNewGetUserBeneficiariesQuery))
            {
                var query = new GetUserBeneficiariesQuery(userId, transferMethod);
                var res = await _mediator.Send(query);
                if (res.IsFailure)
                    return BadRequest(res.Error.Message);
                var beneficiaryDtos = _mapper.Map<List<BankTransferBeneficiaryDto>>(res.Value);
                return Ok(beneficiaryDtos);
            }
            var tryGetBeneficiaries = await _beneficiaryService.GetUserMoneyTransferBeneficiaries(userId, transferMethod);
            if (tryGetBeneficiaries.IsSuccessful == false)
                return BadRequest(tryGetBeneficiaries.ErrorMessage);
            var result = _mapper.Map<List<BankTransferBeneficiaryDto>>(tryGetBeneficiaries.Data);
            return Ok(result);
        }

        /// <summary>
        /// Possible Results:
        /// BeneficiaryNotExists
        /// UserNotExists
        /// TransactionIsInProgress
        /// </summary>
        /// <param name="beneficiaryId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpDelete("beneficiary/{beneficiaryId}")]
        public async Task<ActionResult> DeleteMoneyTransferBeneficiary(Guid beneficiaryId)
        {
            // Try to match username with the current user.
            var doesMatch = await this._securityService.UsernameMatchesUserMoneyTransferBeneficiary(beneficiaryId, false);
            if (doesMatch.Data.HasValue == false)
            {
                return Unauthorized();
            }

            var tryDelete = await _beneficiaryService.DeleteMoneyTransferBeneficiary(beneficiaryId, null);
            if (tryDelete.IsSuccessful == false)
            {
                return BadRequest(tryDelete.ErrorMessage);
            }

            return Ok();
        }

        /// <summary>
        /// Possible Results:
        /// BeneficiaryNotExists
        /// UserNotExists
        /// </summary>
        /// <param name="beneficiaryId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPut("beneficiary/approve/{beneficiaryId}")]
        public async Task<ActionResult<DeleteMoneyTransferBeneficiaryResponseDto>> ApproveMoneyTransferBeneficiary(Guid beneficiaryId)
        {
            var securityResult = await this._securityService.UsernameMatchesUserMoneyTransferBeneficiary(beneficiaryId, false);

            if (!securityResult.Data.HasValue)
            {
                return Unauthorized();
            }
            if (await _featureManager.IsEnabledAsync(FeatureFlags.UseNewApproveBeneficiaryCommand))
            {
                var result = await _mediator.Send(new ApproveBeneficiaryCommand(beneficiaryId));
                if (result.IsFailure)
                {
                    return BadRequest(result.Error.Message);
                }
                var mtbeneficiaryResources = _mapper.Map<MoneyTransferBeneficiary, BankTransferBeneficiaryDto>(result.Value);
                return Ok(mtbeneficiaryResources);
            }
            else
            {
                var approvebeneificary = await _beneficiaryService.ApproveMoneyTransferBeneficiary(beneficiaryId, null);

                if (approvebeneificary.IsSuccessful == false)
                {
                    return BadRequest(approvebeneificary.ErrorMessage);
                }

                var mtbeneficiaryResources = _mapper.Map<MoneyTransferBeneficiary, BankTransferBeneficiaryDto>(approvebeneificary.Data);
                return Ok(mtbeneficiaryResources);
            }


        }

        /// <summary>
        /// Possible Results:
        /// UserNotExists
        /// TransferMethodNotExists
        /// CountryNotExists
        /// CountryNotSupported
        /// AmountNotExists
        /// UserNoOfTransactionsLimitReached
        /// UserMonthlyAmountLimitReached
        /// PartnerMaxAmountReached
        /// PartnerMinAmountReached
        /// NoOfTransactionsLimitReached
        /// MonthlyAmountLimitReached
        /// NoOfTransactionsLimitReached
        /// MonthlyAmountLimitReached
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="transferType"></param>
        /// /// <param name="countryCode"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("transfer-limit-iseligible/{userId}/{transferType}/{countryCode}/{amount}")]
        public async Task<ActionResult<bool>> GetTransferLimitEligibility(Guid userId, string transferType, string countryCode, decimal amount)
        {
            var transferEligiblity = await _moneyTransferService.GetTransferLimitEligibility(userId, transferType, countryCode.ToUpper(), amount);
            if (!transferEligiblity.IsSuccessful)
            {
                return BadRequest(transferEligiblity.ErrorMessage);
            }

            return Ok(transferEligiblity.Data);
        }

        /// <summary>
        ///Possible Results:
        ///ReceiverLimitReached
        ///SendAmountLimitReached
        /// <param name="beneficiaryId"></param>
        /// <param name="transferType"></param>
        /// <param name="amount"></param>
        ///</summary>
        [Authorize]
        [HttpGet("direct-transfer-receiver-eligibility/{beneficiaryId}/{transferType}/{amount}")]
        public async Task<ActionResult<DirectTransferReceiverEligibilityResponseDto>> GetDirectTransferReceiverLimitEligibility(Guid beneficiaryId, string transferType, decimal amount)
        {
            await this.GetLoggedInUser();

            if (_user == null)
                return Unauthorized("");

            // Try to match username with the current user
            var doesMatch = await this._securityService.UsernameMatchesUserMoneyTransferBeneficiary(beneficiaryId, false);
            if (doesMatch.Data.HasValue == false)
                return Unauthorized();

            if (transferType.ToUpper() != BaseEnums.TransferMethod.DIRECTTRANSFER.ToString())
                return Unauthorized();

            var transferEligiblity = await _moneyTransferService.GetDirectTransferReceiverLimitEligibility(beneficiaryId, amount, _user);

            if (!transferEligiblity.IsSuccessful)
                return BadRequest(transferEligiblity.ErrorMessage);

            string identifier = "DirectTransferSenderLimit";

            var response = new DirectTransferReceiverEligibilityResponseDto()
            {
                EligibilityStatus = transferEligiblity.Data,
                ReceiverEligibilityVideo = await GetDirectTransferReceiverLimitVideoUrls(_user, identifier)
            };

            return Ok(response);
        }

        /// <summary>
        ///</summary>
        [Authorize]
        [HttpGet("direct-transfer-receiver-eligibility/get-receiver-limit-video")]
        public async Task<ActionResult<List<DirectTransferReceiverLimitVideo>>> GetDirectTransferReceiverLimitVideo()
        {
            string identifier = "DirectTransferReceiverLimit";
            await this.GetLoggedInUser();

            if (_user == null)
                return Unauthorized("");

            var videoResources = await GetDirectTransferReceiverLimitVideoUrls(_user, identifier);

            return videoResources;
        }

        /// <summary>
        /// Possible results:  BeneficiaryNotExists,  UserNotExists,InvalidToCurrency,AmountNotExists,TransferMethodNotExists,FxRateNotFoundFromRak
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="toCurrency"></param>
        /// <param name="amount"></param>
        /// <param name="transferMethod"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("fx-rate/{userId}/{toCurrency}/{amount}")]
        public async Task<ActionResult<MoneyTransferFxRateResponseDto>> GetFxRates(Guid userId, string toCurrency, decimal amount, string transferMethod = null)
        {

            var fxratesresponse = await _moneyTransferService.GetFxRates(userId, toCurrency, amount, transferMethod);
            if (fxratesresponse.IsSuccessful == false)
            {
                return BadRequest(fxratesresponse.ErrorMessage);
            }

            var fxratesresponseresource = _mapper.Map<FxRateResponseRakModel, MoneyTransferFxRateResponseDto>(fxratesresponse.Data);

            return Ok(fxratesresponseresource);
        }

        /// <summary>
        /// Possible results: UserNotExists
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("free-transfer-details/{userId}")]
        public async Task<ActionResult<MoneyTransferFreeTransferDetailsDto>> GetUserFreeTransferEigibilityLevel(Guid userId, CancellationToken cancellationToken = default(CancellationToken))
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            var freetransfereligibilityresponse = await _moneyTransferService.GetUserFreeTransferEigibilityLevel(userId, cancellationToken);

            if (freetransfereligibilityresponse.IsSuccessful == false)
            {
                return BadRequest(freetransfereligibilityresponse.ErrorMessage);
            }

            var fxratesresponseresource = _mapper.Map<MoneyTransferFreeTransferEligiblity, MoneyTransferFreeTransferDetailsDto>(freetransfereligibilityresponse.Data);
            return Ok(fxratesresponseresource);
        }

        /// <summary>
        /// Possible Results:
        /// BeneficiaryNotExists
        /// UserNotExists
        /// UserNoOfTransactionsLimitReached
        /// UserMonthlyAmountLimitReached
        /// PartnerMinAmountReached
        /// AmountNotExists
        /// SendCurrencyNotExists
        /// PartnerMaxAmountReached
        /// NoOfTransactionsLimitReached
        /// MonthlyAmountLimitReached
        /// InvalidCardNumber
        /// InvalidCardSerialNumber
        /// SendCurrencyNotExists
        /// ReceiveCurrencyNotExists
        /// SendAmountNotExists
        /// ReceiveAmountNotExists
        /// ConversionRateNotExists
        /// ReferralCodeNotExists
        /// </summary>
        /// <param name="transferRequestDto"></param>
        /// <param name="transferMethod"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("send-money")]
        public async Task<ActionResult<PostMoneyTransferResponseDto>> SendMoneyTransfer(PostMoneyTransferRequestDto transferRequestDto, string transferMethod = null)
        {
            // Try to match username with the current user.
            var doesMatch = await this._securityService.UsernameMatchesUserMoneyTransferBeneficiary(transferRequestDto.BeneficiaryId, false);
            if (doesMatch.Data.HasValue == false)
            {
                return Unauthorized();
            }

            var moneyTransferTransaction = _mapper.Map<PostMoneyTransferRequestDto, MoneyTransferTransaction>(transferRequestDto);

            if (string.IsNullOrWhiteSpace(transferMethod) == false)
            {
                transferMethod = transferMethod.ToUpper();
                if (transferMethod == TransferMethod.DIRECTTRANSFER.ToString().ToUpper())
                {
                    moneyTransferTransaction.TransferType = TransactionType.Direct;
                }
            }

            var trySendMoney = await _moneyTransferService.SendMoneyTransfer(moneyTransferTransaction);
            if (trySendMoney.IsSuccessful == false)
            {
                return BadRequest(trySendMoney.ErrorMessage);
            }

            var result = _mapper.Map<MoneyTransferDto>(trySendMoney.Data);
            result.TransferSummary = trySendMoney.Data.TransferSummary;

            return Ok(result);
        }



        [Authorize]
        [HttpPost("v2/send-money")]
        [Idempotency("moneytransferrequests", "send-money")]
        public async Task<ActionResult<PostMoneyTransferResponseDto>> SendMoneyTransferV2(PostMoneyTransferRequestDto transferRequestDto, string transferMethod = null)
        {
            await this.GetLoggedInUser();

            var beneficiaryDetails = await _moneyTransferService.GetBeneficiaryDetailsToPromptOtp(transferRequestDto.BeneficiaryId);

            if (beneficiaryDetails == null || beneficiaryDetails.Item2 == null)
                return BadRequest(MoneyTransfer.BeneficiaryDetailsValidationError.Message);

            // If Wallet or Cashpick : Always prompt OTP
            // If bank Transfer and First Transfer for this Beneficiary : Prompt OTP 
            if ((beneficiaryDetails.Item2 == MoneyTransferType.OutsideUAE && beneficiaryDetails.Item1) || beneficiaryDetails.Item2 == MoneyTransferType.Wallet || beneficiaryDetails.Item2 == MoneyTransferType.RAKMoneyCashPayout)
            {
                var otpFromHeader = Request.Headers["x-2fa-otp"];
                var username = _user.PhoneNumber;
                if (string.IsNullOrWhiteSpace(otpFromHeader) || string.IsNullOrWhiteSpace(username))
                {
                    return Unauthorized(MfaErrors.Invalid2fa.Message);
                }

                // validate OTP
                var verificationResult = await this._identityService.VerifyPhoneAsync(username, otpFromHeader);

                if (verificationResult.IsSuccessful == false)
                {
                    if (this._generalSettings.SkipOtp == false && otpFromHeader != "123456" && otpFromHeader != "696969")
                    {
                        return Unauthorized(MfaErrors.Invalid2fa.Message);
                    }
                }
            }

            var result = await SendMoneyTransfer(transferRequestDto, transferMethod);
            return result;
        }

        /// <summary>
        /// Possible Resuls:
        /// If status was failed => remarks will have validation error messages.
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="transferMethod"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("transactions/{userId}")]
        public async Task<ActionResult<IEnumerable<MoneyTransferDto>>> GetUserTransactions(Guid userId, string transferMethod = null, int? pageSize = null, int? pageNumber = null, CancellationToken cancellationToken = default(CancellationToken))
        {
            string language = Request.Headers["x-lang-code"];
            language = string.IsNullOrWhiteSpace(language) ? "en" : language;
            // Try to match username with the current user.
            var doesMatch = await this._securityService.UsernameMatchesUser(userId, null, false);
            if (doesMatch.Data == false)
            {
                return Unauthorized();
            }

            MoneyTransferType? moneyTransferType = null;
            if (!string.IsNullOrEmpty(transferMethod))
            {
                moneyTransferType = (MoneyTransferType)Enum.Parse(typeof(TransferMethod), transferMethod, true);
            }

            var tryGetTransactions = await _moneyTransferService.GetUserTransactions(userId, moneyTransferType, pageSize, pageNumber, language, cancellationToken);
            if (tryGetTransactions.IsSuccessful == false)
            {
                return BadRequest(tryGetTransactions.ErrorMessage);
            }

            return Ok(_mapper.Map<List<MoneyTransferDto>>(tryGetTransactions.Data));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("transaction-receipt/{transactionId}")]
        public async Task<ActionResult<MoneyTransferReceiptDto>> GetTransactionReceipt(Guid transactionId)
        {
            string language = Request.Headers["x-lang-code"];
            language = string.IsNullOrWhiteSpace(language) ? "en" : language;

            var securityResult = await this._securityService.UsernameMatchesUserMoneyTransferTransaction(transactionId, false);

            if (!securityResult.Data.HasValue)
            {
                return Unauthorized();
            }

            var transactionlist = await _moneyTransferService.GetTransactionReceipt(transactionId, language);

            if (transactionlist.Data == null)
            {
                return Ok(Enumerable.Empty<string>());
            }

            var transactionlistResources = _mapper.Map<MoneyTransferReceiptDto>(transactionlist.Data);

            return Ok(transactionlistResources);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transferMethod"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("settings")]
        public async Task<ActionResult<Dictionary<string, string>>> GetSettings(string transferMethod)
        {
            await GetLoggedInUser();

            transferMethod = transferMethod?.Trim()?.ToUpper();
            if (string.IsNullOrEmpty(transferMethod))
            {
                return BadRequest(TransferStatusValidationMessage.TransferMethodNotExists.ToString());
            }

            transferMethod = transferMethod?.Trim()?.ToUpper();

            MoneyTransferType moneyTransferType;
            if (transferMethod == TransferMethod.BANKTRANSFER.ToString())
            {
                moneyTransferType = MoneyTransferType.OutsideUAE;
            }
            else if (transferMethod == TransferMethod.CASHPICKUP.ToString())
            {
                moneyTransferType = MoneyTransferType.RAKMoneyCashPayout;
            }
            else if (transferMethod == TransferMethod.DIRECTTRANSFER.ToString())
            {
                moneyTransferType = MoneyTransferType.DirectTransfer;
            }
            else
            {
                return BadRequest(TransferStatusValidationMessage.TransferMethodNotExists.ToString());
            }

            var tryGetSettings = _beneficiaryService.GetSettings(moneyTransferType, _user.CardHolder.CorporateId);
            if (tryGetSettings.IsSuccessful == false)
            {
                return BadRequest(tryGetSettings.ErrorMessage);
            }
            return Ok(tryGetSettings.Data);
        }

        /// <summary>
        /// Get Multimedia Resources
        /// </summary>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("multimedia")]
        public async Task<ActionResult<List<MultimediaDto>>> GetMultimediaResources([FromQuery] string identifier,
                                                                                    [FromQuery] MultimediaType? type,
                                                                                    [FromQuery] int? feature)
        {
            await GetLoggedInUser();
            string languageCode = Request.Headers["x-lang-code"];

            var files = await _lookupService.GetMultimediaResources(_user.CardHolderId, _user.CardHolder?.Nationality, identifier, type, languageCode, feature);
            if (files.IsSuccessful == false)
            {
                return BadRequest(files.ErrorMessage);
            }

            return Ok(files.Data);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("{userId}/cancel/{transactionId}")]
        public async Task<ActionResult<MoneyTransferReceiptDto>> CancelTransaction(Guid userId, Guid transactionId)
        {
            // Try to match username with the current user.
            var doesMatch = await this._securityService.UsernameMatchesUser(userId, null, false);
            if (doesMatch.Data == false)
            {
                return Unauthorized();
            }
            var user = await _userService.GetUserById(userId);
            var tryCancelTransaction = await _moneyTransferService.CancelTransfer(user.Data, transactionId);
            if (tryCancelTransaction.IsSuccessful == false)
            {
                return BadRequest(tryCancelTransaction.ErrorMessage);
            }

            return Ok();
        }

        /// <summary>
        /// Gets the comparison between C3Pay and Exchange house
        /// </summary>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("rateComparison")]
        public async Task<ActionResult<MoneyTransferRateComparison>> GetRateComparisonDetails()
        {
            await this.GetLoggedInUser();

            var tryGettingComparisonDetails = await _moneyTransferService.GetRateComparisonDetails(_user.Id, _user.CardHolder.Nationality);
            if (tryGettingComparisonDetails.IsSuccessful == false)
            {
                return BadRequest(tryGettingComparisonDetails.ErrorMessage);
            }

            return Ok(tryGettingComparisonDetails.Data);
        }

        /// <summary>
        /// Enable the guided walkthrough
        /// </summary>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("guided-walkthrough/enable")]
        public async Task<ActionResult<MoneyTransferRateComparison>> EnableGuidedWalkthrough()
        {
            await this.GetLoggedInUser();
            string languageCode = Request.Headers["x-lang-code"];

            var enableGuidedWalkthrough = await _moneyTransferService.EnableGuidedWalkthrough(_user, languageCode);
            if (enableGuidedWalkthrough.IsSuccessful == false)
            {
                return BadRequest(enableGuidedWalkthrough.ErrorMessage);
            }

            return Ok(enableGuidedWalkthrough.Data);
        }

        /// <summary>
        /// Get Recent Transfers
        /// </summary>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("repeat-transfers/{userBalance}")]
        public async Task<ActionResult<List<RepeatTransfer>>> GetRepeatTransfers([FromRoute] decimal userBalance)
        {
            await this.GetLoggedInUser();

            var repeatTransfers = await _moneyTransferService.GetRepeatTransfers(_user.Id, userBalance);
            if (!repeatTransfers.IsSuccessful)
            {
                return BadRequest(repeatTransfers.ErrorMessage);
            }

            return Ok(repeatTransfers.Data);
        }

        /// <summary>
        /// Saves the suspicious information
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("suspicious-information")]
        public async Task<ActionResult<MoneyTransferSmartDefaultDto>> SaveSuspiciousInformation([FromBody] SaveMoneyTransferSuspiciousInformationDto request)
        {
            var additionalInformation = _mapper.Map<MoneyTransferSuspiciousInformation>(request);
            var result = await _moneyTransferService.SaveSuspiciousInformation(additionalInformation);
            if (!result.IsSuccessful)
            {
                return BadRequest(result.ErrorMessage);
            }

            return Ok(result.Data);
        }

        /// <summary>
        /// Get Smart Default
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("smart-default")]
        public async Task<ActionResult<MoneyTransferSmartDefaultDto>> GetSmartDefault(MoneyTransferSmartDefaultRequestDto request)
        {
            var result = await _moneyTransferService.GetSmartDefault(request);
            if (!result.IsSuccessful)
            {
                return BadRequest(result.ErrorMessage);
            }

            return Ok(result.Data);
        }

        [HttpDelete("cache/{key}")]
        public async Task<ActionResult> ClearCache(string key)
        {
            var tryClearCache = await this._moneyTransferService.ClearCache(key);
            if (tryClearCache.IsSuccessful == false)
            {
                return BadRequest(tryClearCache.ErrorMessage);
            }

            return Ok();
        }

        [Authorize]
        [HttpGet("corridors")]
        public async Task<ActionResult<IEnumerable<RemittanceDestinationDto>>> GetCorridors()
        {
            await this.GetLoggedInUser();

            if (await _featureManager.IsEnabledAsync(FeatureFlags.UseNewGetCorridorsQuery))
            {
                var res = await _mediator.Send(new MoneyTransferCorridorsQuery(_user.CardHolderId));
                if (res.IsFailure)
                    return BadRequest(res.Error.Message);
                return Ok(res.Value);
            }
            var tryGetCorridors = await this._moneyTransferService.GetCorridors(_user.CardHolderId);
            if (tryGetCorridors.IsSuccessful == false)
                return BadRequest(tryGetCorridors.ErrorMessage);
            return Ok(tryGetCorridors.Data);
        }



        /// <summary>
        /// Gets the field groups for money transfer based on delivery method
        /// </summary>
        /// <param name="deliveryMethodId">The ID of the delivery method</param>
        /// <returns>List of field groups containing form fields for money transfer</returns>
        /// <response code="200">Returns the list of field groups</response>
        /// <response code="400">If there was an error retrieving the field groups</response>
        [HttpGet("field-groups")]
        public async Task<ActionResult<IEnumerable<FieldGroupDto>>> GetFieldGroups(int deliveryMethodId)
        {
            var action = await this.GetLoggedInUser();
            if (action is UnauthorizedResult)
                return Unauthorized();

            string languageCode = Request.Headers["x-lang-code"];
            if (await _featureManager.IsEnabledAsync(FeatureFlags.UseNewGetFieldGroupsQuery))
            {
                var query = new GetFieldGroupsQuery(deliveryMethodId, languageCode, _user.CardHolder);
                var result = await _mediator.Send(query);
                if (result.IsFailure)
                    return BadRequest(result.Error.Message);
                return Ok(result.Value);
            }

            var fieldGroups = await this._moneyTransferService.GetFieldGroups(deliveryMethodId, languageCode, _user.CardHolder);
            if (fieldGroups.IsSuccessful == false)
                return BadRequest(fieldGroups.ErrorMessage);

            return Ok(fieldGroups.Data);
        }

        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("beneficiary/validate")]
        public async Task<ActionResult<ReviewBeneficiaryDto>> ValidateBeneficiary(PostBeneficiaryRequest request)
        {
            string languageCode = Request.Headers["x-lang-code"];
            var tryValidateBeneficiary = await this._moneyTransferService.ValidateBeneficiary(request, languageCode);
            if (tryValidateBeneficiary.IsSuccessful == false || tryValidateBeneficiary.Data.Successful == false)
            {
                return BadRequest(tryValidateBeneficiary.Data);
            }

            return Ok(tryValidateBeneficiary.Data);
        }

        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("v3/beneficiary")]
        public async Task<ActionResult<MoneyTransferBeneficiary>> AddBeneficiary(PostBeneficiaryRequest request)
        {

            if (await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferUseNewAddBeneficiaryCommand))
            {
                var action = await this.GetLoggedInUser();
                if (action is UnauthorizedResult)
                    return Unauthorized();
                var commandResult = AddBeneficiaryCommand.Create(request, _user);
                if (commandResult.IsFailure)
                    return BadRequest(commandResult.Error.Message);

                var beneficiaryCreationResult = await _mediator.Send(commandResult.Value);
                if (beneficiaryCreationResult.IsSuccess)
                    return Ok(_mapper.Map<BankTransferBeneficiaryDto>(beneficiaryCreationResult.Value));
                else
                    return BadRequest(beneficiaryCreationResult.Error.Message);
            }

            var tryAddBeneficiary = await _moneyTransferService.AddBeneficiary(request);
            if (tryAddBeneficiary.IsSuccessful == false)
            {
                return BadRequest(tryAddBeneficiary.ErrorMessage);
            }

            var result = _mapper.Map<BankTransferBeneficiaryDto>(tryAddBeneficiary.Data);
            return Ok(result);
        }

        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("transfer/validate")]
        public async Task<ActionResult<ReviewTransferDto>> ValidateTransfer(PostTransferDto transferRequestDto)
        {
            var action = await GetLoggedInUser();
            if (action is UnauthorizedResult || _user == null)
                return Unauthorized();

            string languageCode = Request.Headers["x-lang-code"];
            var tryValidateTransfer = await this._moneyTransferService.ValidateTransfer(transferRequestDto, languageCode, _user);
            if (tryValidateTransfer.IsSuccessful == false || tryValidateTransfer.Data.Successful == false)
                return BadRequest(tryValidateTransfer.ErrorMessage);

            return Ok(tryValidateTransfer.Data);
        }


        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("rate/fetch")]
        public async Task<ActionResult<FetchRateDto>> FetchRate(string amount, string targetCurrency, string countryCode, string transferMethod)
        {
            var tryFecthRates = await this._moneyTransferService.FetchRate(amount, targetCurrency, countryCode, transferMethod);
            if (tryFecthRates.IsSuccessful == false)
            {
                return BadRequest(tryFecthRates.ErrorMessage);
            }

            return Ok(tryFecthRates.Data);
        }

        /// <summary>
        /// Get Raffle Ticket
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet("raffle-ticket")]
        public async Task<IActionResult> GetRaffleTicket()
        {
            await GetLoggedInUser();

            var raffle = await this._moneyTransferService.GetRaffleTicket(_user.Id);
            return Ok(raffle);
        }

        /// <summary>
        /// Spin the wheel
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet("spin-the-wheel")]
        public async Task<IActionResult> GetSpinTheWheel()
        {
            await GetLoggedInUser();

            var raffle = await this._moneyTransferService.GetSpinTheWheel(_user.Id);
            return Ok(raffle);
        }

        /// <summary>
        /// Get Acquistion GoldIncentive TicketInfo
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet("GoldIncentive/TicketInfo")]
        public async Task<IActionResult> GetAcquistionGoldIncentiveTicketInfo()
        {
            await GetLoggedInUser();
            if (_user == null) return this.Unauthorized("");

            var command = new GetAcquisitionGoldIncentiveTicketInfoQuery()
            {
                User = _user,
            };
            var result = await _mediator.Send(command);
            if (result.IsSuccess)
                return Ok(result.Value);
            else if (result.Error == Errors.Common.NoContent)
                return NoContent();
            else
                return BadRequest(result.Error.Message);
        }

        /// <summary>
        /// Get Acquistion Gold Incentive Details
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet("GoldIncentive/Details")]
        public async Task<IActionResult> GetAcquistionGoldIncentiveDetails()
        {
            await GetLoggedInUser();
            if (_user == null) return this.Unauthorized("");

            var command = new GetAcquisitionGoldIncentiveDetailsQuery()
            {
                User = _user,
            };
            var result = await _mediator.Send(command);
            if (result.IsSuccess)
                return Ok(result.Value);
            else if (result.Error == Errors.Common.NoContent)
                return NoContent();
            else
                return BadRequest(result.Error.Message);
        }

        /// <summary>
        /// Get Dynamic Screen For Add Beneficiary
        /// </summary>
        /// <param name="deliveryMethodId"></param>
        /// <returns></returns>
        [Authorize]
        [HttpGet("dynamic-screens")]
        public async Task<IActionResult> GetDynamicScreens(int deliveryMethodId)
        {
            await GetLoggedInUser();
            if (_user == null) return this.Unauthorized("");
            string languageCode = Request.Headers["x-lang-code"];

            var query = new GetDynamicScreensQuery(deliveryMethodId, languageCode);
            var result = await _mediator.Send(query);
            if (result.IsFailure)
                return Problem(result.Error.Message);
            return Ok(result.Value);
        }

        #endregion

        #region Private Methods
        private async Task<List<DirectTransferReceiverLimitVideo>> GetDirectTransferReceiverLimitVideoUrls(User _user, string identifier)
        {
            var hindiNationalities = new HashSet<string> { nameof(BaseEnums.MultimediaCountry.PAK), nameof(BaseEnums.MultimediaCountry.IND), nameof(BaseEnums.MultimediaCountry.BGD), nameof(BaseEnums.MultimediaCountry.NPL) };
            var userShouldSeeHindiVideo = hindiNationalities.Contains(_user.CardHolder.Nationality);

            var mobileRechargeRenewalMultimediaResources = await _lookupService.GetMultimediaResources(feature: (int)FeatureType.MoneyTransfer, identifier: identifier).ConfigureAwait(false);

            bool defaultAlreadySet = false;
            return mobileRechargeRenewalMultimediaResources.Data
                .Select(resource => new DirectTransferReceiverLimitVideo
                {
                    LanguageCode = resource.Language,
                    Url = resource.Url,
                    IsDefault = ((resource.Language == "hi" && userShouldSeeHindiVideo)
                                    || (resource.Language != "hi" && !userShouldSeeHindiVideo))
                                && !defaultAlreadySet && (defaultAlreadySet = true)
                })
                .ToList();
        }
        #endregion
    }
}
