﻿using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using C3Pay.Services;
using Edenred.Common.Core;
using Edenred.Common.Services;
using FluentAssertions.Common;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Xunit;


public class MobileRechargeServiceTests
{

    private readonly Mock<IUnitOfWork> _unitOfWorkMock = new();
    private readonly Mock<IDingService> _dingServiceMock = new();
    private readonly Mock<IPPSWebAuthService> _ppsServiceMock = new();
    private readonly Mock<IIdentityService> _identityServiceMock = new();
    private readonly Mock<IMessagingQueueService> _messagingQueueServiceMock = new();
    private readonly Mock<IOptions<MobileRechargeServiceSettings>> _settingsMock = new();
    private readonly Mock<ILogger<MobileRechargeService>> _loggerMock = new();
    private readonly Mock<IUnitOfWorkReadOnly> _unitOfWorkReadOnlyMock = new();
    private readonly Mock<IAuditTrailService> _auditTrailServiceMock = new();
    private readonly Mock<IOptions<TestingSettings>> _testingSettingsMock = new();
    private readonly Mock<IFeatureManager> _featureManagerMock = new();
    private readonly Mock<ILookupService> _lookupServiceMock = new();
    private readonly Mock<IAnalyticsPublisherService> _analyticsPublisherMock = new();
    private readonly Mock<ITextMessageSenderService> _textMessageSenderMock = new();
    private readonly Mock<IPushNotificationSenderService> _pushNotificationSenderMock = new();
    private readonly Mock<IDistributedCache> _cacheMock = new();

    private readonly MobileRechargeService _service;

    public MobileRechargeServiceTests()
    {
        _settingsMock.Setup(s => s.Value).Returns(new MobileRechargeServiceSettings());
        _testingSettingsMock.Setup(s => s.Value).Returns(new TestingSettings());


        _service = new MobileRechargeService(
            _unitOfWorkMock.Object,
            _dingServiceMock.Object,
            _ppsServiceMock.Object,
            _identityServiceMock.Object,
            _messagingQueueServiceMock.Object,
            _settingsMock.Object,
            _loggerMock.Object,
            _unitOfWorkReadOnlyMock.Object,
            _auditTrailServiceMock.Object,
            _testingSettingsMock.Object,
            _featureManagerMock.Object,
            _lookupServiceMock.Object,
            _analyticsPublisherMock.Object,
            _textMessageSenderMock.Object,
            _pushNotificationSenderMock.Object,
            _cacheMock.Object
        );
    }

    #region UpdateOperator Tests

    [Fact]
    public async Task UpdateOperator_ShouldReturnFailure_WhenBeneficiaryNotFound()
    {
        var requestId = Guid.NewGuid();
        _unitOfWorkMock.Setup(u => u.MobileRechargeBeneficiaries.GetByIdAsync(requestId))
            .ReturnsAsync((MobileRechargeBeneficiary)null);

        // Act
        var result = await _service.UpdateOperator(requestId);


        Assert.False(result.IsSuccessful);
        Assert.Equal("Beneficiary not found.", result.ErrorMessage);
    }

    [Fact]
    public async Task UpdateOperator_ShouldReturnFailure_WhenAccountNumberIsMissing()
    {
        var requestId = Guid.NewGuid();
        var beneficiary = new MobileRechargeBeneficiary { AccountNumber = null };
        _unitOfWorkMock.Setup(u => u.MobileRechargeBeneficiaries.GetByIdAsync(requestId))
            .ReturnsAsync(beneficiary);

        var result = await _service.UpdateOperator(requestId);

        Assert.False(result.IsSuccessful);
        Assert.Equal("Beneficiary account number is missing.", result.ErrorMessage);
    }

    [Fact]
    public async Task UpdateOperator_ShouldReturnFailure_WhenThirdPartyFails()
    {
        var requestId = Guid.NewGuid();
        var beneficiary = new MobileRechargeBeneficiary { AccountNumber = "123456" };
        _unitOfWorkMock.Setup(u => u.MobileRechargeBeneficiaries.GetByIdAsync(requestId))
            .ReturnsAsync(beneficiary);



        _dingServiceMock.Setup(d => d.GetProductsByAccountNumber("123456"))
            .ReturnsAsync(new ServiceResponse<IEnumerable<ProductResponseDingModel>>(false, "Third-party error"));


        var result = await _service.UpdateOperator(requestId);

        Assert.False(result.IsSuccessful);
        Assert.Equal("Third-party error", result.ErrorMessage ?? "Failed to retrieve products.");
    }

    [Fact]
    public async Task UpdateOperator_ShouldReturnFailure_WhenNoProviderCodesFound()
    {
        var requestId = Guid.NewGuid();
        var beneficiary = new MobileRechargeBeneficiary { AccountNumber = "123456" };

        _unitOfWorkMock
            .Setup(u => u.MobileRechargeBeneficiaries.GetByIdAsync(requestId))
            .ReturnsAsync(beneficiary);


        _dingServiceMock
            .Setup(d => d.GetProductsByAccountNumber("123456"))
            .ReturnsAsync(new ServiceResponse<IEnumerable<ProductResponseDingModel>>
            {
                IsSuccessful = true,
                Data = new List<ProductResponseDingModel>() // empty
            });

        var result = await _service.UpdateOperator(requestId);

        Assert.False(result.IsSuccessful);
        Assert.Equal("No provider code found.", result.ErrorMessage);
    }

    [Fact]
    public async Task UpdateOperator_ShouldReturnFailure_WhenMultipleProviderCodesFound()
    {
        var requestId = Guid.NewGuid();
        var beneficiary = new MobileRechargeBeneficiary { AccountNumber = "123456" };

        _unitOfWorkMock
            .Setup(u => u.MobileRechargeBeneficiaries.GetByIdAsync(requestId))
            .ReturnsAsync(beneficiary);


        _dingServiceMock
            .Setup(d => d.GetProductsByAccountNumber("123456"))
            .ReturnsAsync(new ServiceResponse<IEnumerable<ProductResponseDingModel>>
            {
                IsSuccessful = true,
                Data = new List<ProductResponseDingModel>
                {
                    new ProductResponseDingModel { ProviderCode = "A" },
                    new ProductResponseDingModel { ProviderCode = "B" }
                }
            });

        var result = await _service.UpdateOperator(requestId);

        Assert.False(result.IsSuccessful);
        Assert.Equal("Multiple provider codes found. Unable to determine the correct one.", result.ErrorMessage);
    }


    [Fact]
    public async Task UpdateOperator_ShouldReturnSuccess_WhenValid()
    {
        var requestId = Guid.NewGuid();
        var beneficiary = new MobileRechargeBeneficiary { AccountNumber = "123456" };
        var provider = new MobileRechargeBeneficiaryProvider
        {
            BeneficiaryId = requestId,
            ProviderCode = "A"
        };

        _unitOfWorkMock.Setup(u => u.MobileRechargeBeneficiaries.GetByIdAsync(requestId))
            .ReturnsAsync(beneficiary);

        _dingServiceMock.Setup(d => d.GetProductsByAccountNumber("123456"))
            .ReturnsAsync(new ServiceResponse<IEnumerable<ProductResponseDingModel>>
            {
                IsSuccessful = true,
                Data = new List<ProductResponseDingModel>
                {
                    new ProductResponseDingModel { ProviderCode = "A" }
                }
            });


        _unitOfWorkMock
                .Setup(u => u.MobileRechargeBeneficiaryProviders.FindAsync(
                    It.IsAny<Expression<Func<MobileRechargeBeneficiaryProvider, bool>>>()))
                .ReturnsAsync(new List<MobileRechargeBeneficiaryProvider> { provider });

        var result = await _service.UpdateOperator(requestId);

        Assert.True(result.IsSuccessful);
        Assert.Equal("Operator updated successfully.", result.ErrorMessage);
        Assert.Equal("A", provider.ProviderCode);
    }


    #endregion

    #region ValidateSendTransferAsync Tests

    [Fact]
    public async Task ValidateSendTransferAsync_ShouldReturnFailure_WhenRequestIsNull()
    {
        // Arrange
        MobileRechargeTransaction request = null;
        var mobileApplicationId = BaseEnums.MobileApplicationId.C3Pay;

        // Act
        var result = await _service.ValidateSendTransferAsync(request, mobileApplicationId);

        // Assert
        Assert.False(result.IsSuccessful);
        Assert.Contains("Bad request", result.ErrorMessage);
    }

    [Fact]
    public async Task ValidateSendTransferAsync_ShouldReturnFailure_WhenProductNotFound()
    {
        // Arrange
        var request = new MobileRechargeTransaction
        {
            ProductCode = "INVALID_PRODUCT_CODE",
            BeneficiaryId = Guid.NewGuid()
        };
        var mobileApplicationId = BaseEnums.MobileApplicationId.C3Pay;

        _unitOfWorkMock.Setup(u => u.MobileRechargeProducts.FirstOrDefaultAsync(
        It.IsAny<Expression<Func<MobileRechargeProduct, bool>>>()))
        .ReturnsAsync((MobileRechargeProduct)null);

        // Act
        var result = await _service.ValidateSendTransferAsync(request, mobileApplicationId);

        // Assert
        Assert.False(result.IsSuccessful);
        Assert.Contains("Product not found.", result.ErrorMessage);
    }

    [Fact]
    public async Task ValidateSendTransferAsync_ShouldReturnFailure_WhenBeneficiaryNotFound()
    {
        // Arrange
        var beneficiaryId = Guid.NewGuid();
        var request = new MobileRechargeTransaction
        {
            ProductCode = "VALID_PRODUCT_CODE",
            BeneficiaryId = beneficiaryId
        };
        var mobileApplicationId = BaseEnums.MobileApplicationId.C3Pay;

        var product = new MobileRechargeProduct
        {
            Code = "VALID_PRODUCT_CODE",
            MinSendValue = 10,
            MinSendCurrencyIso = "AED"
        };

        _unitOfWorkMock
            .Setup(u => u.MobileRechargeProducts.FirstOrDefaultAsync(
                It.IsAny<Expression<Func<MobileRechargeProduct, bool>>>()))
            .ReturnsAsync(product);

        _unitOfWorkMock
        .Setup(u => u.MobileRechargeBeneficiaries.FirstOrDefaultAsync(
            It.IsAny<Expression<Func<MobileRechargeBeneficiary, bool>>>()))
        .ReturnsAsync((MobileRechargeBeneficiary)null);

        // Act
        var result = await _service.ValidateSendTransferAsync(request, mobileApplicationId);

        // Assert
        Assert.False(result.IsSuccessful);
        Assert.Contains("Beneficiary not found.", result.ErrorMessage);
    }

    [Fact]
    public async Task ValidateSendTransferAsync_ShouldReturnFailure_WhenDingValidationFails()
    {
        // Arrange
        var beneficiaryId = Guid.NewGuid();
        var request = new MobileRechargeTransaction
        {
            ProductCode = "VALID_PRODUCT_CODE",
            BeneficiaryId = beneficiaryId
        };
        var mobileApplicationId = BaseEnums.MobileApplicationId.C3Pay;

        var product = new MobileRechargeProduct
        {
            Code = "VALID_PRODUCT_CODE",
            MinSendValue = 10,
            MinSendCurrencyIso = "AED"
        };

        var beneficiary = new MobileRechargeBeneficiary
        {
            Id = beneficiaryId,
            AccountNumber = "************"
        };

        _unitOfWorkMock
            .Setup(u => u.MobileRechargeProducts.FirstOrDefaultAsync(
                It.IsAny<Expression<Func<MobileRechargeProduct, bool>>>()))
            .ReturnsAsync(product);

        _unitOfWorkMock
     .Setup(u => u.MobileRechargeBeneficiaries.FirstOrDefaultAsync(
         It.IsAny<Expression<Func<MobileRechargeBeneficiary, bool>>>()))
     .ReturnsAsync( beneficiary );

        _dingServiceMock.Setup(d => d.SendTransfer(It.IsAny<SendTransferRequestDingModel>(), It.IsAny<Enums.MobileApplicationId>()))
        .ReturnsAsync(new ServiceResponse<SendTransferResponseDingModel>(false, "Ding validation failed"));

        // Act
        var result = await _service.ValidateSendTransferAsync(request, mobileApplicationId);

        // Assert
        Assert.False(result.IsSuccessful);
        Assert.Equal("Ding validation failed", result.ErrorMessage);
    }

    [Fact]
    public async Task ValidateSendTransferAsync_ShouldReturnFailure_WhenAccountNumberInvalid()
    {
        // Arrange
        var beneficiaryId = Guid.NewGuid();
        var request = new MobileRechargeTransaction
        {
            ProductCode = "VALID_PRODUCT_CODE",
            BeneficiaryId = beneficiaryId
        };
        var mobileApplicationId = BaseEnums.MobileApplicationId.C3Pay;

        var product = new MobileRechargeProduct
        {
            Code = "VALID_PRODUCT_CODE",
            MinSendValue = 10,
            MinSendCurrencyIso = "AED"
        };

        var beneficiary = new MobileRechargeBeneficiary
        {
            Id = beneficiaryId,
            AccountNumber = "************"
        };

        _unitOfWorkMock
            .Setup(u => u.MobileRechargeProducts.FirstOrDefaultAsync(
                It.IsAny<Expression<Func<MobileRechargeProduct, bool>>>()))
            .ReturnsAsync(product);

        _unitOfWorkMock
     .Setup(u => u.MobileRechargeBeneficiaries.FirstOrDefaultAsync(
         It.IsAny<Expression<Func<MobileRechargeBeneficiary, bool>>>()))
     .ReturnsAsync(beneficiary);

        _dingServiceMock.Setup(d => d.SendTransfer(It.IsAny<SendTransferRequestDingModel>(), It.IsAny<Enums.MobileApplicationId>()))
            .ReturnsAsync(new ServiceResponse<SendTransferResponseDingModel>(false, "AccountNumberInvalid###ProviderRefusedRequest"));

        // Act
        var result = await _service.ValidateSendTransferAsync(request, mobileApplicationId);

        // Assert

        Assert.False(result.IsSuccessful);
        Assert.Equal("AccountNumberInvalid", result.ErrorMessage);

    }


    [Fact]
    public async Task ValidateSendTransferAsync_ShouldReturnFailure_WhenProviderError()
    {
        // Arrange
        var beneficiaryId = Guid.NewGuid();
        var request = new MobileRechargeTransaction
        {
            ProductCode = "VALID_PRODUCT_CODE",
            BeneficiaryId = beneficiaryId
        };
        var mobileApplicationId = BaseEnums.MobileApplicationId.C3Pay;

        var product = new MobileRechargeProduct
        {
            Code = "VALID_PRODUCT_CODE",
            MinSendValue = 10,
            MinSendCurrencyIso = "AED"
        };

        var beneficiary = new MobileRechargeBeneficiary
        {
            Id = beneficiaryId,
            AccountNumber = "************"
        };

        _unitOfWorkMock
             .Setup(u => u.MobileRechargeProducts.FirstOrDefaultAsync(
                 It.IsAny<Expression<Func<MobileRechargeProduct, bool>>>()))
             .ReturnsAsync(product);

        _unitOfWorkMock
     .Setup(u => u.MobileRechargeBeneficiaries.FirstOrDefaultAsync(
         It.IsAny<Expression<Func<MobileRechargeBeneficiary, bool>>>()))
     .ReturnsAsync(beneficiary);

        _dingServiceMock.Setup(d => d.SendTransfer(It.IsAny<SendTransferRequestDingModel>(), It.IsAny<Enums.MobileApplicationId>()))
            .ReturnsAsync(new ServiceResponse<SendTransferResponseDingModel>(false, "ProviderError###ProviderRefusedRequest"));

        // Act
        var result = await _service.ValidateSendTransferAsync(request, mobileApplicationId);

        // Assert
        Assert.False(result.IsSuccessful);
        Assert.Equal("ProviderError", result.ErrorMessage);
        Assert.NotNull(result.Data);
        Assert.False(result.Data.ValidateSuccess);
        Assert.Equal("ProviderError", result.Data.ValidationMessage);
        Assert.NotNull(result.Data.ScreenContent);
        Assert.Equal("Please change your pack", result.Data.ScreenContent.Title);
        Assert.Equal("This pack cannot be used with the selected number. Please choose a different pack", result.Data.ScreenContent.SubTitle);
        Assert.Equal("Change Pack", result.Data.ScreenContent.PrimaryButton.Text);
        Assert.Equal("Need Help?", result.Data.ScreenContent.SecondaryButton.Text);
    }


    [Fact]
    public async Task ValidateSendTransferAsync_ShouldReturnSuccess_WhenValidationPasses()
    {
        // Arrange
        var beneficiaryId = Guid.NewGuid();
        var request = new MobileRechargeTransaction
        {
            ProductCode = "VALID_PRODUCT_CODE",
            BeneficiaryId = beneficiaryId
        };
        var mobileApplicationId = BaseEnums.MobileApplicationId.C3Pay;

        var product = new MobileRechargeProduct
        {
            Code = "VALID_PRODUCT_CODE",
            MinSendValue = 10,
            MinSendCurrencyIso = "AED"
        };

        var beneficiary = new MobileRechargeBeneficiary
        {
            Id = beneficiaryId,
            AccountNumber = "************"
        };

        _unitOfWorkMock
            .Setup(u => u.MobileRechargeProducts.FirstOrDefaultAsync(
                It.IsAny<Expression<Func<MobileRechargeProduct, bool>>>()))
            .ReturnsAsync(product);

        _unitOfWorkMock
     .Setup(u => u.MobileRechargeBeneficiaries.FirstOrDefaultAsync(
         It.IsAny<Expression<Func<MobileRechargeBeneficiary, bool>>>()))
     .ReturnsAsync(beneficiary);

        _dingServiceMock.Setup(d => d.SendTransfer(It.IsAny<SendTransferRequestDingModel>(), It.IsAny<Enums.MobileApplicationId>()))
            .ReturnsAsync(new ServiceResponse<SendTransferResponseDingModel>(true, null));

        // Act
        var result = await _service.ValidateSendTransferAsync(request, mobileApplicationId);

        // Assert
        Assert.True(result.IsSuccessful);
        Assert.Equal("Transaction validated successfully.", result.Data.ValidationMessage);
        Assert.True(result.Data.ValidateSuccess);
    }

    #endregion ValidateSendTransferAsync Tests
}