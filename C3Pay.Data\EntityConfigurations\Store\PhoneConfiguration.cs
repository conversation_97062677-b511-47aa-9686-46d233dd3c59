﻿using C3Pay.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace C3Pay.Data.EntityConfigurations
{
    public class PhoneConfiguration : IEntityTypeConfiguration<Phone>
    {
        public void Configure(EntityTypeBuilder<Phone> builder)
        {
            builder.HasData(

             new Phone()
             {
                 Id = Guid.Parse("27826AC1-9D05-45DF-8076-0579A5885AE0"),
                 DeletedBy = null,
                 DeletedDate = null,
                 UpdatedDate = null,
                 CreatedDate = new DateTime(2022, 1, 1),
                 IsDeleted = false,
                 Currency = "AED",
                 Price = 660,
                 AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                 {
                     BackCamera = "50MP",
                     Brand = "Samsung",
                     FrontCamera = "8MP",
                     Memory = "128 GB",
                     Model = "A13",
                     ScreenSize = "6.6 inches",
                     Sim = "4G Dual SIM",
                     Warranty = "1 Year ",
                     Colour = "Black",
                     ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/SamsungA134GB 128GB.png",
                 })
             },
              new Phone()
              {
                  Id = Guid.Parse("71EB3E4F-C494-4D2D-AF71-1003818C54A8"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  IsDeleted = false,

                  Currency = "AED",
                  Price = 760,
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "50MP",
                      Brand = "Samsung",
                      FrontCamera = "8MP",
                      Memory = "128 GB",
                      Model = "A23",
                      ScreenSize = "6.6 inches",
                      Sim = "4G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Black",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/Samsung A23 4GB128GB.png",
                  })
              },
              new Phone()
              {
                  Id = Guid.Parse("EF8EACA8-5878-44B5-BD47-7FB1F63AD3B1"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  IsDeleted = false,
                  Price = 1040,
                  Currency = "AED",
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "48MP",
                      Brand = "Samsung",
                      FrontCamera = "13MP",
                      Memory = "128 GB",
                      Model = "A33 ",
                      ScreenSize = "6.4 inches",
                      Sim = "5G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Black",

                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/SamsungA336GB128GB5g.png",
                  })
              },

              new Phone()
              {
                  Id = Guid.Parse("8C1C9BB2-94C5-4713-99A5-7355EEB32ADD"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  IsDeleted = false,
                  Currency = "AED",
                  Price = 920,
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "48MP",
                      Brand = "Oppo",
                      FrontCamera = "16MP",
                      Memory = "128 GB",
                      Model = "A74",
                      ScreenSize = "6.43 inches",
                      Sim = "4G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Black",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/OPPORenoA74.png",
                  })
              },

              new Phone()
              {
                  Id = Guid.Parse("B3F63AF3-1394-46B9-BE5C-BC50D8DC5865"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  IsDeleted = false,
                  Price = 1520,
                  Currency = "AED",
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "64MP",
                      Brand = "Oppo",
                      FrontCamera = "16MP",
                      Memory = "128 GB",
                      Model = "RENO 8Z ",
                      ScreenSize = "6.43 inches",
                      Sim = "5G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Gold",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/OppoReno8z.png",
                  })
              },

              new Phone()
              {
                  Id = Guid.Parse("43976D37-4CAE-4F1F-9C44-5487576D4481"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  IsDeleted = false,
                  Price = 2220,
                  Currency = "AED",
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "12MP",
                      Brand = "Apple",
                      FrontCamera = "12MP",
                      Memory = "128 GB",
                      Model = "Iphone 11",
                      ScreenSize = "6.1 inches",
                      Sim = "4G Single SIM",
                      Warranty = "1 Year ",
                      Colour = "Black",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/Apple11.png",
                  })
              },

              new Phone()
              {
                  Id = Guid.Parse("404A3B6F-8181-40E2-90E6-31EF551D115D"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  Currency = "AED",
                  Price = 920,
                  IsDeleted = false,
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "50MP",
                      Brand = "Vivo",
                      FrontCamera = "16MP",
                      Memory = "128 GB",
                      Model = "Y33S",
                      ScreenSize = "6.58 inches",
                      Sim = "4G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Blue",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/VivoY33s.png",
                  })

              },

              new Phone()
              {
                  Id = Guid.Parse("9D3BA1D9-1C38-4EAD-8CFC-8438DF74C4AB"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  IsDeleted = false,
                  Currency = "AED",
                  Price = 840,
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "50MP",
                      Brand = "Vivo",
                      FrontCamera = "8MP",
                      Memory = "128 GB",
                      Model = "Y22S",
                      ScreenSize = "6.55 inches",
                      Sim = "4G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Blue",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/VivoY22s.png",
                  })
              },

              new Phone()
              {
                  Id = Guid.Parse("854E5C74-4203-4C17-A760-DDC3A943A2BE"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  IsDeleted = false,
                  Price = 780,
                  Currency = "AED",
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "50MP",
                      Brand = "Oppo",
                      FrontCamera = "8MP",
                      Memory = "128 GB",
                      Model = "A77",
                      ScreenSize = "6.56 inches",
                      Sim = "4G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Black",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/OppoA77Black.png",
                  })
              },

              new Phone()
              {
                  Id = Guid.Parse("DD43D743-C359-4EA1-9D8C-4927404DB06B"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  IsDeleted = false,
                  Currency = "AED",
                  Price = 720,
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "50MP",
                      Brand = "Redmi",
                      FrontCamera = "8MP",
                      Memory = "128 GB",
                      Model = "Note 11",
                      ScreenSize = "6.5 inches",
                      Sim = "4G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Grey",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11.png",
                  })
              },

              new Phone()
              {
                  Id = Guid.Parse("FC90B41E-BD5C-4D93-ABA5-5414412DA7DD"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  Currency = "AED",
                  Price = 960,
                  IsDeleted = false,
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "108MP",
                      Brand = "Redmi",
                      FrontCamera = "16MP",
                      Memory = "128 GB",
                      Model = "Note11 Pro 4G",
                      ScreenSize = "6.67 inches",
                      Sim = "4G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Grey",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11pro4g.png",
                  })
              },

              new Phone()
              {
                  Id = Guid.Parse("3F9D0F43-7996-4103-B796-B9054FD47DED"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  Currency = "AED",
                  Price = 1060,
                  IsDeleted = false,
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "108MP",
                      Brand = "Redmi",
                      FrontCamera = "16MP",
                      Memory = "128 GB",
                      Model = "Note11 Pro 5G",
                      ScreenSize = "6.67 inches",
                      Sim = "5G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Grey",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11pro5g.png",
                  })
              },

              new Phone()
              {
                  Id = Guid.Parse("869B6AB6-488B-469A-94A7-33FF84974542"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  Price = 840,
                  Currency = "AED",
                  IsDeleted = false,
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "108MP",
                      Brand = "Redmi",
                      FrontCamera = "16MP",
                      Memory = "128 GB",
                      Model = "Note 11S 4G",
                      ScreenSize = "6.43 inches",
                      Sim = "4G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Grey",
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11S4G.png",
                  })

              },

              new Phone()
              {
                  Id = Guid.Parse("59F640CA-90DF-466F-8492-23D258CB3B67"),
                  DeletedBy = null,
                  DeletedDate = null,
                  UpdatedDate = null,
                  CreatedDate = new DateTime(2022, 1, 1),
                  Price = 1000,
                  Currency = "AED",
                  IsDeleted = false,
                  AttributesJson = JsonConvert.SerializeObject(new PhoneAttribute()
                  {
                      BackCamera = "108MP",
                      Brand = "Redmi",
                      FrontCamera = "13MP",
                      Memory = "128 GB",
                      Model = "Note 11S 5G",
                      ScreenSize = "6.6 inches",
                      Sim = "5G Dual SIM",
                      Warranty = "1 Year ",
                      Colour = "Grey", 
                      ImageUrl = "https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11S5g.png",
                  })
              }
         );
        }
    }
}
