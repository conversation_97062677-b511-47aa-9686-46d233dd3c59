﻿using C3Pay.Core.Models.C3Pay.User;
using C3Pay.Core.Models.MoneyTransfer;
using C3Pay.Core.ResourceFile;
using Edenred.Common.Core;
using Microsoft.Azure.Documents;
using System;
using System.Collections.Generic;
using System.Linq;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models
{
    public class User : BaseModel
    {
        public User()
        {
            SecretAnswers = new List<SecretAnswer>();
            VerificationComments = new List<VerificationComment>();
            Identifications = new List<Identification>();
            Transactions = new List<Transaction>();
            MoneyTransferBeneficiaries = new List<MoneyTransferBeneficiary>();
            MobileRechargeBeneficiaries = new List<MobileRechargeBeneficiary>();
            BillPaymentBillers = new List<BillPaymentBiller>();
            MobileRechargeTransactions = new List<MobileRechargeTransaction>();
            MoneyTransferTransactions = new List<MoneyTransferTransaction>();
            Documents = new List<Document>();
            UploadedDocuments = new List<UploadedDocument>();
            UnemploymentInsurancePayments = new List<UnemploymentInsurancePayment>();
            Devices = new List<UserDevice>();
            UnEmpInsurancePayments = new List<UnEmpInsurancePayment>();
            Preferences = new UserPreferences();
            AutoUnblock = new UserAutoUnblock();
            SpendPolicy = new UserSpendPolicy();
            PhoneNumberChangeLogs = new List<PhoneNumberChangeLog>();
            MobileRechargeRenewals = new List<MobileRechargeRenewal>();
            MoneyTransferUserRewards = new List<MoneyTransferUserReward>();
            MobileRechargeTargetedDiscounts = new List<MobileRechargeTargetedDiscount>();
        }

        public Guid Id { get; set; }
        public int ExternalId { get; set; }
        public string CardHolderId { get; set; }
        public string PhoneNumber { get; set; }
        public string DeviceToken { get; set; }
        public string Email { get; set; }
        public string ReferralCode { get; set; }
        public bool IsVerified { get; set; }
        public bool IsBlocked { get; set; }
        public int? UserBlockReasonId { get; set; }

        public bool HasOldTransactions { get; set; }
        public DateTime? ATMPinBlockEndDate { get; set; }
        public UserBlockType? BlockType { get; set; }
        public DateTime? BlockDate { get; set; }
        public DateTime? UnblockDate { get; set; }
        public MobileApplicationId ApplicationId { get; set; }
        public MoneyTransferProfileStatus MoneyTransferProfileStatus { get; set; }
        public bool RequiresPasswordReset { get; set; }

        public DateTime? FreeTransferExpiryDate { get; set; }
        public bool IsFirstSalaryProcessed { get; set; } = false;
        public DateTime? MtGoldIncentiveExpiryDate { get; set; }


        public bool? EmailConsent { get; set; }
        public DateTime? EmailConsentDate { get; set; }
        public AlertChannelType? ReceiveAlertsVia { get; set; }
        public bool? IsInternationalPhoneNumberUser { get; set; }

        public UserAutoUnblock AutoUnblock { get; set; }
        public UserPreferences Preferences { get; set; }
        public virtual CardHolder CardHolder { get; set; }
        public List<Document> Documents { get; set; }
        public List<UploadedDocument> UploadedDocuments { get; set; }
        public List<Transaction> Transactions { get; set; }
        public List<SecretAnswer> SecretAnswers { get; set; }
        public List<UserSubscription> Subscriptions { get; set; }
        public List<Identification> Identifications { get; set; }
        public List<VerificationComment> VerificationComments { get; set; }
        public List<MobileRechargeBeneficiary> MobileRechargeBeneficiaries { get; set; }
        public List<BillPaymentBiller> BillPaymentBillers { get; set; }
        public List<MobileRechargeTransaction> MobileRechargeTransactions { get; set; }
        public List<MoneyTransferBeneficiary> MoneyTransferBeneficiaries { get; set; }
        public List<MoneyTransferBeneficiary> MoneyTransferDirectBeneficiaries { get; set; }
        public List<MoneyTransferTransaction> MoneyTransferTransactions { get; set; }
        public List<AuditTrail> AuditTrails { get; set; }
        public List<MoneyTransferUserReward> MoneyTransferUserRewards { get; set; }


        public virtual UserBlockReason UserBlockReason { get; set; }

        public UserSpendPolicy SpendPolicy { get; set; }
        public UserSegmentation UserSegmentation { get; set; }

        public List<Rating> Ratings { get; set; }
        public List<Order> Orders { get; set; }
        public List<UnemploymentInsurancePayment> UnemploymentInsurancePayments { get; set; }
        public List<UserDevice> Devices { get; set; }
        public List<UnEmpInsurancePayment> UnEmpInsurancePayments { get; set; }
        public List<PhoneNumberChangeLog> PhoneNumberChangeLogs { get; set; }

        public List<MobileRechargeRenewal> MobileRechargeRenewals { get; set; }
        public List<MobileRechargeTargetedDiscount> MobileRechargeTargetedDiscounts { get; set; }

        public List<MoneyTransferProfileTracker> RMTTrackerLogs { get; set; }

        public List<RmtKycRefinement> RmtKycRefinements { get; set; }
        public void Verify()
        {
            this.IsVerified = true;
            this.UpdatedDate = DateTime.Now;
        }

        public void Block(UserBlockType blockType)
        {
            this.IsBlocked = true;
            this.BlockDate = DateTime.Now;
            this.BlockType = blockType;
            this.UpdatedDate = DateTime.Now;
        }

        public void Unblock()
        {
            this.IsBlocked = false;
            this.UnblockDate = DateTime.Now;
            this.BlockType = null;
            this.UpdatedDate = DateTime.Now;

            if (this.AutoUnblock is null)
            {
                this.AutoUnblock = new UserAutoUnblock();
            }

            AutoUnblock.WrongAttemptsCount = 0;
        }

        public string ValidateDeviceId(string uniqueDeviceId, GeneralSettings generalSettings)
        {
            if (Devices.Any(a => a.UniqueDeviceId == uniqueDeviceId && a.IsActive && !a.IsDeleted))
                return ConstantParam.UserDeviceExistsAlready;
            if (!uniqueDeviceId.StartsWith(generalSettings.DeviceIdBindingPrefix))
                return ConstantParam.InvalidDevice;

            return ConstantParam.ValidDevice;
        }
        public bool AddDevice(string uniqueDeviceId, string model, GeneralSettings generalSettings)
        {
            if (Devices == null)
                Devices = new List<UserDevice>();

            Devices
                .Where(d => d.IsActive)
                .OrderByDescending(d => d.CreatedDate)
                .Skip(generalSettings.MaxDevicesAllowedForBinding - 1)
                .ToList().ForEach(_device =>
            {
                _device.IsActive = false;
                _device.IsDeleted = true;
                _device.UpdatedDate = DateTime.Now;
            });

            Devices.Add(new UserDevice()
            {
                UniqueDeviceId = uniqueDeviceId,
                UserId = Id,
                IsActive = true,
                Model = model,
                IsDeleted = false,
                DeletedBy = null,
                DeletedDate = null,
                UpdatedDate = null,
                CreatedDate = DateTime.Now,
                DeviceId = string.Empty
            });
            return true;
        }

        public static Dictionary<string, string> GetDeviceBindingAwarenessInitialVideoLinks() => UIResource.ResourceManager.GetString("devicebindingawareness_initialvideo")
                                                .Split('|')
                                                .Select(pair => pair.Trim().Split(new string[] { "##" }, StringSplitOptions.None))
                                                .ToDictionary(parts => parts[0], parts => parts[1]);

        public static Dictionary<string, string> GetDeviceBindingAwarenessRecurringVideoLinks() => UIResource.ResourceManager.GetString("devicebindingawareness_recurringvideo")
                                               .Split('|')
                                               .Select(pair => pair.Trim().Split(new string[] { "##" }, StringSplitOptions.None))
                                               .ToDictionary(parts => parts[0], parts => parts[1]);

        public bool AddPhoneNumberUpdateLog(string oldPhoneNumber, string newPhoneNumber, string source)
        {
            PhoneNumberChangeLogs.Add(new PhoneNumberChangeLog
            {
                UserId = Id,
                OldPhoneNumber = oldPhoneNumber,
                NewPhoneNumber = newPhoneNumber,
                Source = source,
                CreatedDate = DateTime.Now
            });
            return true;
        }

        #region C3Pay+
        public Result IsC3PayPlusEligible()
        {
            // Check age of user, should be between 18 and 59.
            if (this.CardHolder.Birthdate.HasValue == false)
            {
                return Result.Failure(Errors.C3PayPlus.UserIsTooOldOrTooYoung);
            }

            int age = DateTime.Now.Year - this.CardHolder.Birthdate.Value.Year;

            // Check if birthday has occurred this year.
            if (DateTime.Now.Month < this.CardHolder.Birthdate.Value.Month
                || (DateTime.Now.Month == this.CardHolder.Birthdate.Value.Month
                && DateTime.Now.Day < this.CardHolder.Birthdate.Value.Day))
            {
                age--;
            }

            if (age < 18 || age > 59)
            {
                return Result.Failure(Errors.C3PayPlus.UserIsTooOldOrTooYoung);
            }

            if (this.IsVerified == false)
            {
                return Result.Failure(Errors.C3PayPlus.UserNotVerified);
            }

            // KYC Checks.
            if (this.Identifications is null || this.Identifications.Any(x => x.Type == Enums.IdentificationType.EmiratesId) == false)
            {
                return Result.Failure(Errors.C3PayPlus.NoKycFound);
            }


            return Result.Success();
        }
        #endregion

        public bool IsTestUser() => CardHolder.CorporateId == "99998";
    }

    public class UserCache
    {
        public static readonly string CacheKey = $"Usr_Phone";
        public Guid Id { get; set; }
        public string PhoneNumber { get; set; }
        public string DeviceId { get; set; }
        public string DeviceToken { get; set; }
        public long DeviceTokenExpiryEpoch { get; set; }
        public static string GetKeyName(string phoneNumber) => $"{CacheKey}_{phoneNumber}";
        public static UserCache Set(string phoneNumber, Guid userId,
            string deviceId, string deviceToken, long deviceTokenExpiryEpoch)
        {
            return new UserCache()
            {
                Id = userId,
                PhoneNumber = phoneNumber,
                DeviceId = deviceId,
                DeviceToken = deviceToken,
                DeviceTokenExpiryEpoch = deviceTokenExpiryEpoch
            };
        }
    }
}
