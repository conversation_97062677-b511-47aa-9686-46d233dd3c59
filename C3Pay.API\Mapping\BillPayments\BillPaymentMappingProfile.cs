﻿using AutoMapper;
using C3Pay.API.Resources;
using C3Pay.API.Resources.BillPayment;
using C3Pay.Core;
using C3Pay.Core.Models;
using Edenred.Common.Core.Models.Messages.Integration.BillPayments;
using System;
using System.Linq;

namespace C3Pay.API.Mapping.BillPayments
{
    /// <summary>
    /// BillPayment Mapping Profile
    /// </summary>
    public class BillPaymentMappingProfile : Profile
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public BillPaymentMappingProfile()
        {
            // Bill Payments 
            CreateMap<BillPaymentCategory, BillPaymentCategoryDto>()
               .ForMember(src => src.CategoryId, opt => opt.MapFrom(dest => dest.Id))
               .ForMember(src => src.CategoryName, opt => opt.MapFrom(dest => dest.Name));

            CreateMap<BillPaymentProvider, BillPaymentProviderDto>()
               .ForMember(src => src.Id, opt => opt.MapFrom(dest => dest.Id))
               .ForMember(src => src.ProviderCode, opt => opt.MapFrom(dest => dest.Code))
               .ForMember(src => src.ProviderLogoUrl, opt => opt.MapFrom(dest => dest.IconUrl))
               .ForMember(src => src.SubProviders, opt => opt.MapFrom(dest => new System.Collections.Generic.List<BillPaymentSubProviderDto>()))
               .ForMember(src => src.IsInquiryAvailable, opt => opt.MapFrom(dest => dest.Products.Any() ? dest.Products.FirstOrDefault().InquiryAvailable : false))
               .ForMember(src => src.Name, opt => opt.MapFrom(dest => dest.Name)).AfterMap((src, dest) =>
               {
                   if (src.Products.Any(a => a.Type == "Service") && src.Products.Count() != 1)
                   {
                       src.Products.ForEach(item =>
                       {
                           var _subProvider = new BillPaymentSubProviderDto()
                           {
                               Id = item.Id,
                               Name = item.Description
                           };
                           dest.SubProviders.Add(_subProvider);
                       });
                   }
               });

            CreateMap<BillPaymentProductIO, BillPaymentFieldDto>()
            .ForMember(src => src.IsPhoneNumber, opt => opt.MapFrom(dest => dest.Name.ToLower() == "mobile number" ? true : false))
            .ForMember(src => src.FieldId, opt => opt.MapFrom(dest => dest.Id));

            CreateMap<BillPaymentProviderField, BillPaymentFieldDto>()
               .ForMember(src => src.IsPhoneNumber, opt => opt.MapFrom(dest => dest.Name.ToLower() == "mobile number" ? true : false))
               .ForMember(src => src.ProviderLogoUrl, opt => opt.MapFrom(dest => dest.ProviderIconUrl))
               .ForMember(src => src.FieldId, opt => opt.MapFrom(dest => dest.Id));

            CreateMap<C3Pay.Core.Models.Country, BillPaymentCountryDto>()
                .ForMember(dest => dest.ISDCode, opt => opt.MapFrom(src => src.STDCode))
                .ForMember(dest => dest.IsPopular, opt => opt.MapFrom(src => src.IsPapularCountry));

            CreateMap<BillPaymentsIODto, BillPaymentProductIO>()
                .ForMember(dest => dest.IOId, opt => opt.MapFrom(src => src.IOID));

            CreateMap<BillPaymentsSKUDto, BillPaymentProduct>()
               .ForMember(dest => dest.BusinessDays, opt => opt.MapFrom(src => src.BusinessDays == 0 ? false : true))
               .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.SKU))
               .ForMember(dest => dest.MaxAmount, opt => opt.MapFrom(src => src.MaxAmount == null ? 0 : Convert.ToDecimal(src.MaxAmount)))
               .ForMember(dest => dest.MinAmount, opt => opt.MapFrom(src => src.MinAmount == null ? 0 : Convert.ToDecimal(src.MinAmount)))
               .ForMember(dest => dest.ExcessPaymentAllowed, opt => opt.MapFrom(src => src.ExcessPaymentAllowed == 0 ? false : true))
               .ForMember(dest => dest.InquiryAvailable, opt => opt.MapFrom(src => src.InquiryAvailable == 0 ? false : true))
               .ForMember(dest => dest.PartialPaymentAllowed, opt => opt.MapFrom(src => src.PartialPaymentAllowed == 0 ? false : true))
               .ForMember(dest => dest.PastDuePaymentAllowed, opt => opt.MapFrom(src => src.PastDuePaymentAllowed == 0 ? false : true));

            CreateMap<BillPaymentAddBillerIORequestDto, BillPaymentBillerIO>()
               .ForMember(dest => dest.ProductIOId, opt => opt.MapFrom(src => src.FieldId))
               .ForMember(dest => dest.FieldValue, opt => opt.MapFrom(src => src.FieldValue));

            CreateMap<BillPaymentAddBillerRequestDto, BillPaymentBiller>()
              .ForMember(dest => dest.NickName, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.NickName) ? string.Empty : src.NickName))
              .ForMember(dest => dest.BillerIOs, opt => opt.MapFrom(x => x.FieldInputs));

            CreateMap<BillPaymentProduct, BillPaymentProductDto>()
                .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.ProductAmount, opt => opt.MapFrom(src => new AmountDto()
                {
                    Amount = Convert.ToDouble(src.Amount),
                    Currency = src.Currency
                }))
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Description));

            CreateMap<BillPaymentBiller, BillPaymentBillerDto>()
                .ForMember(src => src.BillerId, opt => opt.MapFrom(dest => dest.Id))
                .ForMember(src => src.Field, opt => opt.MapFrom(dest => new BillPaymentBillerFieldResponseDto()
                {
                    FieldName = dest.BillerIOs.OrderBy(a => a.ProductIO.IOId).FirstOrDefault().ProductIO.Name,
                    FieldValue = dest.BillerIOs.OrderBy(a => a.ProductIO.IOId).FirstOrDefault().FieldValue
                }))
                .ForMember(src => src.BillerType, opt => opt.MapFrom(dest => dest.Provider.Products.Any(a => a.Currency == "AED") ? "Local" : "International"))
                .ForMember(src => src.ProductDisplayType, opt => opt.MapFrom(dest => BaseEnums.BillPaymentProductDisplayType.Package.ToString()))
                .ForMember(src => src.ProviderLogoUrl, opt => opt.MapFrom(dest => dest.Provider != null ? dest.Provider.IconUrl : ""))
                .ForMember(src => src.ProviderName, opt => opt.MapFrom(dest => dest.Provider != null ? 
                    (dest.Provider.Code == BillPaymentProvider.NolProviderCode ? "Nol (Metro or Bus)" : dest.Provider.Name)
                    : ""))
                .ForMember(src => src.ProviderCode, opt => opt.MapFrom(dest => dest.Provider != null ? dest.Provider.Code : ""))
                .ForMember(src => src.IsInquiryAvailable, opt => opt.MapFrom(dest => dest.Provider.Products.Any() ? dest.Provider.Products.FirstOrDefault().InquiryAvailable : false))
                .ForMember(src => src.ProviderCurrency, opt => opt.MapFrom(dest => dest.Provider.Products.Any() ? dest.Provider.Products.FirstOrDefault().Currency : ""))
                .ForMember(src => src.ProviderMinAmount, opt => opt.MapFrom(dest => dest.Provider.Products.Any() ? dest.Provider.Products.FirstOrDefault().MinAmount : 0))
                 .ForMember(src => src.AmountDueStatus, opt => opt.MapFrom(dest => dest.Status.ToString()))
                 .ForMember(src => src.LastTransactionTime, opt => opt.MapFrom(dest => dest.Transactions.Any() ? ((DateTimeOffset)dest.Transactions.FirstOrDefault().CreatedDate).ToUnixTimeMilliseconds() : 0))
                .ForMember(src => src.AmountDue, opt => opt.MapFrom(dest => !string.IsNullOrEmpty(dest.BillAmountCurrency) ? new AmountDto()
                {
                    Amount = Convert.ToDouble(dest.BillAmount),
                    Currency = dest.BillAmountCurrency
                } : null))
                .ForMember(src => src.ProviderMaxAmount, opt => opt.MapFrom(dest => dest.Provider.Products.Any() ?
                    ((dest.Provider.Products.FirstOrDefault().MaxAmount == 0
                    && dest.Provider.Products.FirstOrDefault().MinAmount != 0)
                    ? decimal.MaxValue : dest.Provider.Products.FirstOrDefault().MaxAmount)
                    : 0))
                .ForMember(src => src.Products, opt => opt.MapFrom(dest => dest.Provider.Products.Count() > 1 ?
                            (
                                dest.Provider.Products.FirstOrDefault().Type == "Service" ? new System.Collections.Generic.List<BillPaymentProduct>()
                                : dest.Provider.Products.OrderBy(a => a.Amount).ToList()
                            ) : new System.Collections.Generic.List<BillPaymentProduct>()));


            CreateMap<BillPaymentBillerIO, BillPaymentBillSummaryFieldDto>()
                        .ForMember(src => src.FieldName, opt => opt.MapFrom(dest => dest.ProductIO.Name));

            CreateMap<BillPaymentBiller, BillPaymentTransactionBillerDto>()
                    .ForMember(src => src.NickName, opt => opt.MapFrom(dest => dest.NickName))
                    .ForMember(src => src.ProviderName, opt => opt.MapFrom(dest => dest.Provider.Name))
                    .ForMember(src => src.Category, opt => opt.MapFrom(dest => dest.Category != null ? dest.Category.Name : ""))
                    .ForMember(src => src.Fields, opt => opt.MapFrom(dest => dest.BillerIOs.OrderBy(a => a.ProductIO.IOId)));

            CreateMap<BillPaymentTransaction, BillPaymentTransactionDetailDto>()
                                 .ForMember(src => src.PaymentDate, opt => opt.MapFrom(dest => dest.CreatedDate))
                                 .ForMember(src => src.FeeAmount, opt => opt.MapFrom(dest => new AmountDto() { Amount = Convert.ToDouble(dest.FeeAmount), Currency = dest.FeeAmountCurrency }))
                                 .ForMember(src => src.ToBePaidAmount, opt => opt.MapFrom(dest => new AmountDto() { Amount = Convert.ToDouble(dest.ConvertedBillAmount), Currency = dest.ConvertedBillAmountCurrency }))
                                 .ForMember(src => src.TotalAmount, opt => opt.MapFrom(dest => new AmountDto() { Amount = Convert.ToDouble(dest.TotalAmount), Currency = dest.TotalAmountCurrency }))
                                 .ForMember(src => src.ProviderDetail, opt => opt.MapFrom(dest => dest.Biller))
                                 .ForMember(src => src.Reason, opt => opt.MapFrom(dest => string.IsNullOrEmpty(dest.Reason) ? string.Empty : (dest.Reason.StartsWith("BP") ? string.Empty : dest.Reason)))
                                 .ForMember(src => src.Status, opt => opt.MapFrom(dest => dest.Status.ToString())).AfterMap((src, dest) =>
                                 {
                                     dest.ProviderDetail.Category = src.Biller.Category != null ? src.Biller.Category.Name : "";
                                 });


            CreateMap<BillPaymentTransaction, BillPaymentTransactionDto>()
                              .ForMember(src => src.TransactionId, opt => opt.MapFrom(dest => dest.Id))
                              .ForMember(src => src.NickName, opt => opt.MapFrom(dest => dest.Biller.NickName))
                              .ForMember(src => src.ProviderName, opt => opt.MapFrom(dest => dest.Biller.Provider.Code == BillPaymentProvider.NolProviderCode ?
                               "Nol (Metro or Bus)" :  dest.Biller.Provider.Name))
                              .ForMember(src => src.PaymentDate, opt => opt.MapFrom(dest => dest.CreatedDate))
                              .ForMember(src => src.PaymentAmount, opt => opt.MapFrom(dest => new AmountDto() { Amount = Convert.ToDouble(dest.BillAmount), Currency = dest.BillAmountCurrency }))
                               .ForMember(src => src.Status, opt => opt.MapFrom(dest => dest.Status.ToString()));

            CreateMap<BillPaymentTransaction, BillPaymentBillSummaryResponseDto>()
                              .ForMember(src => src.BillAmount, opt => opt.MapFrom(dest => new AmountDto() { Amount = (double)dest.BillAmount, Currency = dest.BillAmountCurrency }))
                              .ForMember(src => src.FeeAmount, opt => opt.MapFrom(dest => new AmountDto() { Amount = (double)dest.FeeAmount, Currency = dest.FeeAmountCurrency }))
                              .ForMember(src => src.ProviderCharge, opt => opt.MapFrom(dest => new AmountDto() { Amount = (double)dest.ProviderCharge, Currency = dest.ProviderChargeCurrency }))
                              .ForMember(src => src.ToBePaidAmount, opt => opt.MapFrom(dest => new AmountDto() { Amount = (double)dest.ConvertedBillAmount, Currency = dest.ConvertedBillAmountCurrency }))
                              .ForMember(src => src.Fields, opt => opt.MapFrom(dest => dest.Biller.BillerIOs))
                              .ForMember(src => src.NickName, opt => opt.MapFrom(dest => dest.Biller.NickName))
                              .ForMember(src => src.TotalAmount, opt => opt.MapFrom(dest => new AmountDto() { Amount = (double)dest.TotalAmount, Currency = dest.TotalAmountCurrency }));
        }
    }
}
