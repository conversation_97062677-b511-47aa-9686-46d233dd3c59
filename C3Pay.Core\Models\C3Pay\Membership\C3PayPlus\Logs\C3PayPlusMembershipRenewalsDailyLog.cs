﻿using System;

namespace C3Pay.Core.Models.C3Pay.Membership.C3PayPlus.Logs
{
    public class C3PayPlusMembershipRenewalsDailyLog
    {
        /// <summary>Log ID.</summary>
        public Guid Id { get; set; }

        /// <summary>The target renewal date. Example: If today is the 15th, this would represent memberships created on the 15th of any month.</summary>
        public DateTime RenewingFor { get; set; }

        /// <summary>The exact timestamp when the renewal process started.</summary>
        public DateTime? JobStartTime { get; set; }

        /// <summary>The exact timestamp when the renewal process completed.</summary>
        public DateTime? JobEndTime { get; set; }

        /// <summary>The duration of the renewal process in mins (JobEndTime - JobStartTime).</summary>
        public double? JobDurationInMinutes { get; set; }

        /// <summary>Total number of renewals expected to process.</summary>
        public int TotalRenewalsExpectedToProcess { get; set; }

        /// <summary>Total number of renewals completed successfully.</summary>
        public int RenewalsCompletedSuccessfully { get; set; }

        /// <summary>Number of successful unsubscriptions.</summary>
        public int SuccessfulUnsubscribes { get; set; }

        /// <summary>Number of total skipped renewals.</summary>
        public int TotalSkippedRenewals { get; set; }

        // Unsubscription reason breakdowns
        public int UnsubscribedDueToDeletedUsers { get; set; }
        public int UnsubscribedDueToUserDecision { get; set; }
        public int UnsubscribedDueToMissingAgeInformation { get; set; }
        public int UnsubscribedDueToExceedingAgeLimit { get; set; }
        public int UnsubscribedDueToNoSalaryCreditedInLast3Months { get; set; }
        public int UnsubscribedDueToCardIsNotActive { get; set; }
        public int UnsubscribedDueToCardIsBlocked { get; set; }
        public int UnsubscribedDueToOtherReasons { get; set; }

        // Skipped renewals due to technical/config issues
        public int SkippedDueToDormantCardCheckFailure { get; set; }
        public int SkippedRenewalsDueToBalanceEnquirySubscriptionNotFound { get; set; }
        public int SkippedRenewalsDueToMissingSecuritySmsSubscriptionCode { get; set; }
        public int SkippedRenewalsDueToMissingSecuritySmsSubscriptionFee { get; set; }
        public int SkippedRenewalsDueToIssueSubscribingBackToSecuritySms { get; set; }
        public int SkippedRenewalsDueToMissingSalaryAlertSubscriptionCode { get; set; }
        public int SkippedRenewalsDueToMissingSalaryAlertSubscriptionFee { get; set; }
        public int SkippedRenewalsDueToIssueSubscribingBackToSalaryAlert { get; set; }
        public int SkippedRenewalsDueToIssueSubscribingBackToNone { get; set; }
        public int SkippedRenewalsDueToUnableToRetrieveBalance { get; set; }
        public int SkippedRenewalsDueToUnableToConfirmSalaryReceived { get; set; }
        public int SkippedRenewalsDueToUnableToDebitUser { get; set; }
        public int SkippedRenewalsDueToException { get; set; }

        public C3PayPlusRenewalsDailyLogStatus RunStatus { get; set; }
        public string Remarks { get; set; }
    }
}
