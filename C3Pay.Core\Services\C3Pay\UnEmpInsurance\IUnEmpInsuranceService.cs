﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.UnEmpInsurance;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Services.C3Pay.UnEmpInsurance
{
    public interface IUnEmpInsuranceService
    {
        Task<ServiceResponse<UnEmpInsurancePayment>> GetInsurance(Guid userId);

        Task<ServiceResponse> Subscribe(int paymentOptionId, bool haveBalance,  string referralCode, User user, UnEmpInsurancePartnerType partnerType);

        Task<ServiceResponse<UnEmpInsurancePayment>> UpdateProcessedInsurance(UnEmpInsurancePayment payment, User user, bool isInstallment = false, bool isSalaryDebit = false);

        Task<ServiceResponse> UpdateFailedInsurance(string reason, User user);

        Task UpdateExternallySubscribedInfoWithB2B(EnquiryResponseDto externalInsurance, User user);
        Task<ServiceResponse<UnEmpInsurancePayment>> NotifyInstallmentChangeWithB2B(UnEmpInsurancePayment localData, User user);
        Task<ServiceResponse> CreateNewSubscriptionFromB2B(UnEmpInsurancePayment payment, User user, string paymentOption, UnEmpInsurancePartnerType partnerType, bool isSalaryDebit);
        Task UpdateCleverTapUserPropertiesForInsurance(User user, string status, string isAutoPay, string channel);
        Task<ServiceResponse<UnEmpInsurancePayment>> AddNewInsurance(UnEmpInsurancePayment newEntry, UnEmpInsurancePartnerType partnerType); 

        Task<ServiceResponse> CancelInsuranceSubscriptions (List<User> users);
        Task<ServiceResponse<UnEmpInsurancePayment>> UpdateSubcription(Guid Id, bool isAutoPayment, User user);
        Task<ServiceResponse> PublishFailedInsuranceAutoPayments(List<User> users);

        Task<ServiceResponse> NotifyNearDueInstallments();
        Task<ServiceResponse<(List<Installment>, UnEmpInsurancePaymentOption)>> GetInstallmentDetailsAsync(User user);
        Task<ServiceResponse> AddNewInsuranceFromB2B (UnEmpInsurancePayment newEntry, UnEmpInsurancePartnerType partnerType, bool IsWorkerDetailsNotFoundError = false);

        Task<ServiceResponse> CreateNewSubscriptionFromB2BCompleted(UnEmpInsurancePayment payment, User user, string paymentOption, UnEmpInsurancePartnerType partnerType, bool isSalaryDebit);
        Task<ServiceResponse<bool>> UpdateStatusChange(Guid userId, UnEmpInsuranceStatus waitingForSalary, bool IsWorkerNotFoundError = false);
        UnEmpInsuranceStatus[] GetStatusesForActiveEntry();
    }
}
