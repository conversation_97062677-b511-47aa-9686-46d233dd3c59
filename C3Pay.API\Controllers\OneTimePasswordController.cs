﻿using C3Pay.API.Resources;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using Edenred.Common.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Policy;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    public class OneTimePasswordController : ControllerBase
    {
        private readonly string[] _phoneNumberOTPReasons = new string[]
        {
            OTPVerificationReason.ChangePhoneNumber.ToString(),
            OTPVerificationReason.ForgotPhoneNumber.ToString()
        };
        private readonly ITextMessageSenderService _textMessageService;
        private readonly ITableStorageService _tableStorageService;
        private readonly string _otpStorageTableName = "otp";
        private readonly string _tempAccountPrefix = "Temp_";
        private readonly IIdentityService _identityService;
        private readonly GeneralSettings _generalSettings;
        private readonly IUserService _userService;
        private IHttpContextAccessor _httpContextAccessor;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="textMessageService"></param>
        /// <param name="generalSettings"></param>
        /// <param name="tableStorageService"></param>
        /// <param name="identityService"></param>
        public OneTimePasswordController(ITextMessageSenderService textMessageService,
            IOptions<GeneralSettings> generalSettings,
            ITableStorageService tableStorageService,
            IIdentityService identityService, IHttpContextAccessor httpContextAccessor, IUserService userService)
        {
            this._tableStorageService = tableStorageService;
            this._generalSettings = generalSettings.Value;
            this._textMessageService = textMessageService;
            this._identityService = identityService;
            _httpContextAccessor = httpContextAccessor;
            _userService = userService;
        }

        /// <summary>
        /// Sends OTP to user. Reason values:
        /// SignUp
        /// ForgotPassword
        /// ChangePassword
        /// ForgotPhoneNumber
        /// ChangePhoneNumber
        /// MobileRechargeBeneficiary
        /// CashPickupBeneficiary
        /// BankTransferBeneficiary
        /// MoneyTransfer
        /// AutoUnblock
        /// BillPayment
        /// SalaryAdvance
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> Send(SendOTPRequestDto request)
        {
            if (!Enum.IsDefined(typeof(OTPVerificationReason), request.Reason))
            {
                return this.BadRequest(ConstantParam.InvalidOneTimePasswordReason);
            }

            var username = request.PhoneNumber.ToZeroPrefixedPhoneNumber();

            var phoneNumber = username;

            if (_phoneNumberOTPReasons.Contains(request.Reason))
            {
                username = string.Concat(_tempAccountPrefix, username);

                await this._identityService.DeleteUserAccountAsync(username);
            }

            var userAccountExistsResult = await this._identityService.UserAccountExistsAsync(username);

            if (!userAccountExistsResult.IsSuccessful)
            {
                return BadRequest(userAccountExistsResult.ErrorMessage);
            }

            var userExists = userAccountExistsResult.Data;

            if (!userExists)
            {
                if (request.Reason != BaseEnums.OTPVerificationReason.SignUp.ToString() && !_phoneNumberOTPReasons.Contains(request.Reason))
                {
                    return this.Ok();
                }

                var generatePasswordResult = await this._identityService.GeneratePasswordAsync();

                var password = generatePasswordResult.Data;

                await this._identityService.CreateUserAccountAsync(username, password, phoneNumber, null);
            }

            var oneTimePasswordResult = await this._identityService.RequestPhoneVerificationAsync(username);

            if (!oneTimePasswordResult.IsSuccessful)
            {
                return this.BadRequest(oneTimePasswordResult.ErrorMessage);
            }

            var oneTimePassword = oneTimePasswordResult.Data;

            Enum.TryParse(request.Reason, out OTPVerificationReason otpVerificationReason);

            var reason = EnumUtility.GetDescriptionFromEnumValue(otpVerificationReason);

            await this._textMessageService.SendOTPMessage(new Core.Models.SendOTPSMSRequest()
            {
                PhoneNumber = request.PhoneNumber.ToShortPhoneNumber(),
                Reason = reason,
                OneTimePassword = oneTimePassword,
                RetryCount = request.RetryCount,
            });

            return this.Ok("Success");
        }


        /// <summary>
        /// Possible Results:
        /// Verified
        /// InvalidOTP
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("verification")]
        public async Task<ActionResult<string>> Verify(VerifyOTPRequestDto request)
        {
            if (!Enum.IsDefined(typeof(OTPVerificationReason), request.Reason))
            {
                return this.BadRequest(ConstantParam.InvalidOneTimePasswordReason);
            }

            var username = request.PhoneNumber.ToZeroPrefixedPhoneNumber();

            if (_phoneNumberOTPReasons.Contains(request.Reason))
            {
                username = string.Concat(_tempAccountPrefix, username);
            }

            var verificationResult = await this._identityService.VerifyPhoneAsync(username, request.OTP);

            if (verificationResult.IsSuccessful == false)
            {
                if (this._generalSettings.SkipOtp == false)
                {
                    return Ok(OTPVerificationResult.InvalidOTP.ToString());
                }
                else
                {
                    // Here, SkipOTP is allowed, if the OTP is not skippable, exit.
                    if (request.OTP != "123456" && request.OTP != "696969")
                    {
                        return Ok(OTPVerificationResult.InvalidOTP.ToString());
                    }
                }
            }

            if (_phoneNumberOTPReasons.Contains(request.Reason))
            {
                await this._identityService.DeleteUserAccountAsync(username);
            }

            if (request.Reason == OTPVerificationReason.SignUp.ToString()
                || request.Reason == OTPVerificationReason.ForgotPassword.ToString()
                || request.Reason == OTPVerificationReason.AutoUnblock.ToString())
            {
                var zeroPrefixedPhoneNumber = request.PhoneNumber.ToZeroPrefixedPhoneNumber();

                var result = await _tableStorageService.SetEntityAsync(_otpStorageTableName, new ValueEntity(zeroPrefixedPhoneNumber, request.OTP));

                if (!result.IsSuccessful)
                {
                    return this.BadRequest(result.ErrorMessage);
                }
            }

            return this.Ok(OTPVerificationResult.Verified.ToString());
        }

        /// <summary>
        /// Sends OTP to user. Reason values:
        /// SignUp
        /// ForgotPassword
        /// ChangePassword
        /// ForgotPhoneNumber
        /// ChangePhoneNumber
        /// MobileRechargeBeneficiary
        /// CashPickupBeneficiary
        /// BankTransferBeneficiary
        /// MoneyTransfer
        /// AutoUnblock
        /// BillPayment
        /// SalaryAdvance
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost("v2")]
        public async Task<ActionResult> SendV2(SendOTPDto request)
        {
            if (!Enum.IsDefined(typeof(OTPVerificationReason), request.Reason))
            {
                return this.BadRequest(ConstantParam.InvalidOneTimePasswordReason);
            }
            var username = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;
            username = username.ToZeroPrefixedPhoneNumber();
            var phoneNumber = username;
            if (_phoneNumberOTPReasons.Contains(request.Reason))
            {
                username = string.Concat(_tempAccountPrefix, username);
                await this._identityService.DeleteUserAccountAsync(username);
            }
            var userAccountExistsResult = await this._identityService.UserAccountExistsAsync(username);
            if (!userAccountExistsResult.IsSuccessful)
            {
                return BadRequest(userAccountExistsResult.ErrorMessage);
            }
            var userExists = userAccountExistsResult.Data;
            if (!userExists)
            {
                if (request.Reason != BaseEnums.OTPVerificationReason.SignUp.ToString() && !_phoneNumberOTPReasons.Contains(request.Reason))
                {
                    return this.Ok();
                }
                var generatePasswordResult = await this._identityService.GeneratePasswordAsync();
                var password = generatePasswordResult.Data;
                await this._identityService.CreateUserAccountAsync(username, password, phoneNumber, null);
            }
            var oneTimePasswordResult = await this._identityService.RequestPhoneVerificationAsync(username);
            if (!oneTimePasswordResult.IsSuccessful)
                return this.BadRequest(oneTimePasswordResult.ErrorMessage);

            var oneTimePassword = oneTimePasswordResult.Data;
            Enum.TryParse(request.Reason, out OTPVerificationReason otpVerificationReason);
            var reason = EnumUtility.GetDescriptionFromEnumValue(otpVerificationReason);
            await this._textMessageService.SendOTPMessage(new Core.Models.SendOTPSMSRequest()
            {
                PhoneNumber = username.ToShortPhoneNumber(),
                Reason = reason,
                OneTimePassword = oneTimePassword,
                RetryCount = request.RetryCount,
            });
            return this.Ok("Success");
        }
    }
}
