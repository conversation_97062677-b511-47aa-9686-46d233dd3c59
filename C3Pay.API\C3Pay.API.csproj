﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
	</PropertyGroup>

	<ItemGroup>
	  <None Remove="Docs\LifeInsurance-Terms.pdf" />
	  <None Remove="Fonts\Montserrat-Bold.ttf" />
	  <None Remove="Fonts\Montserrat-SemiBold.ttf" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="Docs\LifeInsurance-Terms.pdf">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </EmbeddedResource>
	  <EmbeddedResource Include="Fonts\Montserrat-Bold.ttf">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </EmbeddedResource>
	  <EmbeddedResource Include="Fonts\Montserrat-SemiBold.ttf">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
		<PackageReference Include="IdentityServer4.AccessTokenValidation" Version="3.0.1" />
		<PackageReference Include="OpenTelemetry" Version="1.8.1" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.8.1" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.8.1" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.8.1" />
		<PackageReference Include="OpenTelemetry.Instrumentation.SqlClient" Version="1.8.0-beta.1" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.7.0" />
		<PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.7.0" />
		<PackageReference Include="OpenTelemetry.Exporter.Jaeger" Version="1.5.1" />
		<PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.AzureKeyVault.HostingStartup" Version="2.0.4" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
		<PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="8.1.1" />
		<PackageReference Include="Microsoft.Azure.Cosmos.Table" Version="1.0.8" />
		<PackageReference Include="Microsoft.Azure.Storage.Common" Version="11.2.3" />
		<PackageReference Include="Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider" Version="5.1.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="3.1.24" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.6" />
		<PackageReference Include="SendGrid" Version="9.29.3" />
		<PackageReference Include="Serilog" Version="4.1.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="Serilog.Sinks.OpenTelemetry" Version="4.1.0" />
		<PackageReference Include="Serilog.Sinks.MSSqlServer" Version="7.0.2" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
		<PackageReference Include="System.Data.OleDb" Version="8.0.1" />
		<PackageReference Include="System.Resources.ResourceManager" Version="4.3.0" />
	</ItemGroup>

	<ItemGroup>
		<WCFMetadata Include="Connected Services" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Resources\BillPayment\BillPaymentResources\" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\C3Pay.Services\C3Pay.Services.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Properties\NolGuideResources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>NolGuideResources.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Properties\NolGuide.Resources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>NolGuide.Resources.resx</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="Properties\NolGuideResources.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>NolGuideResources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Properties\NolGuide.Resources.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>NolGuide.Resources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Resources\BillPayment\BillPaymentResources\NolGuide.Resource.hi.resx">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Resources\BillPayment\BillPaymentResources\NolGuide.Resource.resx">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </EmbeddedResource>
	</ItemGroup>

	<ProjectExtensions>
		<VisualStudio>
			<UserProperties appsettings_1json__JsonSchema="" />
		</VisualStudio>
	</ProjectExtensions>


</Project>
