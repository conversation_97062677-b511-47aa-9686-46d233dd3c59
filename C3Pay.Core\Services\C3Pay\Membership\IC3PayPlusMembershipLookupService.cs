﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace C3Pay.Core.Services.C3Pay.Membership
{
    public interface IC3PayPlusMembershipLookupService
    {
        Task<IEnumerable<C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeDto>> GetLifeInsuranceNomineeRelationshipTypes(string languageCode = "en");

        Task<string> GetLifeInsuranceNomineeRelationshipTypeName(int? relationshipTypeId, string languageCode = "en");

        Task<IEnumerable<C3PayPlusMembershipSupportedCountryDto>> GetLifeInsuranceSupportedCountries();

        Task<IList<Subscription>> GetCachedSubscriptions();

        Task<IList<C3PayPlusMembershipBenefit>> GetCachedBenefits(string languageCode = "en");

        Task<List<string>> GetPolicyDetailsWithTranslation(string languageCode = "en");

        Task<IList<C3PayPlusMembershipBenefit>> GetBenefitsByMembershipTypeWithTranslation(int membershipId, string languageCode = "en");

        Task<bool> IsC3PayPlusMembershipActive(string userPhoneNumber);

        Task<bool> IsC3PayPlusMembershipActiveAndNotCancelled(string userPhoneNumber);

        Task<C3PayPlusMembershipType> GetC3PayPlusMembershipType(Guid userId);

        Task<C3PayPlusMembershipExperiment> GetExperimentDetails(C3PayPlusMembershipExperiments experiment);

    }
}
