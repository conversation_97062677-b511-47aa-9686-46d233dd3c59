﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Structs;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Repositories
{
    public interface IMobileRechargeTransactionRepository : IRepository<MobileRechargeTransaction>
    {
        Task<List<MobileRechargeDetails>> GetUserTransactionsDetails(Guid userId, MobileRechargeType? rechargeType, int? skipValue, int? pageSize);
        Task<Tuple<List<MobileRechargeTransactionStruct>, int>> Search(List<Expression<Func<MobileRechargeTransaction, bool>>> searchMobileRechargeTransactionParameters, int? page, int? size);
        Task<List<MobileRechargeDetails>> GetRecentMobileRechargeTransactions(Guid userId, MobileRechargeType? rechargeType, int? count);
        Task<bool> CheckLastTransactionFailedWithErrorAsync(Guid userId, Guid beneficiaryId, string errorContext);
        Task<MobileRechargeTransaction> GetTransactionAsync(Guid beneficiaryId);
        Task<MobileRechargeTransaction> GetTransactionByIdAsync(Guid transactionId);
    }
}
