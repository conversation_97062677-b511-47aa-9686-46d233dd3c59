﻿using AutoMapper;
using C3Pay.API.Controllers;
using C3Pay.API.Resources.MobileRecharge;
using C3Pay.Core;
using C3Pay.Core.Services;
using C3Pay.Core.Services.Security;
using Edenred.Common.Core;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Moq;
using System;
using System.Threading.Tasks;
using Xunit;


public class MobileRechargeControllerTests
{

    private readonly Mock<IMobileRechargeService> _serviceMock;
    private readonly Mock<IHttpContextAccessor> _httpContextAccessorMock;
    private readonly Mock<ISecurityService> _securityServiceMock;
    private readonly Mock<ILookupService> _lookupServiceMock;
    private readonly Mock<IUserService> _userServiceMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IFeatureManager> _featureManagerMock;
    private readonly Mock<ILogger<MobileRechargeController>> _loggerMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;


    private readonly MobileRechargeController _controller;



    public MobileRechargeControllerTests()
    {

        _serviceMock = new Mock<IMobileRechargeService>();
        _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        _securityServiceMock = new Mock<ISecurityService>();
        _lookupServiceMock = new Mock<ILookupService>();
        _userServiceMock = new Mock<IUserService>();
        _mapperMock = new Mock<IMapper>();
        _featureManagerMock = new Mock<IFeatureManager>();
        _loggerMock = new Mock<ILogger<MobileRechargeController>>();

        _controller = new MobileRechargeController(
            _serviceMock.Object,
            _httpContextAccessorMock.Object,
            _securityServiceMock.Object,
            _lookupServiceMock.Object,
            _userServiceMock.Object,
            _mapperMock.Object,
            _featureManagerMock.Object,
            _loggerMock.Object
        );
        _unitOfWorkMock = new Mock<IUnitOfWork>();

    }

    [Fact]
    public async Task UpdateOperator_ShouldReturnBadRequest_WhenBeneficiaryIdIsEmpty()
    {
        // Arrange
        var request = new UpdateOperatorDto { BeneficiaryId = Guid.Empty };

        // Act
        var result = await _controller.UpdateOperator(request);

        // Assert
        var badRequest = Assert.IsType<BadRequestObjectResult>(result);
        Assert.Equal("BeneficiaryId is required.", badRequest.Value);
    }

    [Fact]
    public async Task UpdateOperator_ShouldReturnServerError_WhenServiceFails()
    {
        // Arrange
        var requestId = Guid.NewGuid();
        var request = new UpdateOperatorDto { BeneficiaryId = requestId };

        _serviceMock.Setup(s => s.UpdateOperator(requestId))
            .ReturnsAsync(new ServiceResponse(false, "Service failed."));

        // Act
        var result = await _controller.UpdateOperator(request);

        // Assert
        var errorResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(500, errorResult.StatusCode);
        Assert.Equal("Service failed.", errorResult.Value);
    }

    [Fact]
    public async Task UpdateOperator_ShouldReturnOk_WhenServiceSucceeds()
    {
        // Arrange
        var requestId = Guid.NewGuid();
        var request = new UpdateOperatorDto { BeneficiaryId = requestId };

        _serviceMock.Setup(s => s.UpdateOperator(requestId))
            .ReturnsAsync(new ServiceResponse(true, "Operator updated successfully."));

        // Act
        var result = await _controller.UpdateOperator(request);

        // Assert
        Assert.IsType<OkObjectResult>(result);
    }
}
