﻿namespace C3Pay.Core.Models
{
    public class DailyRmtProfileStat
    {
        public int TotalCount { get; set; }
        public int AttachmentErrorCount { get; set; }
        public int UpdateFailedCount {  get; set; }
        public int InvalidUpdateRecordStatusCount { get; set; }
        public int NoRecordsExistingCount { get; set; }
        public int TotalFailuresCount { get; set; }
        public int PendingCount { get; set; }
        public int DeletedCount { get; set; }
        public int RejectedCount { get; set; }
        public int ReactivatedCount { get; set; }
        public int DuplicateEidCount {  get; set; }
        public int CreatedCount { get; set; }
        public int UpdatedCount { get; set; }
        public int UsersRmtStatusCreatedCount { get; set; }
    }
}
