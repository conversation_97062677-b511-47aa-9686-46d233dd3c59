using AutoMapper;
using C3Pay.API.Models;
using C3Pay.API.Resources.UnemploymentInsurance;
using C3Pay.Core.Models;
using C3Pay.Core;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Polly;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using C3Pay.Services;
using Microsoft.Extensions.Options;
using C3Pay.Core.Services.C3Pay.UnEmpInsurance;
using Microsoft.Extensions.Logging;
using C3Pay.API.Resources.UnEmpInsurance;
using C3Pay.API.Resources;
using Edenred.Common.Core;

namespace C3Pay.API.Controllers.External
{
    /// <summary>
    ///  External Unemployment Insurance Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    [Authorize(AuthenticationSchemes = AuthenticationScheme.DualAuthentication)]
    public class ExternalUnempInsuranceController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly ILookupService _lookupService;
        private readonly IUnEmpInsuranceLookupService _unEmpInsuranceLookupService;
        private readonly IUnEmpInsuranceService _unEmpInsuranceService;
        private readonly IUserService _userService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="mapper"></param>
        /// <param name="lookupService"></param>
        /// <param name="unEmpInsuranceLookupService"></param>
        /// <param name="userService"></param>
        /// <param name="unEmpInsuranceService"></param>
        public ExternalUnempInsuranceController(
            IMapper mapper,
            ILookupService lookupService,
            IUnEmpInsuranceLookupService unEmpInsuranceLookupService,
            IUserService userService,
            IUnEmpInsuranceService unEmpInsuranceService)
        {
            _mapper = mapper;
            this._lookupService = lookupService;
            this._unEmpInsuranceLookupService = unEmpInsuranceLookupService;
            this._unEmpInsuranceService = unEmpInsuranceService;
            this._userService = userService;
        }

        /// <summary>
        /// Payment Options
        /// </summary>
        /// <returns></returns>
        [HttpGet("{userId}/payment/options")]
        public async Task<ActionResult<IEnumerable<UnEmpInsurancePaymentOptionDto>>> GetUnemploymentInsurancePaymentOptions(Guid userId)
        {
            var getUserResponse = await _userService.GetUserById(userId);

            if (!getUserResponse.IsSuccessful)
            {
                return this.BadRequest(getUserResponse.Data);
            }
            var user = getUserResponse.Data;

            var paymentOptions = await this._lookupService.GetUserUnEmpInsurancePaymentOptions(user, UnEmpInsurancePartnerType.ExchangeHouse);

            var mappedPaymentOptions = _mapper.Map<List<UnEmpInsurancePaymentOptionDto>>(paymentOptions.Data);

            return Ok(mappedPaymentOptions);
        }

       /// <summary>
       /// Get User Subscription
       /// </summary>
       /// <param name="userId"></param>
       /// <param name="emiratesId"></param>
       /// <returns></returns>
        [HttpGet("{userId}/{emiratesId}/subscription-detail")]
        public async Task<ActionResult<UnEmpInsuranceSubscriptionResponseDto>> GetUserSubscription(Guid userId, string emiratesId)
        {
            var getUserResponse = await _userService.GetUserById(userId);

            if (!getUserResponse.IsSuccessful)
            {
                return this.BadRequest(getUserResponse.Data);
            }

            var user = getUserResponse.Data;

            if (string.IsNullOrEmpty(emiratesId))
            {
                return this.BadRequest("EmiratesId not passed");
            }

            user.CardHolder.EmiratesId = emiratesId;
            var responseDto = new UnEmpInsuranceSubscriptionResponseDto
            {
                Status = nameof(UnEmpInsuranceStatus.UnSubscribed),
            };
            
            var localResponse = await _unEmpInsuranceService.GetInsurance(user.Id);

            var allActiveStatuses = _unEmpInsuranceService.GetStatusesForActiveEntry();

            if (localResponse.IsSuccessful && localResponse.Data != null && allActiveStatuses.Contains(localResponse.Data.Status))
            {
                if (localResponse.Data?.IsSubscribedExternally == true && (localResponse.Data.PaidInstallments < localResponse.Data.TotalInstallments))
                {
                    // Check for Installment mismatch based on last Sync Date
                    if (localResponse.Data.LastSyncDate != null)
                    {
                        bool isGreaterThan24Hours = DateTime.Now.Subtract((DateTime)localResponse.Data.LastSyncDate) > TimeSpan.FromHours(24);
                        if (isGreaterThan24Hours)
                        {
                            localResponse = await _unEmpInsuranceService.NotifyInstallmentChangeWithB2B(localResponse.Data, user);
                        }
                    }
                }
            }
            else
            {
                // Calling Dubai Insurance API
                var externalResponse = await _unEmpInsuranceLookupService.GetExternalInsuranceEnquiry(user);
                if (externalResponse.IsSuccessful && externalResponse.Data != null)
                {
                    // If user already subscribed from external 
                    if (string.Equals(externalResponse.Data.HasPolicy, "Yes", StringComparison.OrdinalIgnoreCase))
                    {
                        // Create New Entry in DB with LastSyncDate as Today 
                        var paymentEntry_AlreadySubscribed = _mapper.Map<UnEmpInsurancePayment>(externalResponse.Data);
                        paymentEntry_AlreadySubscribed.UserId = user.Id;
                        paymentEntry_AlreadySubscribed.Source = UnEmpInsuranceSource.MySalary;
                        if (localResponse.Data == null)
                            localResponse = await _unEmpInsuranceService.AddNewInsurance(paymentEntry_AlreadySubscribed, UnEmpInsurancePartnerType.ExchangeHouse);
                        else if (localResponse.Data?.Status == UnEmpInsuranceStatus.UnSubscribed)
                            localResponse = await _unEmpInsuranceService.UpdateProcessedInsurance(paymentEntry_AlreadySubscribed, user, false);

                        // Trigger to B2B - AlreadySubscribedEvent
                        await _unEmpInsuranceService.UpdateExternallySubscribedInfoWithB2B(externalResponse.Data, user);

                        // Clever Tap
                        await _unEmpInsuranceService.UpdateCleverTapUserPropertiesForInsurance(user, Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.Subscribed),
                            "No", Enum.GetName(typeof(UnEmpInsuranceSource), UnEmpInsuranceSource.External));
                    }
                    else if (string.Equals(externalResponse.Data.HasPolicy, "No", StringComparison.OrdinalIgnoreCase))
                    {
                        var paymentEntry_NotSubscribed = new UnEmpInsurancePayment()
                        {
                            UserId = user.Id,
                            Status = UnEmpInsuranceStatus.UnSubscribed,
                            Source = UnEmpInsuranceSource.MySalary,
                            Category = externalResponse.Data?.Category == "A" ? UnEmpInsuranceCategory.A : UnEmpInsuranceCategory.B
                        };

                        if (localResponse.Data == null)
                            localResponse = await _unEmpInsuranceService.AddNewInsurance(paymentEntry_NotSubscribed, UnEmpInsurancePartnerType.ExchangeHouse);

                        // Clever Tap 
                        await _unEmpInsuranceService.UpdateCleverTapUserPropertiesForInsurance(user, Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.UnSubscribed), "", "");
                    }
                }
                else
                {
                    // Dubai Insurance API failed
                    return BadRequest(externalResponse.ErrorMessage);
                }
            }


            responseDto.Status = localResponse.Data != null ? Enum.GetName(typeof(UnEmpInsuranceStatus), localResponse.Data.Status) : responseDto.Status;

            // Get Video only for Unsubsribed
            if (responseDto.Status == Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.UnSubscribed))
            {
                responseDto.Video = await GetVideoUrls(user);
            }
            else
            {
                responseDto.Detail = localResponse.Data != null ? _mapper.Map<UnEmpInsuranceSubscriptionDetailsDto>(localResponse.Data) : responseDto.Detail;
                responseDto.Detail.PhoneNumber = $"+{user.PhoneNumber[2..]}";
            }
            if (localResponse.Data != null)
            {
                if (responseDto.Status == Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.Retrying))
                {
                    if (localResponse.Data.Remarks == EnumUtility.GetDescriptionFromEnumValue(UnemploymentInsuranceValidationMessage.WorkerDetailsNotFound))
                    {
                        responseDto.RetryReason = localResponse.Data.Remarks;
                    }
                }
                responseDto.IsSubscribedByCorporate = localResponse.Data.Source == UnEmpInsuranceSource.Corporate;
            }
            return Ok(responseDto);
        }


        /// <summary>
        /// Subscribe API
        /// Possible results: UserAlreadySubscribed, InvalidPaymentOption
        /// </summary>
        /// <param name="paymentRequest"></param>
        /// <returns></returns>
        [HttpPost("user/subscribe")]
        public async Task<ActionResult> Subscribe(UnEmpInsuranceSubscriptionRequestDto paymentRequest)
        {
            var getUserResponse = await _userService.GetUserById(paymentRequest.UserId);

            if (!getUserResponse.IsSuccessful)
            {
                return this.BadRequest(getUserResponse.Data);
            }
            var user = getUserResponse.Data; 

            var processPaymentResult = await this._unEmpInsuranceService.Subscribe(paymentRequest.PaymentOptionId, paymentRequest.IsBalanceAvailable, paymentRequest.ReferralCode, user, UnEmpInsurancePartnerType.ExchangeHouse);

            if (!processPaymentResult.IsSuccessful)
            {
                return this.BadRequest(processPaymentResult.ErrorMessage);
            }

            return Ok();
        }

        #region Private Methods
        private async Task<List<UnEmpInsuranceVideoDto>> GetVideoUrls(User user)
        {
            var hindiNationalities = new string[] { nameof(BaseEnums.MultimediaCountry.PAK), nameof(BaseEnums.MultimediaCountry.IND) };
            var userShouldSeeHindiVideo = hindiNationalities.Contains(user.CardHolder.Nationality);
            var videoUrls = new List<UnEmpInsuranceVideoDto>();
            var unEmpInsuranceMultimediaResources = await _lookupService.GetMultimediaResources(feature: (int)FeatureType.UnEmpInsurance_MySalary);

            foreach (var resource in unEmpInsuranceMultimediaResources.Data)
            {
                var res = new UnEmpInsuranceVideoDto()
                {
                    LanguageCode = resource.Language,
                    Url = resource.Url,
                    IsDefault = false
                };

                if ((res.LanguageCode == "hi" && userShouldSeeHindiVideo) || (res.LanguageCode != "hi" && !userShouldSeeHindiVideo))
                {
                    res.IsDefault = true;
                }
                videoUrls.Add(res);
            }
            return videoUrls;
        }

        #endregion
    }
}
