﻿using C3Pay.Core.Models;
using Edenred.Common.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace C3Pay.Data.Configurations
{
    public class NonRegisteredIdentificationConfiguration : IEntityTypeConfiguration<NonRegisteredIdentification>
    {
        public void Configure(EntityTypeBuilder<NonRegisteredIdentification> builder)
        {
            builder.ToTable("NonRegisteredIdentification");

            builder.<PERSON>Key(c => c.Id);

            builder.Property(c => c.Id).HasDefaultValueSql("NEWSEQUENTIALID()");

            builder.Property(c => c.CardHolderId)
               .HasMaxLength(15);

            builder.Property(c => c.CardSerialNumber)
                .HasMaxLength(10);

            builder.Property(c => c.FirstName)
               .HasMaxLength(100);

            builder.Property(c => c.LastName)
               .HasMaxLength(100);

            builder.Ignore(eu => eu.FullName);

            builder.Ignore(eu => eu.NationalityCode);

            builder.Property(eu => eu.Type).HasDefaultValue(Enums.IdentificationType.EmiratesId);

            builder.Property(c => c.DocumentNumber)
               .HasMaxLength(15);

            builder.Property(c => c.CardNumber)
               .HasMaxLength(9);

            builder.Property(c => c.Nationality)
               .HasMaxLength(3);

            builder.Property(c => c.Gender)
               .HasMaxLength(6);

            builder.Property(c => c.FrontScanFileName)
               .HasMaxLength(100);

            builder.Property(c => c.BackScanFileName)
               .HasMaxLength(100);

            builder.Property(c => c.SelfieFileName)
               .HasMaxLength(100);

            builder.Property(c => c.ServiceReference)
               .HasMaxLength(100);

            builder.Property(c => c.PassportNumber)
                .HasMaxLength(50);

            builder.Property(c => c.C3RegistrationId)
             .HasMaxLength(50);
        }
    }
}
