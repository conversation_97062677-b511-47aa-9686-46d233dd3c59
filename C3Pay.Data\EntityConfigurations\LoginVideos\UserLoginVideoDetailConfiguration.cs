﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Reflection.Emit;

namespace C3Pay.Data.EntityConfigurations.LoginVideos
{
    public class UserLoginVideoDetailConfiguration : IEntityTypeConfiguration<UserLoginVideoDetail>
    {
        public void Configure(EntityTypeBuilder<UserLoginVideoDetail> builder)
        {
            builder.ToTable("UserLoginVideoDetails");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.ViewCount)
                .HasDefaultValue(1)
                .IsRequired();

            builder.HasIndex(v => new { v.UserId, v.VideoId })
            .IsUnique();
        }
    }
}
