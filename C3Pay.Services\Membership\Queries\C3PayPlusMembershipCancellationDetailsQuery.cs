﻿using AutoMapper;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using C3Pay.Core.Repositories;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay.Membership;
using C3Pay.Services.Helper.Membership;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Translations = C3Pay.Core.C3PayPlusMembershipTranslationTextCode;

namespace C3Pay.Services.Membership.Queries
{
    public class C3PayPlusMembershipCancellationDetailsQuery : IRequest<Result<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipCancellationDetailsDto>>>>
    {
        public string LanguageCode { get; set; } = "en";
        public string UserPhoneNumber { get; set; }
        public CancellationToken CancellationToken { get; set; }

        public static Result IsQueryValid(C3PayPlusMembershipCancellationDetailsQuery query)
        {
            if (query is null)
            {
                return Result.Failure(Errors.C3PayPlus.EmptyRequest);
            }

            if (string.IsNullOrWhiteSpace(query.UserPhoneNumber))
            {
                return Result.Failure(Errors.C3PayPlus.NoPhoneNumberSent);
            }
            return Result.Success();
        }

    }

    public class C3PayPlusMembershipCancellationDetailsQueryHandler : IRequestHandler<C3PayPlusMembershipCancellationDetailsQuery, Result<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipCancellationDetailsDto>>>>
    {
        private readonly ILogger _logger;
        private readonly IUserRepository _userRepository;
        private readonly IExperimentService _experimentService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IC3PayPlusMembershipLookupService _c3PayPlusLookupService;
        private readonly IC3PayPlusMembershipTranslationService _translationService;
        private readonly IFeatureManager _featureManager;
        private readonly IC3PayPlusMembershipLookupService _lookupService;

        private string _languageCode { get; set; } = "en";

        public C3PayPlusMembershipCancellationDetailsQueryHandler(
            ILogger<C3PayPlusMembershipCancellationDetailsQueryHandler> logger,
            IUserRepository userRepository,
            IExperimentService experimentService,
            IUnitOfWork unitOfWork,
             IMapper mapper, IC3PayPlusMembershipLookupService c3PayPlusMembershipLookupService,
             IC3PayPlusMembershipTranslationService translationService,
             IFeatureManager featureManager, IC3PayPlusMembershipLookupService lookupService)
        {
            _logger = logger;
            _userRepository = userRepository;
            _experimentService = experimentService;
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _c3PayPlusLookupService = c3PayPlusMembershipLookupService;
            _translationService = translationService;
            _featureManager = featureManager;
            _lookupService = lookupService;
        }
        public async Task<Result<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipCancellationDetailsDto>>>> Handle(C3PayPlusMembershipCancellationDetailsQuery request, CancellationToken cancellationToken)
        {
            // If C3Pay+ is unavailable, exit.
            var isEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.GlobalC3PayPlus);
            if (isEnabled == false)
            {
                _logger.LogError(Errors.C3PayPlus.Unavailable.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipCancellationDetailsDto>>>(Errors.C3PayPlus.Unavailable);
            }


            // Validate query.
            var isQueryValid = C3PayPlusMembershipCancellationDetailsQuery.IsQueryValid(request);
            if (isQueryValid.IsFailure)
            {
                _logger.LogError(isQueryValid.Error.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipCancellationDetailsDto>>>(isQueryValid.Error);
            }


            // If user is not found, deleted, blocked, or is not a C3Pay app user, exit.
            var user = await this._userRepository.GetUserForC3PayPlus(request.UserPhoneNumber, cancellationToken);
            if (user is null)
            {
                _logger.LogError(Errors.C3PayPlus.UserNotFound.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipCancellationDetailsDto>>>(Errors.C3PayPlus.UserNotFound);
            }



            _languageCode = request.LanguageCode;

            var cancellationDetailsDtos = new List<C3PayPlusMembershipCancellationDetailsDto>();

            // Basic Variant
            var membershipUser = await this._unitOfWork.C3PayPlusMembershipUsers.FirstOrDefaultAsync(x => x.UserId == user.Id
            && x.IsActive == true, i => i.C3PayPlusMembershipLifeInsuranceNominees);


            // If a user is part of the C3Pay+ Surcharge experiment, we need to show an extra benefit.
            var surchargeExperiemnt = await this._lookupService.GetExperimentDetails(C3PayPlusMembershipExperiments.Surcharge);
            if (surchargeExperiemnt != null)
            {
                var inSurchargeExperiemnt = await this._unitOfWork.C3PayPlusMembershipExperimentUsers
               .FirstOrDefaultAsync(x => x.CorporateId == user.CardHolder.CorporateId && x.C3PayPlusMembershipExperimentId == surchargeExperiemnt.Id);

                if (inSurchargeExperiemnt != null)
                {
                    var surchargeBenefit = GetSurchargeBenefit();
                    cancellationDetailsDtos.Add(surchargeBenefit);
                }
            }

            var cancellationInsuranceDto = await GetInsuranceBenefit(membershipUser);
            cancellationDetailsDtos.Add(cancellationInsuranceDto);

            //LuckyDraw Benefit
            var cancellationLuckyDrawDto = await GetLuckyDrawBenefit();
            cancellationDetailsDtos.Add(cancellationLuckyDrawDto);

            //Save Money Benefit
            var cancellationSaveMoneyDto = await GetSaveMoneyBenefit(membershipUser);
            cancellationDetailsDtos.Add(cancellationSaveMoneyDto);

            return Result.Success(new C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipCancellationDetailsDto>>()
            {
                IsSuccessful = true,
                Data = cancellationDetailsDtos
            });
        }

        private async Task<C3PayPlusMembershipCancellationDetailsDto> GetInsuranceBenefit(C3PayPlusMembershipUser membershipUser)
        {
            var nomineeDto = _mapper.Map<C3PayPlusMembershipLifeInsuranceNomineeResponseDto>(membershipUser?.C3PayPlusMembershipLifeInsuranceNominees?.FirstOrDefault(i => i.IsDeleted == false));

            string prefix = string.Empty;
            string nomineeName = string.Empty;
            if (nomineeDto?.RelationshipTypeId != null)
            {
                string nomineeRelation = await _c3PayPlusLookupService.GetLifeInsuranceNomineeRelationshipTypeName(nomineeDto.RelationshipTypeId, _languageCode);
                prefix = await _translationService.GetTranslatedText(Translations.InsuranceBenefitValuePrefixTextYour.ToString(), _languageCode) + " " + nomineeRelation;
                nomineeName = nomineeDto?.Name;
            }
            else
            {
                prefix = await _translationService.GetTranslatedText(Translations.InsuranceBenefitValuePrefixTextYour.ToString(), _languageCode);
                nomineeName = await _translationService.GetTranslatedText(Translations.InsuranceBenefitValueTextFamily.ToString(), _languageCode);
            }

            //Insurance Benefit
            var cancellationInsuranceDto = new C3PayPlusMembershipCancellationDetailsDto();
            cancellationInsuranceDto.Id = C3PayPlusBenefitType.LifeInsurance.ToString();
            cancellationInsuranceDto.IconUrl = MembershipHelper.INSURANCE_BENEFIT_ICON_URL;
            cancellationInsuranceDto.BenefitTitle = await _translationService.GetTranslatedText(Translations.InsuranceBenefitTitle.ToString(), _languageCode);

            cancellationInsuranceDto.BenefitValue = new BenefitValue()
            {
                Prefix = prefix,
                Value = nomineeName,
                Suffix = await _translationService.GetTranslatedText(Translations.InsuranceBenefitValueSuffix.ToString(), _languageCode)
            };
            return cancellationInsuranceDto;
        }

        private async Task<C3PayPlusMembershipCancellationDetailsDto> GetLuckyDrawBenefit()
        {
            var cancellationLuckyDrawDto = new C3PayPlusMembershipCancellationDetailsDto();
            cancellationLuckyDrawDto.Id = C3PayPlusBenefitType.LuckyDraw.ToString();
            cancellationLuckyDrawDto.IconUrl = MembershipHelper.LUCKYDRAW_BENEFIT_ICON_URL;
            cancellationLuckyDrawDto.BenefitTitle = await _translationService.GetTranslatedText(Translations.LuckyDrawBenefitTitle.ToString(), _languageCode);

            cancellationLuckyDrawDto.BenefitValue = new BenefitValue()
            {
                Prefix = await _translationService.GetTranslatedText(Translations.LuckyDrawBenefitValuePrefix.ToString(), _languageCode),
                Value = await _translationService.GetTranslatedText(Translations.BenefitsTextAmount10KAed.ToString(), _languageCode),
                Suffix = ""
            };

            return cancellationLuckyDrawDto;
        }

        private async Task<C3PayPlusMembershipCancellationDetailsDto> GetSaveMoneyBenefit(C3PayPlusMembershipUser membershipUser)
        {
            // Add all savings together.
            var membershipSavingsDto = await MembershipHelper.GetMembershipUserAllSavingsAmount(_unitOfWork, membershipUser);

            var totalSavings = membershipSavingsDto.TotalSavingsAmount;

            string savingsValue;
            string savingsPrefix;

            if (totalSavings > 15)
            {
                savingsPrefix = await _translationService.GetTranslatedText(Translations.SaveMoneyBenefitValuePrefixAbove15.ToString(), _languageCode);
                savingsValue = "AED " + totalSavings.ToString();
            }
            else
            {
                savingsPrefix = await _translationService.GetTranslatedText(Translations.SaveMoneyBenefitValuePrefixBelow15.ToString(), _languageCode);
                savingsValue = await _translationService.GetTranslatedText(Translations.AED15Monthly.ToString(), _languageCode);
            }

            var cancellationSaveMoneyDto = new C3PayPlusMembershipCancellationDetailsDto();
            cancellationSaveMoneyDto.Id = C3PayPlusBenefitType.SaveMoney.ToString();
            cancellationSaveMoneyDto.IconUrl = MembershipHelper.SAVEMONEY_BENEFIT_ICON_URL;
            cancellationSaveMoneyDto.BenefitTitle = await _translationService.GetTranslatedText(Translations.SaveMoneyBenefitTitle.ToString(), _languageCode);
            cancellationSaveMoneyDto.BenefitValue = new BenefitValue()
            {
                Prefix = savingsPrefix,
                Value = savingsValue,
                Suffix = ""
            };

            return cancellationSaveMoneyDto;
        }


        private static C3PayPlusMembershipCancellationDetailsDto GetSurchargeBenefit()
        {
            var surchargeBenefitDto = new C3PayPlusMembershipCancellationDetailsDto();
            surchargeBenefitDto.Id = C3PayPlusBenefitType.Surcharge.ToString();
            surchargeBenefitDto.IconUrl = MembershipHelper.STOP_BENEFIT_ICON_URL;

            // Temp
            surchargeBenefitDto.BenefitTitle = "Transfers from other Apps won’t be FREE";
            //surchargeBenefitDto.BenefitTitle = await _translationService.GetTranslatedText(Translations.LuckyDrawBenefitTitle.ToString(), _languageCode);


            // Temp
            surchargeBenefitDto.BenefitValue = new BenefitValue()
            {
                Prefix = "Extra Fees",
                Value = "AED 3.5",
                Suffix = ""
            };


            //surchargeBenefitDto.BenefitValue = new BenefitValue()
            //{
            //    Prefix = await _translationService.GetTranslatedText(Translations.LuckyDrawBenefitValuePrefix.ToString(), _languageCode),
            //    Value = await _translationService.GetTranslatedText(Translations.BenefitsTextAmount10KAed.ToString(), _languageCode),
            //    Suffix = ""
            //};

            return surchargeBenefitDto;
        }
    }
}
