﻿using C3Pay.Core;
using C3Pay.Core.Abstractions.Cache;
using C3Pay.Core.Models;
using C3Pay.Core.Repositories;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay;
using C3Pay.Core.Services.C3Pay.Membership;
using C3Pay.Core.Services.C3Pay.Mock;
using C3Pay.Core.Services.C3Pay.UnEmpInsurance;
using C3Pay.Core.Services.LoginVideos;
using C3Pay.Data;
using C3Pay.Data.Repositories;
using C3Pay.Services;
using C3Pay.Services.BlobService;
using C3Pay.Services.Cache;
using C3Pay.Services.External;
using C3Pay.Services.Helper;
using C3Pay.Services.Infobip;
using C3Pay.Services.LoginVideos;
using C3Pay.Services.LoginVideos.Validators;
using C3Pay.Services.Membership;
using C3Pay.Services.Membership.BenefitsShell;
using C3Pay.Services.Mock;
using C3Pay.Services.UnEmpInsurnace;
using C3Pay.Services.Validators;
using Edenred.Common.Core;
using Edenred.Common.Services;
using Edenred.Common.Services.Extension;
using FluentValidation;

using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;

namespace C3Pay.API.StartupExtensions
{
    public static class ServicesExtension
    {
        public static void InjectIntegrationServices(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            if (Convert.ToBoolean(configuration["MoneyTransferService:EnableRakMock"]))
            {
                services.AddScoped<IRAKService, RakMockService>();
                services.Configure<RAKSettings>(configuration.GetSection("RAKService"));
            }
            else
            {
                services.AddRakService(configuration, environment.IsDevelopment());
            }

            services.AddDingService(configuration);
            services.AddPPSService(configuration);
            services.AddPPSWebAuthService(configuration);
            services.AddSignzyService(configuration);
            services.AddSecondaryIndividualIdentificationService(configuration);
            services.AddEtisalatSMSService(configuration);
            services.AddInfobipSMSService(configuration);
            services.AddCleverTapService(configuration);
            services.AddFirebaseCloudMessagingService(configuration);
            services.AddPaykiiService(configuration);
            services.AddExchangeHouseMoneyTransferService(configuration);
            services.AddPayrollService(configuration);
            services.AddHRService(configuration);
            services.AddDubaiInsuranceService(configuration);
            services.AddRakBankMoneyTransferService(configuration);
        }

        public static void InjectHelperServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddTransient<ITextMessageSenderService, TextMessageSenderService>();
            services.AddTransient<IPushNotificationSenderService, PushNotificationSenderService>();
            services.AddTransient<IAnalyticsPublisherService, AnalyticsPublisherService>();
            services.AddTransient<IPasswordService, PasswordService>();
            services.AddNameMatchService(configuration);
        }

        public static void InjectAzureServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddKeyVault(configuration);
            services.AddBlobStorageService(configuration, "AzureBlobStorage");
            services.AddTableStorageService(configuration, "AzureBlobStorage");
            services.AddSendGridService(configuration);
            services.AddServiceBusService(configuration);

            // SQL dependency tracking is now handled by OpenTelemetry SqlClient instrumentation
        }

        public static void InjectIdentityServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddEdenredIdentityManagerService(configuration);
        }

        public static void InjectLocalIntegrationServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddKYCService(configuration);
            services.AddESMOWebService(configuration);
            services.AddTransactionsB2CService(configuration);
            services.AddSanctionScreeningService(configuration);
            services.AddRewardService(configuration);
        }


        public static void InjectLocalServices(this IServiceCollection services)
        {
            services.AddTransient<IUserService, UserService>();
            services.AddTransient<ILookupService, LookupService>();
            services.AddTransient<IUploadedDocumentService, UploadedDocumentService>();
            services.AddTransient<ICardHolderService, CardHolderService>();
            services.AddTransient<ISubscriptionService, SubscriptionService>();
            services.AddTransient<IResourceFileService, ResourceFileService>();
            services.AddTransient<IEmailSenderService, EmailSenderService>();
            services.AddTransient<IPopupService, PopupService>();
            services.AddTransient<IStatementService, StatementService>();
            services.AddTransient<IMoneyTransferBeneficiaryService, MoneyTransferBeneficiaryService>();
            services.AddTransient<IMoneyTransferService, MoneyTransferService>();
            services.AddTransient<IMoneyTransferIntegrationService, MoneyTransferIntegrationService>();
            services.AddTransient<IIdentificationService, IdentificationService>();
            services.AddTransient<IRMTProfileService, RMTProfileService>();
            services.AddTransient<IMobileRechargeService, MobileRechargeService>();
            services.AddTransient<IPDFService, PDFService>();
            services.AddTransient<IEHMoneyTransferService, EHMoneyTransferService>();
            services.AddTransient<IAuditTrailService, AuditTrailService>();
            services.AddTransient<IReferralProgramService, ReferralProgramService>();
            services.AddTransient<IBillPaymentProcessingService, BillPaymentProcessingService>();
            services.AddTransient<ISpendPolicyService, SpendPolicyService>();
            services.AddTransient<ISalaryAdvanceCashBackService, SalaryAdvanceCashBackService>();
            services.AddScoped<IRatingService, RatingService>();
            services.AddTransient<IStoreService, StoreService>();
            services.AddTransient<IUnemploymentInsuranceService, UnemploymentInsuranceService>();
            services.AddTransient<IUnEmpInsuranceService, UnEmpInsuranceService>();
            services.AddTransient<IUnEmpInsuranceLookupService, UnEmpInsuranceLookupService>();
            services.AddTransient<IPartnerCorporateService, PartnerCorporateService>();
            services.AddScoped<IExperimentService, ExperimentService>();
            services.AddScoped<IUserInterfaceService, UserInterfaceService>();
            services.AddScoped<IC3PayPlusMembershipLookupService, C3PayPlusMembershipLookupService>();
            services.AddScoped<IC3PayPlusMembershipNotificationService, C3PayPlusMembershipNotificationService>();
            services.AddScoped<IC3PayPlusTargetedDiscountService, C3PayPlusTargetedDiscountService>();
            services.AddScoped<ILoginVideoService, LoginVideoService>();
            services.AddScoped<IVoiceMessageService, InfobipVoiceMessageService>();
            services.AddScoped<IWebhookAuthenticationService, WebhookAuthenticationService>();
            services.AddScoped<IC3PayPlusMembershipTranslationService, C3PayPlusMembershipTranslationService>();
            services.AddTransient<ICacheService, CacheService>();
            services.AddTransient<IExternalService, ExternalService>();
            services.AddTransient<ICsvConfigService, CsvConfigService>();
            services.AddTransient<C3PayPlusMembershipLoginVideoValidator>();
            services.AddTransient<VpnBenefitLoginVideoValidator>();
            services.AddScoped<SpinTheWheelLoginVideoValidator>();
            services.AddTransient<SmsPlusMigrationVideoValidator>();

            // Benefits Shell Services - Simplified Architecture
            services.AddTransient<IBenefitService, BenefitService>();
        }

        /// <summary>
        /// Injects the validators
        /// </summary>
        /// <param name="services"></param>
        public static void InjectValidators(this IServiceCollection services)
        {
            services.AddScoped<IValidator<Rating>, RatingValidator>();
            services.AddScoped<IValidator<MoneyTransferSuspiciousInformation>, MoneyTransferSuspiciousInformationValidator>();

        }

        public static void InjectMockServices(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            services.AddTransient<IUserMockService, UserMockService>();
            services.AddTransient<IStatementMockService, StatementMockService>();
            services.AddTransient<IMoneyTransferBeneficiaryMockService, MoneyTransferBeneficiaryMockService>();
            services.AddTransient<IMoneyTransferMockService, MoneyTransferMockService>();
            services.AddTransient<IRMTProfileMockService, RMTProfileMockService>();
            services.AddTransient<IMobileRechargeMockService, MobileRechargeMockService>();
            services.AddTransient<IBillPaymentProcessingMockService, BillPaymentProcessingMockService>();
            services.AddTransient<ITextMessageSenderMockService, TextMessageSenderMockService>();
            services.AddTransient<Core.Services.IIdentificationMockService, IdentificationMockService>();

            services.AddEsmoWebMockService(configuration);
            services.AddDingMockService(configuration);
            services.AddPaykiiMockService(configuration);
            services.AddSignzyMockService(configuration);
            services.AddPPSMockService(configuration);
            services.AddPPSWebAuthMockService(configuration);
            services.AddEtisalatSMSMockService(configuration);
            services.AddInfobipSMSMockService(configuration);
            services.AddRakMockService(configuration, environment.IsDevelopment());
            services.AddEdenredIdentityManagerMockService(configuration);
        }

        public static void InjectRepositories(this IServiceCollection services)
        {
            services.AddTransient<IUIElementRepository, UIElementRepository>();
            services.AddTransient<IUserRepository, UserRepository>();
            services.AddScoped<IMoneyTransferProfileTrackerRepository, MoneyTransferProfileTrackerRepository>();
        }

        public static void AddRewardService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IRewardService, RewardService>();
            if (!services.Any((ServiceDescriptor x) => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            RewardServiceSettings settings = new RewardServiceSettings();
            configuration.GetSection("RewardService").Bind(settings);
            services.Configure<RewardServiceSettings>(configuration.GetSection("RewardService"));

            Uri uri = null;
            TimeSpan timeout = TimeSpan.FromSeconds(10);
            if (Uri.TryCreate(settings.BaseAddress, UriKind.Absolute, out var address))
                uri = address;

            if (settings.Timeout > 0)
                timeout = TimeSpan.FromSeconds(settings.Timeout);

            services.AddHttpClient("RewardService", delegate (HttpClient client)
            {
                client.BaseAddress = uri;
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.Timeout = timeout;
            }).AddHttpMessageHandler<HttpClientLoggingHandler>();
        }
    }
}
