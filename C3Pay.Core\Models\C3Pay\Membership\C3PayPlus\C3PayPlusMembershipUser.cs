﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;

namespace C3Pay.Core
{
    public class C3PayPlusMembershipUser : BaseModel
    {
        protected C3PayPlusMembershipUser()
        {

        }

        public C3PayPlusMembershipUser(Guid userId,
                                       int membershipId,
                                       bool userHadBalanceEnquirySubscription,
                                       bool userHadSecuritySmsSubscription,
                                       bool userHadSalaryAlertSmsSubscription,
                                       string ticketNumber)
        {
            UserId = userId;
            C3PayPlusMembershipId = membershipId;

            UserHadBalanceEnquirySubscription = userHadBalanceEnquirySubscription;
            UserHadSecuritySmsSubscription = userHadSecuritySmsSubscription;
            UserHadSalaryAlertSmsSubscription = userHadSalaryAlertSmsSubscription;

            TicketNumber = ticketNumber;

            this.Start();
        }

        public Guid Id { get; set; }
        public Guid UserId { get; private set; }
        public int C3PayPlusMembershipId { get; private set; }

        public bool IsActive { get; private set; } = false;
        public bool? UserHasCanceled { get; private set; } = false;

        public DateTime UserSubscribedOn { get; set; }
        public DateTime StartDate { get; private set; }
        public DateTime? NextBillingDate { get; private set; }
        public DateTime? UserCanceledOn { get; private set; }
        public DateTime ValidUntill { get; private set; }

        public int? RenewsEveryWhichDayOfTheMonth { get; set; }
        public string SubscriptionCreationRemarks { get; set; }

        public string LastBillingTransactionNumber { get; set; }
        public DateTime? LastBillingDate { get; private set; }

        public string LastAtmWithrawalReversalTransactionNumber { get; private set; }
        public DateTime? LastAtmWithrawalReversalDate { get; private set; }
        public bool UserHadBalanceEnquirySubscription { get; private set; } = false;
        public bool UserHadSecuritySmsSubscription { get; private set; } = false;
        public bool UserHadSalaryAlertSmsSubscription { get; private set; } = false;

        public string TicketNumber { get; private set; }
        public string Remarks { get; set; }

        public User User { get; set; }
        public C3PayPlusMembership C3PayPlusMembership { get; set; }
        public List<C3PayPlusMembershipTransaction> Transactions { get; set; }
        public List<C3PayPlusMembershipLuckyDrawWinner> LuckDrawWinnings { get; set; }
        public List<C3PayPlusMembershipLifeInsuranceNominee> C3PayPlusMembershipLifeInsuranceNominees { get; set; }

        /// <summary>
        /// This method with start the membership for the user for this first time.
        /// </summary>
        private void Start()
        {
            this.IsActive = true;

            this.UserHasCanceled = false;
            this.UserCanceledOn = null;

            this.UserSubscribedOn = DateTime.Now;


            if (this.UserSubscribedOn.Day == 29 || this.UserSubscribedOn.Day == 30 || this.UserSubscribedOn.Day == 31)
            {
                this.StartDate = new DateTime(this.UserSubscribedOn.Year, this.UserSubscribedOn.Month, 28).Date;
            }
            else
            {
                this.StartDate = this.UserSubscribedOn.Date;
            }



            this.ValidUntill = this.StartDate.AddMonths(1).Date;
            this.NextBillingDate = this.StartDate.AddMonths(1).Date;

            this.RenewsEveryWhichDayOfTheMonth = UserSubscribedOn.Day;

            this.SubscriptionCreationRemarks = "Membership started, waiting for initial billing.";

            this.SetupBenefits();
        }


        /// <summary>
        /// This method will end the membership permanently for the user.
        /// </summary>
        public void EndMembership(string remarks)
        {
            IsActive = false;
            Remarks = remarks;
        }


        /// <summary>
        /// This method will renew the membership of the user after a debit.
        /// </summary>
        public void RenewMembership()
        {
            // We should not run the renewals after the 28th.
            // If the user subscribed on any day between the first to the 28 of each month, then thats fine.
            // if the user subscribed on the 29,30,31, then we need to check if next month, we have 29,30,31
            // to do this, we need to first try parse the date:
            if (this.UserSubscribedOn.Day == 29 || this.UserSubscribedOn.Day == 30 || this.UserSubscribedOn.Day == 31)
            {
                this.StartDate = new DateTime(this.ValidUntill.Year, this.ValidUntill.Month, 28);
            }
            else
            {
                this.StartDate = this.ValidUntill;
                if (this.StartDate.Day != this.UserSubscribedOn.Day)
                {
                    this.StartDate = new DateTime(this.StartDate.Year, this.StartDate.Month, this.UserSubscribedOn.Day);
                }

            }

            this.ValidUntill = this.StartDate.AddMonths(1);
            this.NextBillingDate = this.ValidUntill;

            this.ResetBenefits();
        }


        /// <summary>
        /// This method is called when the user decides to cancel theri membership.
        /// </summary>
        public void CancelMembership()
        {
            UserHasCanceled = true;
            UserCanceledOn = DateTime.Now;
        }


        /// <summary>
        /// This method is called when the user decides to undo membership cancelation.
        /// </summary>
        public void UndoCancelMembership()
        {
            UserHasCanceled = false;
            UserCanceledOn = null;
        }

        public void UndoCreateMembership()
        {
            IsActive = false;
            Remarks = "Billing was not confirmed. Undo membership creation.";
        }


        public void MarkForAtmWithdrawalFeeReversal(string referenceNumber)
        {
            this.LastAtmWithrawalReversalTransactionNumber = referenceNumber;
            this.LastAtmWithrawalReversalDate = DateTime.Now;
        }

        public void MarkAtmWithdrawalFeeReversalCompletion(C3PayPlusMembershipTransaction transaction)
        {
            this.Transactions ??= new List<C3PayPlusMembershipTransaction>();
            this.Transactions.Add(transaction);

            this.HasClaimedAtmWithdrawalFeeReversalForThisMonth = true;
        }

        public void MarkForBilling(string referenceNumber)
        {
            this.LastBillingTransactionNumber = referenceNumber;
            this.LastBillingDate = DateTime.Now;
        }

        public void MarkForBillingCompletion(C3PayPlusMembershipTransaction transaction)
        {
            this.Transactions ??= new List<C3PayPlusMembershipTransaction>();
            this.Transactions.Add(transaction);
        }

        public void UpdateTicketNumber(string newTicketNumber)
        {
            this.TicketNumber = newTicketNumber;
        }


        #region Monetary Benefits
        public bool HasClaimedAtmWithdrawalFeeReversalForThisMonth { get; private set; } = false;
        public bool HasClaimedTheFreeMoneyTransferForThisMonth { get; private set; } = false;
        //public bool HasClaimedCashbackForThisMonth { get; private set; } = false;

        /// <summary>
        /// This method ensures that the tracking flags for the monetary benefits are set to false for new subscribers.
        /// </summary>
        private void SetupBenefits()
        {
            this.HasClaimedAtmWithdrawalFeeReversalForThisMonth = false;
            this.HasClaimedTheFreeMoneyTransferForThisMonth = false;
            //this.HasClaimedCashbackForThisMonth = false;
        }

        /// <summary>
        /// This method releases the lock for the monetary benefits after a user renews their membership.
        /// </summary>
        private void ResetBenefits()
        {
            this.HasClaimedAtmWithdrawalFeeReversalForThisMonth = false;
            this.HasClaimedTheFreeMoneyTransferForThisMonth = false;
            //this.HasClaimedCashbackForThisMonth = false;
        }

        public void ClaimBenefit(C3PayPlusMonitaryBenefits benefit)
        {
            switch (benefit)
            {
                case C3PayPlusMonitaryBenefits.AtmWWithdrawalFeeRefund:
                    this.HasClaimedAtmWithdrawalFeeReversalForThisMonth = true;
                    break;
                case C3PayPlusMonitaryBenefits.MoneyTransferRefund:
                    this.HasClaimedTheFreeMoneyTransferForThisMonth = true;
                    break;
                case C3PayPlusMonitaryBenefits.Cashback:
                    //this.HasClaimedCashbackForThisMonth = true;
                    break;
                default:
                    break;
            }
        }

        #endregion
    }
}
