﻿using C3Pay.Core;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Data.EntityConfigurations.MoneyTransfer
{
    public class MoneyTransferPartnerConfiguration : IEntityTypeConfiguration<MoneyTransferPartner>
    {
        public void Configure(EntityTypeBuilder<MoneyTransferPartner> builder)
        {
            builder.ToTable("MoneyTransferPartners");

            builder.<PERSON><PERSON><PERSON>(c => c.Id);

            builder.HasData(new MoneyTransferPartner()
            {
                Id = 1,
                Name = "RAK Bank",
                Type = MoneyTransferPartnerType.DirectClient
            },
            new MoneyTransferPartner()
            {
                Id = 2,
                Name = "Index",
                Type = MoneyTransferPartnerType.ExchangeHouse
            });
        }
    }
}
