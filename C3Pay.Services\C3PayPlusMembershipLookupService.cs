﻿using AutoMapper;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using C3Pay.Core.Repositories;
using C3Pay.Core.Services.C3Pay.Membership;
using Edenred.Common.Services;
using Microsoft.Extensions.Caching.Distributed;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Translations = C3Pay.Core.C3PayPlusMembershipTranslationTextCode;

namespace C3Pay.Services
{
    public class C3PayPlusMembershipLookupService : IC3PayPlusMembershipLookupService
    {
        private readonly IMapper _mapper;
        private readonly IDistributedCache _distributedCache;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IC3PayPlusMembershipTranslationService _translationService;
        private readonly IUserRepository _userRepository;


        private readonly TimeSpan _cacheValidityForAYear = TimeSpan.FromDays(365);
        private const string _lookupCachePrefix = "C3PayPlusMembershipLookup";

        public C3PayPlusMembershipLookupService(IMapper mapper, IDistributedCache distributedCache, IUnitOfWork unitOfWork, IC3PayPlusMembershipTranslationService translationService, IUserRepository userRepository)
        {
            _mapper = mapper;
            _distributedCache = distributedCache;
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _userRepository = userRepository;
        }

        public async Task<IEnumerable<C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeDto>> GetLifeInsuranceNomineeRelationshipTypes(string languageCode = "en")
        {
            var cachekey = $"{_lookupCachePrefix}_C3PayLifeInsuranceNomineeRelationshipOptions_{languageCode}";
            var options = await _distributedCache.GetRecordAsync<IEnumerable<C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeDto>>(cachekey);
            if (options is null)
            {
                var dbRecords = await _unitOfWork.C3PayPlusLifeInsuranceNomineesRelationshipTypes.FindAsync(f => f.IsActive);

                //map the translated records
                options = await TranslateNomineeRelationshipTypeAsync(dbRecords, languageCode);
                await _distributedCache.SetRecordAsync(cachekey, options, _cacheValidityForAYear);
            }


            return options;
        }

        public async Task<string> GetLifeInsuranceNomineeRelationshipTypeName(int? relationshipTypeId, string languageCode = "en")
        {
            if (relationshipTypeId == null) return string.Empty;

            var relationshipTypes = await GetLifeInsuranceNomineeRelationshipTypes();

            return relationshipTypes.FirstOrDefault(x => x.Id == relationshipTypeId)?.Name ?? string.Empty;
        }

        public async Task<IEnumerable<C3PayPlusMembershipSupportedCountryDto>> GetLifeInsuranceSupportedCountries()
        {
            var cachekey = $"{_lookupCachePrefix}_C3PayLifeInsuranceSupportedCountryOptions";
            var options = await _distributedCache.GetRecordAsync<IEnumerable<C3PayPlusMembershipSupportedCountryDto>>(cachekey);
            if (options is null)
            {
                var dbRecords = await _unitOfWork.C3PayPlusMembershipSupportedCountries.FindAsync(f => f.IsActive, order => order.DisplayOrder, false, null, null, true, null);
                options = _mapper.Map<IEnumerable<C3PayPlusMembershipSupportedCountryDto>>(dbRecords);
                await _distributedCache.SetRecordAsync(cachekey, options, _cacheValidityForAYear);
            }
            return options;
        }

        public async Task<IList<Subscription>> GetCachedSubscriptions()
        {
            var subscriptionCacheName = string.Concat(_lookupCachePrefix, $"All_Subscriptions");
            var subscriptions = await this._distributedCache.GetRecordAsync<IList<Subscription>>(subscriptionCacheName);
            if (subscriptions is null || subscriptions.Count == 0)
            {
                subscriptions = await this._unitOfWork.Subscriptions.FindAsync(x => x.IsAvailable == true);
                await this._distributedCache.SetRecordAsync(subscriptionCacheName, subscriptions, _cacheValidityForAYear);
            }

            return subscriptions;
        }

        public async Task<IList<C3PayPlusMembershipBenefit>> GetCachedBenefits(string languageCode = "en")
        {
            var benefitsCacheName = string.Concat(_lookupCachePrefix, $"All_Benefits_{languageCode}");
            var benefits = await this._distributedCache.GetRecordAsync<IList<C3PayPlusMembershipBenefit>>(benefitsCacheName);
            if (benefits is null || benefits.Count == 0)
            {
                var dbRecords = await this._unitOfWork.C3PayPlusMembershipBenefits.FindAsync(x => x.C3PayPlusMembershipId.HasValue == false);
                benefits = await TranslateBenefitsAsync(dbRecords, languageCode);
                await this._distributedCache.SetRecordAsync(benefitsCacheName, benefits, _cacheValidityForAYear);
            }

            return benefits;
        }

        public async Task<List<string>> GetPolicyDetailsWithTranslation(string languageCode)
        {
            var policyDetails = new List<string>
            {
                await this._translationService.GetTranslatedText(Translations.AccidentalInsuranceClaim.ToString(), languageCode),
                await _translationService.GetTranslatedText(Translations.InsurnacePolicyActive.ToString(), languageCode),
                await _translationService.GetTranslatedText(Translations.InsurnacePolicySharedWithNominee.ToString(), languageCode),
                await _translationService.GetTranslatedText(Translations.InsurnacePolicyViewYourCertificate.ToString(), languageCode)
            };

            return policyDetails;
        }

        public async Task<IList<C3PayPlusMembershipBenefit>> GetBenefitsByMembershipTypeWithTranslation(int membershipId, string languageCode = "en")
        {
            var dbRecords = await this._unitOfWork.C3PayPlusMembershipBenefits.FindAsync(x => x.C3PayPlusMembershipId == membershipId);
            var benefits = await TranslateBenefitsAsync(dbRecords, languageCode);

            return benefits;
        }

        public async Task<bool> IsC3PayPlusMembershipActive(string userPhoneNumber)
        {
            // If user is not found, deleted, blocked, or is not a C3Pay app user, exit.
            var user = await this._userRepository.GetUserForC3PayPlus(userPhoneNumber);

            if (user is null)
                return false;


            // Find membership.
            var membershipUser = await this._unitOfWork.C3PayPlusMembershipUsers
                                                       .FirstOrDefaultAsync(x => x.UserId == user.Id
                                                                            && x.IsActive);
            if (membershipUser is null)
                return false;

            return true;

        }

        public async Task<bool> IsC3PayPlusMembershipActiveAndNotCancelled(string userPhoneNumber)
        {
            // If user is not found, deleted, blocked, or is not a C3Pay app user, exit.
            var user = await this._userRepository.GetUserForC3PayPlus(userPhoneNumber);

            if (user is null)
                return false;


            // Find membership.
            var membershipUser = await this._unitOfWork.C3PayPlusMembershipUsers
                                                       .FirstOrDefaultAsync(x => x.UserId == user.Id
                                                                            && x.IsActive
                                                                            && (x.UserHasCanceled == null || x.UserHasCanceled == false)
                                                                            && x.UserCanceledOn == null);
            if (membershipUser is null)
                return false;

            return true;

        }

        public async Task<C3PayPlusMembershipType> GetC3PayPlusMembershipType(Guid userId)
        {
            // Find membership.
            var membershipUser = await this._unitOfWork.C3PayPlusMembershipUsers
                                                       .FirstOrDefaultAsync(x => x.UserId == userId
                                                                            && x.IsActive);

            if (membershipUser == null || membershipUser.C3PayPlusMembership == null)
                return C3PayPlusMembershipType.UsersWithNoSubscription;

            return membershipUser.C3PayPlusMembership.C3PayPlusTypeId;
        }

        private async Task<IList<C3PayPlusMembershipBenefit>> TranslateBenefitsAsync(IList<C3PayPlusMembershipBenefit> dbRecords, string languageCode)
        {
            var result = new List<C3PayPlusMembershipBenefit>();
            foreach (var item in dbRecords)
            {
                item.Title = await _translationService.GetTranslatedText(item.Title, languageCode);
                item.Subtitle = await _translationService.GetTranslatedText(item.Subtitle, languageCode);
                item.PriceLabel = await _translationService.GetTranslatedText(item.PriceLabel, languageCode);
                item.SummarySubtitle = await _translationService.GetTranslatedText(item.SummarySubtitle, languageCode);
                item.SummaryTitle = await _translationService.GetTranslatedText(item.SummaryTitle, languageCode);
                item.SubtitleText1 = await _translationService.GetTranslatedText(item.SubtitleText1, languageCode);
                item.SubtitleText2 = await _translationService.GetTranslatedText(item.SubtitleText2, languageCode);
                item.SubtitleText3 = await _translationService.GetTranslatedText(item.SubtitleText3, languageCode);

                result.Add(item);
            }
            return result;
        }

        private async Task<IEnumerable<C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeDto>> TranslateNomineeRelationshipTypeAsync(IList<C3PayPlusMembershipLifeInsuranceNomineeRelationshipType> dbRecords, string languageCode)
        {
            var result = new List<C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeDto>();
            foreach (var dbRecord in dbRecords)
            {
                string translatedName = await _translationService.GetTranslatedText(dbRecord.DisplayNameTranslationKey, languageCode) ?? dbRecord.Name;
                string translatedInputQuestion = await _translationService.GetTranslatedText(dbRecord.InputQuestionTranslationKey, languageCode);

                result.Add(new C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeDto() { Id = dbRecord.Id, Name = translatedName, InputQuestion = translatedInputQuestion });
            }
            return result;
        }

        public async Task<C3PayPlusMembershipExperiment> GetExperimentDetails(C3PayPlusMembershipExperiments experiment)
        {
            var experimentsCacheName = string.Concat(_lookupCachePrefix, $"All_Experiment");
            var experiments = await this._distributedCache.GetRecordAsync<IList<C3PayPlusMembershipExperiment>>(experimentsCacheName);
            if (experiments is null || experiments.Count == 0)
            {
                experiments = await this._unitOfWork.C3PayPlusMembershipExperiments.FindAsync(x => x.IsActive);
                await this._distributedCache.SetRecordAsync(experimentsCacheName, experiments, _cacheValidityForAYear);
            }

            // Find the requested experiment.
            return experiments.FirstOrDefault(x => x.ExperimentId == experiment);
        }
    }
}
