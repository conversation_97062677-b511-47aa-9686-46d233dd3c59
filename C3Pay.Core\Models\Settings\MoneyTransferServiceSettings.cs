﻿using System;
using System.Collections.Generic;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models
{
    public class MoneyTransferServiceSettings
    {
        public decimal UserMonthlyTransactionAmountLimit { get; set; }
        public int UserMonthlyTransactionCountLimit { get; set; }
        public string BeneficiaryMockQueueConnectionString { get; set; }
        public string BeneficiaryMockQueueName { get; set; }
        public string MoneyTransferMockQueueConnectionString { get; set; }
        public string MoneyTransferMockQueueName { get; set; }
        public string RefreshRatesEmiratesId { get; set; }
        public bool EnableBeneficiarySameCountryDelay { get; set; }
        public string MultimediaURL { get; set; }
        public List<PopularBank> PopularBanks { get; set; }
        public string PopularBanksIconsBaseUrl { get; set; }
        public ReversalMode ReversalMode { get; set; }
        public DateTime ReversalStartDate { get; set; }
        public int MaxBeneficiaryRetryLimit { get; set; }
        public int RetryBeneficiaryDurationInMin { get; set; }
        public string GWNationalities { get; set; }
        public string GWLanguages { get; set; }
        public DateTime? GWStartDate { get; set; }
        public decimal GeneralDefaultAmount { get; set; }
        public string GeneralDefaultCurrency { get; set; }
        public string GeneralDefaultType { get; set; }
        public int MaxRepeatTransferCount { get; set; }
        public decimal MinUserBalanceForRepeatTransfer { get; set; }
        public int ReverseOnHoldMinNoOfDays { get; set; }
        public int RateExpiryInMinutes { get; set; }
        public int PendingSchedulerMinNoOfDays { get; set; }
        public DateTime CheckMinSuspiciousDate { get; set; }

        #region Comparison

        public decimal ComparisonReceiveAmount { get; set; }
        public decimal ComparisonEHTransferFee { get; set; }
        public decimal ComparisonEHRateIncrement { get; set; }

        #endregion Comparison


        #region Direct Transfers
        public int DirectTransferMaxBeneficiariesCount { get; set; }
        public decimal DirectTransferMinAmountToSend { get; set; }
        public decimal DirectTransferMaxAmountToSend { get; set; }
        public decimal DirectTransferMaxAmountToSendPerMonth { get; set; }
        public decimal DirectTransferReceiverMaxAmountPerMonth { get; set; }
        public decimal DirectTransferFee { get; set; }
        public decimal DirectTransferVAT { get; set; }
        public string ClaimPendingDirectTransfersQueueConnectionString { get; set; }
        public string ClaimPendingDirectTransfersQueueName { get; set; }
        public string ReversePendingDirectMoneyTransfersSchedule { get; set; }
        public int ReversePendingDirectMoneyTransfersDurationInMin { get; set; }
        public string ReverseFailedDirectMoneyTransfersSchedule { get; set; }
        public string FreeDirectTransferBranches { get; set; }
        #endregion

        #region Mock
        public bool EnableRakMock { get; set; }
        public bool EnableRakNegativeScenarioMock { get; set; }
        #endregion

        public string CorridorsCorporateId { get; set; }
        public bool WUTransactionMinLimitValidationEnabled { get; set; }
        public int NonWUEmiratesIdExpiryGracePeriodDays { get; set; }
        public int WUEmiratesIdExpiryGracePeriodDays { get; set; }
        public string NonWUCorridors { get; set; }
        public bool RMTStatusFromCreatedToPendingEnabled { get; set; }
        public string LastRaffleWinnerName { get; set; }
        public string LastRaffleWinnerTicketNumber { get; set; }
        public string RaffleDateString { get; set; }

        public string C3ToC3MinVersionForOtpCheck { get; set; }
    }

    public class PopularBank
    {
        public string Name { get; set; }
        public string CountryCode { get; set; }
        public int Order { get; set; }
    }
}
