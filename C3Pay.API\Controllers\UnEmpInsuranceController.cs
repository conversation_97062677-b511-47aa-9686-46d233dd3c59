﻿using System;
using AutoMapper;
using C3Pay.API.Resources;
using C3Pay.API.Resources.UnEmpInsurance;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.UnEmpInsurance;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay.UnEmpInsurance;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Edenred.Common.Core;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// UnEmpInsuranceController
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    [Authorize]
    //[DeviceAuthorize]
    public class UnEmpInsuranceController : BaseController
    {
        private readonly IMapper _mapper;
        private readonly ILookupService _lookupService;
        private readonly IUnEmpInsuranceLookupService _unEmpInsuranceLookupService;
        private readonly IUnEmpInsuranceService _unEmpInsuranceService;
        private readonly IUserService _userService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="mapper"></param>
        /// <param name="lookupService"></param>
        /// <param name="unEmpInsuranceLookupService"></param>
        /// <param name="httpContextAccessor"></param>
        /// <param name="userService"></param>
        /// <param name="logger"></param>
        /// <param name="unEmpInsuranceService"></param>
        public UnEmpInsuranceController(
            IMapper mapper,
            ILookupService lookupService,
            IUnEmpInsuranceLookupService unEmpInsuranceLookupService,
            IHttpContextAccessor httpContextAccessor,
            IUserService userService,
            ILogger<UnemploymentInsuranceController> logger,
            IUnEmpInsuranceService unEmpInsuranceService) : base(httpContextAccessor, userService, logger)
        {
            _mapper = mapper;
            this._lookupService = lookupService;
            this._unEmpInsuranceLookupService = unEmpInsuranceLookupService;
            this._unEmpInsuranceService = unEmpInsuranceService;
            this._userService = userService;
        }

        /// <summary>
        /// Payment Options
        /// </summary>
        /// <returns></returns>
        [HttpGet("payment/options")]
        public async Task<ActionResult<IEnumerable<UnEmpInsurancePaymentOptionDto>>> GetUnemploymentInsurancePaymentOptions()
        {
            var userResult = await GetLoggedInUser();
            if (userResult != null)
                return Unauthorized();

            var paymentOptions = await this._lookupService.GetUserUnEmpInsurancePaymentOptions(_user, UnEmpInsurancePartnerType.DirectClient);

            var mappedPaymentOptions = _mapper.Map<List<UnEmpInsurancePaymentOptionDto>>(paymentOptions.Data);

            return Ok(mappedPaymentOptions);
        }

        /// <summary>
        /// GetUserSubscription - Old API
        /// </summary>
        /// <returns></returns>
        [HttpGet("user/subscription-detail")]
        public async Task<ActionResult<UnEmpInsuranceSubscriptionResponseDto>> GetUserSubscription()
        {
            var userResult = await GetLoggedInUser();
            if (userResult != null)
                return Unauthorized();

            var responseDto = new UnEmpInsuranceSubscriptionResponseDto
            {
                Status = nameof(UnEmpInsuranceStatus.UnSubscribed),
            };

            // Check for emiratesId status
            bool isValidEid = false;
            var emiratesIdStatusResponse = _userService.ValidateEmiratesId(_user.CardHolder.EmiratesId);

            if (emiratesIdStatusResponse.IsSuccessful)
            {
                isValidEid = emiratesIdStatusResponse.Data;
                if (isValidEid && _user.CardHolder.EmiratesIdExpiryDate != null && _user.CardHolder.EmiratesIdExpiryDate < DateTime.Today)
                    isValidEid = false;
            }

            // Check user verified With EId
            bool isVerifiedWithEId = false;
            var eIdVerifiedResponse = await _userService.IsVerifiedWithEmiratesId(_user.Id);
            if (eIdVerifiedResponse.IsSuccessful)
            {
                isVerifiedWithEId = eIdVerifiedResponse.Data;
            }

            if (_user.IsVerified && isValidEid && isVerifiedWithEId)
            {
                var localResponse = await _unEmpInsuranceService.GetInsurance(_user.Id);
                if (localResponse.IsSuccessful && localResponse.Data != null && (localResponse.Data.Status == UnEmpInsuranceStatus.Subscribed || localResponse.Data.Status == UnEmpInsuranceStatus.InProgress))
                {
                    if (localResponse.Data?.IsSubscribedExternally == true && (localResponse.Data.PaidInstallments < localResponse.Data.TotalInstallments))
                    {
                        // Check for Installment mismatch based on last Sync Date
                        if (localResponse.Data.LastSyncDate != null)
                        {
                            bool isGreaterThan24Hours = DateTime.Now.Subtract((DateTime)localResponse.Data.LastSyncDate) > TimeSpan.FromHours(24);
                            if (isGreaterThan24Hours)
                            {
                                localResponse = await _unEmpInsuranceService.NotifyInstallmentChangeWithB2B(localResponse.Data, _user);
                            }
                        }
                    }
                }
                else
                {
                    // Calling Dubai Insurance API
                    var externalResponse = await _unEmpInsuranceLookupService.GetExternalInsuranceEnquiry(_user);
                    if (externalResponse.IsSuccessful && externalResponse.Data != null)
                    {
                        // If user already subscribed from external 
                        if (string.Equals(externalResponse.Data.HasPolicy, "Yes", StringComparison.OrdinalIgnoreCase))
                        {
                            // Create New Entry in DB with LastSyncDate as Today 
                            var paymentEntry_AlreadySubscribed = _mapper.Map<UnEmpInsurancePayment>(externalResponse.Data);
                            paymentEntry_AlreadySubscribed.UserId = _user.Id;

                            if (localResponse.Data == null)
                                localResponse = await _unEmpInsuranceService.AddNewInsurance(paymentEntry_AlreadySubscribed, UnEmpInsurancePartnerType.DirectClient);
                            else if (localResponse.Data?.Status == UnEmpInsuranceStatus.UnSubscribed)
                                localResponse = await _unEmpInsuranceService.UpdateProcessedInsurance(paymentEntry_AlreadySubscribed, _user, false, false);

                            // Trigger to B2B - AlreadySubscribedEvent
                            await _unEmpInsuranceService.UpdateExternallySubscribedInfoWithB2B(externalResponse.Data, _user);

                            // Clever Tap
                            await _unEmpInsuranceService.UpdateCleverTapUserPropertiesForInsurance(_user, Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.Subscribed),
                                "No", Enum.GetName(typeof(UnEmpInsuranceSource), UnEmpInsuranceSource.External));
                        }
                        else if (string.Equals(externalResponse.Data.HasPolicy, "No", StringComparison.OrdinalIgnoreCase))
                        {
                            var paymentEntry_NotSubscribed = new UnEmpInsurancePayment()
                            {
                                UserId = _user.Id,
                                Status = UnEmpInsuranceStatus.UnSubscribed,
                                Source = UnEmpInsuranceSource.C3Pay,
                                Category = externalResponse.Data?.Category == "A" ? UnEmpInsuranceCategory.A : UnEmpInsuranceCategory.B
                            };

                            if (localResponse.Data == null)
                                localResponse = await _unEmpInsuranceService.AddNewInsurance(paymentEntry_NotSubscribed, UnEmpInsurancePartnerType.DirectClient);

                            // Clever Tap 
                            await _unEmpInsuranceService.UpdateCleverTapUserPropertiesForInsurance(_user, Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.UnSubscribed), "", "");
                        }
                    }
                    else
                    {
                        // Dubai Insurance API failed
                        return BadRequest(externalResponse.ErrorMessage);
                    }
                }


                responseDto.Status = localResponse.Data != null ? Enum.GetName(typeof(UnEmpInsuranceStatus), localResponse.Data.Status) : responseDto.Status;

                // Get Video only for Unsubsribed
                if (responseDto.Status == Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.UnSubscribed))
                {
                    responseDto.Video = await GetVideoUrls(_user);
                }
                else
                {
                    responseDto.Detail = localResponse.Data != null ? _mapper.Map<UnEmpInsuranceSubscriptionDetailsDto>(localResponse.Data) : responseDto.Detail;
                    responseDto.Detail.PhoneNumber = $"+{_user.PhoneNumber[2..]}";
                }
            }
            else
            {
                return BadRequest("Invalid EmiratesId");
            }

            return Ok(responseDto);
        }


        /// <summary>
        /// GetUserSubscriptionV1
        /// </summary>
        /// <returns></returns>
        [HttpGet("user/subscription")]
        public async Task<ActionResult<UnEmpInsuranceSubscriptionResponseDto>> GetUserSubscriptionV1()
        {
            var userResult = await GetLoggedInUser();
            if (userResult != null)
                return Unauthorized();

            var responseDto = new UnEmpInsuranceSubscriptionResponseDto
            {
                Status = nameof(UnEmpInsuranceStatus.UnSubscribed),
            };

            // Check for user eligibility
            bool isValidUser = await IsUserEligibleForUnEmploymentInsurance();

          
            // Check for local entry in Db
            var localResponse = await _unEmpInsuranceService.GetInsurance(_user.Id);

            var allActiveStatuses = _unEmpInsuranceService.GetStatusesForActiveEntry();
            if (localResponse.IsSuccessful && localResponse.Data?.Status != null
            && allActiveStatuses.Contains(localResponse.Data.Status))
            {
                if (localResponse.Data?.IsSubscribedExternally == true && (localResponse.Data.PaidInstallments < localResponse.Data.TotalInstallments))
                {
                    // Check for Installment mismatch based on last Sync Date
                    if (isValidUser)
                    {
                        localResponse.Data.LastSyncDate = localResponse.Data.LastSyncDate == null ? DateTime.Now.AddDays(-2) :localResponse.Data.LastSyncDate;
                        bool isGreaterThan24Hours = DateTime.Now.Subtract((DateTime)localResponse.Data.LastSyncDate) > TimeSpan.FromHours(24);
                        if (isGreaterThan24Hours)
                        {
                            localResponse = await _unEmpInsuranceService.NotifyInstallmentChangeWithB2B(localResponse.Data, _user);
                        }
                    }
                }
            }
            else
            {
                if (isValidUser)
                {
                    // Calling Dubai Insurance API
                    var externalResponse = await _unEmpInsuranceLookupService.GetExternalInsuranceEnquiry(_user);
                    if (externalResponse.IsSuccessful && externalResponse.Data != null)
                    {
                        // If user already subscribed from external 
                        if (string.Equals(externalResponse.Data.HasPolicy, "Yes", StringComparison.OrdinalIgnoreCase))
                        {
                            // Create New Entry in DB with LastSyncDate as Today 
                            var paymentEntry_AlreadySubscribed = _mapper.Map<UnEmpInsurancePayment>(externalResponse.Data);
                            paymentEntry_AlreadySubscribed.UserId = _user.Id;
                            paymentEntry_AlreadySubscribed.IsSubscribedExternally = true;

                            if (localResponse.Data == null)
                                localResponse = await _unEmpInsuranceService.AddNewInsurance(paymentEntry_AlreadySubscribed, UnEmpInsurancePartnerType.DirectClient);
                            else if (localResponse.Data?.Status == UnEmpInsuranceStatus.UnSubscribed)
                                localResponse = await _unEmpInsuranceService.UpdateProcessedInsurance(paymentEntry_AlreadySubscribed, _user, false);

                            // Trigger to B2B - AlreadySubscribedEvent
                            await _unEmpInsuranceService.UpdateExternallySubscribedInfoWithB2B(externalResponse.Data, _user);

                            // Clever Tap
                            await _unEmpInsuranceService.UpdateCleverTapUserPropertiesForInsurance(_user, Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.Subscribed),
                                "No", Enum.GetName(typeof(UnEmpInsuranceSource), UnEmpInsuranceSource.External));
                        }
                        else if (string.Equals(externalResponse.Data.HasPolicy, "No", StringComparison.OrdinalIgnoreCase))
                        {
                            var paymentEntry_NotSubscribed = new UnEmpInsurancePayment()
                            {
                                UserId = _user.Id,
                                Status = UnEmpInsuranceStatus.UnSubscribed,
                                Source = UnEmpInsuranceSource.C3Pay,
                                Category = externalResponse.Data?.Category == "A" ? UnEmpInsuranceCategory.A : UnEmpInsuranceCategory.B
                            };

                            if (localResponse.Data == null)
                                localResponse = await _unEmpInsuranceService.AddNewInsurance(paymentEntry_NotSubscribed, UnEmpInsurancePartnerType.DirectClient);

                            // Clever Tap 
                            await _unEmpInsuranceService.UpdateCleverTapUserPropertiesForInsurance(_user, Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.UnSubscribed), "", "");
                        }
                    }
                    else
                    {
                        // Dubai Insurance API failed
                        return BadRequest(externalResponse.ErrorMessage);
                    }
                }
            }

            responseDto.Status = localResponse.Data != null ? Enum.GetName(typeof(UnEmpInsuranceStatus), localResponse.Data.Status) : responseDto.Status;

            // Get Video only for Unsubsribed
            if (responseDto.Status == Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.UnSubscribed))
            {
                responseDto.Video = await GetVideoUrls(_user);
            }
            else
            {
                responseDto.Detail = localResponse.Data != null ? _mapper.Map<UnEmpInsuranceSubscriptionDetailsDto>(localResponse.Data) : responseDto.Detail;
                responseDto.Detail.PhoneNumber = $"+{_user.PhoneNumber[2..]}";
            }

            if(localResponse.Data != null)
            {
                if (responseDto.Status == Enum.GetName(typeof(UnEmpInsuranceStatus), UnEmpInsuranceStatus.Retrying))
                {
                    if (localResponse.Data.Remarks == EnumUtility.GetDescriptionFromEnumValue(UnemploymentInsuranceValidationMessage.WorkerDetailsNotFound))
                    {
                        responseDto.RetryReason = localResponse.Data.Remarks;
                    }
                }
                responseDto.IsSubscribedByCorporate = localResponse.Data.Source == UnEmpInsuranceSource.Corporate;
            }
          




            return Ok(responseDto);
        }


        /// <summary>
        /// Subscribe API
        /// Possible results: UserAlreadySubscribed, InvalidPaymentOption
        /// </summary>
        /// <param name="paymentRequest"></param>
        /// <returns></returns>
        [HttpPost("user/subscribe")]
        public async Task<ActionResult> Subscribe(UnEmpInsuranceSubscriptionRequestDto paymentRequest)
        {
            var userResult = await GetLoggedInUser();
            if (userResult != null)
                return Unauthorized();

            var processPaymentResult = await this._unEmpInsuranceService.Subscribe(paymentRequest.PaymentOptionId, paymentRequest.IsBalanceAvailable, paymentRequest.ReferralCode, _user, UnEmpInsurancePartnerType.DirectClient);

            if (!processPaymentResult.IsSuccessful)
            {
                return this.BadRequest(processPaymentResult.ErrorMessage);
            }

            return Ok();
        }


        /// <summary>
        /// UpdateSubscription
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut("user/subscription")]
        public async Task<ActionResult> UpdateUnEmploymentInsuranceSubscription([FromBody] UnEmpInsuranceSubscriptionUpdateDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var userResult = await GetLoggedInUser();
            if (userResult != null)
                return Unauthorized();

            var updateResult = await this._unEmpInsuranceService.UpdateSubcription(request.SubscriptionId, request.IsAutoPaymentEnabled, _user);

            if (!updateResult.IsSuccessful)
            {
                return this.BadRequest(updateResult.ErrorMessage);
            }

            return Ok();
        }


        /// <summary>
        /// GetUserSubscriptionV1
        /// </summary>
        /// <returns></returns>
        [HttpGet("user/subscription/installment")]
        public async Task<ActionResult<UnempInsuranceInstallmentDto>> GetUserSubcriptionInstallmentDetails()
        {
            var userResult = await GetLoggedInUser();
            if (userResult != null)
                return Unauthorized();

            var installmentDtoResult = await this._unEmpInsuranceService.GetInstallmentDetailsAsync(_user);

            if (!installmentDtoResult.IsSuccessful)
            {
                return this.BadRequest(installmentDtoResult.ErrorMessage);
            }
            var installmentDto = _mapper.Map<UnempInsuranceInstallmentDto>(installmentDtoResult.Data.Item1);

            if (installmentDto.Installments.Any() && installmentDtoResult.Data.Item2 != null)
            {
                installmentDto.Installments.ToList().ForEach(a =>
                {
                    a.DueAmount.Amount = a.DueAmount.Amount + (double)installmentDtoResult.Data.Item2.Fee;
                });
                installmentDto.RemainingAmount.Amount = installmentDto.Installments.Any(a=>a.Status != "Paid") ?
                   installmentDto.Installments.Where(a => a.Status != "Paid").Sum(a => a.DueAmount.Amount) : installmentDto.RemainingAmount.Amount;
            }
            

            return Ok(installmentDto);
        }

        #region Private Methods
        private async Task<List<UnEmpInsuranceVideoDto>> GetVideoUrls(User _user)
        {
            var hindiNationalities = new HashSet<string> { nameof(BaseEnums.MultimediaCountry.PAK), nameof(BaseEnums.MultimediaCountry.IND) };
            var userShouldSeeHindiVideo = hindiNationalities.Contains(_user.CardHolder.Nationality);

            var unEmpInsuranceMultimediaResources = await _lookupService.GetMultimediaResources(feature: (int)FeatureType.UnEmpInsurance).ConfigureAwait(false);

            bool defaultAlreadySet = false;
            return unEmpInsuranceMultimediaResources.Data
                .Select(resource => new UnEmpInsuranceVideoDto
                {
                    LanguageCode = resource.Language,
                    Url = resource.Url,
                    IsDefault = ((resource.Language == "hi" && userShouldSeeHindiVideo)
                                    || (resource.Language != "hi" && !userShouldSeeHindiVideo))
                                && !defaultAlreadySet && (defaultAlreadySet = true)
                })
                .ToList();
        }

        /// <summary>
        /// Is User Eligible For UnEmployment Insurance
        /// </summary>
        /// <returns></returns>
        private async Task<bool> IsUserEligibleForUnEmploymentInsurance()
        {
            var emiratesIdStatusResponse = _userService.ValidateEmiratesId(_user.CardHolder.EmiratesId);
            bool isValidUser = emiratesIdStatusResponse.IsSuccessful && emiratesIdStatusResponse.Data && 
                _user.IsVerified;

            var eIdVerifiedResponse = await _userService.IsVerifiedWithEmiratesId(_user.Id);
            bool isVerifiedWithEId = eIdVerifiedResponse.IsSuccessful && eIdVerifiedResponse.Data;

            return isValidUser && isVerifiedWithEId;
        }
        #endregion
    }
}
