﻿using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using Edenred.Common.Core;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PPSWebService;
using System;
using System.Globalization;
using System.Threading.Tasks;

namespace C3Pay.Services.Helper
{
    public class TextMessageSenderService : ITextMessageSenderService
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly TextInfo _textFormatter = new CultureInfo("en-US", false).TextInfo;

        /// <summary>
        /// 
        /// </summary>
        private readonly ISecondaryTextMessageService _secondaryTextMessageService;

        /// <summary>
        /// 
        /// </summary>
        private readonly IPrimaryTextMessageService _primaryTextMessageService;

        /// <summary>
        /// 
        /// </summary>
        private readonly IResourceFileService _resourceFileHelper;

        /// <summary>
        /// 
        /// </summary>
        private readonly ILogger _logger;

        /// <summary>
        /// 
        /// </summary>
        private readonly GeneralSettings _generalSettings;

        /// <summary>
        /// 
        /// </summary>
        private readonly ILookupService _lookupService;

        /// <summary>
        /// 
        /// </summary>
        private readonly string _deviceAddedVerifiedUser = "dbnd_vrfd_user";

        /// <summary>
        /// 
        /// </summary>
        private readonly string _deviceAddedNonVerifiedUser = "dbnd_nvrfd_user";

        /// <summary>
        /// 
        /// </summary>
        /// <param name="secondaryTextMessageService"></param>
        /// <param name="primaryTextMessageService"></param>
        /// <param name="generalSettings"></param>
        /// <param name="resourceFileHelper"></param
        /// <param name="logger"></param>
        /// <param name="lookupService"></param>
        public TextMessageSenderService(ISecondaryTextMessageService secondaryTextMessageService,
                                        IPrimaryTextMessageService primaryTextMessageService,
                                        IOptions<GeneralSettings> generalSettings,
                                        IResourceFileService resourceFileHelper,
                                        ILogger<TextMessageSenderService> logger,
                                        ILookupService lookupService)
        {
            this._secondaryTextMessageService = secondaryTextMessageService;
            this._primaryTextMessageService = primaryTextMessageService;
            this._generalSettings = generalSettings.Value;
            this._resourceFileHelper = resourceFileHelper;
            this._logger = logger;
            this._lookupService = lookupService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sendOTPSMSRequest"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> SendOTPMessage(SendOTPSMSRequest sendOTPSMSRequest)
        {
            var result = await GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.OTPMessage.txt");

            var template = result.Data;

            var message = string.Format(template, sendOTPSMSRequest.Reason, sendOTPSMSRequest.OneTimePassword, _generalSettings.MobileAppHashKey);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = sendOTPSMSRequest.PhoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            var retryIsEven = (sendOTPSMSRequest.RetryCount % 2) == 0;

            if (retryIsEven)
            {
                await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);
            }
            else
            {
                var secondarySmsProvider = _generalSettings.PrimarySmsProvider == (int)SmsProvider.Etisalat ? SmsProvider.Infobip : SmsProvider.Etisalat;
                await this.SendTextMessage(smsData, secondarySmsProvider);
            }

            return new ServiceResponse();
        }

        private async Task SendTextMessage(SendSMSRequest smsData, SmsProvider provider)
        {
            if (provider == SmsProvider.Etisalat)
            {
                var tryPrimary = await _primaryTextMessageService.SendTextMessage(smsData);
                if (tryPrimary.IsSuccessful == false)
                {
                    await _secondaryTextMessageService.SendTextMessage(smsData);
                }
            }
            else
            {
                var trySecondary = await _secondaryTextMessageService.SendTextMessage(smsData);
                if (trySecondary.IsSuccessful == false)
                {
                    await _primaryTextMessageService.SendTextMessage(smsData);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> SendPhoneNumberChangedMessage(string phoneNumber)
        {
            var result = await GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.PhoneNumberChanged.txt");

            var message = result.Data;

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> SendRMTProfileCreatedMessage(string phoneNumber)
        {
            var result = await GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.RMTProfileCreated.txt");

            var message = result.Data;

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        /*/// <summary>
        /// 
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> SendRMTProfileMissedMessage(string phoneNumber)
        {
            var result = await GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.RMTProfileMissed.txt");

            var message = result.Data;

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }*/
        /// <summary>
        /// 
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> SendMTBeneficiaryCreatedMessage(string phoneNumber, string name)
        {
            var result = await GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.MTBeneficiaryCreated.txt");

            var template = result.Data;

            var message = string.Format(template, name);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> SendMTBeneficiaryApprovedMessage(string phoneNumber, string name)
        {
            var result = await GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.MTBeneficiaryApproved.txt");

            var template = result.Data;

            var message = string.Format(template, name);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <param name="passsword"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> SendResetPasswordMessage(string phoneNumber, string passsword)
        {
            var result = await GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.ResetPassword.txt");

            var template = result.Data;

            var message = string.Format(template, passsword);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse<string>> GetTextMessageTemplate(string path)
        {
            var result = await this._resourceFileHelper.GetResourceTextContent(path);

            return new ServiceResponse<string>(result.Data);
        }

        public async Task<ServiceResponse> SendPendingDirectTransferSMS(string phoneNumber, string firstName, string amount)
        {
            var result = await GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.PendingDirectTransferTemplate.txt");

            var template = result.Data;

            var message = string.Format(template, _textFormatter.ToTitleCase((firstName ?? "").ToLower()).Split(' ')[0], amount);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendClaimedDirectTransferToReceiverSMS(string phoneNumber, string name, decimal amount)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.ClaimedDirectTransferToReceiverTemplate.txt");

            var template = result.Data;

            var message = string.Format(template, amount);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendEHBeneficiaryCreatedMessage(string phoneNumber, string firstName, string middleName, string lastName)
        {
            var result = await GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.EHBeneficiaryCreated.txt");

            var template = result.Data;

            var message = string.Format(template, firstName, middleName, lastName);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendMissingRakFileMessage(string phoneNumber)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.MissingRakFile.txt");

            var template = result.Data;

            var message = string.Format(template);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendDailyRmtProfileStatsMessage(string phoneNumber, DailyRmtProfileStat dailyProfileStat)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.DailyRmtProfileStats.txt");

            var template = result.Data;

            var message = string.Format(template, DateTime.Now.ToString("dd/MM/yyyy"),
                dailyProfileStat.TotalCount.ToString("N0"),
                dailyProfileStat.AttachmentErrorCount.ToString("N0"),
                dailyProfileStat.UpdateFailedCount.ToString("N0"),
                dailyProfileStat.InvalidUpdateRecordStatusCount.ToString("N0"),
                dailyProfileStat.NoRecordsExistingCount.ToString("N0"),
                dailyProfileStat.TotalFailuresCount.ToString("N0"),
                dailyProfileStat.PendingCount.ToString("N0"),
                dailyProfileStat.DeletedCount.ToString("N0"),
                dailyProfileStat.RejectedCount.ToString("N0"),
                dailyProfileStat.ReactivatedCount.ToString("N0"),
                dailyProfileStat.DuplicateEidCount.ToString("N0"),
                dailyProfileStat.CreatedCount.ToString("N0"),
                dailyProfileStat.UpdatedCount.ToString("N0"),
                dailyProfileStat.UsersRmtStatusCreatedCount.ToString("N0"));

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendMissingProfilesAttachmentsUploadedStatsMessage(string phoneNumber, MissingProfilesAttachmentsUploadedStats missingAttachmentsUploadedStats)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.MissingProfilesAttachmentsUploadedStats.txt");

            var template = result.Data;

            var message = string.Format(template, DateTime.Now.ToString("dd/MM/yyyy"),
                missingAttachmentsUploadedStats.TotalProfilesQueued.ToString("N0"),
                missingAttachmentsUploadedStats.TotalProfilesAttachmentsAvailableInSFTP.ToString("N0"),
                missingAttachmentsUploadedStats.TotalProfilesAttachmentsMissing.ToString("N0"),
                missingAttachmentsUploadedStats.TotalProfilesAttachmentsUploaded.ToString("N0"));

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendOrderPlacedSMSMessage(SendOrderPlacedSMSRequest request)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.OrderPlacedTemplate.txt");

            var template = result.Data;

            var message = string.Format(template, request.PaidAmount, request.PhoneDetail, request.DeliveryDate);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = request.PhoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendSuccessfulUnEmpInsuranceSubscription(string phoneNumber, string policyNumber, UnEmpInsuranceStatus previousStatus)
        {
            var result = previousStatus == UnEmpInsuranceStatus.InProgress ? await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.SuccessfulInsuranceSubscriptionWithBalance.txt")
                       : previousStatus == UnEmpInsuranceStatus.PendingPayment ? await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.SuccessfulInsuranceSubscriptionWithoutBalance.txt")
                       : new ServiceResponse<string>();

            if (!string.IsNullOrEmpty(result.Data))
            {
                var template = result.Data;

                var message = string.Format(template, policyNumber);

                var smsData = new SendSMSRequest()
                {
                    Message = message,
                    MessageCategory = "4.2",
                    MessageContentType = "3.1",
                    PhoneNumber = phoneNumber,
                    Priority = 1,
                    DeliveryReport = "1"
                };

                await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);
            }
            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendSuccessfulUnEmpInsuranceSubscription(string phoneNumber, string policyNumber, bool isSalaryDebit)
        {
            var result = isSalaryDebit ? await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.SuccessfulInsuranceSubscriptionWithoutBalance.txt")
                       : await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.SuccessfulInsuranceSubscriptionWithBalance.txt");

            var template = result.Data;

            var message = string.Format(template, policyNumber);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendUnSuccessfulUnEmpInsuranceSubscription(string phoneNumber, string reason, UnEmpInsuranceStatus previousStatus)
        {
            var result = previousStatus == UnEmpInsuranceStatus.InProgress ? await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.UnSuccessfulInsuranceSubscriptionWithBalance.txt")
                       : previousStatus == UnEmpInsuranceStatus.PendingPayment ? await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.UnSuccessfulInsuranceSubscriptionWithoutBalance.txt")
                       : new ServiceResponse<string>();

            var template = result.Data;

            if (!string.IsNullOrEmpty(result.Data))
            {
                var smsReason = reason.Contains("0020") ? string.Concat(" ", ConstantParam.DubaiInsurancePurchaseErrorReason) : string.Empty;

                var message = string.Format(template, smsReason);

                var smsData = new SendSMSRequest()
                {
                    Message = message,
                    MessageCategory = "4.2",
                    MessageContentType = "3.1",
                    PhoneNumber = phoneNumber,
                    Priority = 1,
                    DeliveryReport = "1"
                };

                await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendUnEmpInsuranceCancellationRequest(string phoneNumber)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.CancelInsuranceRequest.txt");

            var template = result.Data;

            var message = string.Format(template);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            try
            {
                await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

                _logger.LogDebug($"SMS is sent to {phoneNumber} for UnEmpInsuranceCancellationRequest");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"SMS is not sent to {phoneNumber} for UnEmpInsuranceCancellationRequest");
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendNewDeviceAddedMessage(string phoneNumber, bool userVerified)
        {
            string message = string.Empty;

            var textContents = await _lookupService.GetDeviceAdditionTextMessage(LanguageContentType.dev_bind_msg.ToString());

            foreach (var textContent in textContents.Data)
            {
                if ((userVerified && textContent.Code == _deviceAddedVerifiedUser) || (!userVerified && textContent.Code == _deviceAddedNonVerifiedUser))
                {
                    message = textContent.Text;
                    break;
                }
            }

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendKycGracePeriod(string phoneNumber, bool isNotSubmitted)
        {
            ServiceResponse<string> result;
            if (isNotSubmitted)
            {
                result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.KycNotSubmittedGracePeriod.txt");
            }
            else
            {
                result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.KycExpiringGracePeriod.txt");
            }

            var smsData = new SendSMSRequest()
            {
                Message = result.Data,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            try
            {
                await SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

                _logger.LogDebug("SMS is sent to {phoneNumber} for KycGracePeriod", phoneNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SMS is not sent to {phoneNumber} for KycGracePeriod", phoneNumber);
            }

            return new ServiceResponse();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <param name="daysLeft"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> SendFreeExpiryNotification(string phoneNumber, double daysLeft)
        {
            var result = await GetTextMessageTemplate($"C3Pay.Services.Helper.Templates.SMS.FreeTransferExpiry_{daysLeft}_DaysLeft.txt");

            var smsData = new SendSMSRequest()
            {
                Message = result.Data,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendAtmWithdrawalFeeReversalSms(string phoneNumber)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.AtmWithdrawalFeeReversal.txt");

            var smsData = new SendSMSRequest()
            {
                Message = result.Data,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendC3PayLifeInsuranceNomineeChangesToNominee(string phoneNumber, string cardHolderName, string nomineeName, string policyNumber)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.C3PayPlusLifeInsuranceNomineeChangesToNominee.txt");
            var template = result.Data;
            var message = string.Format(template, cardHolderName, nomineeName, policyNumber);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendC3PayLifeInsuranceNomineeChangesToMembershipUser(string phoneNumber, string cardHolderName, string nomineeName)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.C3PayPlusLifeInsuranceNomineeChangesToMembershipUser.txt");
            var template = result.Data;
            var message = string.Format(template, cardHolderName, nomineeName);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendMRAutoRenewalDeactivationMessage(string phoneNumber, bool dueToLowBalance, string productName)
        {
            var result = dueToLowBalance ? await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.MRAutoRenewalDeactivationLowBalance.txt")
                        : await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.MRAutoRenewalDeactivationPackUnavailable.txt");

            var template = result.Data;

            var message = string.Format(template, productName);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendC3PayPlusSubscriptionSms(string phoneNumber)
        {
            var resource = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.C3PayPlusSubscriptionSmsTemplate.txt");

            var message = resource.Data;

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendMRLowBalanceStartSms(string phoneNumber, string firstName)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.MRLowBalanceStartSmsTemplate.txt");

            var template = result.Data;

            var message = string.Format(template, firstName);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }
        public async Task<ServiceResponse> SendMRLowBalanceEndSms(string phoneNumber)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.SMS.MRLowBalanceEndSmsTemplate.txt");

            var message = result.Data;

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            await SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendVpnSubscriptionSuccessMessage(string phoneNumber, string vpnCode)
        {
            var result = await GetTextMessageTemplate("C3Pay.Services.Helper.Templates.SMS.VpnSubscriptionSuccess.txt");

            var template = result.Data;

            var message = string.Format(template, vpnCode);

            var smsData = new SendSMSRequest()
            {
                Message = message,
                MessageCategory = "4.2",
                MessageContentType = "3.1",
                PhoneNumber = phoneNumber,
                Priority = 1,
                DeliveryReport = "1"
            };

            try
            {
                await this.SendTextMessage(smsData, (SmsProvider)_generalSettings.PrimarySmsProvider);

            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Failed to send VPN subscription success SMS to {phoneNumber}");
            }

            return new ServiceResponse();
        }

    }
}
