﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace C3Pay.Core.Services
{
    public interface ISubscriptionService
    {
        public Task<ServiceResponse<List<UserSubscriptionDetailsDto>>> GetUserSubscriptionList(User user, string languageCode = "en");
        public Task<ServiceResponse<Subscription>> GetSubscripitonsById(Guid id);        
        public Task<ServiceResponse<IEnumerable<Subscription>>> GetSubscripitonsList(string corporateId, string language);
        public Task<ServiceResponse<IEnumerable<Subscription>>> GetSubscripitonsListReadOnly();
        public Task<ServiceResponse<IEnumerable<UserSubscription>>> GetUserSubscriptions(Guid userId);
        Task<ServiceResponse<bool>> UserHasActiveSubscription(Guid userId, Guid subscriptionId, CancellationToken cancellationToken = default(CancellationToken));
        public Task<ServiceResponse> Subscribe(Guid userId, Guid subscriptionId, Guid? portalUserId = null, bool? addOutboxMessage = null,string portalEmailId=null);
        public Task<ServiceResponse> Unsubscribe(Guid userId, Guid subscriptionId, Guid? portalUserId = null, bool? addOutboxMessage = null, string portalEmailId = null);
        Task<ServiceResponse<SubscriptionFee>> GetSubscriptionFee(Guid subscriptionId, string corporateId);
        Task<ServiceResponse> SyncSubscription(UpdateSubscriptionDto updateSubscriptionDto);
    }
}
