﻿using C3Pay.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Data.Configurations
{
    public class UserConfiguration : IEntityTypeConfiguration<User>
    {
        public void Configure(EntityTypeBuilder<User> builder)
        {
            builder.ToTable("Users");

            builder.Has<PERSON>ey(c => c.Id);

            builder.Property(c => c.Id).HasDefaultValueSql("NEWSEQUENTIALID()");

            builder.Property(c => c.CardHolderId)
                .HasMaxLength(15);

            builder.Property(c => c.PhoneNumber)
              .HasMaxLength(15);

            builder.Property(c => c.DeviceToken)
              .HasMaxLength(300);

            builder.Property(c => c.Email)
              .HasMaxLength(100);

            builder.Property(c => c.ReferralCode)
              .HasMaxLength(5);

            builder.Property(c => c.MoneyTransferProfileStatus)
              .HasDefaultValue(MoneyTransferProfileStatus.Missing);

            builder
               .HasMany(c => c.UploadedDocuments)
               .WithOne(u => u.User)
               .IsRequired()
               .HasForeignKey(u => u.UserId)
               .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.Documents)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.Transactions)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.SecretAnswers)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.Subscriptions)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.Identifications)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.VerificationComments)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.MobileRechargeBeneficiaries)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.MoneyTransferBeneficiaries)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.MobileRechargeTransactions)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.MoneyTransferTransactions)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.AuditTrails)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.MoneyTransferDirectBeneficiaries)
                .WithOne(u => u.LinkedUser)
                .HasForeignKey(u => u.LinkedUserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
              .HasMany(c => c.BillPaymentBillers)
              .WithOne(u => u.User)
              .IsRequired()
              .HasForeignKey(u => u.UserId)
              .OnDelete(DeleteBehavior.Restrict);

            builder
              .HasMany(c => c.Ratings)
              .WithOne(u => u.User)
              .IsRequired()
              .HasForeignKey(u => u.UserId)
              .OnDelete(DeleteBehavior.Restrict);

            builder
              .HasMany(c => c.Orders)
              .WithOne(u => u.User)
              .IsRequired()
              .HasForeignKey(u => u.UserId)
              .OnDelete(DeleteBehavior.Restrict);

            builder
              .HasMany(c => c.UnemploymentInsurancePayments)
              .WithOne(u => u.User)
              .IsRequired()
              .HasForeignKey(u => u.UserId)
              .OnDelete(DeleteBehavior.Restrict);

            builder
              .HasMany(c => c.Devices)
              .WithOne(u => u.User)
              .IsRequired()
              .HasForeignKey(u => u.UserId)
              .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.UnEmpInsurancePayments)
                .WithOne(u => u.User)
                .IsRequired()
                .HasForeignKey(u => u.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
              .HasMany(c => c.PhoneNumberChangeLogs)
              .WithOne(u => u.User)
              .HasForeignKey(u => u.UserId)
              .OnDelete(DeleteBehavior.Restrict);

            builder
              .HasMany(c => c.MobileRechargeRenewals)
              .WithOne(u => u.User)
              .HasForeignKey(u => u.UserId)
              .OnDelete(DeleteBehavior.Restrict);

            builder
               .HasOne(c => c.UserSegmentation)
               .WithOne(p => p.User)
               .HasForeignKey<UserSegmentation>(c => c.UserId);

            builder.HasIndex(x => x.PhoneNumber);
            builder.HasIndex(x => x.ExternalId);
            builder.HasIndex(x => x.CreatedDate);
            builder.HasIndex(x => x.ReferralCode);
        }
    }
}
