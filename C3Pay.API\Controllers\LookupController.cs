﻿using AutoMapper;
using C3Pay.API.Resources;
using C3Pay.API.Resources.Languages;
using C3Pay.API.Resources.Subscriptions;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs.Lookup;
using C3Pay.Core.Models.DTOs.MoneyTransfer.Lookups;
using C3Pay.Core.Models.Settings;
using C3Pay.Core.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [InputValidation]
    [ApiController]
    public class LookupController : BaseController
    {
        private readonly ILookupService _lookupService;
        private readonly IMapper _mapper;
        private readonly LanguageSettings _languageSettings;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lookupService"></param>
        /// <param name="mapper"></param>
        /// <param name="languageSettings"></param>
        public LookupController(ILookupService lookupService,
                                IMapper mapper,
                                IOptions<LanguageSettings> languageSettings,
                                IHttpContextAccessor httpContextAccessor,
                                IUserService userService,
                                ILogger<MoneyTransferController> logger) : base(httpContextAccessor, userService, logger)
        {
            _lookupService = lookupService;
            _mapper = mapper;
            _languageSettings = languageSettings.Value;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("security-questions")]
        public async Task<ActionResult<IEnumerable<SecurityQuestion>>> GetAllSecurityQuestions()
        {
            var securityQuestionsResult = await _lookupService.GetAllSecurityQuestions();

            var questions = securityQuestionsResult.Data;

            var mappedQuestions = this._mapper.Map<List<SecurityQuestionDto>>(questions);

            return this.Ok(mappedQuestions);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="product">Add a Arabic language when product is Salary advance(SA) </param>
        /// <returns></returns>
        [HttpGet("supported-languages")]
        public async Task<ActionResult<List<SupportedLanguagesDto>>> GetAllSupportedLanguages(string product = "")
        {
            var supportedLanguages = await _lookupService.GetSupportedLanguages(product);
            var languages = supportedLanguages.Data;
            var mappedLanguages = this._mapper.Map<List<SupportedLanguagesDto>>(languages);
            return this.Ok(mappedLanguages);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("eligibility-criteria")]
        public async Task<ActionResult<IEnumerable<EligibilityCriteriaDto>>> GetEligibilityCriteria(string nationality)
        {
            string languageCode = Request.Headers["x-lang-code"];

            var Criteria = await _lookupService.GetEligibilityCriteria(languageCode, LanguageContentType.sa_criteria.ToString());

            if (!Criteria.IsSuccessful)
                return this.BadRequest(Criteria.ErrorMessage);

            var Response = new EligibilityCriteriaDto();
            Response.LanguageCode = languageCode;
            Response.Criteria = Criteria.Data;

            var audioLink = _languageSettings.AudioLinks.FirstOrDefault(l => l.Nationality == nationality.ToUpper());
            if (audioLink != null)
                Response.AudioLink = audioLink.URL;

            return this.Ok(Response);
        }

        /// <summary>
        /// Gets the list of subscriptions and its features
        /// </summary>
        /// <returns></returns>
        [HttpGet("subscriptions")]
        public async Task<ActionResult<IEnumerable<SubscriptionDto>>> GetSubscriptions(string corporateId)
        {
            string languageCode = Request.Headers["x-lang-code"];

            var subscriptions = await _lookupService.GetSubscriptions(corporateId, languageCode);
            var result = this._mapper.Map<List<SubscriptionDto>>(subscriptions.Data);
            result.Single(subscription => subscription.Code == nameof(BaseEnums.SMSSubscriptionType.T)).Video = await GetVideoUrls();

            return this.Ok(result);
        }

        [HttpGet("store-cities")]
        public async Task<ActionResult<IEnumerable<CityDto>>> GetCities()
        {
            var cities = await this._lookupService.GetStoreEnabledCities();

            var cityDtos = _mapper.Map<IList<City>, IList<CityDto>>(cities.Data);

            return Ok(cityDtos);
        }

        [HttpGet("store-phones")]
        public async Task<ActionResult<IList<PhoneDto>>> GetAllPhones()
        {
            var phones = await this._lookupService.GetAllStorePhones();

            var phoneDtos = _mapper.Map<IList<Phone>, IList<PhoneDto>>(phones.Data);

            return Ok(phoneDtos);
        }

        /// <summary>
        /// Get Cities based on state id
        /// </summary>
        /// <param name="stateId"></param>
        /// <returns></returns>
        [HttpGet("cities/{stateId}")]
        public async Task<ActionResult<IList<LocationDto>>> GetCities([FromRoute] int stateId)
        {
            var cities = await _lookupService.GetCities(stateId);
            return Ok(_mapper.Map<IList<LocationDto>>(cities.Data));
        }

        /// <summary>
        /// Get States based on country code
        /// </summary>
        /// <param name="countryCode"></param>
        /// <returns></returns>
        [HttpGet("states/{countryCode}")]
        public async Task<ActionResult<IList<LocationDto>>> GetStates([FromRoute] string countryCode)
        {
            var states = await _lookupService.GetStates(countryCode);
            return Ok(_mapper.Map<IList<LocationDto>>(states.Data));
        }

        /// <summary>
        /// Get Multimedia Resources
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet("multimedia")]
        public async Task<ActionResult<List<MultimediaDto>>> GetMultimediaResources([FromQuery] string identifier,
                                                                                    [FromQuery] MultimediaType? type,
                                                                                    [FromQuery] int? feature)
        {
            await GetLoggedInUser();
            string languageCode = Request.Headers["x-lang-code"];

            var files = await _lookupService.GetMultimediaResources(_user.CardHolderId, _user.CardHolder?.Nationality, identifier, type, languageCode, feature);
            if (files.IsSuccessful == false)
            {
                return BadRequest(files.ErrorMessage);
            }

            return Ok(files.Data);
        }

        /// <summary>
        /// Get Provinces By Country Code
        /// </summary>
        /// <param name="nationality"></param>
        /// <returns></returns>
        [HttpGet("provinces")]
        public async Task<ActionResult<IList<ProvinceDto>>> GetProvinces([FromQuery] string nationality)
        {
            var provinces = await _lookupService.GetProvincesByCountry(nationality);

            if(provinces.IsSuccessful == false)
            {
                return Problem(provinces.ErrorMessage);
            }

            return Ok(provinces.Data);
        }

        #region Private Methods
        private async Task<List<SubscriptionMultimediaDto>> GetVideoUrls()
        {
            var userShouldSeeHindiVideo = false;
            var multimediaResources = await _lookupService.GetMultimediaResources(feature: (int)FeatureType.Subscription_SecuritySMS).ConfigureAwait(false);

            bool defaultAlreadySet = false;
            return multimediaResources.Data
                .Select(resource => new SubscriptionMultimediaDto
                {
                    LanguageCode = resource.Language,
                    Url = resource.Url,
                    IsDefault = ((resource.Language == "hi" && userShouldSeeHindiVideo)
                                    || (resource.Language != "hi" && !userShouldSeeHindiVideo))
                                && !defaultAlreadySet && (defaultAlreadySet = true)
                })
                .ToList();
        }
        #endregion
    }
}