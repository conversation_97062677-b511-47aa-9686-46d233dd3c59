﻿using System;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models.Messages
{
    public class MobileRechargeDetails
    {
        public Guid Id { get; set; }
        public string BeneficiaryFullName { get; set; }
        public decimal RechargeAmount { get; set; }
        public string RechargeCurrency { get; set; }
        public decimal FeeAmount { get; set; }
        public string FeeCurrency { get; set; }
        public decimal TotalAmount { get; set; }
        public string TotalCurrency { get; set; }
        public decimal ReceiveAmount { get; set; }
        public string ReceiveCurrency { get; set; }
        public decimal DiscountAmount { get; set; }
        public string DiscountCurrency { get; set; }
        public MobileRechargeType RechargeType { get; set; }
        public string Operator { get; set; }
        public string MobileNumber { get; set; }
        public string SecretCode { get; set; }
        public DateTime Date { get; set; }
        public Status Status { get; set; }
        public string Remarks { get; set; }
        public string Logo { get; set; }
    }
}
