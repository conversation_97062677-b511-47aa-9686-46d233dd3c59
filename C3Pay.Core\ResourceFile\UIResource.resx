﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="devicebindingawareness_initialvideo" xml:space="preserve">
    <value>en##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_en.mp4 | bn##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_bn.mp4 | hi##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_hi.mp4 | ml##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_ml.mp4 | ta##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_ta.mp4 | te##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_te.mp4 | ur-en##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_ur-en.mp4</value>
  </data>
  <data name="devicebindingawareness_recurringvideo" xml:space="preserve">
    <value>en##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_en.mp4 | bn##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_bn.mp4 | hi##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_hi.mp4 | ml##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_ml.mp4 | ta##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_ta.mp4 | te##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_te.mp4 | ur-en##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_ur-en.mp4</value>
  </data>
  <data name="securitysmsmigration_video" xml:space="preserve">
    <value>bn##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_bn.mp4 | 
en##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_en.mp4 | 
hi##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_hi.mp4 | 
ml##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_ml.mp4 | 
ta##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_ta.mp4 | 
te##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_te.mp4 | 
ur-en##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_ur-en.mp4</value>
  </data>
  <data name="securitysms_video" xml:space="preserve">
    <value>bn##https://cdn.edenred.ae/security-sms-awareness/securitysms_bn.mp4 | en##https://cdn.edenred.ae/security-sms-awareness/securitysms_en.mp4 | hi##https://cdn.edenred.ae/security-sms-awareness/securitysms_hi.mp4 | ml##https://cdn.edenred.ae/security-sms-awareness/securitysms_ml.mp4 | ta##https://cdn.edenred.ae/security-sms-awareness/securitysms_ta.mp4 | te##https://cdn.edenred.ae/security-sms-awareness/securitysms_te.mp4 | ur-en##https://cdn.edenred.ae/security-sms-awareness/securitysms_ur-en.mp4</value>
  </data>
  <data name="t9n_AccountNumber" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_District_Heading" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_FullName_Wallet_Example" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_IBAN_Example" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_IFSC_Code_Example" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Last_Name_Example" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Middle_Name_Example" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Name_Example" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Next_Button" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_PhoneNumber" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Search_Bank_Branch_Example" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Search_Bank_Example" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_IFSC" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Help" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Personal_Details_Heading" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_FirstName" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_LastName" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Choose_Option_Heading" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Select_Bank_Heading" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Select_Branch_Heading" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Select_Bank_description" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Select_Bank" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Select_IFSC_Code" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Account_Number_Placeholder" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_MiddleName" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_FullName" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_IFSC_Details_Heading" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Account_Details_Heading" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_AccountNumberOrIBAN" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_PostalCode" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Postal_Code" xml:space="preserve">
    <value></value>
  </data>
  <data name="t9n_Postal_Code_Heading" xml:space="preserve">
    <value></value>
  </data>
</root>