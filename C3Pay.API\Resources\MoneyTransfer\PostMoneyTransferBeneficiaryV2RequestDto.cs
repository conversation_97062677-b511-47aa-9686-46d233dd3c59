﻿using C3Pay.Core.Models;
using System;

namespace C3Pay.API.Resources.MoneyTransfer
{
    /// <summary>
    /// 
    /// </summary>
    public class PostMoneyTransferBeneficiaryV2RequestDto
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string TransferMethod { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public MoneyTransferBeneficiaryBaseDto Beneficiary { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public BankDetailsV2Dto BankDetails { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? ReasonId { get; set; }
    }
}
