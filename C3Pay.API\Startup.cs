﻿using C3Pay.API.Middleware;
using C3Pay.API.StartupExtensions;
using C3Pay.Core.Services;
using C3Pay.Services;
using C3Pay.Services.Handlers;
using C3Pay.Services.Handlers.MoneyTransfer.BankSearch.Handlers;
using Edenred.Common.Core;
using Edenred.Common.Services.Azure.Extensions;
using Edenred.Common.Services.Extensions;
using MassTransit;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Microsoft.FeatureManagement.FeatureFilters;
using Serilog;
using System;

namespace C3Pay.API
{
    public class Startup
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        public Startup(IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
        {
            Configuration = configuration;
            _webHostEnvironment = webHostEnvironment;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddMassTransit(x =>
            {
                x.UsingAzureServiceBus((context, cfg) =>
                {
                    cfg.Host(Configuration.GetConnectionString("ServiceBusConnection"));

                    cfg.ConfigureEndpoints(context);
                });
            });

            services.AddControllers();
            services.AddGrpc();
            services.InjectAuthentication(Configuration);

            //Configure Application insight
            services.InjectLogging(Configuration);

            services.InjectDatabase(Configuration);
            services.InjectRepositories();
            services.InjectIntegrationServices(Configuration, _webHostEnvironment);
            services.InjectIdentityServices(Configuration);
            services.InjectHelperServices(Configuration);

            services.InjectLocalServices();

            services.InjectLocalIntegrationServices(Configuration);

            services.InjectAzureServices(Configuration);

            services.InjectMockServices(Configuration, _webHostEnvironment);

            //app settings injection
            services.InjectSettings(Configuration);

            // Repositories

            //Add caching
            if (Convert.ToBoolean(Configuration["General:EnableRedis"]))
            {
                services.AddRedisCaching(Configuration, "C3Pay");
            }
            else
            {
                services.AddDistributedMemoryCache();
            }

            services.AddHttpContextAccessor();


            //Add Command Handlers
            services.AddScoped<UserCommandHandler>();
            services.AddScoped<IdentificationCommandHandler>();
            services.AddScoped<SynchronizeBanksAndBranchesHandler>();

            //Add Mock Command Handlers
            services.AddScoped<UserCommandMockHandler>();

            //for testing only 
            services.AddTransient<IKycUnblockService, KycUnblockService>();
            services
                .AddControllers(options =>
                {
                    options.Filters.Add(typeof(InputValidationAttribute));
                })
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
                });

            services.AddSwaggerDocumentation();

            services.AddLogging();

            services.AddAutoMapper(typeof(Startup).Assembly, typeof(Edenred.Common.Services.MappingProfile).Assembly);

            //  services.AddAutoMapper(typeof(Startup), typeof(Edenred.Common.Services.MappingProfile));
            services.AddOptions();

            services.AddHealthChecks();

            services.InjectValidators();

            services.AddAzureAppConfiguration();

            services.AddSingleton<ITargetingContextAccessor, TransactionTargetingContextAccessor>();

            services.AddFeatureManagement()
                .AddFeatureFilter<TargetingFilter>()
                .AddFeatureFilter<MRCrossCheckBeneficiaryFeatureFilter>();

            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(GetBottomNavQuery).Assembly));
            //  services.AddMediatR(typeof(GetBottomNavQuery).GetTypeInfo().Assembly);
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILogger<Startup> logger)
        {
            if (!string.IsNullOrEmpty(Configuration.GetConnectionString("AzureAppConfig")))
            {
                app.UseAzureAppConfiguration();
            }

            app.ConfigureExceptionHandler(logger);

            app.UseHttpsRedirection();

            app.UseSerilogRequestLogging();

            app.UseRouting();


            app.UseAuthentication();

            app.UseAuthorization();
            app.UseMiddleware<TelemetryUserIdMiddleware>();

            app.UseRequestBodyLogging();

            app.UseResponseBodyLogging();

            app.UseMiddleware<C3Pay.API.Middleware.RequestHeaderLoggingMiddleware>();
            app.UseMiddleware<RequestVerificationMiddleware>();
            app.UseMiddleware<ResponseVerificationMiddleware>();
            app.UseMiddleware<DeviceAuthorizeMiddleware>();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHealthChecks("/api/health");
            });

            if (env.IsDevelopment() || (Configuration["General:EnableSwagger"] != null && Configuration["General:EnableSwagger"] == "true"))
            {
                app.UseSwaggerAuthorized(Configuration["Swagger:Username"], Configuration["Swagger:Password"]);
                app.UseSwagger();
                app.UseSwaggerUI(configuration =>
                {
                    configuration.RoutePrefix = "";
                    configuration.SwaggerEndpoint("/swagger/v1/swagger.json", "C3Pay V1");
                    configuration.DisplayRequestDuration();
                });
            }
        }
    }
}
