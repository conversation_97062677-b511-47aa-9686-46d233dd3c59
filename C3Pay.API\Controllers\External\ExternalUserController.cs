﻿using AutoMapper;
using C3Pay.API.Models;
using C3Pay.API.Resources;
using C3Pay.API.Resources.Balance;
using C3Pay.API.Resources.Card;
using C3Pay.API.Resources.Login;
using C3Pay.API.Resources.SignUp;
using C3Pay.API.Resources.UserProfile;
using C3Pay.Core.Services;
using C3Pay.Services.Commands;
using C3Pay.Services.Handlers;
using Edenred.Common.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using static Edenred.Common.Core.Enums;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    [Authorize(AuthenticationSchemes = AuthenticationScheme.AzureActiveDirectory, Policy = "ApplicationIdEntry")]
    public class ExternalUserController : ControllerBase
    {
        private readonly UserCommandHandler _userCommandHandler;
        private readonly IESMOWebService _esmoWebService;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IStatementService _statementService;
        private readonly IPPSService _ppsService;

        /// <summary>
        /// ExternalUser Controller
        /// </summary>
        /// <param name="userCommandHandler"></param>
        /// <param name="mapper"></param>
        /// <param name="esmoWebService"></param>
        /// <param name="userService"></param>
        /// <param name="statementService"></param>
        public ExternalUserController(UserCommandHandler userCommandHandler,
            IMapper mapper,
            IESMOWebService esmoWebService,
            IUserService userService,
            IStatementService statementService)
        {
            _userCommandHandler = userCommandHandler;
            _mapper = mapper;
            _esmoWebService = esmoWebService;
            _userService = userService;
            _statementService = statementService;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("register-or-update")]
        public async Task<ActionResult<ExternalUserDto>> RegisterOrUpdate(RegisterOrUpdateExternalUserRequestDto request)
        {
            var command = this._mapper.Map<RegisterOrUpdateExternalUserCommand>(request);

            command.ApplicationId = Core.BaseEnums.MobileApplicationId.MySalary;

            var createOrUpdateResult = await this._userCommandHandler.Handle(command);

            if (!createOrUpdateResult.IsSuccessful)
            {
                return BadRequest(createOrUpdateResult.ErrorMessage);
            }

            createOrUpdateResult.Data.ApplicationId = Core.BaseEnums.MobileApplicationId.MySalary;

            var user = this._mapper.Map<ExternalUserDto>(createOrUpdateResult.Data);

            return this.Ok(user);
        }

        /// <summary>
        /// Possible Results: 400-BlockedTooManyFailedAttempts, 400-InvalidCard, 400-IncorrectCvc2, 400-Error, 200
        /// </summary>
        /// <param name="cardNumber"></param>
        /// <param name="cvc2"></param>
        /// <returns></returns>
        [HttpGet("card/verify/{cardNumber}/{cvc2}")]
        public async Task<ActionResult<ExternalUserDto>> VerifyCVC(string cardNumber, string cvc2, [FromQuery] bool requiresUnblock)
        {
            // Get card's details.
            var cardDetails = await this._esmoWebService.GetCardDetails(new GetCardDetailsRequest() { CardNumber = cardNumber });

            if (cardDetails.IsSuccessful == false || string.IsNullOrEmpty(cardDetails?.Data?.SerialNumber))
            {
                return BadRequest(CardDetailsResults.InvalidCard.ToString());
            }

            if (requiresUnblock)
            {
                var unblockedKycCardholder = await this._ppsService.UnblockCard(cardDetails.Data.SerialNumber);
                if (unblockedKycCardholder.IsSuccessful == false)
                {
                    return BadRequest(CardDetailsResults.Error.ToString());
                }
            }

            // Validate CVC2
            var isCvc2Valid = await this._esmoWebService.VerifyCvc2(new VerifyCvc2Request()
            {
                CardSerialNumber = cardDetails.Data.SerialNumber,
                Cvc2 = cvc2
            });

            if (requiresUnblock)
            {
                var blockedKycCardholder = await this._ppsService.BlockCard(cardDetails.Data.SerialNumber);
                if (blockedKycCardholder.IsSuccessful == false)
                {
                    return BadRequest(CardDetailsResults.Error.ToString());
                }
            }

            if (isCvc2Valid.IsSuccessful == false)
            {
                Enum.TryParse(isCvc2Valid.ErrorMessage, out Enums.AtmPinErrors atmPinError);

                switch (atmPinError)
                {
                    case AtmPinErrors.MAX_PIN_TRIES_EXCEEDED:
                        return BadRequest(CardDetailsResults.BlockedTooManyFailedAttempts.ToString());
                    case AtmPinErrors.INVALID_CVC2:
                        return BadRequest(CardDetailsResults.IncorrectCvc2.ToString());
                    case AtmPinErrors.GENERAL_ERROR:
                        break;
                    default:
                        return BadRequest(CardDetailsResults.Error.ToString());
                }
            }

            return this.Ok(cardDetails.Data.CardholderRef);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        [HttpPost("card/serial-number")]
        public async Task<ActionResult> GetCardSerialNumber(GetCardSerialNumberRequestDto requestDto)
        {
            // Get card's details.
            var cardDetails = await this._esmoWebService.GetCardDetails(new GetCardDetailsRequest() { CardNumber = requestDto.CardNumber });

            if (cardDetails.IsSuccessful == false || string.IsNullOrEmpty(cardDetails?.Data?.SerialNumber))
            {
                return BadRequest(CardDetailsResults.InvalidCard.ToString());
            }
           
            return this.Ok(cardDetails.Data.SerialNumber);
        }

        /// <summary>
        /// Possible Returns 400-Error, 400-WrongCombination, 200
        /// </summary>
        /// <param name="verifyAutoUnblockDetailsRequest"></param>
        /// <returns></returns>
        [HttpPost("auto-unblock/verify")]
        public async Task<ActionResult<Guid>> AutoUnblockVerifyDetails(VerifyAutoUnblockDetailsRequestDto verifyAutoUnblockDetailsRequest)
        {
            var command = this._mapper.Map<VerifyAutoUnblockDetailsCommand>(verifyAutoUnblockDetailsRequest);

            command.ApplicationId = Core.BaseEnums.MobileApplicationId.MySalary;

            var verifyDetailsResult = await this._userCommandHandler.Handle(command);

            if (!verifyDetailsResult.IsSuccessful)
            {
                return this.BadRequest(verifyDetailsResult.ErrorMessage);
            }

            return this.Ok(verifyDetailsResult.Data);
        }

        [HttpGet("{corporateId}/device-tokens")]
        public async Task<ActionResult<bool>> GetActiveUsersDeviceTokensByCorporateId(string corporateId)
        {
            var command = new GetActiveUsersDeviceTokensByCorporateIdCommand()
            {
                CorporateId = corporateId
            };

            var deviceTokensResult = await this._userCommandHandler.Handle(command);

            return this.Ok(deviceTokensResult.Data);
        }

        /// <summary>
        /// Generates and sends the User's statement
        /// </summary>
        /// <param name="statementRequest"></param>
        /// <returns></returns>
        [HttpPost("statement")]
        public async Task<ActionResult> GenerateStatement(PostTransactionsStatementRequestDto statementRequest)
        {
            var getUserResponse = await this._userService.GetUserById(statementRequest.UserId);

            if (!getUserResponse.IsSuccessful)
            {
                return this.BadRequest(getUserResponse.Data);
            }

            var user = getUserResponse.Data;

            var generateAndSendStatementResult = await this._statementService.GenerateAndSendStatement(user, statementRequest.StartDate, statementRequest.EndDate, statementRequest.Email);

            if (!generateAndSendStatementResult.IsSuccessful)
            {
                if (generateAndSendStatementResult.ErrorMessage == EnumUtility.GetDescriptionFromEnumValue(GetCardStatementResponse.ESSOA012))
                {
                    return this.BadRequest(StatementResult.NoTransactionsFound.ToString());
                }

                return this.BadRequest(generateAndSendStatementResult.ErrorMessage);
            }

            return this.Ok();
        }

        [HttpPost("card/pin")]
        public async Task<ActionResult<CardPinDto>> GetCardPin(Guid userId, GetCardPinRequestDto requestDto)
        {
            #region verify it's the same card as the user's
            var tryGetCardDetails = await this._esmoWebService.GetCardDetails(new GetCardDetailsRequest()
            {
                CardNumber = requestDto.CardNumber
            });

            if (tryGetCardDetails.IsSuccessful == false || string.IsNullOrEmpty(tryGetCardDetails.Data?.CardholderRef))
            {
                return BadRequest(tryGetCardDetails.ErrorMessage);
            }

            var cardSerialNumber = tryGetCardDetails.Data.SerialNumber;
            var cardHolderId = tryGetCardDetails.Data.CardholderRef;

            if (cardHolderId != requestDto.CardHolderId)
            {
                return BadRequest(CardEligibilityResult.InvalidCombination.ToString());
            }
            #endregion

            #region verify cvc2
            var getPinResult = await this._esmoWebService.GetCardPin(new GetCardPinRequest()
            {
                CardSerialNumber = cardSerialNumber,
                Cvc2 = requestDto.Cvc2
            });

            if (!getPinResult.IsSuccessful)
            {
                Enum.TryParse(getPinResult.ErrorMessage, out Enums.AtmPinErrors atmPinError);
                switch (atmPinError)
                {
                    case AtmPinErrors.MAX_PIN_TRIES_EXCEEDED:
                        return BadRequest(CardDetailsResults.BlockedTooManyFailedAttempts.ToString());
                    case AtmPinErrors.INVALID_CVC2:
                        return BadRequest(CardDetailsResults.InvalidCombination.ToString());
                    case AtmPinErrors.GENERAL_ERROR:
                    default:
                        return BadRequest(CardDetailsResults.Error.ToString());
                }
            }

            if (!getPinResult.IsSuccessful)
            {
                return BadRequest(getPinResult.ErrorMessage);
            }
            #endregion

            #region return pin
            var result = new CardPinDto()
            {
                Pin = getPinResult.Data.Pin
            };

            return this.Ok(result);
            #endregion
        }

        [HttpPost("card/kyc-unblock")]
        public async Task<ActionResult> CompleteKycUnblock(CompleteKycUnblockRequestDto requestDto)
        {                        
            var tryUnblockCard = await this._ppsService.UnblockCard(requestDto.CardSerialNumber);

            if (!tryUnblockCard.IsSuccessful)
            {
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
            else
            {                
                await this._esmoWebService.MarkCardAsKycUnblocked(requestDto.CitizenId);
            }

            return this.Ok();
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteUser(int id)
        {
            var deleteResult = await this._userCommandHandler.Handle(new DeleteExternalUserCommand
            {
                ExternalId = id
            });

            if (!deleteResult.IsSuccessful)
            {
                return BadRequest(deleteResult.ErrorMessage);
            }

            return this.Ok();
        }
    }
}
