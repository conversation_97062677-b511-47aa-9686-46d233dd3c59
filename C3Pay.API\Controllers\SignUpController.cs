﻿using AutoMapper;
using C3Pay.API.Filters;
using C3Pay.API.Resources;
using C3Pay.API.Resources.Card;
using C3Pay.API.Resources.EmiratesId;
using C3Pay.API.Resources.SignUp;
using C3Pay.API.Resources.UserProfile;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs.SignUp;
using C3Pay.Core.Services;
using C3Pay.Core.Services.Security;
using C3Pay.Services.Commands;
using C3Pay.Services.Handlers;
using C3Pay.Services.Membership.Queries;
using Edenred.Common.Core;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using FaceMatchResponseDto = C3Pay.API.Resources.FaceMatchResponseDto;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    public class SignUpController : ControllerBase
    {
        private readonly IdentificationCommandHandler _identificationCommandHandler;
        private readonly UserCommandHandler _userCommandHandler;
        private readonly ICardHolderService _cardHolderService;
        private readonly IESMOWebService _esmoWebService;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly ISubscriptionService _subscriptionService;
        private readonly IMediator _mediator;
        private readonly ISecurityService _securityService;
        private readonly ILookupService _lookupService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="identificationCommandHandler"></param>
        /// <param name="userCommandHandler"></param>
        /// <param name="cardHolderService"></param>
        /// <param name="esmoWebService"></param>
        /// <param name="mapper"></param>
        /// <param name="userService"></param>
        /// <param name="subscriptionService"></param>
        /// <param name="mediator"></param>
        /// <param name="securityService"></param>
        /// <param name="lookupService"></param>
        public SignUpController(IdentificationCommandHandler identificationCommandHandler,
            UserCommandHandler userCommandHandler,
            ICardHolderService cardHolderService,
            IESMOWebService esmoWebService,
            IMapper mapper,
            IUserService userService,
            ISubscriptionService subscriptionService,
            IMediator mediator,
            ISecurityService securityService,
            ILookupService lookupService)
        {
            this._identificationCommandHandler = identificationCommandHandler;
            this._userCommandHandler = userCommandHandler;
            this._cardHolderService = cardHolderService;
            this._esmoWebService = esmoWebService;
            this._mapper = mapper;
            _userService = userService;
            _subscriptionService = subscriptionService;
            _mediator = mediator;
            _securityService = securityService;
            _lookupService = lookupService;
        }

        /// <summary>
        /// Possible Results:
        /// Eligible
        /// InvalidCombination
        /// Blocked
        /// </summary>
        /// <param name="eligibilityRequestDto"></param>
        /// <returns></returns>
        [HttpPost("eligibility/card-details")]
        public async Task<ActionResult<MobileApplicationEligibilityResponseDto>> GetCardDetailsEligibility(EligibilityRequestDto eligibilityRequestDto)
        {
            var commandInput = new CheckCardAndCvc2EligibilityCommand()
            {
                CardNumber = eligibilityRequestDto.CardNumber,
                Cvc2 = eligibilityRequestDto.Cvc2
            };

            var eligibilityResult = await this._userCommandHandler.Handle(commandInput);

            var eligibility = eligibilityResult.Data;

            return Ok(_mapper.Map<MobileApplicationEligibilityResponseDto>(eligibility));
        }

        /// <summary>
        /// Possible Results:
        /// Eligible
        /// AlreadyRegistered
        /// InvalidPhoneNumber
        /// MatchesUserPhone (Only when updating phone number with KYC API)
        /// </summary>
        /// <param name="eligibilityRequestDto"></param>
        /// <returns></returns>
        [HttpPost("eligibility/phone-number")]
        public async Task<ActionResult<MobileAppEligibilityResponseDto>> GetPhoneNumberEligibility(EligibilityRequestDto eligibilityRequestDto)
        {
            var commandInput = new CheckPhoneNumberEligbilityCommand()
            {
                PhoneNumber = eligibilityRequestDto.PhoneNumber,
                CheckIdentity = false
            };

            var eligibilityResult = await this._userCommandHandler.Handle(commandInput);

            var eligibility = eligibilityResult.Data;

            return Ok(new MobileAppEligibilityResponseDto(eligibility.ToString()));
        }

        [HttpPost("V3")]
        public async Task<ActionResult> RegisterUserV3(RegisterUserRequest registerUserRequestDto)
        {
            if (await _securityService.IsUserOTPAuthorized() == false)
                return Unauthorized();

            var commandInput = new RegisterUserCommand()
            {
                CardNumber = registerUserRequestDto.CardNumber,
                DeviceToken = registerUserRequestDto.DeviceToken,
                Password = registerUserRequestDto.Password,
                PhoneNumber = registerUserRequestDto.PhoneNumber,
                Version = BaseEnums.Version.V2,
                CVV = registerUserRequestDto.CVV
            };

            if (registerUserRequestDto.SecurityAnswers != null && registerUserRequestDto.SecurityAnswers.Count == 2)
            {
                commandInput.SecretAnswers = new List<SecretAnswer>()
                {
                    new SecretAnswer()
                    {
                        Answer = registerUserRequestDto.SecurityAnswers.First().SecurityAnswer,
                        SecurityQuestionId = registerUserRequestDto.SecurityAnswers.First().SecurityQuestionId
                    },
                    new SecretAnswer()
                    {
                        Answer = registerUserRequestDto.SecurityAnswers.Last().SecurityAnswer,
                        SecurityQuestionId = registerUserRequestDto.SecurityAnswers.Last().SecurityQuestionId
                    }
                };
            }

            var registrationResult = await this._userCommandHandler.Handle(commandInput);
            if (registrationResult.IsSuccessful == false)
                return BadRequest(registrationResult.ErrorMessage);

            var tryGetUser = await this._userCommandHandler.Handle(new GetUserProfileCommand()
            {
                Id = registrationResult.Data.Id
            });

            if (tryGetUser.IsSuccessful == false)
                return BadRequest(tryGetUser.ErrorMessage);

            var user = tryGetUser.Data;
            var password = commandInput.Password.ToLower();

            var tryResetPassword = await this._userCommandHandler.Handle(new UpdateUserPasswordCommand()
            {
                UserId = user.Id,
                Password = password
            });

            if (tryResetPassword.IsSuccessful == false)
                return BadRequest(tryResetPassword.ErrorMessage);

            if (user != null && !string.IsNullOrWhiteSpace(user.PhoneNumber)
                && !string.IsNullOrWhiteSpace(registerUserRequestDto.UniqueDeviceId) && !string.IsNullOrWhiteSpace(registerUserRequestDto.Model))
            {
                await _userService.UpdateUserDevice(user.PhoneNumber, registerUserRequestDto.Model, registerUserRequestDto.UniqueDeviceId, default);
            }

            return Created(string.Empty, this._mapper.Map<UserDto>(user));
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="updateUserRequest"></param>
        /// <returns></returns>
        [HttpPut("email")]
        public async Task<ActionResult> UpdateEmail(UpdateUserRequestDto updateUserRequest)
        {
            if (await _securityService.IsUserOTPAuthorized() == false)
            {
                return Unauthorized();
            }

            var updateEmailResult = await this._userCommandHandler.Handle(new UpdateUserEmailCommand()
            {
                UserId = updateUserRequest.UserId,
                Email = updateUserRequest.Email,
                EmailConsent = updateUserRequest.EmailConsent
            });

            if (!updateEmailResult.IsSuccessful)
            {
                return BadRequest(updateEmailResult.ErrorMessage);
            }

            return Ok();

        }
        #region Identifications
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="saveIdentificationDocumentsDto"></param>
        /// <returns></returns>
        [HttpPost("identification/{userId}/save")]
        public async Task<ActionResult> SaveIdentificationDocuments(Guid userId, SaveIdentificationDocumentsDto saveIdentificationDocumentsDto)
        {
            if (await _securityService.IsUserOTPAuthorized() == false)
            {
                return this.Unauthorized();
            }

            if (Enum.TryParse(saveIdentificationDocumentsDto.DocumentType, true, out Enums.IdentificationType type) == false)
            {
                return BadRequest(ConstantParam.InvalidDocumentType);
            }

            if (type == Enums.IdentificationType.EmiratesId && string.IsNullOrEmpty(saveIdentificationDocumentsDto.BackScanFileName))
            {
                return BadRequest(string.Format(ConstantParam.DocumentNotFound, nameof(saveIdentificationDocumentsDto.BackScanFileName)));
            }

            var commandInput = new SaveIdentificationDocumentsCommand()
            {
                UserId = userId,
                FrontScanFileName = saveIdentificationDocumentsDto.FrontScanFileName,
                BackScanFileName = saveIdentificationDocumentsDto.BackScanFileName,
                SelfieFileName = saveIdentificationDocumentsDto.SelfieFileName,
                IdentificationType = type
            };

            var handlerResult = await this._identificationCommandHandler.Handle(commandInput);

            if (handlerResult.IsSuccessful == false)
            {
                return BadRequest(handlerResult.ErrorMessage);
            }

            return Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="uploadAndCheckQualityRequestDto"></param>
        /// <returns></returns>
        [HttpPost("identification/{userId}/image-quality")]
        public async Task<ActionResult<UploadAndCheckQualityResponseDto>> UploadAndCheckIdentificationQuality(Guid userId, UploadAndCheckQualityRequestDto uploadAndCheckQualityRequestDto)
        {
            if (await _securityService.IsUserOTPAuthorized() == false)
            {
                return Unauthorized();
            }

            if (Enum.TryParse(uploadAndCheckQualityRequestDto.DocumentType, true, out Enums.DocumentType documentType) == false)
            {
                return BadRequest(ConstantParam.InvalidDocumentType);
            }

            var uploadAndQualityResult = await this._identificationCommandHandler.Handle(new UploadAndCheckQualityCommand()
            {
                DocumentName = uploadAndCheckQualityRequestDto.DocumentName,
                DocumentType = documentType,
                ImageBase64 = uploadAndCheckQualityRequestDto.ImageBase64,
                UserId = userId
            });

            if (uploadAndQualityResult.IsSuccessful == false)
            {
                return BadRequest(uploadAndQualityResult.ErrorMessage);
            }

            var isGoodQuality = uploadAndQualityResult.Data;

            return Ok(new UploadAndCheckQualityResponseDto()
            {
                IsGoodQuality = isGoodQuality,
                FileName = isGoodQuality ? uploadAndCheckQualityRequestDto.DocumentName : default
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="updateIdentificationDto"></param>
        /// <returns></returns>
        [HttpPut("identification")]
        public async Task<ActionResult> UpdateIdentification(UpdateIdentificationDto updateIdentificationDto)
        {
            var isAuthorized = await _securityService.IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            if (Enum.TryParse(updateIdentificationDto.IdentificationType, true, out Enums.IdentificationType identificationType) == false)
            {
                return BadRequest(ConstantParam.InvalidDocumentType);
            }

            switch (identificationType)
            {
                case Enums.IdentificationType.EmiratesId:
                    if (string.IsNullOrEmpty(updateIdentificationDto.BackScanFileName))
                    {
                        return BadRequest(string.Format(ConstantParam.DocumentNotFound, nameof(updateIdentificationDto.BackScanFileName)));
                    }

                    if (string.IsNullOrEmpty(updateIdentificationDto.DocumentNumber))
                    {
                        return BadRequest(ConstantParam.MissingIdNumber);
                    }
                    break;
                case Enums.IdentificationType.Passport:
                    if (string.IsNullOrEmpty(updateIdentificationDto.DocumentNumber))
                    {
                        return BadRequest(ConstantParam.MissingPassportNumber);
                    }
                    break;
                default:
                    break;
            }

            var identification = _mapper.Map<Identification>(updateIdentificationDto);

            identification.Nationality = updateIdentificationDto.NationalityCode;

            identification.Type = identificationType;

            var updateEmiratesIdResult = await this._identificationCommandHandler.Handle(new AddIdentificationCommand()
            {
                Identification = identification
            });

            if (updateEmiratesIdResult.IsSuccessful == false)
            {
                return BadRequest(updateEmiratesIdResult.ErrorMessage);
            }

            return Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="identificationType"></param>
        /// <param name="frontScanFileName"></param>
        /// <param name="backScanFileName"></param>
        /// <returns></returns>
        [HttpGet("identification/{userId}/data-extraction/{identificationType}/{frontScanFileName}")]
        public async Task<ActionResult<DataExtractionDto>> ReadIdentificationDocument(Guid userId, string identificationType, string frontScanFileName, string backScanFileName)
        {
            if (await _securityService.IsUserOTPAuthorized() == false)
            {
                return Unauthorized();
            }

            if (Enum.TryParse(identificationType, true, out Enums.IdentificationType type) == false)
            {
                return BadRequest(ConstantParam.InvalidDocumentType);
            }

            if (type == Enums.IdentificationType.EmiratesId && string.IsNullOrEmpty(backScanFileName))
            {
                return BadRequest(string.Format(ConstantParam.DocumentNotFound, nameof(backScanFileName)));
            }

            var commandInput = new ReadIdentificationDocumentCommand()
            {
                UserId = userId,
                FrontScanFileName = frontScanFileName,
                BackScanFileName = backScanFileName,
                IdentificationType = type
            };

            var tryExtractData = await this._identificationCommandHandler.Handle(commandInput);

            if (tryExtractData.IsSuccessful == false)
            {
                return BadRequest(tryExtractData.ErrorMessage);
            }

            var dataExtracted = this._mapper.Map<DataExtractionDto>(tryExtractData.Data);
            dataExtracted.IdentificationType = type;

            return this.Ok(dataExtracted);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="frontScanFileName"></param>
        /// <param name="selfieFileName"></param>
        /// <returns></returns>
        [HttpGet("identification/{userId}/face-match/{frontScanFileName}/{selfieFileName}")]
        public async Task<ActionResult<FaceMatchResponseDto>> IdentificationFaceMatch(Guid userId, string frontScanFileName, string selfieFileName)
        {
            if (await _securityService.IsUserOTPAuthorized() == false)
            {
                return this.Unauthorized();
            }

            var faceMatchResult = await this._identificationCommandHandler.Handle(new CheckFaceMatchCommand()
            {
                UserId = userId,
                FrontScanFileName = frontScanFileName,
                SelfieFileName = selfieFileName
            });

            if (!faceMatchResult.IsSuccessful)
            {
                return BadRequest(faceMatchResult.ErrorMessage);
            }

            var faceMatch = faceMatchResult.Data;

            var mappedFaceMatch = this._mapper.Map<FaceMatchResponseDto>(faceMatch);

            return this.Ok(mappedFaceMatch);
        }
        #endregion
        /// <summary>
        /// Set the Subscription for the user
        /// </summary>
        /// <returns></returns>
        [HttpGet("{userId}/subscribe/{subscriptionId}")]
        public async Task<ActionResult> Subscribe(Guid userId, Guid subscriptionId)
        {
            if (await _securityService.IsUserOTPAuthorized() == false)
            {
                return Unauthorized();
            }

            // If the user is a C3Pay+ subscriber, do not allow them to subscribe to any other subscription.
            var isC3PayPlusSubscriber = await this._mediator.Send(new GetC3PayPlusMembershipUserDetailsQuery()
            {
                UserId = userId
            });

            if (isC3PayPlusSubscriber.IsFailure)
            {
                return this.BadRequest(isC3PayPlusSubscriber.Error.Code);
            }

            var subscriptionResult = await this._subscriptionService.GetSubscripitonsById(subscriptionId);

            if (!subscriptionResult.IsSuccessful)
            {
                return this.BadRequest(subscriptionResult.ErrorMessage);
            }

            var subscription = subscriptionResult.Data;

            if (subscription.Code == BaseEnums.SMSSubscriptionType.BE.ToString())
            {
                var subscribeResult = await this._subscriptionService.Subscribe(userId, subscriptionId, null, true);

                if (!subscribeResult.IsSuccessful)
                {
                    return BadRequest(subscribeResult.ErrorMessage);
                }

                return this.Ok();
            }
            else
            {
                var userResult = await this._userService.GetUserById(userId);

                if (!userResult.IsSuccessful)
                {
                    return this.BadRequest(userResult.ErrorMessage);
                }

                var user = userResult.Data;

                var subscribeResult = await this._esmoWebService.SubscribeToSMSNotification(new SubscribeToSMSNotificationRequestDto()
                {
                    CardSerialNumber = user.CardHolder.CardSerialNumber,
                    CorporateId = int.Parse(user.CardHolder.CorporateId),
                    NotificationType = subscription.Code,
                    PhoneNumber = user.PhoneNumber
                });

                if (!subscribeResult.IsSuccessful)
                {
                    return this.BadRequest(subscribeResult.ErrorMessage);
                }

                return this.Ok();
            }
        }

        #region Depricated
        /// <summary>
        /// 
        /// </summary>
        /// <param name="updateEmiratesIdRequest"></param>
        /// <returns></returns>
        [HttpPut("emirates-id")]
        public async Task<ActionResult> UpdateEmiratesId(UpdateIdentificationRequestDto updateEmiratesIdRequest)
        {
            var isAuthorized = await this._securityService.IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return Unauthorized();
            }

            var mappedIdentification = _mapper.Map<Identification>(updateEmiratesIdRequest);

            mappedIdentification.Nationality = updateEmiratesIdRequest.NationalityCode;

            mappedIdentification.Type = Enums.IdentificationType.EmiratesId;

            var updateEmiratesIdResult = await this._identificationCommandHandler.Handle(new AddIdentificationCommand()
            {
                Identification = mappedIdentification
            });


            if (!updateEmiratesIdResult.IsSuccessful)
            {
                return BadRequest(updateEmiratesIdResult.ErrorMessage);
            }

            return Ok();
        }

        /// <summary>
        /// Possible Results:
        /// Ok
        /// BlockedTooManyFailedAttempts
        /// MissingCredentials
        /// InvalidCard
        /// IncorrectCvc2
        /// Error
        /// </summary>
        /// <param name="cardSerialNumber"></param>
        /// <param name="cvc2"></param>
        /// <returns></returns>
        [HttpPost("card/verify/{cardSerialNumber}/{cvc2}")]
        public async Task<ActionResult<CardDetailsDto>> VerifyCvv2(string cardSerialNumber, string cvc2)
        {
            // Validate input.
            if (string.IsNullOrEmpty(cardSerialNumber) || string.IsNullOrEmpty(cvc2))
            {
                return BadRequest(CardDetailsResults.MissingCredentials.ToString());
            }

            var isCvc2Valid = await this._esmoWebService.VerifyCvc2(new VerifyCvc2Request()
            {
                CardSerialNumber = cardSerialNumber,
                Cvc2 = cvc2
            });

            // Validate CVC2
            if (isCvc2Valid.IsSuccessful == false)
            {
                Enum.TryParse(isCvc2Valid.ErrorMessage, out Enums.AtmPinErrors atmPinError);
                switch (atmPinError)
                {
                    case Enums.AtmPinErrors.MAX_PIN_TRIES_EXCEEDED:
                        await this._cardHolderService.BlockCard(cardSerialNumber);
                        return BadRequest(CardDetailsResults.BlockedTooManyFailedAttempts.ToString());
                    case Enums.AtmPinErrors.INVALID_CVC2:
                        return BadRequest(CardDetailsResults.IncorrectCvc2.ToString());
                    case Enums.AtmPinErrors.GENERAL_ERROR:
                    default:
                        return BadRequest(CardDetailsResults.Error.ToString());
                }
            }


            var result = new CardDetailsDto()
            {
                Result = EnumUtility.GetDescriptionFromEnumValue(CardDetailsResults.Ok),
            };

            return Ok(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("emirates-id/{userId}/image-quality")]
        public async Task<ActionResult<UploadAndCheckQualityResponseDto>> UploadAndCheckQuality(Guid userId, UploadAndCheckQualityRequestDto request)
        {
            if (await this._securityService.IsUserOTPAuthorized() == false)
            {
                return this.Unauthorized();
            }

            if (Enum.TryParse(request.DocumentType, true, out Enums.DocumentType documentType) == false)
            {
                return BadRequest(ConstantParam.InvalidDocumentType);
            }

            var uploadAndQualityResult = await this._identificationCommandHandler.Handle(new UploadAndCheckQualityCommand()
            {
                DocumentName = request.DocumentName,
                DocumentType = documentType,
                ImageBase64 = request.ImageBase64,
                UserId = userId
            });

            if (!uploadAndQualityResult.IsSuccessful)
            {
                return BadRequest(uploadAndQualityResult.ErrorMessage);
            }

            var isGoodQuality = uploadAndQualityResult.Data;

            return this.Ok(new UploadAndCheckQualityResponseDto()
            {
                IsGoodQuality = isGoodQuality,
                FileName = isGoodQuality ? request.DocumentName : default
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="frontScanFileName"></param>
        /// <param name="backScanFileName"></param>
        /// <returns></returns>
        [HttpGet("emirates-id/{userId}/data-extraction/{frontScanFileName}/{backScanFileName}")]
        public async Task<ActionResult<IdentificationDto>> ReadEmiratesId(Guid userId, string frontScanFileName, string backScanFileName)
        {
            if (await this._securityService.IsUserOTPAuthorized() == false)
            {
                return this.Unauthorized();
            }

            var readEmiratesIdResult = await this._identificationCommandHandler.Handle(new ReadIdentificationDocumentCommand()
            {
                UserId = userId,
                FrontScanFileName = frontScanFileName,
                BackScanFileName = backScanFileName,
                IdentificationType = Enums.IdentificationType.EmiratesId
            });

            if (!readEmiratesIdResult.IsSuccessful)
            {
                return BadRequest(readEmiratesIdResult.ErrorMessage);
            }

            var emiratesId = readEmiratesIdResult.Data;

            var mappedEmiratesId = this._mapper.Map<IdentificationDto>(emiratesId);

            return this.Ok(mappedEmiratesId);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="frontScanFileName"></param>
        /// <param name="selfieFileName"></param>
        /// <returns></returns>
        [HttpGet("emirates-id/{userId}/face-match/{frontScanFileName}/{selfieFileName}")]
        public async Task<ActionResult<Resources.FaceMatchResponseDto>> FaceMatch(Guid userId, string frontScanFileName, string selfieFileName)
        {
            if (await this._securityService.IsUserOTPAuthorized() == false)
            {
                return Unauthorized();
            }

            var faceMatchResult = await this._identificationCommandHandler.Handle(new CheckFaceMatchCommand()
            {
                UserId = userId,
                FrontScanFileName = frontScanFileName,
                SelfieFileName = selfieFileName
            });

            if (!faceMatchResult.IsSuccessful)
            {
                return BadRequest(faceMatchResult.ErrorMessage);
            }

            var faceMatch = faceMatchResult.Data;

            var mappedFaceMatch = this._mapper.Map<Resources.FaceMatchResponseDto>(faceMatch);

            return this.Ok(mappedFaceMatch);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="saveIdentificationDocumentsDto"></param>
        /// <returns></returns>
        [HttpPost("emirates-id/{userId}/save")]
        public async Task<ActionResult> SaveEmiratesIdDocuments(Guid userId, SaveIdentificationDocumentsDto saveIdentificationDocumentsDto)
        {
            if (await this._securityService.IsUserOTPAuthorized() == false)
            {
                return Unauthorized();
            }

            var commandInput = new SaveIdentificationDocumentsCommand()
            {
                UserId = userId,
                FrontScanFileName = saveIdentificationDocumentsDto.FrontScanFileName,
                BackScanFileName = saveIdentificationDocumentsDto.BackScanFileName,
                SelfieFileName = saveIdentificationDocumentsDto.SelfieFileName
            };

            var handlerResult = await this._identificationCommandHandler.Handle(commandInput);

            if (handlerResult.IsSuccessful == false)
            {
                return BadRequest(handlerResult.ErrorMessage);
            }

            return Ok();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="registerUserRequestDto"></param>
        /// <returns></returns>
        [HttpPost("V2")]
        public async Task<ActionResult> RegisterUserV2(RegisterUserRequestDto registerUserRequestDto)
        {
            if (await this._securityService.IsUserOTPAuthorized() == false)
            {
                return Unauthorized();
            }

            var commandInput = new RegisterUserCommand()
            {
                CardNumber = registerUserRequestDto.CardNumber,
                DeviceToken = registerUserRequestDto.DeviceToken,
                Password = registerUserRequestDto.Password,
                PhoneNumber = registerUserRequestDto.PhoneNumber,
                Version = BaseEnums.Version.V2,
                CVV = registerUserRequestDto.CVV
            };

            if (registerUserRequestDto.SecurityAnswers != null && registerUserRequestDto.SecurityAnswers.Count == 2)
            {
                commandInput.SecretAnswers = new List<SecretAnswer>()
                {
                    new SecretAnswer()
                    {
                        Answer = registerUserRequestDto.SecurityAnswers.First().SecurityAnswer,
                        SecurityQuestionId = registerUserRequestDto.SecurityAnswers.First().SecurityQuestionId
                    },
                    new SecretAnswer()
                    {
                        Answer = registerUserRequestDto.SecurityAnswers.Last().SecurityAnswer,
                        SecurityQuestionId = registerUserRequestDto.SecurityAnswers.Last().SecurityQuestionId
                    }
                };
            }

            var registrationResult = await this._userCommandHandler.Handle(commandInput);

            if (registrationResult.IsSuccessful == false)
            {
                return BadRequest(registrationResult.ErrorMessage);
            }

            var tryGetUser = await this._userCommandHandler.Handle(new GetUserProfileCommand()
            {
                Id = registrationResult.Data.Id
            });

            if (tryGetUser.IsSuccessful == false)
            {
                return BadRequest(tryGetUser.ErrorMessage);
            }

            var user = tryGetUser.Data;
            var password = commandInput.Password.ToLower();

            var tryResetPassword = await this._userCommandHandler.Handle(new UpdateUserPasswordCommand()
            {
                UserId = user.Id,
                Password = password
            });

            if (tryResetPassword.IsSuccessful == false)
            {
                return BadRequest(tryResetPassword.ErrorMessage);
            }

            return Created(string.Empty, this._mapper.Map<UserDto>(user));
        }
        /// <summary>
        /// Possible Results:
        /// Eligible
        /// InEligible
        /// AlreadyRegistered
        /// InvalidCard
        /// Blocked 
        /// </summary>
        /// <param name="eligibilityRequestDto"></param>
        /// <returns></returns>
        [HttpPost("eligibility/card")]
        public async Task<ActionResult<MobileApplicationEligibilityResponseDto>> GetCardNumberEligibility(EligibilityRequestDto eligibilityRequestDto)
        {
            var commandInput = new CheckCardNumberEligibilityCommand()
            {
                CardNumber = eligibilityRequestDto.CardNumber
            };

            var eligibilityResult = await this._userCommandHandler.Handle(commandInput);

            var eligibility = eligibilityResult.Data;

            return Ok(_mapper.Map<MobileApplicationEligibilityResponseDto>(eligibility));
        }

        #endregion


        #region International Phone Number Sign Up

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("countries")]
        public async Task<ActionResult<IList<SignUpCountryDto>>> GetSignUpCountries()
        {
            var tryGetSignUpCountries = await _lookupService.GetSignUpCountries();

            if (tryGetSignUpCountries.IsSuccessful == false)
            {
                return Problem(tryGetSignUpCountries.ErrorMessage);
            }

            return Ok(tryGetSignUpCountries.Data);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("v2/eligibility/phone-number")]
        public async Task<ActionResult<MobileAppEligibilityResponseDto>> GetPhoneNumberEligibilityV2(CheckPhoneNumberEligbilityDtoV2 request)
        {
            var commandInput = new CheckPhoneNumberEligbilityCommandV2()
            {
                PhoneNumber = request.PhoneNumber,
                CheckIdentity = false
            };

            var eligibilityResult = await this._userCommandHandler.Handle(commandInput);

            var eligibility = eligibilityResult.Data;

            return Ok(new MobileAppEligibilityResponseDto(eligibility.ToString()));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="registerUserRequestDto"></param>
        /// <returns></returns>
        [HttpPost("v4")]
        [Idempotency("signuprequests","signup")]
        public async Task<ActionResult> RegisterUserV4(RegisterUserRequest registerUserRequestDto)
        {
            // Check if the user is OTP verified.
            if (await _securityService.IsUserOTPAuthorizedV2() == false)
                return Unauthorized();

            // Prepare the input for user registration
            var commandInput = new RegisterUserCommandV2
            {
                CardNumber = registerUserRequestDto.CardNumber,
                DeviceToken = registerUserRequestDto.DeviceToken,
                Password = registerUserRequestDto.Password,
                PhoneNumber = registerUserRequestDto.PhoneNumber,
                Version = BaseEnums.Version.V2,
                CVV = registerUserRequestDto.CVV
            };

            // Map security questions and answers if exactly two are provided
            if (registerUserRequestDto.SecurityAnswers != null && registerUserRequestDto.SecurityAnswers.Count == 2)
            {
                commandInput.SecretAnswers = [
                    new SecretAnswer
                    {
                        Answer = registerUserRequestDto.SecurityAnswers.First().SecurityAnswer,
                        SecurityQuestionId = registerUserRequestDto.SecurityAnswers.First().SecurityQuestionId
                    },
                    new SecretAnswer
                    {
                        Answer = registerUserRequestDto.SecurityAnswers.Last().SecurityAnswer,
                        SecurityQuestionId = registerUserRequestDto.SecurityAnswers.Last().SecurityQuestionId
                    }];
            }

            // Execute the user registration command
            var registrationResult = await _userCommandHandler.Handle(commandInput);

            if (!registrationResult.IsSuccessful)
                return BadRequest(registrationResult.ErrorMessage);

            // Retrieve the newly created user profile
            var tryGetUser = await _userCommandHandler.Handle(new GetUserProfileCommand
            {
                Id = registrationResult.Data.Id
            });

            if (!tryGetUser.IsSuccessful)
                return BadRequest(tryGetUser.ErrorMessage);

            var user = tryGetUser.Data;

            // Convert password to lowercase for normalization
            var password = commandInput.Password.ToLower();

            // Update the password to the normalized version
            var tryResetPassword = await _userCommandHandler.Handle(new UpdateUserPasswordCommand
            {
                UserId = user.Id,
                Password = password
            });

            if (!tryResetPassword.IsSuccessful)
                return BadRequest(tryResetPassword.ErrorMessage);

            // Update the user device details if all required fields are present
            if (user != null &&
                !string.IsNullOrWhiteSpace(user.PhoneNumber) &&
                !string.IsNullOrWhiteSpace(registerUserRequestDto.UniqueDeviceId) &&
                !string.IsNullOrWhiteSpace(registerUserRequestDto.Model))
            {
                await _userService.UpdateUserDevice(
                    user.PhoneNumber,
                    registerUserRequestDto.Model,
                    registerUserRequestDto.UniqueDeviceId,
                    default
                );
            }

            // Return the created user profile as a response
            return Created(string.Empty, _mapper.Map<UserDto>(user));

        }
        #endregion

    }
}