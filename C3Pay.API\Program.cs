using Microsoft.AspNetCore.Hosting;
using Microsoft.Azure.KeyVault;
using Microsoft.Azure.Services.AppAuthentication;
using Microsoft.Data.SqlClient;
using Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.AzureKeyVault;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;
using System.Collections.Generic;
using Azure.Identity;

namespace C3Pay.API
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .Build();

            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .CreateLogger();

            try
            {
                Log.Information("Application starting up...");
                CreateHostBuilder(args).Build().Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Application failed to start");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            return Host.CreateDefaultBuilder(args)
                       .ConfigureAppConfiguration((context, config) =>
                       {
                           var root = config.Build();

                           if (!string.IsNullOrEmpty(root.GetConnectionString("AzureAppConfig")))
                           {
                               config.AddAzureAppConfiguration(Options => Options.Connect(root.GetConnectionString("AzureAppConfig")).UseFeatureFlags());
                           }

                           if (!string.IsNullOrEmpty(root["KeyVault:Authority"]))
                           {
                               var azureServiceTokenProvider = new AzureServiceTokenProvider();

                               var keyVaultClient =
                               new KeyVaultClient
                               (new KeyVaultClient.AuthenticationCallback(azureServiceTokenProvider.KeyVaultTokenCallback));

                               config.AddAzureKeyVault(root["KeyVault:Authority"], keyVaultClient, new DefaultKeyVaultSecretManager());

                               
                               var credential = new DefaultAzureCredential();

                               // Create the SqlColumnEncryptionAzureKeyVaultProvider
                               var sqlColumnEncryptionAzureKeyVaultProvider = new SqlColumnEncryptionAzureKeyVaultProvider(credential);

// Register the provider
                               SqlConnection.RegisterColumnEncryptionKeyStoreProviders(
                                   customProviders: new Dictionary<string, SqlColumnEncryptionKeyStoreProvider>(
                                       capacity: 1,
                                       comparer: StringComparer.OrdinalIgnoreCase)
                                   {
                                       [SqlColumnEncryptionAzureKeyVaultProvider.ProviderName] = sqlColumnEncryptionAzureKeyVaultProvider
                                   });

                               // var sqlColumnEncryptionAzureKeyVaultProvider =
                               // new SqlColumnEncryptionAzureKeyVaultProvider
                               // (new KeyVaultClient.AuthenticationCallback(azureServiceTokenProvider.KeyVaultTokenCallback));
                               //
                               // SqlConnection.RegisterColumnEncryptionKeyStoreProviders(customProviders: new Dictionary<string, SqlColumnEncryptionKeyStoreProvider>(capacity: 1, comparer: StringComparer.OrdinalIgnoreCase)
                               //  {
                               //      {
                               //          SqlColumnEncryptionAzureKeyVaultProvider.ProviderName, sqlColumnEncryptionAzureKeyVaultProvider
                               //      }
                               //  });
                           }
                       })
             .UseSerilog()
             .ConfigureWebHostDefaults(webBuilder =>
             {
                 webBuilder.UseStartup<Startup>();
             });
        }
    }
}
