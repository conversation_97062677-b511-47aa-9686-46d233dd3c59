﻿namespace C3Pay.Core
{
    public static class ConstantParam
    {
        #region Response Message
        public const string BogusUsersCreated = "Bogus Users created successfully";
        public const string UserAlreadyRegistered = "UserAlreadyRegistered";
        public const string UserAlreadyExists = "User already exists";
        public const string InvalidDocumentType = "Invalid Document Type";
        public const string DocumentSizeLimitExceeded = "Document Limit Size Exceeded, Size: {0} KB, Limit: {1} KB";
        public const string PoorQuality = "Front scan image is of poor quality";
        public const string InvalidOneTimePasswordReason = "Invalid One Time Password Reason";
        public const string DocumentNotFound = "Document: {0} Was Not Found";
        public const string ContainerNotFound = "Container Was Not Found";
        public const string UnableToConnectToEIDService = "Unable to Connect to Emirates Id Service";
        public const string UnableToConnectToEdenredService = "Unable to Connect to Edenred Service";
        public const string UnableToConnectToPPSWebAuthService = "Unable to Connect to PPS Web Auth Service";
        public const string FileUploadFailed = "Unable to Upload File: {0}";
        public const string Invalid = "Invalid";
        public const string Verified = "Verified";
        public const string DefaultCurrency = "AED";
        public const string DefaultCountryCode = "AE";
        public const string DefaultLanguageCode = "en";

        #region Benefit Names
        public const string VpnBenefitName = "VPN";
        #endregion

        #region Benefit IDs
        public const int VpnBenefitId = 1; // TODO: Replace with actual VPN benefit ID from database
        #endregion
        public const string InEligiblePhoneNumber = "Ineligible Phone Number";
        public const string InEligibleCard = "Ineligible Card";
        public const string CardHolderNotFound = "Card Holder Not Found. Citizen Id: {0}";
        public const string CardHolderNotFoundByCardNumber = "Card Holder Not Found. Card Number: {0}";
        public const string CardHolderNotFoundByCitizenId = "Card Holder Not Found. Citizen Id: {0}";
        public const string CardHolderNotFoundBySerialNumber = "Card Holder Not Found. Serial Number: {0}";
        public const string CardHolderNotFoundByEmiratesId = "Card Holder Not Found. Emirates Id: {0}";
        public const string BankListNotFound = "20014-BANKLIST NOT FOUND";
        public const string InvalidUser = "Invalid User";
        public const string UserDeviceExistsAlready = "UserDeviceExistsAlready";
        public const string InvalidDevice = "InvalidDevice";
        public const string ValidDevice = "ValidDevice";
        public const string UserNotFound = "User Not Found. Id: {0}";
        public const string UserNotFoundByExternalId = "User Not Found. External Id: {0}";
        public const string UserNotFoundByPhoneNumber = "User Not Found. Phone Number: {0}";
        public const string UserNotFoundByCitizenId = "User Not Found. Citizen Id: {0}";
        public const string UserNotFoundByCardNumber = "User Not Found. Card Number: {0}";
        public const string UserNotFoundByC3RegistrationId = "User Not Found. C3 Registration Id: {0}";
        public const string UserNotFoundByEmiratesId = "User Not Found. Emirates Id: {0}";
        public const string UserIdentityNotFound = "User Identity Not Found. Username: {0}";
        public const string UserInEligibleForRegistration = "User In Eligible for Registration. Card Number: {0}, PhoneNumber: {1}";
        public const string UserInEligibleForRegistrationByCitizenId = "User In Eligible for Registration. Citizen Id: {0}";
        public const string IdentificationDocumentNotFound = "Emirates Id Update Not Found. Id: {0}";
        public const string UserAlreadyUploadedAPassportIdentification = "User already uploaded a passport identification";
        public const string UserAlreadySubscribed = "User already has an active subscription";
        public const string UserSubscriptionReactivated = "User subscription re-activated";
        public const string UserNotSubscribed = "User is not subscribed to this service";
        public const string UserAlreadyUnsubscribed = "User already unsubscribed from this service";
        public const string UnableToConnectToPPSService = "Unable to connect to PPS service";
        public const string CountryNotFound = "Country not found. Country: {0}";
        public const string CountryHasNoCurrency = "This country has no currency. Country: {0}";
        public const string SubscriptionNotFound = "Subscription Not Found. Id: {0}";
        public const string PopupNotFound = "Popup Not Found. Id: {0}";
        public const string InvalidUserSecurityQuestion = "Invalid User Question";
        public const string UnableToGetCardBalance = "Unable to Get Card Balance";
        public const string InsufficientCardBalance = "InsufficientBalance";
        public const string UnableToDebitUserBalance = "Unable to Debit Card Balance";
        public const string UserBlockedLogin = "UserBlocked";
        public const string UserBlockFromAppNarration = "Blocked by user from app";
        public const string UserUnblockFromAppNarration = "Unblocked by user from app";
        public const string DocumentMissing = "Please upload valid combination of documents";
        public const string IdentificationNotFound = "Identification record not found id:{0}";
        public const string PartnerNotFound = "PartnerNotFound";
        public const string NotAvailable = "N/A";
        public const string NoDataFound = "No Data Found";
        public const string MissingCountry = "Missing Country Name";
        public const string MissingBankName = "Missing Bank Name";
        public const string MissingBankCode = "Missing Bank Code";
        public const string MissingBankBranchName = "Missing Bank Branch Name";
        public const string MissingBankBranchCode = "Missing Bank Branch Code";
        public const string MissingUserId = "Missing UserId";
        public const string MissingBeneficiaryId = "Missing Beneficiary Id";
        public const string MissingTransferMethod = "Missing Transfer Method";
        public const string MissingTransferType = "Missing Transfer Type";
        public const string MissingFirstName = "Missing First Name";
        public const string MissingLastName = "Missing Last Name";
        public const string MissingMiddleName = "Missing Middle Name";
        public const string MissingMoneyTransferPurpose = "Missing Money Transfer Purpose";
        public const string MissingMoneyTransferReasonId = "Missing Money Transfer Reason Id";
        public const string MissingAccountNumber = "Missing Account Number";
        public const string MissingSendAmount = "Missing Send Amount";
        public const string MissingSendAmountCurrency = "Missing Send Amount Currency";
        public const string MissingFeeAmount = "Missing Fee Amount";
        public const string MissingFeeAmountCurrency = "Missing Fee Amount Currency";
        public const string MissingConversionRate = "Missing Conversion Rate";
        public const string MissingTransactionId = "Missing Transaction Id";
        public const string MissingProductCode = "Missing Product Code";
        public const string MissingReceiveAmount = "Missing Receive Amount";
        public const string MissingReceiveCurrency = "Missing Receive Currency";
        public const string MissingToCurrency = "Missing To Currency";
        public const string MissingFromCurrency = "Missing From Currency";
        public const string InvalidToCurrency = "Invalid To Currency";
        public const string MissingAmount = "Missing Amount";
        public const string MissingReferralCode = "Missing Referral Code";
        public const string MissingIdNumber = "Missing Id Number";
        public const string MissingPassportNumber = "Missing Passport";
        public const string MissingIfscCode = "Missing IFSC Code";
        public const string ExistsBeneficiary = "Beneficiary already exists";
        public const string NotExistsReason = "Money Transfer Reason not exists";
        public const string CountryNotSupport = "Country {0} is not supported to add money transfer beneficiary";
        public const string CountryNotSupportForMobilerecharge = "Country {0} is not supported for the mobile recharge type {1}";
        public const string ExceededBeneficiaryCount = "Exceeded the beneficiary count {0}. Please remove and add new beneficiary";
        public const string NotExistsUserId = "User Id not exists";
        public const string NotExistsUser = "User not exists";
        public const string InvalidCardNumber = "Invalid Card Number";
        public const string InvalidCardSerialNumber = "Invalid Card Serial Number";
        public const string NotExistsBankBranchId = "Bank Branch Id not exists";
        public const string NotExistsEmiratesId = "Emirates Id details not found";
        public const string NotExistsCardHolder = "Card Holder information Not Found.";
        public const string EmiratesIdExpired = "Emirates Id Expired";
        public const string NotExistsBeneficiary = "Beneficiary details not found";
        public const string Transactioninprogress = "Due to pending transactions, can not delete the beneficiary {0}";
        public const string NotExistsProductsForBeneficiary = "Product details not found for benenficiary {0} and mobile number {0}";
        public const string RMTProfileNotCreated = "RMT Profile is not created for this user {0} and emirates id {1} ";
        public const string NotExistsProducts = "Product details not found ";
        public const string AccountBlocked = "Your account has been blocked. Please contact our customer service team via WhatsApp on ************";
        public const string EmiratesIdNotPostedToPartnerBankDueExpiry = "Not posted to partner bank due to expiry";
        public const string PassportExpired = "Passport Expired";
        public const string UnableToFetchMTFees = "Unable to fetch fees for Country Code {0}";
        public const string EmiratesIdAlreadyExists = "It looks like the Emirates Id you're trying to upload already exists in our system. Please ensure you are uploading a new Emirates Id";

        public const string MobileRechargeLimitCountExceeded = " limit count {0} reached ";
        public const string MobileRechargeLimitAmountExceeded = " limit amount {0} reached ";
        public const string DefaultMRProductValidity = "Lifetime";
        public const string MobileRechargeAutoRenewalFailure = "Auto Renewal Failure for {0}, Error: {1}";
        public const string MobileRechargePaymentStatus = "MR Payment Status!";
        public const string MobileRechagrePendingRetrial = "User has pending mobile recharge transaction under retrial.";

        public const string InvalidBeneficiaryId = "Invalid Beneficiary Id";
        public const string InvalidUserId = "Invalid User Id";
        public const string InvalidMoneyTransferReasonId = "Invalid Money Transfer Reason Id";
        public const string InvalidTransactionType = "Invalid Transaction Type";
        public const string InvalidTransferType = "Invalid Transfer Type";
        public const string InvalidTransferMethod = "Invalid Transfer Method";
        public const string UnableToConnectToExternalService = "Unable to Connect to external Service";
        public const string NotExistsTransactions = "Transaction details not found";
        public const string TransactionCompleted = "Transaction completed";
        public const string RedRevProcessedAlready = "Redemption reversal processed already";
        public const string InvalidBankCountry = "Invalid Bank Country";
        public const string InvlalidOperator = "Invalid Operator Name";
        public const string InvlalidMobileRechargeTransation = "Invalid Mobile Recharge Transaction {0}";
        public const string MTApprovalBeneficiaryAdded = "Money transfer manual approval benedificary added. UserId: {0}";

        public const string InsufficientBalance = "You don't have sufficient balance to do the transaction. Your current Balance: {0} {1}";
        public const string ActivateYourCard = "Please visit your nearest ATM to activate your card";
        public const string UnBlockYourCard = "Your card has been blocked due to multiple PIN attempts at an ATM. Please contact the call center to unblock it.";

        //Attachment Names
        public const string StatementFileName = "C3PayStatement_{0}_{1}.pdf";
        public const string EmiratesIdGuideFileName = "emirates-id-guide.pdf";

        //Email Subjects
        public const string StatementEmailSubject = "C3Pay Statement: {0} to {1}";
        public const string RMTProfileCreatedEmailSubject = "C3Pay RAK Money Transfer Profile Created";
        public const string RegistrationRejectionEmailSubject = "We Could Not Verify Your Emirates ID ";
        public const string PortalUserCreatedEmailSubject = "C3Pay Portal New User Information";

        //Logs
        public const string UserEmailAdded = "User: {0} email has been added: {1}";

        public const string InvalidTransactionId = "Invalid  Transaction Id";
        public const string InvalidTransaction = "Invalid  Transaction ";
        public const string InvalidMoneyTransferBeneficiary = "Invalid Money Transfer Beneficiary";
        public const string InvalidMoneyTransferBeneficiaryBranch = "Invalid Money Transfer Beneficiary Branch";
        public const string InvalidCardNoOrSerialNumber = "Invalid Card Number : {0} or Serial Number : {1}";
        public const string SkippedUpdatingBeneficiaryNoTransferFound = "Skipped Updating beneficiary since no previous successful transfer was found for the bank";
        public const string SkippedUpdatingBeneficiarySameBranch = "Skipped Updating beneficiary since the branch is the same";
        public const string UpdatingBeneficiary = "Updating external beneficiary...";
        public const string UpdatingBeneficiaryFailed = "Updating external beneficiary failed, {0}";
        public const string SuccessfullyUpdatedBeneficiary = "Successfully updated beneficiary";
        public const string FetchingExternalBeneficiary = "Fetching external beneficiary...";
        public const string FetchingExternalBeneficiaryFailed = "Failed to fetch external beneficiary, {0}";

        public const string InvalidPhoneNumber = "Invalid Phone Number";
        public const string InvalidPhoneNumberForCountry = "Invalid Phone Number {0} for the country {1}";
        public const string InvalidRechargeType = "Invalid Recharge Type";
        public const string NicknameExceedLength = "Beneficiary nick name {0} exceeded the length and it should be less than 20 character’s";
        public const string InvalidMobileRechargeBeneficiary = "Invalid Mobile recharge Beneficiary ";
        public const string InvalidMobileRechargeBeneficiaryForCallingCards = "Invalid Mobile recharge Beneficiary for calling card product {0} ";

        public const string InvlaidProductCode = "Invalid product code {0} ";
        public const string ReferrerCodeNotFound = "Referrer Code not found";
        public const string ReferrerCodeDuplicate = "Referrer Code already Exists";
        public const string TransactionIsInProgress = "Transaction is in-progress";

        public const string MissingBlackList = "Missing Black List";
        public const string CardActivated = "Card Activated : {0}";

        public const string Registered = "Registered";
        public const string NotRegistered = "Not Registered";
        public const string DuplicateBlackList = "Duplicate Black List";
        public const string PendingMoneyTransferProfile = "As the RMT profile was sent recently, it's not possible to remove the user, however you can do this on {0}";
        public const string SanctionScreeningPendingOrSanctioned = "User sanction screening is pending or user is sanctioned. Please contact the compliance team if needed.";
        #endregion

        #region Logging Messages

        //User Service
        public const string CreatingUser = "Creating user. Username: {0}";
        public const string CreatingUserFailed = "Failed to create user. Username: {0}";
        public const string UserCreated = "User created. Username: {0}, UserId: {1}";

        public const string BlockingUser = "Blocking user... UserId: {0}, Blocktype: {1}";
        public const string BlockingUserFailed = "Blocking user failed... UserId: {0}, Blocktype: {1}, Error: {2}";
        public const string UserBlocked = "User blocked... UserId: {0}, Blocktype: {1}";

        public const string UnblockingUser = "Unblocking user... UserId: {0}";
        public const string UnblockingUserFailed = "Unblocking user failed... UserId: {0}, Error: {2}";
        public const string UserUnblocked = "User unblocked... UserId: {0}";

        public const string DeletingAccount = "Deleting account... UserId: {0}";
        public const string DeletingAccountFailed = "Deleting account failed... UserId: {0}, Error: {2}";
        public const string AccountMarkedAsDeleted = "Account marked as deleted... UserId: {0}";
        public const string AccountDeleted = "Account deleted... UserId: {0}";

        public const string ResettingPassword = "Resetting password for account... UserId: {0}";
        public const string ResettingPasswordFailed = "Resetting password for account failed... UserId: {0}, Error: {2}";
        public const string PasswordReset = "Password reset for account... UserId: {0}";

        public const string SendingPassword = "Password reset for account... UserId: {0}";
        public const string SendingPasswordFailed = "Password reset for account... UserId: {0}";
        public const string Passwordsent = "Password reset for account... UserId: {0}";

        public const string AddingIdentificationDocument = "Adding identification document... UserId: {0}, Emirates Id: {1}";
        public const string AddingIdentificationDocumentFailed = "Adding identification document failed... UserId: {0}, Emirates Id: {1}";
        public const string IdentificationDocumentAdded = "Identification document added... UserId: {0}, Emirates Id: {1}";

        public const string UpdatingIdentificationDocument = "Updating identification document... UserId: {0}, Emirates Id: {1}";
        public const string UpdatingIdentificationDocumentFailed = "Updating identification document failed... UserId: {0}, Emirates Id: {1}";
        public const string IdentificationDocumentUpdated = "Identification document updated... UserId: {0}, Emirates Id: {1}";

        public const string ApprovingIdentificationDocument = "Approving identification document... Emirates Id: {0}";
        public const string ApprovingIdentificationDocumentFailed = "Approving identification document failed... Emirates Id: {0}";
        public const string IdentificationDocumentApproved = "Identification document approved... Emirates Id: {0}";

        public const string RejectingIdentificationDocument = "Rejecting identification document... Emirates Id: {0}";
        public const string RejectingIdentificationDocumentFailed = "Rejecting identification document failed... Emirates Id: {0}";
        public const string IdentificationDocumentRejected = "Identification document rejected... Emirates Id: {0}";

        public const string UpdatingUserEmail = "Updating user email. Username: {0}, UserId: {1}, Current Email: {2}";
        public const string UpdatingUserEmailFailed = "Failed to update user email. Username: {0}";
        public const string UserEmailUpdated = "User email updated. Username: {0}, UserId: {1}, New Email: {2}";
        public const string SendingEmailFailed = "Failed to send email";

        public const string UpdatingUserDeviceToken = "Updating user device token. UserId: {0}, Current Device Token: {1}, New Device Token: {2}";
        public const string UpdatingUserDeviceTokenFailed = "Failed to update user device token. UserId: {0}, Current Device Token: {1}, New Device Token: {2}";
        public const string UserDeviceTokenUpdated = "User device token updated. UserId: {0}, Current Device Token: {1}, New Device Token: {2}";

        public const string UpdatingUserSecretAnswers = "Updating user secret answers. UserId: {0}";
        public const string UpdatingUserSecretAnswersFailed = "Failed to update user secret answers. UserId: {0}, Error: {1}";
        public const string UserSecretAnswersUpdated = "User secret answers updated. UserId: {0}";

        public const string SendingTextMessage = "Sending sms failed";
        public const string TextMessageSent = "Sending sms failed";
        public const string SendingTextMessageFailed = "Sending sms failed";

        public const string SendingStatementEmail = "Sending statement email... UserId: {0}, Email: {1}";
        public const string StatementEmailSent = "Successfully sent statement email... UserId: {0}, Email: {1}";
        public const string SendingStatementEmailFailed = "Faild to send statement email... UserId: {0}, Email: {1}";

        public const string SendingRegistrationRejectionEmail = "Sending reject registration email... UserId: {0}, Email: {1}";
        public const string RegistrationRejectionEmailSent = "Successfully sent reject registration  email... UserId: {0}, Email: {1}";
        public const string SendingRegistrationRejectionEmailFailed = "Faild to send reject registration email... UserId: {0}, Error: {1}";

        public const string SendingRMTProfileCreatedEmail = "Sending rmt profile created email... UserId: {0}";
        public const string RMTProfileCreatedEmailSent = "Successfully sent rmt profile created email... UserId: {0}";
        public const string SendingRMTProfileCreatedEmailFailed = "Faild to send rmt profile created email... UserId: {0}, Error: {1}";

        public const string MissingRakFileAlertSent = "Successfully sent Missing Rak File Alert.. PhoneNumber: {0}";
        public const string SendingMissingRakFileAlertFailed = "Faild to send Missing Rak File Alert.. PhoneNumber: {0}, , Error: {1}";

        public const string SendingRMTProfileCreatedSMS = "Sending rmt profile created sms... UserId: {0}";
        public const string RMTProfileCreatedSMSSent = "Successfully sent rmt profile created sms... UserId: {0}";
        public const string SendingRMTProfileCreatedSMSFailed = "Faild to send rmt profile created sms... UserId: {0}";

        public const string SendingMTBeneficiartyCreatedSMS = "Sending money transfer beneficiary created sms... UserId: {0}";
        public const string MTBeneficiartyCreatedSMSSent = "Successfully sent money transfer beneficiary created sms... UserId: {0}";
        public const string SendingMTBeneficiartyCreatedSMSFailed = "Faild to send money transfer beneficiary created sms... UserId: {0} and failed remarks : {1}";
        public const string PendingDirectTransferSMSSent = "Successfully sent pending direct transfer SMS to {0}";
        public const string EHMTBeneficiartyFailed = "Failed to create money transfer beneficiary as the beneficiary is not in the same exchange house";

        public const string BlockuserBeneficiartyCreatedProcess = "Block user process started. UserId: {0} and User name : {1}";
        public const string BlockuserBeneficiartyCreatedSuccess = "Successfully blocked the user. UserId: {0} and User name : {1}";
        public const string BlockuserBeneficiartyCreatedFailed = "Faild to block the user. UserId: {0} and failed remarks: {1}";

        public const string SendingMTBeneficiaryApprovedSMS = "Sending money transfer beneficiary approved sms... UserId: {0}";
        public const string MTBeneficiaryApprovedSMSSent = "Successfully sent money transfer beneficiary approved sms... UserId: {0}";
        public const string SendingMTBeneficiaryApprovedSMSFailed = "Failed to send money transfer beneficiary approved sms... UserId: {0} and failed remarks : {1}";
        public const string SendingPendingDirectTransferSMSFailed = "Failed to send pending direct transfer sms... Phone number: {0} and failed remarks : {1}";

        public const string SendingRMTProfileCreatedPushNotification = "Sending rmt profile created push notification... UserId: {0}";
        public const string RMTProfileCreatedPushNotificationSent = "Successfully sent rmt profile created push notification... UserId: {0}";
        public const string SendingRMTProfileCreatedPushNotificationFailed = "Faild to send rmt profile created push notification... UserId: {0}, Error: {1}";
        public const string SendingRegistrationApprovalPushNotification = "Sending registration approval push notification... UserId: {0}";
        public const string RegistrationApprovedNotificationSent = "Successfully sent registration approval push notification... UserId: {0}";
        public const string SendingRegistrationApprovalPushNotificationFailed = "Faild to send registration approval push notification... UserId: {0}, Error: {1}";
        public const string SendingRegistrationRejectionPushNotification = "Sending registration reject push notification... UserId: {0}";
        public const string RegistrationRejectionPushNotificationSent = "Successfully sent registration reject push notification... UserId: {0}";
        public const string SendingRegistrationRejectionPushNotificationFailed = "Faild to send registration reject push notification... UserId: {0}, Error: {1}";

        public const string InitiatingStatementGeneration = "User statement generation started... UserId: {0}, Start Date: {1}, End Date: {2}, Email: {3}";
        public const string CheckingBalanceForStatement = "Checking user balance for statement... UserId: {0}";
        public const string FailedToGetBalanceForStatement = "Failed to get user balance for statement... UserId: {0}";
        public const string InsufficientBalanceForStatement = "User balance is insufficient for statement... UserId: {0}, Balance: {1} AED";
        public const string DebitingBalanceForStatement = "Debiting user balance for statement... UserId: {0}, Amount: {1} AED";
        public const string FailedToDebitBalanceForStatement = "Failed to debit user balance for statement... UserId: {0}, Amount {1} AED, Reason: {2}";
        public const string ReversingStatementTransaction = "Reversing statement transaction... UserId: {0}, Amount: {1} AED";
        public const string FailedToReverseStatementTransaction = "Failed to reverse statement transaction... UserId: {0}, Amount: {1} AED";
        public const string StatementTransactionReversed = "Statement transaction reversed... UserId: {0}, Amount: {1} AED";
        public const string StatementGeneratedAndSent = "Statement generated and sent... UserId: {0}, Start Date: {1}, End Date: {2}, Email: {3}";

        public const string UserDeviceNotRegistred = "User has no device token";
        public const string UserHasNoEmail = "User has no email";

        public const string UploadingUserIdentificationDocumentToSignzy = "Uploading user {0} document to signzy... User Id: {1}, Document Name: {2}";
        public const string UploadingUserIdentificationDocumentToSignzyFailed = "Uploading user {0} document to signzy failed... User Id: {1}, Document Name: {2}, Error: {3}";
        public const string UserIdentificationDocumentUploadedToSignzy = "User {0} document uploaded to signzy... User Id: {1}, Document Name: {2}, Url: {3}";
        public const string BadGateway = "bad gateway returned from {0}";

        public const string UploadingUserIdentificationDocumentToAzureStorageAccount = "Uploading user {0} document to Azure storage account... User Id: {1}, Document Name: {2}";
        public const string UploadingUserIdentificationDocumentToAzureStorageAccountFailed = "Uploading user {0} document to Azure storage account  failed... User Id: {1}, Document Name: {2}, Error: {3}";
        public const string UserIdentificationDocumentUploadedToAzureStorageAccount = "User {0} document uploaded to Azure storage account... User Id: {1}, Document Name: {2}, Url: {3}";

        public const string CheckingUserIdentificationDocumentQuality = "Checking user {0} document quality... User Id: {1}, Document Name: {2}";
        public const string CheckingUserIdentificationDocumentQualityFailed = "Checking user {0} document quality failed... User Id: {1}, Document Name: {2}, Error: {3}";
        public const string UserIdentificationDocumentQualityChecked = "User {0} document quality checked... User Id: {1}, Document Name: {2}, Is Good Quality: {3}";

        public const string ReadingUserIdentificationDocumentStarted = "Reading user emirates Id started. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Name to Match: {nameToMatch}";
        public const string ReadingUserIdentificationDocumentFailed = "Reading user emirates Id failed. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Name to Match: {nameToMatch}, Error: {error}";
        public const string ReadingUserIdentificationDocumentTimeout = "Reading identification document timeout. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Name to Match: {nameToMatch}, Error: {error}";
        public const string ReadingUserIdentificationContractMappingFailed = "Reading identification document failed due to contract change. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Name to Match: {nameToMatch}, Error: {error}";

        public const string UpdatingEmiratesIdIdentificationDueToIdNo = "Updating emirates id due to incorrect id number. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Cardholder EmiratesId: {cardholderEmiratesId}, IncorrectEIDNo: {incorrectEIDNo}, NewEIDNo: {newEIDNo}";
        public const string UpdatingEmiratesIdIdentificationDueToNationality = "Updating emirates id due to incorrect nationality. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Cardholder Nationality: {cardholderNationality}, IncorrectNationality: {incorrectNationality}, NewNationality: {newNationality}";
        public const string UpdatingEmiratesIdIdentificationDueToDateOfBirth = "Updating emirates id due to incorrect date of birth. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Cardholder DateOfBirth: {cardholderDateOfBirth}, Incorrect DateOfBirth: {incorrectDateOfBirth}, New DateOfBirth: {newDateOfBirth}";
        public const string UpdatingEmiratesIdIdentificationDueToExpiryDate = "Updating emirates id due to incorrect expiry date. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Cardholder ExpiryDate: {cardholderExpiryDate}, IncorrectExpiryDate: {incorrectExpiryDate}, NewExpiryDate: {newExpiryDate}";
        public const string UpdatingPassportIdentificationDueToExpiryDate = "Updating passport due to incorrect expiry date. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, IncorrectExpiryDate: {incorrectExpiryDate}, NewExpiryDate: {newExpiryDate}";
        public const string UpdatingPassportIdentificationDueToDateOfBirth = "Updating passport due to incorrect date of birth. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Cardholder DateOfBirth: {cardholderDateOfBirth}, Incorrect DateOfBirth: {incorrectDateOfBirth}, New DateOfBirth: {newDateOfBirth}";

        public const string UpdatingCardholderEmiratesIdNo = "Updating cardholder's emirates id number. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Cardholder EmiratesId: {cardholderEmiratesId}, New EIDNo: {newEIDNo}";
        public const string UpdatingCardholderNationality = "Updating cardholder's nationality code. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Cardholder Nationality: {cardholderNationality}, New Nationality: {newNationality}";
        public const string UpdatingCardholderDateOfBirth = "Updating cardholder's date of birth. UserId: {userId}, Front Scan Url: {frontScanUrl}, Back Scan Url: {backScanUrl}, Cardholder DateOfBirth: {cardholderDateOfBirth}, New DateOfBirth: {newDateOfBirth}";
        public const string AllowEmiratesIdAssociatedWithAnotherUser = "Allowing EmiratesId to be associated with another user. UserId: {userId}, EmiratesId: {emiratesId}";

        public const string ImageQualityTimeout = "Image quality timeout. UserId: {0}, Error: {1}";
        public const string FaceMatchTimeout = "Face match timeout. UserId: {0}, Front Scan Url: {1}, Back Scan Url: {2}, Error: {3}";
        public const string UserIdentificationDocumentIdRead = "User emirates Id read. UserId: {0}, Front Scan Url: {1}, Back Scan Url: {2}, Name to Match: {3}";
        public const string AzureUserIdentificationDocumentIdRead = "Azure ocr read. UserId: {0}, Front Scan Url: {1}, Back Scan Url: {2}, Name to Match: {3}, Result: {4}";

        public const string CheckingUserFaceMatch = "Checking user face match... User Id: {0}, Front Scan File Name: {1}, Selfie File Name: {2}";
        public const string CheckingUserFaceMatchFailed = "Checking user face match failed... User Id: {0}, Front Scan File Name: {1}, Selfie File Name: {2}, Error: {3}";
        public const string UserFaceMatchChecked = "User face match checked... User Id: {0}, Front Scan File Name: {1}, Selfie File Name: {2}, Face Matched: {3}";
        public const string AzureUserFaceMatchChecked = "User azure face match checked... User Id: {0}, Front Scan File Name: {1}, Selfie File Name: {2}, Face Matched: {3}";

        public const string SavingUserIdentificationDocumentsToStorage = "Saving user documents to storage... User Id: {0}, Documents Names: {1}";
        public const string SavingUserIdentificationDocumentsToStorageFailed = "Saving user document to storage failed... User Id: {0}, Documents Names: {1}, Error: {2}";
        public const string UserIdentificationDocumentsSavedToStorage = "User document saved to storage... User Id: {0}, Documents Names: {1}";

        public const string ReadingUserIdentificationDocumentInvalid = "Couldn't read data from the scanned copy";
        public const string IdentificationDocumentWillbeExpired = "Emirates Id {0} will be Expired within {1} days";
        public const string PassportDocumentWillbeExpired = "Passport {0} will be Expired within {1} days";
        public const string DuplicateIdentificationDocument = "Emirates Id {0} is associated with another user";
        public const string DuplicatePassportDocument = "Passport {0} is associated with another user";

        public const string CheckingDuplicateEmiratesId = "Checking duplicate EmiratesId {0} for User Id: {1}";
        public const string DuplicateEmiratesIdChecked = "Checked duplicate EmiratesId {0} for User Id: {1}";

        public const string DeActivateBlackListStarted = "DeActivate the BlackList {0}";
        public const string DeActivateBlackListFailed = "DeActivate the BlackList {0} , Error {2}";

        public const string UploadingEmiratesIdVerifiedEvent = "Uploading EmiratesIdVerified Event...";
        public const string UploadingEmiratesIdVerifiedEventFailed = "Uploading EmiratesIdVerified Event Failed. Error: {0}";
        public const string EmiratesIdVerifiedEventUploaded = "EmiratesIdVerified Event Uploaded.";

        public const string UploadingEmiratesIdRejectedEvent = "Uploading EmiratesIdRejected Event...";
        public const string UploadingEmiratesIdRejectedEventFailed = "Uploading EmiratesIdRejected Event Failed. Error: {0}";
        public const string EmiratesIdRejectedEventUploaded = "EmiratesIdRejected Event Uploaded.";

        public const string CleverTapProfileNotFound = "Profile not found, Cardholder Id: {0}";

        public const string EvaluatingMoneyTransferEvent = "Evaluating MoneyTransfer Event. Status {0}-{1}, Initial Status: {2}-{3}";
        public const string EvaluatingMoneyTransferEventFinished = "Evaluating MoneyTransfer Event Finished. Status {0}-{1}, Initial Status: {2}-{3}";
        public const string UploadingMoneyTransferEventSkipped = "Uploading MoneyTransfer Event Skipped. Status {0}-{1}, Initial Status: {2}-{3}";

        public const string RewardingUserForReferral = "Rewarding user: {0} for referral";
        public const string UserRewardedForReferral = "Successfully Rewarded user: {0} for referral";
        public const string UserReferralCountAnalyticsUpdate = "Successfully updated user referral count: User: {0}, Count: {1}";
        public const string ReferralRewardFailedDueToInvalidCard = "Referral reward crediting failed for User: {0} due to invalid card. Card Number: {1}, Card Serial Number: {2}";
        public const string ReferralRewardFailedDueToFailedPPSTransaction = "Referral reward crediting failed for User: {0} due to failed pps transaction";
        public const string ReferralRewardFailedDueToUserBeingDeleted = "Referral reward crediting failed for User: {0} due to user being deleted";
        public const string ReferralRewardNotificationFailedDueToInvalidDeviceToken = "Referral reward notification failed for User: {0} due to invalid device token";
        public const string ReferralCodeUsedNotificationFailedDueToInvalidDeviceToken = "Referral code used notification failed for User: {0} due to invalid device token";

        public const string ActivatingReplacementCard = "Activating replacement card... Serial Number: {0}";
        public const string ReplacementCardActivated = "Replacement card activated. Serial Number: {0}";
        public const string ReplacementCardActivationFailed = "Replacement card activation failed. Serial Number: {0}, Reason: {1}";
        public const string ReplacementCardActivationSkipped = "Replacement card activation skipped due to user not being verified. Serial Number: {0}";

        public const string BranchIdUpdateReceived = "Branch Id Update Received... Branch Id: {0}, Employee Count: {1}";
        public const string BranchIdUpdateNCardHoldersFound = "Branch Id Update - found {0}/{1} of those employees registered on the app... Branch Id: {2}";
        public const string CardHolderBranchIdUpdated = "Cardholder branch Id updated. CardHolder Id: {0}, Old Branch Id: {1}, New Branch Id: {2}";
        public const string BranchIdUpdateComplete = "Branch Id Update Complete... Branch Id: {0}, CardHolder Count: {1}";

        public const string DuplicateUser = "Duplicate user";
        public const string FailedToSendPushNotificationDueToNullToken = "Failed to send push notification due to null device token, UserId: {0}";

        //Experiment
        public const string UnableToInsertUserInExperiment = "Unable to add user in experiment for User Id: {userId}, Error message: {errorMessage}";

        //Direct Transfer
        public const string DirectTransferFeePrefix = "F";
        public const string DirectTransferAmountPrefix = "A";
        public const string DebitedFeeInitiator = "Debited fee initiator";
        public const string DebitedAmountInitiator = "Debited amount initiator";
        public const string CrossTransfer = "Cross Transfer from user with id {0} in partner code {1} to user with id {2} in partner code {3}";
        public const string AutoCancelled = "Auto-cancelled";
        public const string DebitedInitiatorAndCreditedReceiver = "Debited initiator and credited receiver";
        public const string DirectTransferClaimed = "Debited initiator and credited receiver (Claimed)";
        public const string FoundNTransfersToClaim = "Found {0} pending transfers for {1}";
        public const string ClaimingTransfer = "Claiming transfer: {0}";
        public const string DirectTransferSenderNotFound = "Sender not found";

        // Bill Payments
        public const string BillPaymentFailedToConnectToPaykii = "Failed to connect with Paykii Service";
        public const string BillPaymentNickNameNotPassed = "NickNameNotPassed";
        public const string BillPaymentProviderNotFound = "ProviderNotFound";
        public const string BillPaymentCategoryNotFound = "CategoryNotFound";
        public const string BillPaymentFieldsNotFound = "FieldsNotFound";
        public const string BillPaymentFieldNotFound = "FieldNotFound";
        public const string BillPaymentFieldValue = "InvalidFieldValue";
        public const string BillPaymentInvalidDueAmount = "BillPaymentInvalidDueAmount";
        public const string BillPaymentFxRateConversionNotFound = "FxRateConversionNotFound";
        public const string BillPaymentBillersSyncingReactivatedStatus = "Bill Providers Synchronize Status : {0}  no of providers reactivated in DB.";
        public const string BillPaymentBillersSyncingRetainedStatus = "Bill Providers Synchronize Status : {0}  no of providers retained in DB.";
        public const string BillPaymentBillersNoNewProviders = "Bill Providers Synchronize Status : No new providers.";
        public const string BillPaymentBillersNewProvidersAdded = "Bill Providers Synchronize Status : {0} no of new providers to added in DB.";
        public const string BillPaymentSameCatalogVersion = "Bill Providers Synchronize Status : No Updates, Catalog version remains the same.";
        public const string BillPaymentAmountDueNotification = "You have a bill due!";
        public const string BillPaymentInvalidDetailsNotification = "Incorrect bill details!";
        public const string BillPaymentNoBillNotification = "No Bills due!";
        public const string BillPaymentProcessedNotification = "Bill Paid!";
        public const string BillPaymentFailedProcessingPayment = " Payment Failed!";
        public const string BillPaymentAmountDueNotificationFailed = "Faild to send registration bill payment amount due push notification... UserId: {0}, Error: {1}";
        public const string BillPaymentAmountDueNotificationSent = "Successfully sent bill payment amount due  push notification... UserId: {0}";
        public const string BillPaymentPaymentNotificationFailed = "Faild to send registration bill payment  transaction  push notification... UserId: {0}, Error: {1}";
        public const string BillPaymentPaymentNotificationSent = "Successfully sent bill payment transaction notification... UserId: {0}";
        public const string BillPaymentInitiatingCardBalanceFetch = "Initiating Card Balance fetch from PPS for user -  {0}";
        public const string BillPaymentFailedToFetchCardBalance = "Failed to fetch card balance from PPS for user -  {0}";
        public const string FailedToDebitBalanceForBillPayment = "Failed to debit user balance for bill payment... UserId: {0}, Amount {1} AED, Reason: {2}";
        public const string FailedToReverseBillPaymentTransaction = "Failed to reverse bill payment transaction... UserId: {0}, Amount: {1} AED";
        public const string BillPaymentFailedIncorrectAmountNotification = "Bill Amount Incorrect";
        public const string BillPaymentFailedBillerNotAvailableNotification = "Biller Unavailable!";
        public const string BillPaymentAmountNotInRangeNotification = "Bill Amount not in allowed range!";
        public const string BillPaymentBillExpiredNotification = "Account expired/invalid!";


        // Bank Search
        public const string BankSearchSyncrhonizationStarted = "Bank search syncrhonization started...";
        public const string BankSearchSyncrhonizationFinished = "Bank search syncrhonization finished";
        public const string BankSearchSyncrhonizationDownloadingFiles = "Downloading new bank search files...";
        public const string BankSearchSyncrhonizationDownloadingFilesComplete = "Downloading new bank search files complete, found {0} files";
        public const string BankSearchSyncrhonizationProcessingFile = "Processing new bank search file, name {0}...";
        public const string BankSearchSyncrhonizationProcessingFileComplete = "Processing new bank search file complete, name {0}";
        public const string BankSearchSyncrhonizationArchivingFiles = "Archiving bank search files...";
        public const string BankSearchSyncrhonizationArchivingFilesCount = "Found {0} bank search files to archive...";
        public const string BankSearchSyncrhonizationArchivingFilesComplete = "Archiving bank search files complete";
        public const string BankSearchSyncrhonizationClearBanks = "Deleting banks from DB...";
        public const string BankSearchSyncrhonizationClearBranches = "Deleting branches from DB...";
        public const string EHBankSearchSyncrhonizationStarted = "EH Bank search syncrhonization started...";
        public const string EHBankSearchSyncrhonizationStartedForCountry = "Getting banks for {0}...";
        public const string EHBankSearchSyncrhonizationInsertBanksForCountry = "Inserting banks for {0} to DB...";
        public const string EHBankSearchSyncrhonizationComplete = "EH Bank search syncrhonization done :D";

        public const string UserSignUpNoPPSAccountNumber = "No PPS account number found.";

        //SalaryAdvanceCashback
        public const string SalaryAdvanceCashBackFailedDueToUserBeingDeleted = "Salary Advance Cashback crediting failed for User: {0} due to user being deleted";
        public const string SalaryAdvanceCashBackEligibiltyMethodFailed = "Salary Advance Cashback Eligibilty Method failed for CitizenId: {0}";
        public const string CashbackNotSuccessfulDueToPPSConnectionIssue = "Cashback crediting failed for User: {0} due to PPS Connection Issue";
        public const string CashbackFailedDueToErrorAddingCredit = "Cashback crediting failed  for User: {0} due to Error in adding Credit";
        public const string UpdateCashBackStatusFailedOnEsmoWebService = "Update of CashBack Status for CitizenId: {0} failed";

        public const string UpdateCashBackStatusSuccess = "Update of CashBack Status for CitizenId: {0} successful";
        public const string UpdateCashBackStatusNotApplicable = "Update of CashBack Status for CitizenId: {0} is not applicable";
        public const string UpdateCashBackStatusUpdateError = "Update of CashBack Status for CitizenId: {0} has update error";
        public const string UpdateCashBackStatusGeneralError = "Update of CashBack Status for CitizenId: {0} has general error";

        //Rating
        public const string RatingInitialInformation = "The userId {0} gave a rating of {1}";
        public const string RatingSavedSuccessfully = "Rating saved successfully.";

        //Feature
        public const string FeatureDoesNotExist = "Feature does not exist.";

        //BaseController
        public const string LoggedInUserIdInformation = "Loggedin UserId is {0}";


        public const string PartOfExperimentAnalyticsUpdate = "Successfully updated user experiment: User: {0}, Experiment: {1}";

        //UnEmpInsurance
        public const string DubaiInsurancePurchaseErrorReason = "because your profile is not updated in the ministry of labour";
        public const string SuccessfulInsuranceSubscription = "Congratulations!";

        #endregion

        #region General 

        public const string DefaultDateFormat = "dd-MM-yyyy";
        public const string RegistrationEmiratesIdApprovalNotificationHeader = "C3Card";
        public const string RegistrationPassportApprovalNotificationHeader = "Your account just got verified!";
        public const string RegistrationRejectionNotificationHeader = "We could not verify your Emirates ID";
        public const string PopularCountry = "POPULAR";
        public const string BillPaymentCountry = "BILLPAYMENT";
        public const string Username = "username";
        public const string RakErrorStatus = "RAK Error";
        public const string DelayStatus = "Delay";
        public const string FailedToVerifyStatus = "Failed to Verify";
        public const string International = "International";
        public const string Local = "Local";
        public const string AutoApproved = "Auto-Approved";
        public const string AutoRejected = "Auto-Rejected";
        public const string Reversed = "Reversed";
        public const string Successful = "Successful";
        public const string Pending = "Pending";
        public const string MobileRecharge = "MobileRecharge";
        public const string CardSerialNumber = "Card Serial Number";
        public const string MobileRechargePhoneNumber = "Mobile Recharge Phone Number";
        public const string MoneyTransferAccountNumber = "Money Transfer Account Number";


        //AuditTrail
        public const string AuditTrailBlock = "Block";
        public const string AuditTrailUnblock = "Unblock";
        public const string AuditTrailSendPassword = "Send Password";
        public const string AuditTrailRemoveAccount = "Remove Account";
        public const string AuditTrailApproveBeneficiary = "Approve Beneficiary";
        public const string AuditTrailDeleteBeneficiary = "Delete Beneficiary";
        public const string AuditTrailVerifyRegistration = "Verify Registration";
        public const string AuditTrailRejectRegistration = "Reject Registration";
        public const string AuditTrailSubscribeToBESalarySMSSecuritySMS = "Subscribe to BE/Salary SMS/Security SMS";
        public const string AuditTrailUnsubscribeFromBESalarySMSSecuritySMS = "Unsubscribe from BE/Salary SMS/Security SMS";
        public const string AuditActivateCard = "Activate Card";
        public const string AuditBlockMobileBeneficiary = "Block Mobile Beneficiary";
        public const string AuditBlockMoneyBeneficiary = "Block Money Beneficiary";
        public const string Unauthorized = "Unauthorized";
        public const string AuditUpdateMoneyTransferTransactionStatus = "Update Money Transfer Transaction Status";
        public const string AuditTrailUpdateIdentification = "Update KYC";
        public const string AuditTrailInsertIdentification = "Insert KYC";
        #endregion

        #region Notifications

        public const string ReferralCodeUsedMessageHeader = "Your referral code was used by {0}!";
        public const string ReferralAmountRewardedMessageHeader = "We've added AED {0} to your account!";
        public const string InstantDirectTransferTemplateHeader = "You've got money!";
        public const string ClaimedDirectTransferToSenderTemplateHeader = "Your money has been collected!";
        public const string ClaimedDirectTransferToReceiverTemplateHeader = "You've got money!";
        #endregion

        #region HR Validation Messages  

        public const string DepartmentDoesntExists = "Department(s) not exists";
        public const string LeavePolicyDoesntExists = "Leave Policy does not exist";
        public const string AlreadyRequestedForSameDays1 = "Leave already requested for the same day(s) in Annual leave type";
        public const string AlreadyRequestedForSameDays2 = "Leave already requested for the same day(s) in Sick leave type";
        public const string AlreadyRequestedForSameDays3 = "Leave already requested for the same day(s) in Unpaid leave type";
        public const string AlreadyRequestedForSameDays4 = "Leave already requested for the same day(s) in Maternity leave type";
        public const string AlreadyRequestedForSameDays5 = "Leave already requested for the same day(s) in Paternal leave type";
        public const string AlreadyRequestedForSameDays6 = "Leave already requested for the same day(s) in Bereavement leave type";
        public const string LeaveApprovalWorkflowDoesntExists = "Leave approval workflow does not setup for this department.Please contact system administrator.";
        public const string NoManagerFoundForThisCorporate = "No manager(s) found for this corporate";
        public const string ApprovalManagerDoesntExists = "The approving manager does not exists on manager table.";

        public const string IsValidLeaves = "Leave Valid";
        public const string PendingLeaveRequestInSameDays = "This employee has a pending leave request in these dates";
        public const string ErrorInConnectingHRService = "ErrorInCallingAPI";

        public const string ProbationPeriodExist = "Probation period does exist";
        public const string ProbationPeriodNotExist = "Probation period does not exist";
        public const string DepartmentNotConfigured = "Department Not Configured for the Employee";




        #endregion

        #region Store Validation Messages

        public const string OrderPaymentSMSFailed = "Failed to send order transaction SMS";
        public const string OrderPaymentSMSSent = "Successfully sent order transaction SMS";
        public const string OrderIdNotPassed = "Invalid Order Id";

        public const string FailedToReverseStoreTransaction = "Failed to reverse store transaction... UserId: {0}, Amount: {1} AED";


        #endregion

        #region UnEmpInsurance 

        public const string InsuranceAnalyticsUpdate = "Successfully updated Insurance Details in CleverTap: User: {0}, Experiment: {1}";

        #endregion

        #region Money Transfer Status

        // User messages.
        public const string UM_TransferStarted = "UM_TransferStarted"; // Transfer started.
        public const string UM_TransferFailed = "UM_TransferFailed"; // Transfer failed.
        public const string UM_TransferFailedInsufficientFunds = "UM_TransferFailedInsufficientFunds"; // Transfer failed. Insufficient funds.
        public const string UM_TransferFailedButWillBeReversed = "UM_TransferFailedButWillBeReversed"; // Your transfer failed. We will transfer money back to your account.
        public const string UM_SuccessfulTransfer = "UM_SuccessfulTransfer"; // Transfer was successful.
        public const string UM_WaitingForSuspiciousTransactionAuthorization = "UM_WaitingForSuspiciousTransactionAuthorization"; // You transfer is pending due to missing documents. We will reach out to you shortly.
        public const string UM_WaitingForCorrespondentBankAuthorization = "UM_WaitingForCorrespondentBankAuthorization"; // You transfer is pending due to delays from your bank in the country.
        public const string UM_InvalidBeneficiaryAccountNumber = "UM_InvalidBeneficiaryAccountNumber"; // Your transfer failed due to wrong account number. We will transfer money back to your account.
        public const string UM_AccountFrozen = "UM_AccountFrozen"; // Your transfer failed due to account being closed in your country. We will transfer money back to your account.
        public const string UM_IncorrectPurposeCode = "UM_IncorrectPurposeCode"; // Your transfer failed. We will transfer money back to your account.
        public const string UM_InvalidIfsCode = "UM_InvalidIfsCode"; // Your transfer failed due to wrong branch/IFSC code. We will transfer money back to your account.
        public const string UM_BeneficiaryAccountClosed = "UM_BeneficiaryAccountClosed"; // Your transfer failed due to account being closed in your country. We will transfer money back to your account.
        public const string UM_InvalidBeneficiaryName = "UM_InvalidBeneficiaryName"; // Your transfer failed due to wrong name. We will transfer money back to your account.
        public const string UM_BeneficiaryBankDidNotRespond = "UM_BeneficiaryBankDidNotRespond"; // You transfer failed due to issues from your bank in the country. We will transfer money back to your account.
        public const string UM_StopPaymentDone = "UM_StopPaymentDone"; // You transfer has been stopped after your request. We will transfer money back to your account.
        public const string UM_TransferPending = "UM_TransferPending"; // Your transfer is pending and is currently in progress.
        public const string UM_TransferPendingReverse = "UM_TransferPendingReverse"; // Your transfer failed. We will transfer money back to your account.

        public const string UM_InvalidBeneficiaryAccountNumber_R = "UM_InvalidBeneficiaryAccountNumber_R"; // Your transfer failed due to wrong account number. Please try again with correct details. 
        public const string UM_AccountFrozen_R = "UM_AccountFrozen_R"; // Your transfer failed due to account being closed in your country. Please try again with correct details.
        public const string UM_IncorrectPurposeCode_R = "UM_IncorrectPurposeCode_R"; // Your transfer failed. Please try again with correct details.
        public const string UM_InvalidIfsCode_R = "UM_InvalidIfsCode_R"; // Your transfer failed due to wrong branch/IFSC code. Please try again with correct details.
        public const string UM_BeneficiaryAccountClosed_R = "UM_BeneficiaryAccountClosed_R"; // Your transfer failed due to account being closed in your country. Please try again with correct details.
        public const string UM_InvalidBeneficiaryName_R = "UM_InvalidBeneficiaryName_R"; // Your transfer failed due to wrong name. Please try again with correct details.
        public const string UM_BeneficiaryBankDidNotRespond_R = "UM_BeneficiaryBankDidNotRespond_R"; // You transfer failed due to issues from your bank in the country. Please try again with correct details.
        public const string UM_StopPaymentDone_R = "UM_StopPaymentDone_R"; // You transfer has been stopped after your request. Please try again with correct details.
        public const string UM_TransferReversed_R = "UM_TransferReversed_R"; // Your transfer came back.


        // Provider error messages.
        public const string PE_WaitingForSuspiciousTransactionAuthorizationError = "WAITING FOR SUSPICIOUS TRANSACTION AUTHORIZATION";
        public const string PE_WaitingForCorrespondentBankAuthorizationError = "WAITING FOR CORRESPONDENT BANK AUTHORIZATION";
        public const string PE_InvalidBeneficiaryAccountNumberError = "Invalid Beneficiary A/c no";
        public const string PE_AccountFrozenError = "Account Frozen";
        public const string PE_IncorrectPurposeCodeError = "Incorrect Purpose code";
        public const string PE_InvalidIfsCodeError = "INVALID IFS CODE";
        public const string PE_BeneficiaryAccountClosedError = "Bene Account Closed";
        public const string PE_InvalidBeneficiaryNameError = "Invalid Beneficiary Name";
        public const string PE_BeneficiaryBankDidNotRespondError = "Bene Bank didnt respond";
        public const string PE_StopPaymentDoneError = "STOP PAYMENT DONE";
        #endregion

        public const string UnableToDeleteCacheKey = "Unable to delete cache key: {cacheKey}";

        //KycExpiry
        public const string AdditionKycBlockRemark = "KYC Block-Addition";
        public const string UpdateKycBlockRemark = "KYC Block-Updation";
        public const string UnblockingCardWithValidEidRemark = "Kyc completed Auto Unblock from C3Pay";

        public const string SenderUsername = "C3Pay";



        /// <summary>
        /// Default message for closing a membership when the user is deleted, blocked, or not found.
        /// </summary>
        public const string C3PayPlus_RNWLS_ClosedMembershipDueToUserNotFoundOrBlockedOrDeleted
            = "C3P_SYSTEM_ACTION: Closed membership because the user was deleted, blocked, or not found.";

        /// <summary>
        /// Default message for closing a membership when the cardholder voluntarily decides to close it.
        /// </summary>
        public const string C3PayPlus_RNWLS_CloseMembershipDueToUsersDecision
            = "C3P_SYSTEM_ACTION: Closed membership due to the cardholder's decision.";

        /// <summary>
        /// Message for closing a membership when the member's age cannot be determined.
        /// </summary>
        public const string C3PayPlus_RNWLS_CloseMembershipDueToAgeNotFound
            = "C3P_SYSTEM_ACTION: Closed membership due to missing age information.";

        /// <summary>
        /// Message for closing a membership due to the member exceeding the allowed age limit.
        /// </summary>
        public const string C3PayPlus_RNWLS_CloseMembershipDueToAgeLimitExceeded
            = "C3P_SYSTEM_ACTION: Closed membership due to exceeding the age limit.";

        /// <summary>
        /// Message for closing a membership due to the user's card being inactive or dormant.
        /// </summary>
        public const string C3PayPlus_RNWLS_CloseMembershipDueToInactiveCard
            = "C3P_SYSTEM_ACTION: Closed membership due to inactive or dormant card.";

        /// <summary>
        /// Message for closing a membership due to no salary being credited in the last three months.
        /// </summary>
        public const string C3PayPlus_RNWLS_CloseMembershipDueToNoSalaryCreditedInLast3Months
            = "C3P_SYSTEM_ACTION: Closed membership due to no salary credited in the last three months.";

        /// <summary>
        /// Message for closing a membership due to other unspecified reasons.
        /// </summary>
        public const string C3PayPlus_RNWLS_CloseMembershipDueToOtherReasons
            = "C3P_SYSTEM_ACTION: Closed membership due to other unspecified reasons.";

        public const string DefaultBankLogo = "https://cdn.edenred.ae/money-transfer-icons/bank-default-logo.webp";
    }


    public static class C3PayPlusConstantParams
    {
        /// <summary>
        /// Closed membership because the user was deleted, blocked, or not found.
        /// </summary>
        public const string C3PayPlus_RenewMembership_ClosedMembershipDueToUserNotFoundOrBlockedOrDeleted
            = "C3P_SYSTEM_ACTION: Closed membership because the user was deleted, blocked, or not found.";

        /// <summary>
        /// Closed membership based on the cardholder's decision.
        /// </summary>
        public const string C3PayPlus_RenewMembership_CloseMembershipDueToUsersDecision
            = "C3P_SYSTEM_ACTION: Closed membership due to the cardholder's decision.";


        /// <summary>
        /// Closed membership due to missing age information.
        /// </summary>
        public const string C3PayPlus_RenewMembership_CloseMembershipDueToAgeNotFound
            = "C3P_SYSTEM_ACTION: Closed membership due to missing age information.";

        /// <summary>
        /// Closed membership due to exceeding the allowed age limit.
        /// </summary>
        public const string C3PayPlus_RenewMembership_CloseMembershipDueToAgeLimitExceeded
            = "C3P_SYSTEM_ACTION: Closed membership due to exceeding the age limit.";

        /// <summary>
        /// Closed membership due to an inactive or dormant card.
        /// </summary>
        public const string C3PayPlus_RenewMembership_CloseMembershipDueToInactiveCard
            = "C3P_SYSTEM_ACTION: Closed membership due to inactive or dormant card.";

        /// <summary>
        /// Closed membership due to no salary credited in the last three months.
        /// </summary>
        public const string C3PayPlus_RenewMembership_CloseMembershipDueToNoSalaryCreditedInLast3Months
            = "C3P_SYSTEM_ACTION: Closed membership due to no salary credited in the last three months.";

        /// <summary>
        /// Closed membership due to the card being blocked.
        /// </summary>
        public const string C3PayPlus_RenewMembership_CloseMembershipDueToCardBeingBlocked
            = "C3P_SYSTEM_ACTION: Closed membership due to the card being blocked.";

        /// <summary>
        /// Closed membership due to other unspecified reasons.
        /// </summary>
        public const string C3PayPlus_RenewMembership_CloseMembershipDueToOtherReasons
            = "C3P_SYSTEM_ACTION: Closed membership due to other unspecified reasons.";

    }
}

