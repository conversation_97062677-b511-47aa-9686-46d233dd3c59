﻿using C3Pay.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace C3Pay.Data.Configurations
{
    public class CountryConfiguration : IEntityTypeConfiguration<Country>
    {
        public void Configure(EntityTypeBuilder<Country> builder)
        {
            builder.ToTable("Countries");

            builder.HasKey(c => c.Code);

            builder.Property(c => c.Code)
                .HasMaxLength(2);

            builder.Property(c => c.Name)
                .HasMaxLength(50)
                .IsRequired();

            builder.Property(c => c.LongName)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(c => c.Code3)
                .HasMaxLength(3)
                .IsRequired();

            builder.Property(c => c.STDCode)
               .HasMaxLength(8);

            builder.Property(c => c.Currency)
               .HasMaxLength(3);

            builder.Property(c => c.CashPickUpProvider)
               .HasMaxLength(50);

            builder.Property(c => c.CashPickUpProviderLocationsURL)
                .HasMaxLength(200);

            builder.Property(c => c.BankTransferLatestRate)
                .HasPrecision(18, 6);

            builder.Property(c => c.CashPickUpLatestRate)
                .HasPrecision(18, 6);

            builder.HasMany(c => c.Popups)
                .WithOne(p => p.Country)
                .HasForeignKey(c => c.CountryCode)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(c => c.BlackListedEntities)
               .WithOne(p => p.Country)
               .HasForeignKey(c => c.CountryCode)
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(c => c.MobileRechargeBeneficiaries)
               .WithOne(p => p.Country)
               .IsRequired()
               .HasForeignKey(c => c.CountryCode)
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(c => c.MoneyTransferBeneficiaries)
               .WithOne(p => p.Country)
               .IsRequired()
               .HasForeignKey(c => c.CountryCode)
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(c => c.BillPaymentProviders)
               .WithOne(p => p.Country)
               .HasForeignKey(c => c.CountryCode)
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(c => c.Banks)
               .WithOne(p => p.Country)
               .HasForeignKey(c => c.CountryCode)
               .IsRequired()
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(c => c.Languages)
               .WithOne(p => p.Country)
               .HasForeignKey(c => c.CountryCode)
               .IsRequired()
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(c => c.Cities)
               .WithOne(p => p.Country)
               .HasForeignKey(c => c.CountryCode)
               .IsRequired()
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(c => c.MoneyTransferDelays)
               .WithOne(p => p.Country)
               .IsRequired()
               .HasForeignKey(c => c.CountryCode)
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(c => c.SocialProofings)
               .WithOne(p => p.Country)
               .IsRequired()
               .HasForeignKey(c => c.CountryCode)
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(c => c.MoneyTransferSmartDefaults)
               .WithOne(p => p.Country)
               .IsRequired()
               .HasForeignKey(c => c.CountryCode)
               .OnDelete(DeleteBehavior.Restrict);

            builder
               .HasMany(c => c.States)
               .WithOne(u => u.Country)
               .IsRequired()
               .HasForeignKey(u => u.CountryCode)
               .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasMany(c => c.Provinces)
                .WithOne(u => u.Country)
                .IsRequired().HasForeignKey(u => u.CountryCode)
                .OnDelete(DeleteBehavior.Restrict);

            builder
             .HasMany(c => c.Districts)
             .WithOne(u => u.Country)
             .IsRequired().HasForeignKey(u => u.CountryCode)
             .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
