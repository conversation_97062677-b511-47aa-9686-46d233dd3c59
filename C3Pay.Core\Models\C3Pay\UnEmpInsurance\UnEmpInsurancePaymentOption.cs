﻿using System;
using System.Collections.Generic;
using System.Text;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models.C3Pay.UnEmpInsurance
{
    public class UnEmpInsurancePaymentOption
    {
        public int Id { get; set; }
        public string Description { get; set; }
        public decimal Amount { get; set; }
        public string AmountCurrency { get; set; }
        public decimal AmountVAT { get; set; }
        public string AmountVATCurrency { get; set; }
        public decimal Fee { get; set; }
        public string FeeCurrency { get; set; }
        public bool IsActive { get; set; }
        public UnEmpInsurancePaymentOptionFromExternal Frequency { get; set; }
        public UnEmpInsurancePartnerType PartnerCode { get; set; }
        public int PartnerId { get; set; }
        public Partner Partner { get; set; }
        public UnEmpInsuranceCategory Category { get; set; }

        public void ExtendAnnualTo2Years()
        {
            if (Frequency == UnEmpInsurancePaymentOptionFromExternal.Annual)
            {
                Amount = Amount * 2;
                Fee = Fee * 2;
                AmountVAT = AmountVAT * 2;

                Description = $"AED {Amount.ToString("#.####").TrimEnd('.')} (2 Year)";
            }
        }

        public void RemoveMonthlyPlan()
        {

        }
    }
}
