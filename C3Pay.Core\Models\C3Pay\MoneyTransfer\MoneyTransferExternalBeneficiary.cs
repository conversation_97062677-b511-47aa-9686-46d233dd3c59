﻿using Edenred.Common.Core;
using System;

namespace C3Pay.Core.Models
{
    public class MoneyTransferExternalBeneficiary : BaseModel
    {
        public Guid Id { get; set; }
        public Guid MoneyTransferBeneficiaryId { get; set; }
        public string ExternalId { get; set; } // benf id from index 1 2 4 5
        public Guid? CreationMessageId { get; set; } // ignore 
        public Guid? DeletionMessageId { get; set; } // ignore
        public MoneyTransferBeneficiary MoneyTransferBeneficiary { get; set; }
    }
}
