﻿using AutoMapper;
using C3Pay.API.Models;
using C3Pay.API.Resources.EH;
using C3Pay.API.Resources.MoneyTransfer;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay;
using Edenred.Common.Core.Models.Messages.Integration.ExchangeHouse.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    [Authorize(AuthenticationSchemes = AuthenticationScheme.AzureActiveDirectory, Policy = "ApplicationIdEntry")]
    public class ExchangeHouseMoneyTransferController : ControllerBase
    {
        private readonly IEHMoneyTransferService _moneyTransferService;
        private readonly ILookupService _lookupService;
        private readonly IUserService _userService;
        private readonly IMapper _mapper;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="moneyTransferService"></param>
        /// <param name="lookupService"></param>
        /// <param name="userService"></param>
        /// <param name="mapper"></param>
        public ExchangeHouseMoneyTransferController(IEHMoneyTransferService moneyTransferService,
                                                    ILookupService lookupService,
                                                    IUserService userService,
                                                    IMapper mapper)
        {
            this._moneyTransferService = moneyTransferService;
            this._lookupService = lookupService;
            this._userService = userService;
            this._mapper = mapper;
        }

        #region Card Holder
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("cardholder/{userId}/eligibility")]
        public async Task<ActionResult<string>> CheckCardHolderEligibility(Guid userId)
        {
            // Find user.
            var tryFindUser = await this._userService.FindUser(userId);
            if (tryFindUser.IsSuccessful == false)
            {
                return BadRequest(EHMoneyTransferApiResponses.UserNotFoundOrBlocked.ToString());
            }

            // Call service.
            var tryGetEligibility = await this._moneyTransferService.CheckCardHolderEligibility(tryFindUser.Data);
            if (tryGetEligibility.IsSuccessful == false)
            {
                return BadRequest(tryGetEligibility.ErrorMessage);
            }

            // Return result.
            return Ok(EHMoneyTransferApiResponses.EligibleForMoneyTransfer.ToString());
        }
        #endregion

        #region Lookups
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("payment-purposes")]
        public async Task<ActionResult<IEnumerable<EHPaymentPurposeDto>>> GetPaymentPurposes()
        {
            // Call service.
            var tryGetPaymentPurposes = await this._lookupService.GetPaymentPurposes();
            if (tryGetPaymentPurposes.IsSuccessful == false)
            {
                return BadRequest(tryGetPaymentPurposes.ErrorMessage);
            }

            // Return result.
            var paymentPurposes = _mapper.Map<IEnumerable<EHPaymentPurposeDto>>(tryGetPaymentPurposes.Data);
            return Ok(paymentPurposes);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("corridors")]
        public async Task<ActionResult<IEnumerable<MoneyTransferReasonDto>>> GetCorridors()
        {
            // Call service.
            var tryGetCorridors = await this._lookupService.GetEHCorridors(EHMoneyTransferClients.Index.ToString());
            if (tryGetCorridors.IsSuccessful == false)
            {
                return BadRequest(tryGetCorridors.ErrorMessage);
            }

            // Return result.
            var corridors = _mapper.Map<IEnumerable<EHCorridorDto>>(tryGetCorridors.Data);
            return Ok(corridors);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="countryCode"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("cardholder/{countryCode}/required-fields")]
        public ActionResult<List<EHRequiredFieldDto>> GetRequiredFields(string countryCode)
        {
            // Call service.
            var tryGetRequiredFields = this._lookupService.GetEHRequiredFields(countryCode);
            if (tryGetRequiredFields.IsSuccessful == false)
            {
                return BadRequest(tryGetRequiredFields.ErrorMessage);
            }

            // Return result.
            var requiredFields = this._mapper.Map<List<EHRequiredFieldDto>>(tryGetRequiredFields.Data);
            return Ok(requiredFields);
        }
        #endregion

        #region Beneficiary
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("cardholder/{userId}/beneficiaries")]
        public async Task<ActionResult<List<EHBeneficiaryDto>>> GetAllBeneficiaries(Guid userId)
        {
            // Find user.
            var tryFindUser = await this._userService.FindUser(userId);
            if (tryFindUser.IsSuccessful == false)
            {
                return BadRequest(EHMoneyTransferApiResponses.UserNotFoundOrBlocked.ToString());
            }

            // Call service.
            var tryGetBeneficiaries = await this._moneyTransferService.GetAllBeneficiaries(tryFindUser.Data);
            if (tryGetBeneficiaries.IsSuccessful == false)
            {
                return BadRequest(tryGetBeneficiaries.ErrorMessage);
            }

            // Return result.
            var beneficiaries = this._mapper.Map<List<EHBeneficiaryDto>>(tryGetBeneficiaries.Data);
            return Ok(beneficiaries);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="beneficiaryDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("cardholder/{userId}/beneficiaries")]
        public async Task<ActionResult<EHBaseApiResponse<string>>> AddBeneficiary(Guid userId, EHBeneficiaryDto beneficiaryDto)
        {
            // Find user.
            var tryFindUser = await this._userService.FindUser(userId);
            if (tryFindUser.IsSuccessful == false)
            {
                return BadRequest(new EHBaseApiResponse<string>()
                {
                    IsSuccessful = false,
                    ErrorMessage = EHMoneyTransferApiResponses.UserNotFound.ToString(),
                    StatusCode = 400,
                });
            }

            // Call service.
            var beneficiary = this._mapper.Map<MoneyTransferBeneficiary>(beneficiaryDto);
            var tryAddBeneficiary = await this._moneyTransferService.AddBeneficiary(tryFindUser.Data, beneficiary);
            if (tryAddBeneficiary.IsSuccessful == false)
            {
                return BadRequest(new EHBaseApiResponse<string>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryAddBeneficiary.ErrorMessage,
                    StatusCode = 400
                });
            }

            // Return result.
            return Ok(new EHBaseApiResponse<string>()
            {
                IsSuccessful = true,
                Response = EHMoneyTransferApiResponses.BeneficiaryAdded.ToString(),
                StatusCode = 200
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="beneficiaryId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("cardholder/{userId}/beneficiaries/{beneficiaryId}")]
        public async Task<ActionResult<EHBeneficiaryDto>> GetBeneficiaryById(Guid userId, Guid beneficiaryId)
        {
            // Find user.
            var tryFindUser = await this._userService.FindUser(userId);
            if (tryFindUser.IsSuccessful == false)
            {
                return BadRequest(EHMoneyTransferApiResponses.UserNotFoundOrBlocked.ToString());
            }

            // Call service.
            var tryGetBeneficiary = await this._moneyTransferService.GetBeneficiaryById(userId, beneficiaryId);
            if (tryGetBeneficiary.IsSuccessful == false)
            {
                return BadRequest(tryGetBeneficiary.ErrorMessage);
            }

            // Return result.
            var beneficiary = this._mapper.Map<EHBeneficiaryDto>(tryGetBeneficiary.Data);
            return Ok(beneficiary);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="beneficiaryId"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("cardholder/{userId}/beneficiaries/{beneficiaryId}")]
        public async Task<ActionResult<string>> DeleteBeneficiary(Guid userId, Guid beneficiaryId)
        {
            // Find user.
            var tryFindUser = await this._userService.FindUser(userId);
            if (tryFindUser.IsSuccessful == false)
            {
                return BadRequest(EHMoneyTransferApiResponses.UserNotFoundOrBlocked.ToString());
            }

            // Call service.
            var tryDeleteBeneficiary = await this._moneyTransferService.DeleteBeneficiary(tryFindUser.Data, beneficiaryId);
            if (tryDeleteBeneficiary.IsSuccessful == false)
            {
                return BadRequest(tryDeleteBeneficiary.ErrorMessage);
            }

            // Return result.
            return Ok(EHMoneyTransferApiResponses.BeneficiaryDeleted.ToString());
        }
        #endregion

        #region Transfer
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="transferDetailsDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("cardholder/{userId}/transfers")]
        public async Task<ActionResult<EHMoneyTransferReceiptDto>> TransferMoney(Guid userId, EHMoneyTransferDetailsDto transferDetailsDto)
        {
            // Find user.
            var tryFindUser = await this._userService.FindUser(userId);
            if (tryFindUser.IsSuccessful == false)
            {
                return BadRequest(EHMoneyTransferApiResponses.UserNotFoundOrBlocked.ToString());
            }

            // Call service.
            var moneyTransferTransaction = this._mapper.Map<MoneyTransferTransaction>(transferDetailsDto);
            var tryPlaceTransaction = await this._moneyTransferService.PlaceTransaction(tryFindUser.Data, moneyTransferTransaction);
            if (tryPlaceTransaction.IsSuccessful == false)
            {
                return BadRequest(tryPlaceTransaction.ErrorMessage);
            }

            // Return result.
            var transfer = this._mapper.Map<EHMoneyTransferReceiptDto>(tryPlaceTransaction.Data);
            return Ok(transfer);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="transferId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("cardholder/{userId}/transfers/{transferId}")]
        public async Task<ActionResult<EHMoneyTransferReceiptDto>> GetTransferById(Guid userId, Guid transferId)
        {
            // Find user.
            var tryFindUser = await this._userService.FindUser(userId);
            if (tryFindUser.IsSuccessful == false)
            {
                return BadRequest(EHMoneyTransferApiResponses.UserNotFoundOrBlocked.ToString());
            }

            // Call service.
            var tryGetTransfer = await this._moneyTransferService.GetTransferById(userId, transferId);
            if (tryGetTransfer.IsSuccessful == false)
            {
                return BadRequest(tryGetTransfer.ErrorMessage);
            }

            // Return result.
            var transfer = this._mapper.Map<EHMoneyTransferReceiptDto>(tryGetTransfer.Data);
            return Ok(transfer);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("cardholder/{userId}/transfers")]
        public async Task<ActionResult<EHMoneyTransferReceiptDto>> GetTransfers(Guid userId)
        {
            // Find user.
            var tryFindUser = await this._userService.FindUser(userId);
            if (tryFindUser.IsSuccessful == false)
            {
                return BadRequest(EHMoneyTransferApiResponses.UserNotFoundOrBlocked.ToString());
            }

            // Call service.
            var tryGetTransfers = await this._moneyTransferService.GetTransfers(userId);
            if (tryGetTransfers.IsSuccessful == false)
            {
                return BadRequest(tryGetTransfers.ErrorMessage);
            }

            // Return result.
            var transfers = this._mapper.Map<List<EHMoneyTransferReceiptDto>>(tryGetTransfers.Data);
            return Ok(transfers);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="transferId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("cardholder/{userId}/transaction-receipt/{transferId}")]
        public async Task<ActionResult<byte[]>> GenerateReport(Guid userId, Guid transferId)
        {
            // Find user.
            var tryFindUser = await this._userService.FindUser(userId);
            if (tryFindUser.IsSuccessful == false)
            {
                return BadRequest(EHMoneyTransferApiResponses.UserNotFoundOrBlocked.ToString());
            }

            // Call service.
            var tryGenerateReport = await this._moneyTransferService.GenerateReport(tryFindUser.Data, transferId);
            if (tryGenerateReport.IsSuccessful == false)
            {
                return BadRequest(tryGenerateReport.ErrorMessage);
            }

            return new ActionResult<byte[]>(tryGenerateReport.Data);
        }
        #endregion

        #region Back Search

        /// <summary>
        /// 
        /// </summary>
        /// <param name="countryCode"></param>
        /// <param name="criteria"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{countryCode}/bank-search")]
        public async Task<ActionResult<List<EHBankDto>>> SearchBanks(string countryCode, EHBankSearchCriteriaDto criteria)
        {
            var searchCriteria = this._mapper.Map<EHBankSearchCriteria>(criteria);
            var trySearchBanks = await this._moneyTransferService.SearchBanks(countryCode, searchCriteria);
            if (trySearchBanks.IsSuccessful == false)
            {
                return BadRequest(trySearchBanks.ErrorMessage);
            }

            var banks = this._mapper.Map<List<EHBankDto>>(trySearchBanks.Data);
            return Ok(banks);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="countryCode"></param>
        /// <param name="criteria"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{countryCode}/branch-search")]
        public async Task<ActionResult<List<EHBranchDto>>> SearchBranchs(string countryCode, EHBranchSearchCriteriaDto criteria)
        {
            var searchCriteria = this._mapper.Map<EHBranchSearchCriteria>(criteria);
            var trySearchBranches = await this._moneyTransferService.SearchBranches(countryCode, searchCriteria);
            if (trySearchBranches.IsSuccessful == false)
            {
                return BadRequest(trySearchBranches.ErrorMessage);
            }

            var branches = this._mapper.Map<List<EHBranchDto>>(trySearchBranches.Data);
            return Ok(branches);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ifsc"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ifsc-search/{ifsc}")]
        public async Task<ActionResult<EHBankBranchDto>> SearchBanksByIfsc(string ifsc)
        {
            var tryGetBankBranch = await this._moneyTransferService.SearchBanksByIfsc(ifsc);
            if (tryGetBankBranch.IsSuccessful == false)
            {
                return BadRequest(tryGetBankBranch.ErrorMessage);
            }

            var bankBranch = this._mapper.Map<EHBankBranchDto>(tryGetBankBranch.Data);
            return Ok(bankBranch);
        }
        #endregion

        #region Conversion Rates
        /// <summary>
        /// 
        /// </summary>
        /// <param name="conversionsDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("rates")]
        public async Task<ActionResult<ExchangeHouseRateCalculation>> GetConversionRates(List<EHConversionRateDto> conversionsDto)
        {
            // Call service.
            var conversionRates = this._mapper.Map<List<ExchangeHouseConversionRate>>(conversionsDto);
            var tryGetConversionRates = await this._moneyTransferService.GetConversionRates(conversionRates);
            if (tryGetConversionRates.IsSuccessful == false)
            {
                return BadRequest(tryGetConversionRates.ErrorMessage);
            }

            // Return result.
            var conversions = this._mapper.Map<List<EHRateCalculationDto>>(tryGetConversionRates.Data);
            return Ok(conversions);
        }
        #endregion
    }
}
