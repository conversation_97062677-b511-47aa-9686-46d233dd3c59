﻿using C3Pay.API.Models;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.ExchangeHouse.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    [Authorize(AuthenticationSchemes = AuthenticationScheme.AzureActiveDirectory, Policy = "ApplicationIdEntry")]
    public class KYCController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly IIdentityService _identityService;
        private readonly IEHMoneyTransferService _moneyTransferService;
        private readonly ILogger<KYCController> _logger;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userService"></param>
        /// <param name="identityService"></param>
        /// <param name="moneyTransferService"></param>
        public KYCController(IUserService userService, IIdentityService identityService, IEHMoneyTransferService moneyTransferService, ILogger<KYCController> logger)
        {
            this._userService = userService;
            this._identityService = identityService;
            this._moneyTransferService = moneyTransferService;
            _logger = logger;
        }

        [HttpGet("user-exists/{citizenId}")]
        public async Task<ActionResult<bool>> UserExistsByCitizenId(string citizenId)
        {
            var userResult = await this._userService.GetUserByCitizenId(citizenId);

            return this.Ok(userResult.IsSuccessful);
        }

        [HttpGet("phone-number-eligibility/{citizenId}/{phoneNumber}")]
        public async Task<ActionResult<bool>> PhoneNumberEligibility(string citizenId, string phoneNumber)
        {
            var eligibilityResult = await this._userService.GetPhoneNumberUpdateEligibility(citizenId, phoneNumber.ToZeroPrefixedPhoneNumber());

            var eligibility = eligibilityResult.Data;

            return this.Ok(eligibility);
        }

        [HttpPut("phone-number-and-username/{citizenId}/{phoneNumber}")]
        public async Task<ActionResult<bool>> UpdatePhoneNumberAndUsername(string citizenId, string phoneNumber)
        {
            try
            {
                _logger.LogInformation("Called Phone Number update v1");
                _logger.LogInformation($"citizenId: {citizenId}. NewPhoneNumber:{phoneNumber}");

                var zeroPrefixedPhoneNumber = phoneNumber.ToZeroPrefixedPhoneNumber();

                if (zeroPrefixedPhoneNumber.Length != 14 || !zeroPrefixedPhoneNumber.StartsWith("009715"))
                {
                    return BadRequest(PhoneEligibilityResult.InvalidPhoneNumber.ToString());
                }

                var userResult = await this._userService.GetUserByCitizenId(citizenId);

                if (!userResult.IsSuccessful)
                {
                    return BadRequest(userResult.ErrorMessage);
                }

                var user = userResult.Data;

                var oldUserName = user.PhoneNumber;

                _logger.LogInformation($"Got User Details. Old Phone Number: {oldUserName}. NewPhoneNumber: {phoneNumber}");



                _logger.LogInformation($"CHECKING EDC OLD PHONE NUMBER: SHOULD BE FOUND");
                var identityAccountExistsResultNew = await this._identityService.UserAccountExistsAsync(oldUserName);
                if (identityAccountExistsResultNew.Data)
                {
                    _logger.LogInformation($"EDC OLD PHONE NUMBER FOUND. CORRECT");
                }
                else
                {
                    _logger.LogInformation($"EDC OLD PHONE NUMBER NOT FOUND. NOT CORRECT");
                }



                var identityAccountExistsResult = await this._identityService.UserAccountExistsAsync(zeroPrefixedPhoneNumber);

                if (identityAccountExistsResult.Data)
                {
                    _logger.LogInformation($"Username: {zeroPrefixedPhoneNumber} FOUND in edc.");
                    await this._userService.RemoveIdenityIfUserDoesntExist(zeroPrefixedPhoneNumber);

                    var identityAccountExistsResult2 = await this._identityService.UserAccountExistsAsync(zeroPrefixedPhoneNumber);
                    if (identityAccountExistsResult2.Data == false)
                    {
                        _logger.LogInformation($"Username: {zeroPrefixedPhoneNumber} REMOVED AFTER FOUND in edc.");
                    }
                }
                else
                {
                    _logger.LogInformation($"Username: {zeroPrefixedPhoneNumber} NOT FOUND in edc.");
                }


                _logger.LogInformation($"calling change phone number Old: {oldUserName}. new: {zeroPrefixedPhoneNumber}");
                var updateUsernameResult = await this._identityService.ChangeUsernameAsync(oldUserName, zeroPrefixedPhoneNumber);

                if (!updateUsernameResult.IsSuccessful)
                {
                    _logger.LogInformation($"error in change username edc. Error: {updateUsernameResult.ErrorMessage}");
                    return BadRequest(updateUsernameResult.ErrorMessage);
                }


                _logger.LogInformation($"calling C3Pay phone number update");
                var updateResult = await this._userService.UpdatePhoneNumber(user, zeroPrefixedPhoneNumber);

                if (!updateResult.IsSuccessful)
                {
                    return BadRequest(updateResult.ErrorMessage);
                }

                return this.Ok();
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        /// <summary>
        /// Updates Phone Number and Username V2
        /// </summary>
        /// <param name="citizenId"></param>
        /// <param name="phoneNumber"></param>
        /// <param name="source"></param>
        /// <returns></returns>
        [HttpPut("phone-number-and-username-v2/{citizenId}/{phoneNumber}/{source}")]
        public async Task<ActionResult<bool>> UpdatePhoneNumberAndUsernameV2(string citizenId, string phoneNumber, string source)
        {
            try
            {
                _logger.LogInformation("Called Phone Number update v1");
                _logger.LogInformation($"citizenId: {citizenId}. NewPhoneNumber:{phoneNumber}");

                var zeroPrefixedPhoneNumber = phoneNumber.ToZeroPrefixedPhoneNumber();

                if (zeroPrefixedPhoneNumber.Length != 14 || !zeroPrefixedPhoneNumber.StartsWith("009715"))
                {
                    return BadRequest(PhoneEligibilityResult.InvalidPhoneNumber.ToString());
                }

                var userResult = await this._userService.GetUserByCitizenId(citizenId);

                if (!userResult.IsSuccessful)
                {
                    return BadRequest(userResult.ErrorMessage);
                }

                var user = userResult.Data;

                var oldUserName = user.PhoneNumber;

                _logger.LogInformation($"Got User Details. Old Phone Number: {oldUserName}. NewPhoneNumber: {phoneNumber}");


                _logger.LogInformation($"CHECKING EDC OLD PHONE NUMBER: SHOULD BE FOUND");
                var identityAccountExistsResultNew = await this._identityService.UserAccountExistsAsync(oldUserName);
                if (identityAccountExistsResultNew.Data)
                {
                    _logger.LogInformation($"EDC OLD PHONE NUMBER FOUND. CORRECT");
                }
                else
                {
                    _logger.LogInformation($"EDC OLD PHONE NUMBER NOT FOUND. NOT CORRECT");
                }


                var identityAccountExistsResult = await this._identityService.UserAccountExistsAsync(zeroPrefixedPhoneNumber);

                if (identityAccountExistsResult.Data)
                {
                    _logger.LogInformation($"Username: {zeroPrefixedPhoneNumber} FOUND in edc.");
                    await this._userService.RemoveIdenityIfUserDoesntExist(zeroPrefixedPhoneNumber);

                    var identityAccountExistsResult2 = await this._identityService.UserAccountExistsAsync(zeroPrefixedPhoneNumber);
                    if (identityAccountExistsResult2.Data == false)
                    {
                        _logger.LogInformation($"Username: {zeroPrefixedPhoneNumber} REMOVED AFTER FOUND in edc.");
                    }
                }
                else
                {
                    _logger.LogInformation($"Username: {zeroPrefixedPhoneNumber} NOT FOUND in edc.");
                }

                var updateUsernameResult = await this._identityService.ChangeUsernameAsync(oldUserName, zeroPrefixedPhoneNumber);

                if (!updateUsernameResult.IsSuccessful)
                {
                    _logger.LogInformation($"error in change username edc. Error: {updateUsernameResult.ErrorMessage}");
                    return BadRequest(updateUsernameResult.ErrorMessage);
                }

                var updateResult = await this._userService.UpdatePhoneNumberV2(user, oldUserName, zeroPrefixedPhoneNumber, source);

                if (!updateResult.IsSuccessful)
                {
                    return BadRequest(updateResult.ErrorMessage);
                }

                return this.Ok();
            }
            catch (Exception ex)
            {
                throw;
            }
            
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="citizenId"></param>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("cardholder/{citizenId}/phone-number/{phoneNumber}")]
        public async Task<ActionResult> UpdateCardHolderPhoneNumber(string citizenId, string phoneNumber)
        {
            var user = await this._userService.GetUserByCitizenId(citizenId);
            if (!user.IsSuccessful)
            {
                return BadRequest(user.ErrorMessage);
            }

            var information = new ExchangeHouseCardHolderInformation()
            {
                EmiratesId = user.Data.CardHolder.EmiratesId,
                PhoneNumber = phoneNumber.ToShortPhoneNumber()
            };

            var upadted = await this._moneyTransferService.UpdateCardHolderPhoneNumber(information);
            if (!upadted.IsSuccessful)
            {
                return BadRequest(upadted.ErrorMessage);
            }

            return Ok(EHMoneyTransferApiResponses.UpdatedCardHolderPhoneNumber.ToString());
        }
    }
}
