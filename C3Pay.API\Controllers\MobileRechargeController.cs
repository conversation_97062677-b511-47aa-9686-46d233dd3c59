﻿using AutoMapper;
using C3Pay.API.Resources;
using C3Pay.API.Resources.Countries;
using C3Pay.API.Resources.MobileRecharge;
using C3Pay.API.Resources.MobileRecharge.TargetedDiscount;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs.MobileRecharge;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Services;
using C3Pay.Core.Services.Security;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Beneficiaries;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using NetTopologySuite.Densify;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    public class MobileRechargeController : BaseController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IMobileRechargeService _mobileRechargeService;

        /// <summary>
        /// 
        /// </summary>
        private readonly ISecurityService _securityService;

        /// <summary>
        /// 
        /// </summary>
        private readonly ILookupService _lookupService;

        /// <summary>
        /// 
        /// </summary>
        private readonly IMapper _mapper;

        /// <summary>
        /// 
        /// </summary>
        private readonly IFeatureManager _featureManager;

        private readonly ILogger<MobileRechargeController> _logger;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpContextAccessor"></param>
        /// <param name="userService"></param>
        /// <param name="mobileRechargeService"></param>
        /// <param name="mapper"></param>
        /// <param name="lookupService"></param>
        /// <param name="securityService"></param>
        /// <param name="logger"></param>
        public MobileRechargeController(IMobileRechargeService mobileRechargeService,
                                        IHttpContextAccessor httpContextAccessor,
                                        ISecurityService securityService,
                                        ILookupService lookupService,
                                        IUserService userService,
                                        IMapper mapper,
                                        IFeatureManager featureManager,
                                        ILogger<MobileRechargeController> logger) : base(httpContextAccessor, userService, logger)
        {
            this._mobileRechargeService = mobileRechargeService;
            this._securityService = securityService;
            this._lookupService = lookupService;
            this._mapper = mapper;
            this._featureManager = featureManager;
            _logger = logger;
        }

        #region Mobile Recharge Module

        /// <summary>
        /// Possible results: UserNotExists,NickNameNotExists,ExceedNicknameLength,BeneficiaryAlreadyExists, InvalidPhoneNumber ,CountryNotSupported
        /// </summary>
        /// <param name="RequestObject"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("beneficiary-details-iseligible")]
        public async Task<ActionResult<bool>> GetRechargeBeneficiaryDetailsEligibility(BeneficiaryDetailsEligibilityRequestDto RequestObject)
        {
            if (string.IsNullOrEmpty(RequestObject.CountryCode))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.CountryNotSupported.ToString());
            else if (string.IsNullOrEmpty(RequestObject.PhoneNumber))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.InvalidPhoneNumber.ToString());
            else if (string.IsNullOrEmpty(RequestObject.NickName))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.NickNameNotExists.ToString());

            var phoneeligiblity = await _mobileRechargeService.GetRechargeBeneficiaryDetailsEligibility(RequestObject.UserId, RequestObject.CountryCode.ToUpper(), RequestObject.PhoneNumber, RequestObject.NickName);

            if (!phoneeligiblity.IsSuccessful)
            {
                return BadRequest(phoneeligiblity.ErrorMessage);
            }

            return Ok(phoneeligiblity.Data);
        }

        /// <summary>
        /// To get the country lists
        /// </summary>
        /// <returns></returns>
        [HttpGet("countries")]
        public async Task<ActionResult<IEnumerable<MobileRechageCountryDto>>> GetCountries()
        {
            var countries = await this._lookupService.GetMobileRechargeCountries();

            if (countries.Data == null || !countries.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }

            var countryResources = _mapper.Map<IEnumerable<Country>, IEnumerable<MobileRechageCountryDto>>(countries.Data);

            return Ok(countryResources);
        }

        [Authorize]
        [HttpGet("targeted-discount-config")]
        public async Task<ActionResult<TargetedDiscountConfigDto>> GetTargetedDiscountConfig()
        {
            await this.GetLoggedInUser();

            var response = await _mobileRechargeService.GetTargetedDiscountConfig(_user);
            if (response.IsSuccessful)
            {
                return Ok(response.Data);
            }

            return BadRequest();
        }

        /// <summary>
        ///  Get all Providers 
        /// </summary>
        /// <returns></returns>
        [HttpGet("{countryCode}/providers")]
        public async Task<ActionResult<IEnumerable<MobileRechargeProviderDto>>> GetProviders(string countryCode)
        {
            var providers = await this._lookupService.GetMobileRechargeProviders(countryCode);

            if (providers.Data == null || !providers.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }

            var countryResources = _mapper.Map<IEnumerable<MobileRechargeProvider>, IEnumerable<MobileRechargeProviderDto>>(providers.Data);

            return Ok(countryResources);
        }

        /// <summary>
        /// Check Transaction failed with Error - ProviderUnknownError
        /// </summary> 
        /// <returns></returns>
        [HttpGet("check-beneficiary-modification/{beneficiaryId}")]
        public async Task<ActionResult<IEnumerable<MobileRechargeErrorResponseDto>>> CheckForPastError(Guid beneficiaryId)
        {
            await this.GetLoggedInUser();

            var response = await this._mobileRechargeService.CheckForBeneficiaryModification(_loggedInUserId, beneficiaryId);
            if (response.IsSuccessful)
            {
                return Ok(new MobileRechargeErrorResponseDto() { RequiresBeneficiaryModification = response.Data });
            }

            return BadRequest();

        }

        /// <summary>
        /// Mobile recharge products
        /// Possible results:  BeneficiaryNotExists,ProductsNotExistsForBeneficiary
        /// <param name="beneficiaryId"></param>
        /// <param name="productType"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>        
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("beneficiary/{beneficiaryId}/products")]
        public async Task<ActionResult<IEnumerable<MobileRechargeProductDto>>> GetProducts(Guid beneficiaryId, string? productType = null, int? pageSize = null, int? pageNumber = null)
        {
            await this.GetLoggedInUser();

            bool rechargeForMyself = beneficiaryId == this._user.Id ? true : false;
            var productlist = await _mobileRechargeService.GetProducts(beneficiaryId, productType, pageSize, pageNumber, MobileApplicationId.C3Pay, this._user.CardHolder.CorporateId, rechargeForMyself);

            if (productlist.Data == null || !productlist.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }
            var productlistResources = _mapper.Map<IEnumerable<MobileRechargeProduct>, IEnumerable<MobileRechargeProductDto>>(productlist.Data);
            return Ok(productlistResources);
        }

        /// <summary>
        /// Mobile recharge products
        /// <param name="beneficiaryId"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>   
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("beneficiary/{beneficiaryId}/productsv2/{pageNumber}")]
        public async Task<ActionResult<IEnumerable<MobileRechargeProductDtoV2>>> GetProductsV2(Guid beneficiaryId, int? pageNumber = null)
        {
            await this.GetLoggedInUser();

            bool rechargeForMyself = beneficiaryId == _user.Id;

            var productList = await _mobileRechargeService.GetProductsV2(beneficiaryId, pageNumber, MobileApplicationId.C3Pay, _user.CardHolder.CorporateId, _user.CardHolderId, _user.Id, rechargeForMyself);

            var productlistResources = _mapper.Map<IEnumerable<MobileRechargeProduct>, IEnumerable<MobileRechargeProductDtoV2>>(productList.Data);

            return Ok(productlistResources);
        }

        /// <summary>
        /// Possible Results:
        /// ProductCodeNotExists
        /// ProductNotExists
        /// DingConnectionIssue
        /// </summary>
        /// <param name="productCode"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("product-estimate-rate/{productCode}")]
        public async Task<ActionResult<ProductEstimatePriceResponseDto>> GetProductEstimateRate(string productCode)
        {
            if (string.IsNullOrEmpty(productCode))
            {
                return BadRequest(RechargeStatusValidationMessage.ProductCodeNotExists.ToString());
            }

            await this.GetLoggedInUser();

            var tryGetEstimateRate = await _mobileRechargeService.GetProductEstimateRate(productCode, this._user.CardHolder.CorporateId, MobileApplicationId.C3Pay);
            if (tryGetEstimateRate.IsSuccessful == false)
            {
                return BadRequest(tryGetEstimateRate.ErrorMessage);
            }

            var estimateRate = _mapper.Map<ProductEstimatePriceResponseDto>(tryGetEstimateRate.Data);
            return Ok(estimateRate);
        }

        /// <summary>
        /// Possible results:  OperatorNotExists,ProductNotExists
        /// </summary>
        /// <param name="operatorName"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("products/international-calling-cards")]
        public async Task<ActionResult<IEnumerable<MobileRechargeProductDto>>> GetInternationalCallingCards(string? operatorName = null)
        {
            if (!string.IsNullOrEmpty(operatorName) && operatorName.ToUpper() != EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CallingCardOperator.FUAE) && operatorName.ToUpper() != EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CallingCardOperator.HAAE))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.OperatorNotExists.ToString());

            if (!string.IsNullOrEmpty(operatorName))
                operatorName = operatorName.ToUpper();

            await this.GetLoggedInUser();
            var productlist = await _mobileRechargeService.GetCallingCardProducts(operatorName, MobileApplicationId.C3Pay, this._user.CardHolder.CorporateId);

            if (productlist.Data == null || !productlist.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }
            var productlistResources = _mapper.Map<IEnumerable<MobileRechargeProduct>, IEnumerable<MobileRechargeProductDto>>(productlist.Data);
            return Ok(productlistResources);
        }

        /// <summary>
        /// Get Local CallingCards - API - C3Pay
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet("products/local-calling-cards")]
        [ProducesResponseType(typeof(IEnumerable<MobileRechargeProductDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetLocalCallingCards(CancellationToken cancellationToken)
        {
            // Get Logged in User Info 
            var products = await _mobileRechargeService.GetLocalCallingCardProductsAsync(cancellationToken);
            var productDtos = _mapper.Map<IEnumerable<MobileRechargeProduct>, IEnumerable<MobileRechargeProductDto>>(products);
            return Ok(productDtos);
        }

        /// <summary>
        /// Possible results:  BeneficiaryNotExists,ExceedNicknameLength,FullNameNotExists,CountryNotExists,phoneNumberNotExists,RechargeTypeNotExists,UserNotExists,BeneficiaryAlreadyExists,InvalidPhoneNumber,CountryNotSupported,ProviderNotExistsforPhoneNumber,
        /// UserBlocked
        /// </summary>
        /// <param name="requestobject"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("beneficiary")]
        public async Task<ActionResult<PostMobileRechargeBeneficiaryResponseDto>> AddMobileRechargeBeneficiary(PostMobileRechargeBeneficiaryRequestDto requestobject)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(requestobject.UserId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            if (string.IsNullOrEmpty(requestobject.Beneficiary.FullName))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.FullNameNotExists.ToString());
            else if (string.IsNullOrEmpty(requestobject.Beneficiary.PhoneNumber))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.phoneNumberNotExists.ToString());
            else if (string.IsNullOrEmpty(requestobject.Beneficiary.CountryCode))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.CountryNotExists.ToString());
            else if (string.IsNullOrEmpty(requestobject.Beneficiary.RechargeType))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());

            if (requestobject.Beneficiary.RechargeType.ToUpper() != BaseEnums.MobileRechargeType.LOCAL.ToString() &&
                requestobject.Beneficiary.RechargeType.ToUpper() != BaseEnums.MobileRechargeType.INTERNATIONAL.ToString()
                )
            {
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());
            }

            var newbeneficiaryResources = _mapper.Map<PostMobileRechargeBeneficiaryRequestDto, MobileRechargeBeneficiary>(requestobject);

            var mtbeneficiaryall = await _mobileRechargeService.AddMobileRechargeBeneficiary(newbeneficiaryResources,
                requestobject.Beneficiary.SelectedProviderCode);

            if (!mtbeneficiaryall.IsSuccessful)
                return BadRequest(mtbeneficiaryall.ErrorMessage);

            var mtbeneficiaryResources = _mapper.Map<MobileRechargeBeneficiary, MobileRechargeBeneficiarieyDto>(mtbeneficiaryall.Data);

            return Ok(mtbeneficiaryResources);
        }

        /// <summary>
        /// Possible resuls : If status is Failed,  Remarks will have validation,Ding Error messages
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="rechargeType"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("beneficiary/{userId}")]
        public async Task<ActionResult<IEnumerable<MobileRechargeBeneficiarieyDto>>> GetMobileRechargeBeneficiaries(Guid userId, string? rechargeType = null)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            if (!string.IsNullOrEmpty(rechargeType) && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.LOCAL.ToString()
                 && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.INTERNATIONAL.ToString()
                 && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.MYNUMBER.ToString())
            {
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());
            }


            var mtbeneficiarylist = await _mobileRechargeService.GetMobileRechargeBeneficiaries(userId, rechargeType);

            if (mtbeneficiarylist.Data == null || !mtbeneficiarylist.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }

            var mtbeneficiaryResources = _mapper.Map<List<MobileRechargeBeneficiaryDetails>, List<MobileRechargeBeneficiarieyDto>>(mtbeneficiarylist.Data);

            return Ok(mtbeneficiaryResources);
        }

        /// <summary>
        /// Possible results:  BeneficiaryNotExists,TransactionIsInProgress
        /// </summary>
        /// <param name="beneficiaryId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpDelete("beneficiary/{beneficiaryId}")]
        public async Task<ActionResult> DeleteMobileRechargeBeneficiary(Guid beneficiaryId)
        {
            var securityResult = await this._securityService.UsernameMatchesUserMobileRechargeBeneficiary(beneficiaryId, false);

            if (!securityResult.Data.HasValue)
            {
                return this.Unauthorized();
            }

            var deletebeneificary = await _mobileRechargeService.DeleteMobileRechargeBeneficiary(beneficiaryId);

            if (!deletebeneificary.IsSuccessful)
            {
                return BadRequest(deletebeneificary.ErrorMessage);
            }

            return Ok();
        }

        /// <summary>
        /// Possible results:  BeneficiaryNotExists,,UserNotExists,InvalidBeneficiaryForCallingCards,ProductNotExists,InvalidCardNumber,InvalidCardSerialNumber,RechargeAmountLimitReachedWithoutEmiratesId
        /// SendAmountNotExists,SendCurrencyNotExists,ReceiveAmountNotExists,ReceiveCurrencyNotExists, RechargeAmountMonthlyLimitReached, DingResponseFailed,InvalidRechargeTransaction,ActivateYourCard,UnblockYourCard,InsufficientBalance,PPSConnectionIssue  
        /// </summary>
        /// <param name="requestobject"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("send-transfer")]
        public async Task<ActionResult<PostMobileRechargeResponseDto>> SendMobileRechargeTransfer(PostMobileRechargeRequestDto requestobject)
        {
            await this.GetLoggedInUser();
            if (requestobject.BeneficiaryId.HasValue && requestobject.BeneficiaryId.Value != requestobject.UserId)
            {
                var securityResult = await this._securityService.UsernameMatchesUserMobileRechargeBeneficiary(requestobject.BeneficiaryId.Value, false);

                if (!securityResult.Data.HasValue || (securityResult.Data.HasValue && securityResult.Data.Value != requestobject.UserId))
                {
                    return this.Unauthorized();
                }
            }
            else
            {
                var usernameMatchesUser = await this._securityService.UsernameMatchesUser(requestobject.UserId, null, false);

                if (!usernameMatchesUser.Data)
                {
                    return this.Unauthorized();
                }
            }

            if (string.IsNullOrEmpty(requestobject.ProductCode))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.ProductCodeNotExists.ToString());
            else if (requestobject.SendValue.Amount == 0)
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.SendAmountNotExists.ToString());
            else if (string.IsNullOrEmpty(requestobject.SendValue.Currency))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.SendCurrencyNotExists.ToString());
            else if (requestobject.ReceiveValue.Amount == 0)
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.ReceiveAmountNotExists.ToString());
            else if (string.IsNullOrEmpty(requestobject.ReceiveValue.Currency))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.ReceiveCurrencyNotExists.ToString());

            var mobilerechargetransferresources = _mapper.Map<PostMobileRechargeRequestDto, MobileRechargeTransaction>(requestobject);

            if (mobilerechargetransferresources.BeneficiaryId.HasValue && mobilerechargetransferresources.BeneficiaryId.Value == mobilerechargetransferresources.UserId)
            {
                await this.GetLoggedInUser();
            }

            var mobilerechargeresult = await _mobileRechargeService.SendTransfer(mobilerechargetransferresources, BaseEnums.MobileApplicationId.C3Pay, this._user?.PhoneNumber, this._user.CardHolder.CorporateId);

            if (!mobilerechargeresult.IsSuccessful)
                return BadRequest(mobilerechargeresult.ErrorMessage);

            var mobilerechargeresponse = _mapper.Map<CallingCardResponseDingModel, PostMobileRechargeResponseDto>(mobilerechargeresult.Data);

            return Ok(mobilerechargeresponse);
        }

        /// <summary>
        /// Possible resuls : If status is Failed,  Remarks will have validation,Ding Error messages 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="rechargeType"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("transactions/{userId}")]
        public async Task<ActionResult<IEnumerable<MobileRechargeDto>>> GetUserMobileRechargeTransactions(Guid userId, string? rechargeType = null, int? pageSize = null, int? pageNumber = null)
        {
            var usernameMatchesUser = await this._securityService.UsernameMatchesUser(userId, null, false);

            if (!usernameMatchesUser.Data)
            {
                return this.Unauthorized();
            }

            else if (!string.IsNullOrEmpty(rechargeType) && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.LOCAL.ToString()
                && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.INTERNATIONAL.ToString()
                && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.MYNUMBER.ToString() && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.CALLINGCARDS.ToString())
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());

            var transactionlist = await _mobileRechargeService.GetUserMobileRechargeTransactions(userId, rechargeType, pageSize, pageNumber);

            if (!transactionlist.IsSuccessful)
                return BadRequest(transactionlist.ErrorMessage);

            if (transactionlist.Data == null || !transactionlist.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }

            var transactionlistResources = _mapper.Map<IEnumerable<MobileRechargeDetails>, IEnumerable<MobileRechargeDto>>(transactionlist.Data);
            return Ok(transactionlistResources);
        }

        /// <summary>
        /// Possible resuls : If status is Failed,  Remarks will have validation,Ding Error messages
        /// </summary>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("transaction-receipt/{transactionId}")]
        public async Task<ActionResult<MobileRechargeDto>> GetMobileRechargeTransactionReceipt(Guid transactionId)
        {
            var securityResult = await this._securityService.UsernameMatchesUserMobileRechargeTransaction(transactionId, false);

            if (!securityResult.Data.HasValue)
            {
                return this.Unauthorized();
            }

            await GetLoggedInUser();

            if (_user == null) return Unauthorized("");

            var transaction = await _mobileRechargeService.GetMobileRechargeTransactionReceipt(transactionId);

            if (!transaction.IsSuccessful)
                return BadRequest(transaction.ErrorMessage);

            if (transaction.Data == null)
            {
                return Ok(Enumerable.Empty<string>());
            }

            var transactionlistResources = _mapper.Map<MobileRechargeTransaction, MobileRechargeDto>(transaction.Data);

            if (transactionlistResources.Status == "PENDING")
            {
                transactionlistResources.VideoDetails = await GetVideoUrls(_user, "MobileRecharge_PendingRetrialVideo");
            }

            return Ok(transactionlistResources);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="rechargeType"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("recent-transactions/{userId}")]
        public async Task<ActionResult<IEnumerable<MobileRechargeDto>>> GetRecentMobileRechargeTransactions(Guid userId, string rechargeType = null, int? count = null)
        {
            var doesMatch = await this._securityService.UsernameMatchesUser(userId, null, false);
            if (doesMatch.Data == false)
            {
                return this.Unauthorized();
            }

            if (string.IsNullOrEmpty(rechargeType))
            {
                return BadRequest(RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());
            }

            if (Enum.TryParse(rechargeType, true, out MobileRechargeType mobileRechargeType) == false)
            {
                return BadRequest(RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());
            }

            var tryGetRecentTransactions = await _mobileRechargeService.GetRecentMobileRechargeTransactions(userId, mobileRechargeType, count);
            if (tryGetRecentTransactions.IsSuccessful == false)
            {
                return BadRequest(tryGetRecentTransactions.ErrorMessage);
            }

            var transactionlistResources = _mapper.Map<IEnumerable<MobileRechargeDetails>, IEnumerable<MobileRechargeDto>>(tryGetRecentTransactions.Data);
            return Ok(transactionlistResources);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("repeat-transfer/{transactionId}")]
        public async Task<ActionResult<MobileRechargeDto>> RepeatMobileRechargeTransfer(Guid transactionId)
        {
            var doesMatch = await this._securityService.UsernameMatchesUserMobileRechargeTransaction(transactionId, false);
            if (doesMatch.Data.HasValue == false)
            {
                return this.Unauthorized();
            }

            await this.GetLoggedInUser();

            var trySendTransfer = await _mobileRechargeService.RepeatTransfer(transactionId, MobileApplicationId.C3Pay, this._user?.PhoneNumber);
            if (trySendTransfer.IsSuccessful == false)
            {
                return BadRequest(trySendTransfer.ErrorMessage);
            }

            var result = _mapper.Map<CallingCardResponseDingModel, PostMobileRechargeResponseDto>(trySendTransfer.Data);
            return Ok(result);
        }


        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("trial-promotions")]
        public async Task<ActionResult<MobileRechargeTrialPromotionDto>> GetTrialPromotions()
        {
            await GetLoggedInUser();

            if (_user == null) return Unauthorized("");

            var response = await _lookupService.GetMultimediaResources(feature: (int)FeatureType.MobileRecharge_Promotion).ConfigureAwait(false);
            if (response.IsSuccessful)
            {
                /* if  _user.CardHolder.Nationality is equal to any of one response.Data NationalityCode then return that else return  MobileRechargeTrialPromotionDto with PromotionImageUrl as empty */
                if (response.Data.Any() && _user.CardHolder.Nationality == response.Data.FirstOrDefault().NationalityCode)
                {
                    return Ok(new MobileRechargeTrialPromotionDto()
                    {
                        PromotionImageUrl = response.Data.FirstOrDefault().Url
                    });
                }
            }

            return Ok(new MobileRechargeTrialPromotionDto());
        }


        /// <summary>
        /// Get Summary Details - For Local, International & Calling Cards
        /// </summary>
        /// <returns></returns>
        [Authorize]
        //[DeviceAuthorize]
        [HttpGet("summary-details/{productCode}")]
        public async Task<ActionResult<MobileRechargeSummaryDto>> GetSummaryDetails(string productCode)
        {
            if (string.IsNullOrEmpty(productCode))
            {
                return BadRequest(RechargeStatusValidationMessage.ProductCodeNotExists.ToString());
            }
            await GetLoggedInUser();
            if (_user == null) return this.Unauthorized("");

            var summaryResponse = await _mobileRechargeService.GetSummary(_user, productCode, BaseEnums.MobileApplicationId.C3Pay,
                new MobileRechargeTransaction());

            if (!summaryResponse.IsSuccessful)
                return BadRequest(summaryResponse.ErrorMessage);

            var mobilerechargeresponse = _mapper.Map<MobileRechargeTransaction, MobileRechargeSummaryDto>(summaryResponse.Data);

            var isAutoRenewalEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableAutoRenewal);
            if (isAutoRenewalEnabled && mobilerechargeresponse.RenewalInfo.IsEligibleForRenewal)
            {
                if ((mobilerechargeresponse?.TargetedDiscount?.DiscountAmountValue?.Amount ?? 0.0) != 0.0)
                {
                    //Renewal with Targeted Discount Video
                    mobilerechargeresponse.RenewalInfo.RenewalVideo = await GetVideoUrls(_user, "TargetedDiscountRenewalVideo");
                }
                else
                {
                    //Default Renewal Video
                    mobilerechargeresponse.RenewalInfo.RenewalVideo = await GetVideoUrls(_user, "RenewalVideo");
                }
            }
            else
            {
                mobilerechargeresponse.RenewalInfo = new MobileRechargeRenewalInfo
                {
                    IsEligibleForRenewal = false
                };
            }

            return Ok(mobilerechargeresponse);
        }

        [Authorize]
        //[DeviceAuthorize]
        [HttpPost("send-transfer-v2")]
        public async Task<ActionResult<PostMobileRechargeResponseDto>> SendMobileRechargeTransferV2(MobileRechargeRequestDto requestobject)
        {
            await this.GetLoggedInUser();

            if (requestobject.BeneficiaryId.HasValue && requestobject.BeneficiaryId.Value != _user.Id)
            {
                var securityResult = await this._securityService.UsernameMatchesUserMobileRechargeBeneficiary(requestobject.BeneficiaryId.Value, false);

                if (!securityResult.Data.HasValue || (securityResult.Data.HasValue && securityResult.Data.Value != _user.Id))
                {
                    return this.Unauthorized();
                }
            }

            if (string.IsNullOrEmpty(requestobject.ProductCode))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.ProductCodeNotExists.ToString());

            var thismRTransaction = _mapper.Map<MobileRechargeRequestDto, MobileRechargeTransaction>(requestobject);
            thismRTransaction.UserId = _user.Id;

            var isAutoRenewalJobExecution = false;

            var mobilerechargeresult = await _mobileRechargeService.SendTransferV2(_user, thismRTransaction, BaseEnums.MobileApplicationId.C3Pay,
                this._user?.PhoneNumber, requestobject.IsAutoRenewalEnabled, isAutoRenewalJobExecution);

            if (!mobilerechargeresult.IsSuccessful)
                return BadRequest(mobilerechargeresult.ErrorMessage);

            var mobilerechargeresponse = _mapper.Map<CallingCardResponseDingModel, PostMobileRechargeResponseDto>(mobilerechargeresult.Data);

            if (mobilerechargeresponse.Status == "PENDING")
            {
                mobilerechargeresponse.PendingRetrialRechargeVideo = await GetVideoUrls(_user, "MobileRecharge_PendingRetrialVideo");
            }

            return Ok(mobilerechargeresponse);
        }

        [Authorize]
        [HttpPut("update-product-renewal")]
        public async Task<ActionResult> ActivateMobileRechargeRenewal(MobileRechargeRenewalRequestDto requestobject)
        {
            await this.GetLoggedInUser();

            if (requestobject.BeneficiaryId.HasValue && requestobject.BeneficiaryId.Value != _user.Id)
            {
                var securityResult = await this._securityService.UsernameMatchesUserMobileRechargeBeneficiary(requestobject.BeneficiaryId.Value, false);

                if (!securityResult.Data.HasValue || (securityResult.Data.HasValue && securityResult.Data.Value != _user.Id))
                {
                    return this.Unauthorized();
                }
            }

            if (string.IsNullOrEmpty(requestobject.ProductCode))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.ProductCodeNotExists.ToString());

            var result = await _mobileRechargeService.UpdateRenewal(userId: _user.Id,
                                                                    productCode: requestobject.ProductCode,
                                                                    beneficiaryId: requestobject.BeneficiaryId.Value,
                                                                    cardHolderId: _user.CardHolderId,
                                                                    deactivationCode: null,
                                                                    IsActive: requestobject.IsActive,
                                                                    hasClaimedTargetedDiscount: false);

            if (!result.IsSuccessful)
                return BadRequest(result.ErrorMessage);

            return Ok();
        }

        [Authorize]
        [HttpGet("get-all-active-renewals")]
        public async Task<ActionResult<MobileRechargeAutoRenewalDto>> GetAllActiveRenewals()
        {

            await this.GetLoggedInUser();

            if (_user == null)
                return Unauthorized("");

            // Proceed only if Auto-renewal flag is on.
            var isAutoRenewalEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableAutoRenewal);
            if (!isAutoRenewalEnabled)
            {
                _logger.LogInformation($"MobileRechargeAutorenewal: Flag is not enabled. Current user: {_user.Id}");
                return BadRequest();
            }

            // Get all active auto-renewal for the current user.
            var allRenewals = await _mobileRechargeService.GetAllActivateRenewals(_user);
            if (!allRenewals.IsSuccessful)
                return BadRequest(allRenewals.ErrorMessage);


            // Mapping
            var autoRenewalList = _mapper.Map<List<MobileRechargeRenewal>, List<MobileRechargeRenewalDetailDto>>(allRenewals.Data);

            // Setup response and add videos Urls.
            var response = new MobileRechargeAutoRenewalDto
            {
                MobileRechargeRenewals = autoRenewalList,
                Videos = await GetVideoUrls(_user, "MobileRecharge_DeactivateRenewal")
            };

            return Ok(response);

        }

        /// <summary>
        /// Marks the user as targeted discount offered.
        /// Developed to be called through a webhook.
        /// </summary>
        [HttpPost("initiate-discounted-offer")]
        public async Task<ActionResult> InitiateDiscountedOffer(MobileRechargeTargetedOfferUserDto request)
        {
            // Basic Authentication
            string sourceKey = Request.Headers["sourceKey"];
            string basicAuthString = Request.Headers["Authorization"];

            var isAuthenticated = await AuthenticateBasic(basicAuthString, sourceKey);
            if (!isAuthenticated)
                return Unauthorized();

            if (request.profiles is null
                && request.profiles.Count() < 1)
                return BadRequest();

            // Initiate discounted Offer.
            _logger.LogInformation($"Mobile recharge: Initiating Targeted discount offer for the the user. CitizenId: {request.profiles[0].identity}.");
            await _mobileRechargeService.ProcessTargetedDiscount(request.profiles[0].identity, request.profiles[0].phone);

            return Ok();
        }

        /// <summary>
        /// Receives the final status of a deferred send transfer recharge.
        /// Developed to be called through a webhook.
        /// </summary>
        [HttpPost("receive-deferred-recharge-response")]
        public async Task<IActionResult> ReceiveDeferredRechargeResponseFromDing(CancellationToken cancellationToken)
        {
            // Attempt to retrieve the correlation ID from the request headers.
            if (!Request.Headers.TryGetValue("x-correlation-id", out var correlationIds))
            {
                _logger.LogWarning("Correlation ID header 'x-correlation-id' is missing.");
                // Optionally, you can return a BadRequest if the correlation ID is mandatory.
                // return BadRequest(new { message = "Missing correlation ID." });
            }
            string correlationId = correlationIds.FirstOrDefault() ?? "N/A";

            // Get request metadata.
            long contentLength = Request.ContentLength ?? 0;
            string contentType = Request.ContentType ?? "unknown";

            _logger.LogInformation("Received webhook with Correlation ID: {CorrelationId}, Content Length: {ContentLength}, Content Type: {ContentType}",
                                   correlationId, contentLength, contentType);

            // Read the request body.
            string requestBody;
            using (var reader = new System.IO.StreamReader(Request.Body, Encoding.UTF8))
            {
                requestBody = await reader.ReadToEndAsync();
            }

            _logger.LogInformation("Webhook Payload: {Payload}", requestBody);

            try
            {
                // Process the webhook payload.
                var result = await _mobileRechargeService.ReceiveDeferredRechargeResponseFromDing(requestBody, correlationId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing deferred recharge response for Correlation ID: {CorrelationId}", correlationId);
                return StatusCode(StatusCodes.Status500InternalServerError, new { message = "An error occurred while processing the webhook." });
            }

            return Ok(new { message = "Webhook received successfully." });
        }


        #endregion

        #region Private Methods

        private Task<bool> AuthenticateBasic(string basicCredential, string sourceKey)
        {
            try
            {
                if (!string.IsNullOrEmpty(basicCredential)
                    && !string.IsNullOrEmpty(sourceKey))
                {
                    /* for later
                    var credentials = Convert.FromBase64String(basicCredential);
                    var username = credentials[0];
                    var password = credentials[1];
                    */
                    //clevertap:23w$T56^d3d
                    //"Basic Y2xldmVydGFwOjIzdyRUNTZeZDNk"
                    if (basicCredential == "Basic Y2xldmVydGFwOjIzdyRUNTZeZDNk"
                        && sourceKey == "A00rtJVydGFwOjIzdyRUNTL^26KL")
                        return Task.FromResult(true);
                }

                return Task.FromResult(false);
            }
            catch (Exception ex)
            {
                return Task.FromResult(false);
            }
        }

        private async Task<List<MobileRechargeVideoDto>> GetVideoUrls(User _user, string identifier)
        {
            var hindiNationalities = new HashSet<string>
            {
                nameof(BaseEnums.MultimediaCountry.PAK),
                nameof(BaseEnums.MultimediaCountry.IND),
                nameof(BaseEnums.MultimediaCountry.BGD),
                nameof(BaseEnums.MultimediaCountry.NPL)
            };
            var userShouldSeeHindiVideo = hindiNationalities.Contains(_user.CardHolder.Nationality);

            var mobileRechargeRenewalMultimediaResources = await _lookupService
                .GetMultimediaResources(feature: (int)FeatureType.MobileRecharge, identifier: identifier)
                .ConfigureAwait(false);

            // Convert to a list to check count
            var resources = mobileRechargeRenewalMultimediaResources.Data.ToList();

            // If there's only one resource, mark it as default
            if (resources.Count == 1)
            {
                return resources.Select(resource => new MobileRechargeVideoDto
                {
                    LanguageCode = resource.Language,
                    Url = resource.Url,
                    IsDefault = true
                }).ToList();
            }

            bool defaultAlreadySet = false;
            return resources.Select(resource => new MobileRechargeVideoDto
            {
                LanguageCode = resource.Language,
                Url = resource.Url,
                IsDefault = ((resource.Language == "hi" && userShouldSeeHindiVideo)
                                || (resource.Language != "hi" && !userShouldSeeHindiVideo))
                            && !defaultAlreadySet && (defaultAlreadySet = true)
            }).ToList();
        }

        #endregion 
    }
}
