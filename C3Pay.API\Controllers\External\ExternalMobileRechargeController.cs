﻿using AutoMapper;
using C3Pay.API.Models;
using C3Pay.API.Resources.Countries;
using C3Pay.API.Resources.MobileRecharge;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Services;
using C3Pay.Services;
using Edenred.Common.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    [Authorize(AuthenticationSchemes = AuthenticationScheme.DualAuthentication)]
    public class ExternalMobileRechargeController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IMobileRechargeService _mobileRechargeService;
        private readonly ILookupService _lookupService;
        private readonly IUserService _userService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mobileRechargeService"></param>
        /// <param name="mapper"></param>
        /// <param name="lookupService"></param>
        public ExternalMobileRechargeController(IMobileRechargeService mobileRechargeService, IMapper mapper, ILookupService lookupService, IUserService userService)
        {
            this._mapper = mapper;
            this._mobileRechargeService = mobileRechargeService;
            this._lookupService = lookupService;
            this._userService = userService;
        }

        #region Mobile Recharge Module

        /// <summary>
        /// Possible results: UserNotExists,NickNameNotExists,ExceedNicknameLength,BeneficiaryAlreadyExists, InvalidPhoneNumber ,CountryNotSupported
        /// </summary>
        /// <param name="RequestObject"></param>
        /// <returns></returns>
        [HttpPost("beneficiary-details-iseligible")]
        public async Task<ActionResult<bool>> GetRechargeBeneficiaryDetailsEligibility(BeneficiaryDetailsEligibilityRequestDto RequestObject)
        {
            if (string.IsNullOrEmpty(RequestObject.CountryCode))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.CountryNotSupported.ToString());
            else if (string.IsNullOrEmpty(RequestObject.PhoneNumber))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.InvalidPhoneNumber.ToString());
            else if (string.IsNullOrEmpty(RequestObject.NickName))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.NickNameNotExists.ToString());

            var phoneeligiblity = await _mobileRechargeService.GetRechargeBeneficiaryDetailsEligibility(RequestObject.UserId, RequestObject.CountryCode.ToUpper(), RequestObject.PhoneNumber, RequestObject.NickName);

            if (!phoneeligiblity.IsSuccessful)
            {
                return BadRequest(phoneeligiblity.ErrorMessage);
            }

            return Ok(phoneeligiblity.Data);
        }

        /// <summary>
        /// To get the country lists
        /// </summary>
        /// <returns></returns>
        [HttpGet("countries")]
        public async Task<ActionResult<IEnumerable<MobileRechageCountryDto>>> GetCountries()
        {
            var countries = await this._lookupService.GetMobileRechargeCountries();

            if (countries.Data == null || !countries.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }

            var countryResources = _mapper.Map<IEnumerable<Country>, IEnumerable<MobileRechageCountryDto>>(countries.Data);

            return Ok(countryResources);
        }

        /// <summary>
        /// Mobile recharge products
        /// Possible results:  BeneficiaryNotExists,ProductsNotExistsForBeneficiary
        /// <param name="beneficiaryId"></param>
        /// <param name="productType"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>        
        [HttpGet("beneficiary/{beneficiaryId}/products")]
        public async Task<ActionResult<IEnumerable<MobileRechargeProductDto>>> GetProducts(Guid beneficiaryId, string productType = null, int? pageSize = null, int? pageNumber = null)
        {
            var productlist = await _mobileRechargeService.GetProducts(beneficiaryId, productType, pageSize, pageNumber, MobileApplicationId.MySalary, null);

            if (productlist.Data == null || !productlist.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }
            var productlistResources = _mapper.Map<IEnumerable<MobileRechargeProduct>, IEnumerable<MobileRechargeProductDto>>(productlist.Data);
            return Ok(productlistResources);
        }

        /// <summary>
        /// Possible Results:
        /// ProductCodeNotExists
        /// ProductNotExists
        /// DingConnectionIssue
        /// </summary>
        /// <param name="productCode"></param>
        /// <returns></returns>
        [HttpGet("product-estimate-rate/{productCode}")]
        public async Task<ActionResult<ProductEstimatePriceResponseDto>> GetProductEstimateRate(string productCode)
        {
            if (string.IsNullOrEmpty(productCode))
            {
                return BadRequest(RechargeStatusValidationMessage.ProductCodeNotExists.ToString());
            }

            var tryGetEstimateRate = await _mobileRechargeService.GetProductEstimateRate(productCode, null, MobileApplicationId.MySalary);
            if (tryGetEstimateRate.IsSuccessful == false)
            {
                return BadRequest(tryGetEstimateRate.ErrorMessage);
            }

            var estimateRate = _mapper.Map<ProductEstimatePriceResponseDto>(tryGetEstimateRate.Data);
            return Ok(estimateRate);
        }

        /// <summary>
        /// Possible results:  OperatorNotExists,ProductNotExists
        /// </summary>
        /// <param name="operatorName"></param>
        /// <returns></returns>
        [HttpGet("products/international-calling-cards")]
        public async Task<ActionResult<IEnumerable<MobileRechargeProductDto>>> GetInternationalCallingCards(string? operatorName = null)
        {
            string TransferMethod = string.Empty;

            if (!string.IsNullOrEmpty(operatorName) && operatorName.ToUpper() != EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CallingCardOperator.FUAE) && operatorName.ToUpper() != EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CallingCardOperator.HAAE))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.OperatorNotExists.ToString());

            if (!string.IsNullOrEmpty(operatorName))
                operatorName = operatorName.ToUpper();

            var productlist = await _mobileRechargeService.GetCallingCardProducts(operatorName, MobileApplicationId.MySalary, null);

            if (productlist.Data == null || !productlist.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }
            var productlistResources = _mapper.Map<IEnumerable<MobileRechargeProduct>, IEnumerable<MobileRechargeProductDto>>(productlist.Data);
            return Ok(productlistResources);
        }

        /// <summary>
        /// Possible results:  BeneficiaryNotExists,ExceedNicknameLength,FullNameNotExists,CountryNotExists,phoneNumberNotExists,RechargeTypeNotExists,UserNotExists,BeneficiaryAlreadyExists,InvalidPhoneNumber,CountryNotSupported,ProviderNotExistsforPhoneNumber,
        /// UserBlocked
        /// </summary>
        /// <param name="requestobject"></param>
        /// <returns></returns>
        [HttpPost("beneficiary")]
        public async Task<ActionResult<PostMobileRechargeBeneficiaryResponseDto>> AddMobileRechargeBeneficiary(PostMobileRechargeBeneficiaryRequestDto requestobject)
        {
            if (string.IsNullOrEmpty(requestobject.Beneficiary.FullName))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.FullNameNotExists.ToString());
            else if (string.IsNullOrEmpty(requestobject.Beneficiary.PhoneNumber))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.phoneNumberNotExists.ToString());
            else if (string.IsNullOrEmpty(requestobject.Beneficiary.CountryCode))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.CountryNotExists.ToString());
            else if (string.IsNullOrEmpty(requestobject.Beneficiary.RechargeType))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());

            if (requestobject.Beneficiary.RechargeType.ToUpper() != BaseEnums.MobileRechargeType.LOCAL.ToString() &&
                requestobject.Beneficiary.RechargeType.ToUpper() != BaseEnums.MobileRechargeType.INTERNATIONAL.ToString()
                )
            {
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());
            }

            var newbeneficiaryResources = _mapper.Map<PostMobileRechargeBeneficiaryRequestDto, MobileRechargeBeneficiary>(requestobject);

            var mtbeneficiaryall = await _mobileRechargeService.AddMobileRechargeBeneficiary(newbeneficiaryResources, requestobject.Beneficiary.SelectedProviderCode);

            if (!mtbeneficiaryall.IsSuccessful)
                return BadRequest(mtbeneficiaryall.ErrorMessage);

            var mtbeneficiaryResources = _mapper.Map<MobileRechargeBeneficiary, MobileRechargeBeneficiarieyDto>(mtbeneficiaryall.Data);

            return Ok(mtbeneficiaryResources);
        }

        /// <summary>
        /// Possible resuls : If status is Failed,  Remarks will have validation,Ding Error messages
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="rechargeType"></param>
        /// <returns></returns>
        [HttpGet("beneficiary/{userId}")]
        public async Task<ActionResult<IEnumerable<MobileRechargeBeneficiarieyDto>>> GetMobileRechargeBeneficiaries(Guid userId, string? rechargeType = null)
        {
            string TransferMethod = string.Empty;

            if (!string.IsNullOrEmpty(rechargeType) && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.LOCAL.ToString()
                  && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.INTERNATIONAL.ToString()
                  && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.MYNUMBER.ToString())
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());


            var mtbeneficiarylist = await _mobileRechargeService.GetMobileRechargeBeneficiaries(userId, rechargeType);

            if (mtbeneficiarylist.Data == null || !mtbeneficiarylist.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }

            var mtbeneficiaryResources = _mapper.Map<List<MobileRechargeBeneficiaryDetails>, List<MobileRechargeBeneficiarieyDto>>(mtbeneficiarylist.Data);

            return Ok(mtbeneficiaryResources);
        }

        /// <summary>
        /// Possible results:  BeneficiaryNotExists,TransactionIsInProgress
        /// </summary>
        /// <param name="beneficiaryId"></param>
        /// <returns></returns>
        [HttpDelete("beneficiary/{beneficiaryId}")]
        public async Task<ActionResult> DeleteMobileRechargeBeneficiary(Guid beneficiaryId)
        {
            var deletebeneificary = await _mobileRechargeService.DeleteMobileRechargeBeneficiary(beneficiaryId);

            if (!deletebeneificary.IsSuccessful)
            {
                return BadRequest(deletebeneificary.ErrorMessage);
            }

            return Ok();
        }

        /// <summary>
        /// Possible results:  BeneficiaryNotExists,,UserNotExists,InvalidBeneficiaryForCallingCards,ProductNotExists,InvalidCardNumber,InvalidCardSerialNumber,RechargeAmountLimitReachedWithoutEmiratesId
        /// SendAmountNotExists,SendCurrencyNotExists,ReceiveAmountNotExists,ReceiveCurrencyNotExists, RechargeAmountMonthlyLimitReached, DingResponseFailed,InvalidRechargeTransaction,ActivateYourCard,UnblockYourCard,InsufficientBalance,PPSConnectionIssue  
        /// </summary>
        /// <param name="requestobject"></param>
        /// <returns></returns>
        [HttpPost("send-transfer")]
        public async Task<ActionResult<PostMobileRechargeResponseDto>> SendMobileRechargeTransfer(PostMobileRechargeRequestDto requestobject)
        {
            if (string.IsNullOrEmpty(requestobject.ProductCode))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.ProductCodeNotExists.ToString());
            else if (requestobject.SendValue.Amount == 0)
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.SendAmountNotExists.ToString());
            else if (string.IsNullOrEmpty(requestobject.SendValue.Currency))
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.SendCurrencyNotExists.ToString());

            var mobilerechargetransferresources = _mapper.Map<PostMobileRechargeRequestDto, MobileRechargeTransaction>(requestobject);

            var getUserResponse = await _userService.GetUserById(requestobject.UserId);

            if (!getUserResponse.IsSuccessful)
            {
                return this.BadRequest(getUserResponse.Data);
            }  

            var mobilerechargeresult = await _mobileRechargeService.SendTransfer(mobilerechargetransferresources, BaseEnums.MobileApplicationId.MySalary);

            if (!mobilerechargeresult.IsSuccessful)
                return BadRequest(mobilerechargeresult.ErrorMessage);

            var mobilerechargeresponse = _mapper.Map<CallingCardResponseDingModel, PostMobileRechargeResponseDto>(mobilerechargeresult.Data);

            return Ok(mobilerechargeresponse);
        }

        /// <summary>
        /// Possible resuls : If status is Failed,  Remarks will have validation,Ding Error messages 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="rechargeType"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>
        [HttpGet("transactions/{userId}")]
        public async Task<ActionResult<IEnumerable<MobileRechargeDto>>> GetUserMobileRechargeTransactions(Guid userId, string? rechargeType = null, int? pageSize = null, int? pageNumber = null)
        {
            if (!string.IsNullOrEmpty(rechargeType) && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.LOCAL.ToString()
                 && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.INTERNATIONAL.ToString()
                 && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.MYNUMBER.ToString() && rechargeType.ToUpper() != BaseEnums.MobileRechargeType.CALLINGCARDS.ToString())
                return BadRequest(BaseEnums.RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());

            var transactionlist = await _mobileRechargeService.GetUserMobileRechargeTransactions(userId, rechargeType, pageSize, pageNumber);

            if (!transactionlist.IsSuccessful)
                return BadRequest(transactionlist.ErrorMessage);

            if (transactionlist.Data == null || !transactionlist.Data.Any())
            {
                return Ok(Enumerable.Empty<string>());
            }

            var transactionlistResources = _mapper.Map<IEnumerable<MobileRechargeDetails>, IEnumerable<MobileRechargeDto>>(transactionlist.Data);
            return Ok(transactionlistResources);
        }

        /// <summary>
        /// Possible resuls : If status is Failed,  Remarks will have validation,Ding Error messages
        /// </summary>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        [HttpGet("transaction-receipt/{transactionId}")]
        public async Task<ActionResult<MobileRechargeDto>> GetMobileRechargeTransactionReceipt(Guid transactionId)
        {
            var transaction = await _mobileRechargeService.GetMobileRechargeTransactionReceipt(transactionId);

            if (!transaction.IsSuccessful)
                return BadRequest(transaction.ErrorMessage);

            if (transaction.Data == null)
            {
                return Ok(Enumerable.Empty<string>());
            }

            var transactionlistResources = _mapper.Map<MobileRechargeTransaction, MobileRechargeDto>(transaction.Data);

            return Ok(transactionlistResources);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="rechargeType"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet("recent-transactions/{userId}")]
        public async Task<ActionResult<IEnumerable<MobileRechargeDto>>> GetRecentMobileRechargeTransactions(Guid userId, string rechargeType = null, int? count = null)
        {
            if (string.IsNullOrEmpty(rechargeType))
            {
                return BadRequest(RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());
            }

            if (Enum.TryParse(rechargeType, true, out MobileRechargeType mobileRechargeType) == false)
            {
                return BadRequest(RechargeStatusValidationMessage.RechargeTypeNotExists.ToString());
            }

            var tryGetRecentTransactions = await _mobileRechargeService.GetRecentMobileRechargeTransactions(userId, mobileRechargeType, count);
            if (tryGetRecentTransactions.IsSuccessful == false)
            {
                return BadRequest(tryGetRecentTransactions.ErrorMessage);
            }

            var transactionlistResources = _mapper.Map<IEnumerable<MobileRechargeDetails>, IEnumerable<MobileRechargeDto>>(tryGetRecentTransactions.Data);
            return Ok(transactionlistResources);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        [HttpPost("repeat-transfer")]
        public async Task<ActionResult<PostMobileRechargeResponseDto>> RepeatMobileRechargeTransfer(Guid transactionId)
        {
            var trySendTransfer = await _mobileRechargeService.RepeatTransfer(transactionId, MobileApplicationId.MySalary);
            if (trySendTransfer.IsSuccessful == false)
            {
                return BadRequest(trySendTransfer.ErrorMessage);
            }

            var result = _mapper.Map<CallingCardResponseDingModel, PostMobileRechargeResponseDto>(trySendTransfer.Data);
            return Ok(result);
        }

        #endregion
    }
}
