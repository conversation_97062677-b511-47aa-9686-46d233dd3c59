﻿using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace C3Pay.Data.EntityConfigurations.Membership
{
    public class C3PayPlusMembershipExperimentUserConfiguration : IEntityTypeConfiguration<C3PayPlusMembershipExperimentUser>
    {

        public void Configure(EntityTypeBuilder<C3PayPlusMembershipExperimentUser> builder)
        {
            builder.ToTable("C3PayPlusMembershipExperimentUsers");

            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.HasIndex(c => c.CardholderId);
            builder.HasIndex(c => c.CorporateId);
            builder.HasIndex(c => c.IsActive);
        }
    }
}
