﻿namespace C3Pay.Core.Common
{
    public static class FeatureFlags
    {
        public const string RemoveUserIfCardIsDeleted = "Users_RemoveUserIfCardIsDeleted";
        public const string AllowDromantCardsRegistration = "Users_AllowDromantCardsRegistration";
        public const string UserRecheckEmiratesIdDetails = "User_RecheckEmiratesIdDetails";
        public const string UserRecheckPassportExpiryDate = "User_RecheckPassportExpiryDate";
        public const string CardholderUpdateDetails = "Cardholder_UpdateDetails";
        public const string CardholderAllowEmiratesIdAssociatedWithAnotherUser = "Cardholder_AllowEmiratesIdAssociatedWithAnotherUser";
        public const string IdentificationCheckWithAzureIfFirstNameNull = "Identification_CheckWithAzureIfFirstNameNull";
        public const string KycValidateEmiratesIdExpiry = "Kyc_ValidateEmiratesIdExpiry";
        public const string KycEnableBlockingOfExchangeHouseUsers = "Kyc_EnableBlockingOfExchangeHouseUsers";
        public const string KycEnableKycBlockAddition = "Kyc_EnableKycBlockAddition";
        public const string KycEnableInGraceStatus = "Kyc_EnableInGraceStatus";
        public const string MobileRechargeEnableCrossCheckBeneficiary = "MobileRecharge_EnableCrossCheckBeneficiary";
        public const string MobileRechargeEnableCrossCheckBeneficiaryTesting = "MobileRecharge_EnableCrossCheckBeneficiaryTesting";
        public const string MobileRechargeEnableMobileRechargeRetrialTesting = "MobileRecharge_EnableMobileRechargeRetrialTesting";
        public const string MobileRechargeEnableRatesExperiment = "MobileRecharge_EnableRatesExperiment";
        public const string MobileRechargeEnableTargetedDiscount = "MobileRecharge_EnableTargetedDiscounts";
        public const string MobileRechargeEnableRechargeWithDingRetrial = "MobileRecharge_EnableRechargeWithDingRetrial";
        public const string MobileRechargeEnableBestValue = "MobileRecharge_EnableBestValue";
        public const string MoneyTransferEnableUpdatePendingStatusScheduler = "MoneyTransfer_EnableUpdatePendingStatusScheduler";
        public const string MoneyTransferEnableDeletedCardUpdate = "MoneyTransfer_EnableDeletedCardUpdate";
        public const string MoneyTransferEnableDeactivateRmt = "MoneyTransfer_EnableDeactivateRmt";
        public const string MoneyTransferEnableSkipDeactivateRmt = "MoneyTransfer_EnableSkipDeactivateRmt";
        public const string MoneyTransferDisableRepeatTransferCaching = "MoneyTransfer_DisableRepeatTransferCaching";
        public const string MoneyTransferDisableBeneficiaryListCaching = "MoneyTransfer_DisableBeneficiaryListCaching";
        public const string MoneyTransferDisableSettingOnHold = "MoneyTransfer_DisableSettingOnHold";
        public const string MoneyTransferCsQueriesUpdate = "MoneyTransfer_CsQueriesUpdate";
        public const string MoneyTransferEnableReversal = "MoneyTransfer_EnableReversal";
        public const string MoneyTransferEnableMarkAsReversed = "MoneyTransfer_EnableMarkAsReversed";
        public const string MoneyTransferEnableProcessRakStatementScheduler = "MoneyTransfer_EnableProcessRakStatementScheduler";
        public const string MoneyTransferEnableFreeTransferExpiry = "MoneyTransfer_EnableFreeTransferExpiry";
        public const string MoneyTransferNonWUEIDGraceBlockTransaction = "MoneyTransfer_EnableNonWUEIDGraceBlockTransaction";
        public const string MoneyTransferHighlightAccountNumber = "MoneyTransfer_HighlightAccountNumber";
        public const string MoneyTransferEnableStoriesScaling = "MoneyTransfer_EnableStoriesScaling";
        public const string MoneyTransferEnableInstantReversalAllowedPhoneNumbers = "MoneyTransfer_InstantReversalAllowedPhoneNumbers";
        public const string MoneyTransferEnableInstantReversalForRemarks = "MoneyTransfer_EnableInstantReversalForRemarks";
        public const string MoneyTransferEnableComplainceAdditionalFieldsForPAK = "mt_enableplaceofbirth";
        public const string MtValidateTransferMock = "mt_ValidateTransferMock";
        public const string Mt_DisableLoyaltyWaive = "mt_DisableLoyaltyWaive";
        public const string mt_enable_maxsendamount = "mt_enable_maxsendamount";


        public const string MultimediaRefactoring = "Multimedia_Refactoring";
        public const string SecuritySMSAwareness = "SecuritySMS_Awareness";
        public const string GlobalC3PayPlus = "Memberships_GlobalC3PayPlus";
        public const string MoneyTransferAccountNameForIndiaPakistanNepal = "MoneyTransfer_in_pk_np_accountname";
        public const string MoneyTransferUseNewAddBeneficiaryCommand = "MT_UseNewAddBeneficiaryCommand";
        public const string UseNewGetFieldGroupsQuery = "MoneyTransfer_UseNewGetFieldGroupsQuery";
        public const string Memberships_C3PayPlus_CorporateRollout = "Memberships_C3PayPlus_CorporateRollout";
        public const string Memberships_C3PayPlus_ExperimentRollout = "Memberships_C3PayPlus_ExperimentRollout";
        public const string FetchTransactionsFromOrianRollout = "FetchTransactionsFromOrianRollout";

        public const string MoneyTransferEnableRMTProfileNewUpdates = "MoneyTransfer_EnableRMTProfileNewUpdates";
        public const string MoneyTransferEnableDailyRmtProfileStats = "MoneyTransfer_EnableDailyRmtProfileStats";
        public const string MoneyTransferEnableUploadingMissingProfilesAttachments = "MoneyTransfer_EnableUploadingMissingProfilesAttachments";
        public const string MoneyTransferEnableUploadingMissingProfilesAttachmentsSMS = "MoneyTransfer_EnableUploadingMissingProfilesAttachmentsSMS";
        public const string MoneyTransferUS23044 = "MoneyTransfer_US23044";
        public const string BlockEIDsExpiredForMoreThan90Days = "Kyc_BlockEIDsExpiredForMoreThan90Days";
        public const string BlockEIDsUpdateIfDifferentNationality = "Kyc_BlockEIDsUpdateIfDifferentNationality";
        public const string MobileRechargeEnableAutoRenewal = "MobileRecharge_EnableAutoRenewal";
        public const string MobileRechargeEnableAutoRenewalTargetedDiscount = "MobileRecharge_EnableAutoRenewalTargetedDiscount";
        public const string BlockUploadingExistingEmiratesId = "Kyc_BlockUploadingExistingEmiratesId";
        public const string KycUnblockAllCards = "Kyc_UnblockAllCards";
        public const string MFADeviceAuthorize = "MFA_DeviceAuthorize";
        public const string MFADigitalSignature = "MFA_DigitalSignature";
        public const string MFAGetDeviceExistsV2 = "MFA_GetDeviceExistsV2";
        public const string BPFetchBillerPerformanceFixEnabled = "BP_FetchBillerPerformanceFixEnabled";
        public const string C3PayPlus_BERefund = "Memberships_C3PayPlus_BERefund";
        public const string C3PayPlus_SMSRefund = "Memberships_C3PayPlus_SMSRefund";


        #region C3Pay+
        public const string C3PayPlusLuckyDraw = "Memberships_C3PayPlus_LuckyDraw";
        public const string C3PayPlusUnsubscribeFlow = "Memberships_C3PayPlus_UnsubscribeFlow";
        public const string C3PayPlusReverseAtmWithdrawalFee = "Memberships_C3PayPlus_ReverseAtmWithdrawalFee";
        public const string C3PayPlusCompleteBillingForNewMemberships = "Memberships_C3PayPlus_CompleteBillingForNewMemberships";
        public const string C3PayPlusInlineBilling = "Memberships_C3PayPlus_InlineBilling";
        public const string C3PayPlusDownloadLifeInsuranceCertificate = "Memberships_C3PayPlus_DownloadLifeInsuranceCertificate";
        public const string MoneyTransferEnableGoldIncentiveAcquisition = "MoneyTransfer_EnableGoldIncentiveAcquisition";
        public const string C3PayPlusLuckyDrawTicketsGeneration = "Memberships_C3PayPlus_LuckyDrawTicketsGeneration";
        public const string C3PayPlusLucky_ProcessMoneyTransferRefunds = "Memberships_C3PayPlus_ProcessMoneyTransferRefunds";

        public const string C3PayPlus_Event_ProcessAtmWithdrawalRefunds = "Memberships_C3PayPlus_Events_ProcessAtmWithdrawalRefunds";
        public const string Memberships_C3PayPlus_Exp_TargetedDiscounts = "Memberships_C3PayPlus_Exp_TargetedDiscounts";
        public const string Memberships_C3PayPlus_RNWLS = "Memberships_C3PayPlus_Renewals";
        public const string C3PayPlus_TargetedDiscount_PhoneFilter = "C3PayPlus_TargetedDiscount_PhoneFilter";
        public const string Memberships_C3PayPlus_UseNewTargetedDiscountService = "Memberships_C3PayPlus_UseNewTargetedDiscountService";
        #endregion



        public const string LoginVideos = "User_LoginVideos";
        public const string InAppPaymentAuthentication = "PayAuth_InAppAuthentication";
        public const string EnableOddEvenExperiments = "Experiment_EnableOddEven";
        public const string EnableSetControlUsersOnlyExperiments = "Experiment_EnableSetControlUsersOnly";
        public const string SanctionScreeningValidateScreeningStatus = "SanctionScreening_ValidateScreeningStatus";
        public const string EnableRewardSpinTheWheel = "Reward_SpinTheWheel";
        public const string C3PayPlusAutomatedCalls = "Memberships_AutomatedCalls";
        public const string TestVariableFlag = "HR_TestVariable";

        #region Refactoring flags
        public const string UseNewGetMoneyTransferReasonsQuery = "MT_UseNewGetMoneyTransferReasonsQuery";
        public const string UseNewGetBranchesByBankQuery = "MT_UseNewGetBranchesByBank";
        public const string UseNewGetBanksByCountryCodeQuery = "MT_UseNewGetBanksByCountryCode";
        public const string UseNewGetBranchDetailsByIfscCodeQuery = "MT_UseNewGetBranchDetailsByIfscCode";
        public const string UseNewGetUserBeneficiariesQuery = "MT_UseNewGetUserBeneficiariesQuery";
        public const string UseNewGetCorridorsQuery = "MT_UseNewGetCorridorsQuery";
        public const string UseNewGetCountriesQuery = "MT_UseNewGetCountriesQuery";
        public const string UseNewBankBranchQuery = "MT_UseNewBankBranchQuery";
        public const string UseNewApproveBeneficiaryCommand = "MT_UseNewApproveBeneficiaryCommand";
        #endregion

        #region Profile Enhancement flags
        public const string Kyc_AutoDeleteEIDsExpiredForMoreThan90Days = "Kyc_AutoDeleteEIDsExpiredForMoreThan90Days";
        public const string Kyc_DoUpdateAfterReactivateSuccess = "Kyc_DoUpdateAfterReactivateSuccess";
        public const string Kyc_RetryFailedProfiles = "Kyc_RetryFailedProfiles";
        #endregion

        public const string Dashboard_Popup = "Dashboard_Popup";

        public const string MobileRecharge_RetryDueToLowBalance = "MobileRecharge_RetryDueToLowBalance";

        public const string C3P_MockExp = "C3P_MockExp";

        public const string AlerPreferences_GetAlertAlerPreferences = "AlertPreferences_GetAlertAlerPreferences";
        public const string AlerPreferences_UpdateEmailV2 = "AlertPreferences_UpdateEmailV2";
        public const string AlerPreferences_UpdateAlertPreferences = "AlertPreferences_UpdateAlertPreferences";

        public const string SmsPlusFlow = "SmsPlusFlow";

        #region C3 to C3
        public const string C32C3_Validate_OTP_When_Adding_Beneficiary = "C32C3_Validate_OTP_When_Adding_Beneficiary";
        public const string C32C3_Validate_OTP_When_Adding_Beneficiary_Edenred_Card_Only = "C32C3_Validate_OTP_When_Adding_Beneficiary_Edenred_Card_Only";
        #endregion

        public const string Reset_Password_Clear_User_Id = "Reset_Password_Clear_User_Id";

        #region VPN
        public const string Memberships_Vpn_Global = "Memberships_Vpn_Global";
        public const string Memberships_Vpn_Renewals = "Memberships_Vpn_Renewals";
        #endregion

        #region Benefits Shell
        public const string BenefitsShell_Global = "BenefitsShell_Global";
        #endregion

        public const string EnableIdempotency = "Enable_Idempotency";
        public static string MoneyTransferEnableSalaryEventProcessFromTopic = "MT_EnableSalaryEventProcess_FromTopic";
        public const string EnableSpinTheWheelCardHolderExperiment = "SpinTheWheel_CardHolder_TwoDigit_Exp";

        public const string C3PayPlus_LuckyDraw_GrandPrize = "C3PayPlus_LuckyDraw_GrandPrize";


        public const string LoginVideos_SMSMigration = "LoginVideos_SMSMigration";

        public const string MT_OtpValidation_OnlyFirstTransfer = "Enable_OtpValidation_OnlyFirstTransfer";
    }

    public static class C3PayPlusFeatures
    {
        public const string RenewC3PayPlusMemberships = "C3PayPlus_RenewC3PayPlusMemberships";
    }
}
