﻿using C3Pay.Core;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Serilog.Context;
using System.Threading.Tasks;
using System.Diagnostics;

namespace C3Pay.API.Middleware
{
    public class TelemetryUserIdMiddleware
    {
        private readonly RequestDelegate _next;

        public TelemetryUserIdMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var activity = Activity.Current;

            if (activity != null && context.User.FindFirst(ConstantParam.Username) != null)
            {
                var username = context.User.FindFirst(ConstantParam.Username).Value;
                
                // Set user information on the current activity
                activity.SetTag("user.id", username);
                activity.SetTag("user.authenticated", "true");
                
                // Also add to Serilog context for structured logging
                using (LogContext.PushProperty("UserId", username))
                {
                    await _next(context);
                }
            }
            else
            {
                activity?.SetTag("user.authenticated", "false");
                await _next(context);
            }
        }
    }
}
