﻿using System;
using System.Collections.Generic;
using C3Pay.API.Resources.MoneyTransfer;
using C3Pay.Core.Models;

namespace C3Pay.API.Resources.MobileRecharge
{
    public class MobileRechargeDto
    {
        public string Id { get; set; }
        public string BeneficiaryFullName { get; set; }
        public AmountDto RechargeAmount { get; set; }
        public AmountDto FeeAmount { get; set; }
        public AmountDto TotalAmount { get; set; }
        public AmountDto ReceiveAmount { get; set; }
        public string RechargeType { get; set; }
        public string Operator { get; set; }
        public string MobileNumber { get; set; }
        public string SecretCode { get; set; }
        public DateTime Date { get; set; }
        public string Status { get; set; }
        public string Remarks { get; set; }
        public string Logo { get; set; }
        public List<MobileRechargeVideoDto> VideoDetails { get; set; }
    }
}
