﻿using C3Pay.API.Resources.BillPayment;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay;
using C3Pay.Core.Services.C3Pay.UnEmpInsurance;
using Edenred.Common.Core;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [ApiController]
    public class DevToolsController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly IIdentityService _identityService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMobileRechargeService _mobileRechargeService;
        private readonly IMoneyTransferService _moneyTransferService;
        private readonly BillPaymentSettings _billPaymentSettings;
        private readonly IStoreService _storeService;
        private readonly IUnemploymentInsuranceService _unemploymentInsuranceService;
        private readonly IUnEmpInsuranceService _unEmpInsuranceService;
        private readonly ILogger _logger;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userService"></param>
        /// <param name="identityService"></param>
        /// <param name="unitOfWork"></param>
        /// <param name="mobileRechargeService"></param>
        /// <param name="moneyTransferService"></param>
        /// <param name="billPaymentSettings"></param>
        /// <param name="storeService"></param>
        public DevToolsController(
            IUserService userService,
            IIdentityService identityService, IUnitOfWork unitOfWork,
            IMobileRechargeService mobileRechargeService,
            IMoneyTransferService moneyTransferService,
            ILogger<DevToolsController> logger,
            IOptions<BillPaymentSettings> billPaymentSettings,
            IStoreService storeService, IUnemploymentInsuranceService unemploymentInsuranceService
            , IUnEmpInsuranceService unEmpInsuranceService)
        {
            this._userService = userService;
            this._identityService = identityService;
            _unitOfWork = unitOfWork;
            _mobileRechargeService = mobileRechargeService;
            _moneyTransferService = moneyTransferService;
            _billPaymentSettings = billPaymentSettings.Value;
            _storeService = storeService;
            _unemploymentInsuranceService = unemploymentInsuranceService;
            _unEmpInsuranceService = unEmpInsuranceService;
            _logger = logger;
        }

        /// <summary>
        /// Status Values: Missing, Pending, Created
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [HttpPost("set-rmt-profile-status/{phoneNumber}/{status}")]
        public async Task<ActionResult> SetRMTProfileStatus(string phoneNumber, string status)
        {
            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber.ToZeroPrefixedPhoneNumber() && !u.IsDeleted);

            if (!Enum.IsDefined(typeof(BaseEnums.MoneyTransferProfileStatus), status))
            {
                return BadRequest();
            }

            var statusEnum = (MoneyTransferProfileStatus)Enum.Parse(typeof(MoneyTransferProfileStatus), status);

            user.MoneyTransferProfileStatus = statusEnum;

            await this._unitOfWork.CommitAsync();

            return Ok();
        }


        [HttpGet("get-bill-payment-base-rate/{currency}/{amount}")]
        public async Task<ActionResult<BillPaymentFxResponseDto[]>> GetBillPaymentFxRates(
            string currency, decimal amount)
        {
            var thisFxRates = await _unitOfWork.BillPaymentDailyFxRates.FindAsync(a => a.SettlementCurrency.ToLower().Trim() ==
             currency.ToLower().Trim());

            var thisAEDFxRate = await _unitOfWork.BillPaymentDailyFxRates.FirstOrDefaultAsync(a => a.SettlementCurrency.ToLower().Trim() ==
            ConstantParam.DefaultCurrency.ToLower());


            if (!(thisFxRates.Any()) || thisAEDFxRate == null)
                return BadRequest("InvalidCurrency");

            BillPaymentFxResponseDto[] result = new BillPaymentFxResponseDto[thisFxRates.Count()];

            for (int i = 0; i < thisFxRates.Count(); i++)
            {
                var baseCurrency = _billPaymentSettings.TransactionEnvironment == BaseEnums.TransactionEnvironment.UAT.ToString() ? "USD" : ConstantParam.DefaultCurrency.ToString();
                if (baseCurrency == "USD")
                {
                    var convertedFxRate = Convert.ToDecimal(thisFxRates[i].FxRate) / Convert.ToDecimal(thisAEDFxRate.FxRate);

                    result[i] = new BillPaymentFxResponseDto()
                    {
                        EquivalentAED = Convert.ToDecimal((amount / convertedFxRate).ToString("N4")),
                        Type = thisFxRates[i].BillerType
                    };
                }
            }
            return result;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="referenceNumber"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [HttpPost("set-rmt-transfer-status/{referenceNumber}/{status}")]
        public async Task<ActionResult> SetRMTTransferStatus(string referenceNumber, string status)
        {
            if (!Enum.IsDefined(typeof(BaseEnums.Status), status.ToUpper()))
            {
                return BadRequest();
            }

            var statusEnum = (Status)Enum.Parse(typeof(Status), status.ToUpper());

            await this._moneyTransferService.ManualStatusUpdate(referenceNumber, statusEnum);

            return Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        [HttpPost("user/{phoneNumber}/orders")]
        public async Task<ActionResult> ClearUserOrders(string phoneNumber)
        {
            var result = await this._storeService.ClearUserOrders(phoneNumber);

            return this.Ok(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        [HttpPost("user/{phoneNumber}/unemployment-insurance")]
        public async Task<ActionResult> ClearUserUnemploymentInsurancePayments(string phoneNumber)
        {
            var result = await this._unemploymentInsuranceService.ClearUserUnemploymentInsurancePayments(phoneNumber);

            return this.Ok(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="citizenId"></param>
        /// <returns></returns>
        [HttpPost("cardholder/{citizenId}/cancel-unemp-insurance")]
        public async Task<ActionResult> CancelUnEmpInsurance (string citizenId)
        {
            var tryGetUsers = await _userService.GetUsersByCitizenIds(new List<string>(){citizenId});

            await _unEmpInsuranceService.CancelInsuranceSubscriptions(tryGetUsers.Data);

            return this.Ok();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="citizenId"></param>
        /// <returns></returns>
        [HttpPost("mysalary/user/{citizenId}/delete")]
        public async Task<ActionResult> DeleteMySalaryAppUser(string citizenId)
        {
            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(u => u.CardHolderId == citizenId && !u.IsDeleted);

            user.MarkAsDeleted();

            await _unitOfWork.CommitAsync();

            return this.Ok();
        }

        [HttpGet("test-logging")]
        public async Task<ActionResult> TestLogging()
        {
            var username = "mahmoud";
            var password = "test";

            _logger.LogInformation("user logged in {username}, password: {password}", username, password);

            _logger.LogInformation("user logged in {0}, password: {1}", username, password);

            return this.Ok();
        }
    }

}
