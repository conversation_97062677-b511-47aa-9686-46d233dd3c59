﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus;
using C3Pay.Core.Models.C3Pay.OutboxMessage;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using C3Pay.Core.Repositories;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay.Membership;
using C3Pay.Services.Helper;
using C3Pay.Services.IntegrationEvents.Out.Enums;
using C3Pay.Services.Membership.Queries;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.ESMO;
using Edenred.Common.Core.Models.Messages.Integration.PPS;
using Edenred.Common.Core.Models.Messages.Integration.PPS.Transaction;
using Edenred.Common.Core.Models.Messages.Integration.Transactions;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Services.Membership.Commands
{
    public class SubscribeToC3PayPlusCommand : IRequest<Result<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>>
    {
        public string UserPhoneNumber { get; set; }
        public C3PayPlusMembershipType C3PayPlusTypeId { get; set; }
        public string UserLocale { get; set; }

        public static Result IsCommandValid(SubscribeToC3PayPlusCommand command)
        {
            if (command is null)
            {
                return Result.Failure(Errors.C3PayPlus.EmptyRequest);
            }

            if (string.IsNullOrWhiteSpace(command.UserPhoneNumber))
            {
                return Result.Failure(Errors.C3PayPlus.NoPhoneNumberSent);
            }

            if (command.C3PayPlusTypeId == C3PayPlusMembershipType.None)
            {
                return Result.Failure(Errors.C3PayPlus.NoTypeIdSent);
            }

            return Result.Success();
        }
    }
    public class SubscribeToC3PayPlusCommandHandler : IRequestHandler<SubscribeToC3PayPlusCommand, Result<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>>
    {
        private readonly ILogger _logger;

        private readonly IFeatureManager _featureManager;
        private readonly IUserRepository _userRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IESMOWebService _esmoWebService;
        private readonly ITransactionsB2CService _transactionsB2CService;
        private readonly IC3PayPlusMembershipNotificationService _notificationService;
        private readonly IC3PayPlusMembershipLookupService _lookupService;
        private readonly IPPSWebAuthService _ppsWebAuthService;
        private readonly IPPSService _ppsService;
        private readonly IPushNotificationSenderService _pushNotificationSenderService;
        private readonly IC3PayPlusTargetedDiscountService _targetedDiscountService;


        public SubscribeToC3PayPlusCommandHandler(ILogger<SubscribeToC3PayPlusCommandHandler> logger,
                                                  IFeatureManager featureManager,
                                                  IUnitOfWork unitOfWork,
                                                  IUserRepository userRepository,
                                                  IESMOWebService esmoWebService,
                                                  ITransactionsB2CService transactionsB2CService,
                                                  IC3PayPlusMembershipNotificationService notificationService,
                                                  IC3PayPlusMembershipLookupService lookupService,
                                                  IPPSWebAuthService ppsWebAuthService,
                                                  IPPSService ppsService,
                                                  IPushNotificationSenderService pushNotificationSenderService,
                                                  IC3PayPlusTargetedDiscountService targetedDiscountService)
        {
            _logger = logger;

            _featureManager = featureManager;
            _unitOfWork = unitOfWork;
            _userRepository = userRepository;
            _esmoWebService = esmoWebService;
            _transactionsB2CService = transactionsB2CService;
            _notificationService = notificationService;
            _lookupService = lookupService;
            _ppsWebAuthService = ppsWebAuthService;
            _ppsService = ppsService;
            _pushNotificationSenderService = pushNotificationSenderService;
            _targetedDiscountService = targetedDiscountService;
        }
        public async Task<Result<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>> Handle(SubscribeToC3PayPlusCommand request, CancellationToken ct)
        {
            try
            {
                // If C3Pay+ is unavailable, exit.
                var isEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.GlobalC3PayPlus);
                if (isEnabled == false)
                {
                    _logger.LogError(Errors.C3PayPlus.Unavailable.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.Unavailable);
                }



                // Validate command.
                var isCommandValid = SubscribeToC3PayPlusCommand.IsCommandValid(request);
                if (isCommandValid.IsFailure)
                {
                    _logger.LogError(isCommandValid.Error.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(isCommandValid.Error);
                }



                // If user is not found, deleted, blocked, or is not a C3Pay app user, exit.
                var user = await this._userRepository.GetUserForC3PayPlus(request.UserPhoneNumber, ct);
                if (user is null)
                {
                    _logger.LogError(Errors.C3PayPlus.UserNotFound.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.UserNotFound);
                }



                // Make sure the user is eligibile to subscribe to C3Pay+.
                var isC3PayPlusEligible = user.IsC3PayPlusEligible();
                if (isC3PayPlusEligible.IsFailure)
                {
                    _logger.LogError(isC3PayPlusEligible.Error.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(isC3PayPlusEligible.Error);
                }



                // Get membership details based on the type.
                var membership = await this._unitOfWork.C3PayPlusMemberships.FirstOrDefaultAsync(x => x.C3PayPlusTypeId == request.C3PayPlusTypeId
                                                                                                      && x.IsAvailable == true,
                                                                                                      include => include.Benefits,
                                                                                                      include => include.Videos);
                if (membership is null)
                {
                    _logger.LogError(Errors.C3PayPlus.NoMembershipFound.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.NoMembershipFound);
                }



                // Check if user has an active subscription.
                var UserSubscription = await this._unitOfWork.C3PayPlusMembershipUsers.FirstOrDefaultAsync(x => x.UserId == user.Id && x.IsActive == true);
                if (UserSubscription != null)
                {
                    // Handle if the user already has a membership subscription.
                    return await HandleExistingMembershipSubscription(membership, UserSubscription);
                }
                else
                {
                    // Handle creation for new membership subscriptions.
                    return await HandleNewMembershipSubscription(user, membership);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                throw;
            }
        }

        private async Task<Result<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>> HandleNewMembershipSubscription(User user, C3PayPlusMembership membership)
        {
            // Make sure that user has an active (non dormant) card.
            var tryCheckIsCardDormant = await this._esmoWebService.IsCardDormant(user.CardHolder.C3RegistrationId);
            if (tryCheckIsCardDormant.IsSuccessful == false)
            {
                // Dormant check has failed. Log error.
                _logger.LogError(Errors.C3PayPlus.C3PayPlusCantPerformDomantCheck.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.C3PayPlusCantPerformDomantCheck);
            }
            if (tryCheckIsCardDormant.Data.IsCardDormant == true)
            {
                _logger.LogError(Errors.C3PayPlus.C3PayPlusNoCardIsDormant.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.C3PayPlusNoCardIsDormant);
            }



            // If the user does not have enough money. We need to check if they had a salary in the last 3 months.
            var tryGetBalance = await this.GetBalance(user.CardHolder);
            if (tryGetBalance.IsSuccessful == false)
            {
                _logger.LogError(Errors.C3PayPlus.CantGetBalance.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.CantGetBalance);
            }
            if (tryGetBalance.Data < membership.ActualDebitPrice)
            {
                var tryCheckingIfUserGotSalaryInLast3Months = await this._esmoWebService.GotSalaryInLast3Months(user.CardHolderId);
                if (tryCheckingIfUserGotSalaryInLast3Months.IsSuccessful == false || tryCheckingIfUserGotSalaryInLast3Months.Data.IsSuccessful == false)
                {
                    _logger.LogError(Errors.C3PayPlus.CantCheckIfUserGotSalary.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.CantCheckIfUserGotSalary);
                }

                if (tryCheckingIfUserGotSalaryInLast3Months.Data.GotSalary == false)
                {
                    _logger.LogError(Errors.C3PayPlus.UserDidNotGetSalary.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.UserDidNotGetSalary);
                }
            }



            // Make sure we have not debited the user before.
            var tryGetC3PayPlusTransactions = await this._transactionsB2CService.GetC3PayPlusTransactions(new GetC3PayPlusTransactionsRequest()
            {
                CardholderId = user.CardHolderId
            });
            if (tryGetC3PayPlusTransactions.IsSuccessful == false)
            {
                _logger.LogError(Errors.C3PayPlus.CantGetC3PayPlusTransactions.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.CantGetC3PayPlusTransactions);
            }

            // Check if the user has subscribed to Balance Enquiry.
            var hasBalanceEnquirySubscription = false;
            var userBalanceEnquirySubscription = await this._unitOfWork.UserSubscriptions.FirstOrDefaultAsync(x => x.Subscription.Code == SMSSubscriptionType.BE.ToString()
            && x.UserId == user.Id && x.EndDate.HasValue == false);

            if (userBalanceEnquirySubscription != null)
            {
                hasBalanceEnquirySubscription = true;
            }



            // Check if the user has subscribed to Security SMS and/or salary alerts.
            var hasSecuritySmsSubscription = false;
            var hasSalaryAlertSmsSubscription = false;
            var tryGetSMSSubscriptionMode = await _esmoWebService.GetSMSSubscriptionMode(new GetSMSSubscriptionModeRequest()
            {
                CardSerialNo = user.CardHolder.CardSerialNumber,
                CorporateId = int.Parse(user.CardHolder.CorporateId)
            });
            if (tryGetSMSSubscriptionMode.IsSuccessful == false)
            {
                _logger.LogError(Errors.C3PayPlus.CantGetEsmoSubscriptionDetails.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.CantGetEsmoSubscriptionDetails);
            }

            hasSecuritySmsSubscription = tryGetSMSSubscriptionMode.Data.SubscribedToSecurityAlerts;
            hasSalaryAlertSmsSubscription = tryGetSMSSubscriptionMode.Data.SubscribedToSalaryAlert;



            // Generate ticket number.
            // The ticket number will be in this format: XX1111. So 2 letters followed by 4 numbers.
            var ticketNumber = this.GenerateLuckyDrawTicketNumber();
            while (await this._unitOfWork.C3PayPlusMembershipUsers.Any(t => t.TicketNumber == ticketNumber))
            {
                ticketNumber = this.GenerateLuckyDrawTicketNumber();
            }

            // For billing, we need to do the following:
            // 1- Check if the user is in a discount experiment. If not => continue as normal.
            // Else, check if experiment is still available and user was already billed for old memberships.
            // 2- If part of a discount experiment and was billed for old memberships => Do not bill the user for C3Pay+, do not refund.
            // 3- If part of a discount experiment and was not billed for old memberships =>  Do not bill the user for C3Pay+, bill for those memberships.
            // 4- Check if user has active targeted discount. If so => Do not bill the user for C3Pay+.
            var inDiscountExperiment = await this.CheckIfInDiscountExperiment(user);
            
            // Check if user has an active targeted discount offer
            bool hasActiveTargetedDiscount = false;
            C3PayPlusTargetedDiscountOffer activeTargetedDiscountOffer = null;

            activeTargetedDiscountOffer = await this._unitOfWork.C3PayPlusTargetedDiscountOffers.FirstOrDefaultAsync(
                x => x.UserId == user.Id && 
                     x.IsActive == true && 
                     x.OfferEndDate > DateTime.Now && 
                     !x.HasExpired && 
                     !x.WasUsed);

            if (activeTargetedDiscountOffer != null)
            {
                hasActiveTargetedDiscount = true;
            }

            if (inDiscountExperiment.IsSuccessful == false && !hasActiveTargetedDiscount)
            {
                // Not in experiment and no active targeted discount.
                // Initial billing for the user.
                var referenceNumber = await this.GenerateBillingReferenceNumber();
                var tryBillUserForC3PayPlus = await this.DecreaseBalance(user, referenceNumber);
                if (tryBillUserForC3PayPlus.IsSuccessful == false)
                {
                    _logger.LogError(Errors.C3PayPlus.CantPerformDebitInline.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.CantPerformDebitInline);
                }
            }
            else
            {
                // Here, we will not do the initial billing (user is in experiment OR has active targeted discount).
            }



            // At this point, we can create the membership subscription.
            var newSubscription = new C3PayPlusMembershipUser(user.Id,
                membership.Id, hasBalanceEnquirySubscription, hasSecuritySmsSubscription,
                hasSalaryAlertSmsSubscription, ticketNumber);

            await this._unitOfWork.C3PayPlusMembershipUsers.AddAsync(newSubscription);

            var outboxMessage = new OutboxMessage
            {
                Type = OutboxMessageTypeEnum.C3PayPlusUserSubscribedEvent.ToString(),
                Data = JsonConvert.SerializeObject(new
                {
                    C3EmployeeRegistrationId = user.CardHolder.C3RegistrationId,
                    CitizenId = user.CardHolderId,
                    Action = "UserSubscribedToC3PayPlusMembership",
                    Timestamp = newSubscription.CreatedDate
                }),
                CreatedDate = DateTime.Now
            };
            await _unitOfWork.OutboxMessages.AddAsync(outboxMessage);


            await this._unitOfWork.CommitAsync();

            // Mark targeted discount offer as used if applicable
            if (hasActiveTargetedDiscount && activeTargetedDiscountOffer != null)
            {
                activeTargetedDiscountOffer.WasUsed = true;
                activeTargetedDiscountOffer.IsActive = false;
                await this._unitOfWork.CommitAsync();

                _logger.LogInformation("Marked targeted discount offer {OfferId} as used for user {UserId} subscription",
                    activeTargetedDiscountOffer.Id, user.Id);
            }



            // Find user again, this is a temp solution.
            user = await this._userRepository.GetUserForC3PayPlus(user.PhoneNumber, default);
            if (user is null)
            {
                _logger.LogError(Errors.C3PayPlus.UserNotFound.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.UserNotFound);
            }


            // Get old membership billings.
            var performedBalanceEnquiryTransaction = false;
            var performedSmsTransaction = false;

            var tryGetSubscriptionTransactions = await this._transactionsB2CService.GetSubscriptionTransactions(new GetSubscriptionTransactionsRequest()
            {
                CardholderId = user.CardHolderId,
                Date = DateTime.Now
            });
            if (tryGetSubscriptionTransactions.IsSuccessful == false)
            {
                newSubscription.SubscriptionCreationRemarks = "Get subscription transaction has failed.";
                await this._unitOfWork.CommitAsync();

                _logger.LogError(Errors.C3PayPlus.CantGetSubscriptionsTransactions.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.CantGetSubscriptionsTransactions);
            }
            else
            {
                if (tryGetSubscriptionTransactions != null && tryGetSubscriptionTransactions.Data != null)
                {
                    performedBalanceEnquiryTransaction = tryGetSubscriptionTransactions.Data.PerformedBalanceEnquiryTransaction;
                    performedSmsTransaction = tryGetSubscriptionTransactions.Data.PerformedSecuritySmsTransaction;
                }
            }


            // Next, we need to update ESMO subscription.
            var trySubscribeToSmsNotification = await this._esmoWebService.SubscribeToSMSNotification(new SubscribeToSMSNotificationRequestDto()
            {
                CardSerialNumber = user.CardHolder.CardSerialNumber,
                CorporateId = int.Parse(user.CardHolder.CorporateId),
                NotificationType = SMSSubscriptionType.C3P.ToString(),
                PhoneNumber = user.PhoneNumber,
                SMSFee = 0
            });
            if (trySubscribeToSmsNotification.IsSuccessful == false)
            {
                newSubscription.SubscriptionCreationRemarks = "ESMO subscription updates has failed.";
                await this._unitOfWork.CommitAsync();

                _logger.LogError(Errors.C3PayPlus.EsmoSubscriptionUpdateFailed.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.EsmoSubscriptionUpdateFailed);
            }

            newSubscription.SubscriptionCreationRemarks = "ESMO subscription updates is done. Waiting for BE subscription updates.";
            await this._unitOfWork.CommitAsync();



            // Next, we need to update our DB for BE subscriptions.
            if (userBalanceEnquirySubscription != null)
            {
                // If we already have a record, end the subscription first.
                userBalanceEnquirySubscription.EndDate = DateTime.Now;
            }

            var subscriptions = await _lookupService.GetCachedSubscriptions();
            var balanceEnquirySubscription = subscriptions.FirstOrDefault(x => x.Code == SMSSubscriptionType.BE.ToString());
            var balanceEnquirySubscrption = new UserSubscription()
            {
                UserId = user.Id,
                SubscriptionId = balanceEnquirySubscription.Id,
                StartDate = newSubscription.StartDate,
                C3PayPlusMembershipUser = newSubscription,
            };

            await this._unitOfWork.UserSubscriptions.AddAsync(balanceEnquirySubscrption);
            newSubscription.SubscriptionCreationRemarks = "BE subscription updates is done. Waiting for refunds.";
            await this._unitOfWork.CommitAsync();



            // Refunds.
            if (inDiscountExperiment.IsSuccessful == true || hasActiveTargetedDiscount)
            {
                // If the user is part of the experiment, we need to bill for BE and SMS.
            }
            else
            {
                // Only do refunds if user is not part of discount experiment.
                // If we have subscription transactions, we need to make sure that we refund the user for those transactions.
                if (performedBalanceEnquiryTransaction == true)
                {
                    // Check if we have already refunded the user for balance enquiry. This should never happen.
                    if (tryGetC3PayPlusTransactions.Data?.BalanceEnquiryRefundTransactions != null
                        && tryGetC3PayPlusTransactions.Data?.BalanceEnquiryRefundTransactions.Count > 0)
                    {
                        newSubscription.SubscriptionCreationRemarks = "Double BE credit detected.";
                        await this._unitOfWork.CommitAsync();

                        _logger.LogError(Errors.C3PayPlus.DoubleCreditInBalanceEnquiryDetected.Code);
                        return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.DoubleCreditInBalanceEnquiryDetected);
                    }


                    // Credit the user back the BE fee.
                    var balanceEnquiryReferenceNumber = await this.GenerateBalanceEnquiryReferenceNumber();

                    // Perform the reversal for balance enquiry fee.
                    var tryReverseBalanceEnquiryTransaction = await this.ReverseBalanceEnquiryTransaction(user.CardHolder, newSubscription, balanceEnquiryReferenceNumber);
                    if (tryReverseBalanceEnquiryTransaction.IsSuccessful == false)
                    {
                        newSubscription.SubscriptionCreationRemarks = "Refund BE has failed.";
                        await this._unitOfWork.CommitAsync();

                        _logger.LogError(Errors.C3PayPlus.CantPerformBalanceEnquiryRefund.Code);
                        return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.CantPerformBalanceEnquiryRefund);
                    }
                    else
                    {
                        // Refund was succesful. Save details and continue;
                        newSubscription.SubscriptionCreationRemarks = "Refund BE is done. Waiting for SMS refund.";
                        await this._unitOfWork.CommitAsync();


                        // Push notification.
                        await _pushNotificationSenderService.SendC3PayPlusBalanceEnquiryRefundNotification(user.DeviceToken);
                    }

                }

                // If we have subscription transactions, we need to make sure that we refund the user for those transactions.
                if (performedSmsTransaction == true)
                {
                    // Check if we have already refunded the user for SMS. This should never happen.
                    // Only perform this check for non-Edenred cardholders.
                    if (tryGetC3PayPlusTransactions.Data?.SmsRefundTransactions != null
                        && tryGetC3PayPlusTransactions.Data?.SmsRefundTransactions.Count > 0)
                    {
                        newSubscription.SubscriptionCreationRemarks = "Double SMS credit detected.";
                        await this._unitOfWork.CommitAsync();

                        _logger.LogError(Errors.C3PayPlus.DoubleCreditInSmsDetected.Code);
                        return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.DoubleCreditInSmsDetected);
                    }


                    // Credit the user back the SMS fee.
                    var smsReferenceNumber = await this.GenerateSmsReferenceNumber();

                    // Perform the reversal for SMS fee.
                    var tryReverseSmsTransaction = await this.ReverseSmsTransaction(user.CardHolder, newSubscription, smsReferenceNumber);
                    if (tryReverseSmsTransaction.IsSuccessful == false)
                    {
                        newSubscription.SubscriptionCreationRemarks = "Refund SMS has failed.";
                        _logger.LogError(Errors.C3PayPlus.CantPerformSmsRefund.Code);
                    }
                    else
                    {
                        // Refund was succesful. Save details and continue.
                        newSubscription.SubscriptionCreationRemarks = "Refund SMS is done. Waiting to send SMS.";

                        await _pushNotificationSenderService.SendC3PayPlusSmsRefundNotification(user.DeviceToken);
                        await this._unitOfWork.CommitAsync();
                    }
                }
            }


            // Send confirmation SMS.
            await _notificationService.SendSubscriptionSms(user.PhoneNumber);

            newSubscription.SubscriptionCreationRemarks = "Membership created successfully.";
            await this._unitOfWork.CommitAsync();

            return Result.Success(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>()
            {
                IsSuccessful = true,
                Data = new C3PayPlusMembershipSubscribeDto()
                {
                    IsRenewal = false
                }
            });
        }

        private async Task<Result<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>> HandleExistingMembershipSubscription(C3PayPlusMembership membership, C3PayPlusMembershipUser userSubscription)
        {
            // If the user already has an active membership and is trying to subscribe to a different membership, exit.
            if (userSubscription.C3PayPlusMembershipId != membership.Id)
            {
                _logger.LogError(Errors.C3PayPlus.MembershipSubscriptionMismatch.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.MembershipSubscriptionMismatch);
            }

            // Undo membership cancellation if the user has canceled before.
            if (userSubscription.UserHasCanceled == true && userSubscription.UserCanceledOn.HasValue == true)
            {
                userSubscription.UndoCancelMembership();
                await this._unitOfWork.CommitAsync();

                return Result.Success(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>()
                {
                    IsSuccessful = true,
                    Data = new C3PayPlusMembershipSubscribeDto()
                    {
                        IsRenewal = true,
                    }
                });
            }
            else
            {
                // The user has to cancel their old membership and wait for it to end before subscribing to a new membership.
                _logger.LogError(Errors.C3PayPlus.CantSubscribe.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>(Errors.C3PayPlus.CantSubscribe);
            }
        }


        public async Task<ServiceResponse<decimal>> GetBalance(CardHolder cardholder)
        {
            var cardNumber = cardholder.CardNumber;
            var cardSerialNumber = cardholder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var request = new BalanceRequest()
            {
                CardPanNumber = cardPanNumber,
                CardSerialNumber = cardSerialNumber,
                Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                ReferenceNumber = TypeUtility.GetReferenceNumber(TransactionPrefix.BAL.ToString(), 12)
            };

            var getCardBalanceResult = await _ppsWebAuthService.GetCardBalance(request);

            if (!getCardBalanceResult.IsSuccessful)
            {
                return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.PPSConnectionIssue.ToString());
            }

            if (getCardBalanceResult.Data.StatusCode != "00")
            {
                if (getCardBalanceResult.Data.Message == EnumUtility.GetDescriptionFromEnumValue(PPSResponseStatus.CARDNOTACTIVATED))
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.ActivateYourCard.ToString());
                }
                else if (getCardBalanceResult.Data.Message == EnumUtility.GetDescriptionFromEnumValue(PPSResponseStatus.MAXPINEXCEEDED))
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.UnblockYourCard.ToString());
                }
                else
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.ErrorGettingBalance.ToString());
                }
            }

            var balance = TypeUtility.GetDecimalFromString(getCardBalanceResult.Data.EndBalanace.Amt) / 100;
            return new ServiceResponse<decimal>(balance);
        }
        private string GenerateLuckyDrawTicketNumber()
        {
            var random = new Random();

            char letter1 = (char)random.Next('A', 'Z' + 1);
            char letter2 = (char)random.Next('A', 'Z' + 1);

            int number = random.Next(1000, 10000); // Generates a 4-digit number.

            return $"{letter1}{letter2}{number:D4}";
        }


        #region Reference number generation
        public async Task<string> GenerateBillingReferenceNumber()
        {
            var referencePrefix = TransactionPrefix.C3P.ToString();

            var dateStamp = DateTime.Now;

            var startYear = dateStamp.Year.ToString().Substring(2);
            var startMonth = dateStamp.Month.ToString();
            var startDay = dateStamp.Day.ToString();

            // We are doing this because we want the date in this format.
            // 1994/08/29 becomes: 940829
            var dateComponent = Convert.ToDecimal(startYear.PadLeft(2, '0') + startMonth.PadLeft(2, '0') + startDay.PadLeft(2, '0'));

            referencePrefix = $"{referencePrefix}{dateComponent}X";
            var referenceDigits = 10;
            var referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            while (await this._unitOfWork.C3PayPlusMembershipTransactions.Any(t => t.ReferenceNumber == referenceNumber))
            {
                referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            }

            return referenceNumber;
        }
        public async Task<string> GenerateBalanceEnquiryReferenceNumber()
        {
            var referencePrefix = TransactionPrefix.C3PBER.ToString();

            // Format start date component.
            var startYear = DateTime.Now.Year.ToString().Substring(2);
            var startMonth = DateTime.Now.Month.ToString();
            var startDay = DateTime.Now.Day.ToString();

            // We are doing this because we want the date in this format.
            // 1994/08/29 becomes: 940829
            var dateComponent = Convert.ToDecimal(startYear.PadLeft(2, '0') + startMonth.PadLeft(2, '0') + startDay.PadLeft(2, '0'));

            referencePrefix = $"{referencePrefix}{dateComponent}X";
            var referenceDigits = 10;
            var referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            while (await this._unitOfWork.C3PayPlusMembershipTransactions.Any(t => t.ReferenceNumber == referenceNumber))
            {
                referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            }

            return referenceNumber;
        }
        public async Task<string> GenerateSmsReferenceNumber()
        {
            var referencePrefix = TransactionPrefix.C3PSMSR.ToString();

            // Format start date component.
            var startYear = DateTime.Now.Year.ToString().Substring(2);
            var startMonth = DateTime.Now.Month.ToString();
            var startDay = DateTime.Now.Day.ToString();

            // We are doing this because we want the date in this format.
            // 1994/08/29 becomes: 940829
            var dateComponent = Convert.ToDecimal(startYear.PadLeft(2, '0') + startMonth.PadLeft(2, '0') + startDay.PadLeft(2, '0'));

            referencePrefix = $"{referencePrefix}{dateComponent}X";
            var referenceDigits = 10;
            var referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            while (await this._unitOfWork.C3PayPlusMembershipTransactions.Any(t => t.ReferenceNumber == referenceNumber))
            {
                referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            }

            return referenceNumber;
        }
        #endregion

        #region Billing methods
        public async Task<ServiceResponse<C3PayPlusMembershipTransaction>> DecreaseBalance(User user, string referenceNumber)
        {
            var cardNumber = user.CardHolder.CardNumber;
            var cardSerialNumber = user.CardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var decreaseBalanceRequest = new PPSDecreaseBalanceRequest()
            {
                accountField = new PPSAccountDetails()
                {
                    accountNoField = user.CardHolder.PpsAccountNumber,
                    cardSerialField = cardSerialNumber,
                    cardPanField = cardPanNumber
                },
                amountField = (long)(10.5 * 100),
                reasonField = "C3Pay+ Monthly Billing",
                schemeRefField = referenceNumber,
                allowNegativeBalance = true
            };

            ServiceResponse tryDecreaseBalance;

            try
            {
                tryDecreaseBalance = await _ppsService.DecreaseBalance(decreaseBalanceRequest, true);
            }
            catch (Exception ex)
            {
                _logger.LogError($"{Errors.C3PayPlus.CantPerformDebit} EX: {ex}");
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlus.CantPerformDebit);
            }

            if (tryDecreaseBalance.IsSuccessful == false)
            {

                _logger.LogError($"{Errors.C3PayPlus.CantPerformDebit} EX: {tryDecreaseBalance.ErrorMessage}");
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlus.CantPerformDebit);
            }

            return new ServiceResponse<C3PayPlusMembershipTransaction>();
        }
        public async Task<ServiceResponse<C3PayPlusMembershipTransaction>> ReverseBalanceEnquiryTransaction(CardHolder cardHolder, C3PayPlusMembershipUser newSubscription, string referenceNumber)
        {
            var cardNumber = cardHolder.CardNumber;
            var cardSerialNumber = cardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);
            var topUpRequest = new TopUpRequest
            {
                CardSerialNumber = cardSerialNumber,

                CardPanNumber = cardPanNumber,
                MerchantLoopCode = "5000",
                Amount = decimal.Truncate(1.05M * 100),

                Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayPlusBESubscriptionFeeReversal),
                Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayPlusBESubscriptionFeeReversal),

                TerminalId = TransactionMerchantCodeService.GetMerchantCode(cardHolder.BelongsToExchangeHouse, MobileApplicationId.C3Pay, TransactionMerchantCodeFeature.C3PayPlusBESubscriptionFeeReversal),
                ReferenceNumber = referenceNumber
            };

            ServiceResponse<PPSWebAuthResponseModel> tryTopUpUser = await _ppsWebAuthService.DoTopUp(topUpRequest);

            if (tryTopUpUser.IsSuccessful == false)
            {
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlus.CantPerformCredit);
            }

            if (tryTopUpUser.Data.StatusCode != "00")
            {
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlus.CantPerformCredit);
            }

            return new ServiceResponse<C3PayPlusMembershipTransaction>();
        }
        public async Task<ServiceResponse<C3PayPlusMembershipTransaction>> ReverseSmsTransaction(CardHolder cardHolder, C3PayPlusMembershipUser newSubscription, string referenceNumber)
        {
            var cardNumber = cardHolder.CardNumber;
            var cardSerialNumber = cardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var topUpRequest = new TopUpRequest()
            {
                CardSerialNumber = cardSerialNumber,
                CardPanNumber = cardPanNumber,
                MerchantLoopCode = "5000",
                Amount = decimal.Truncate(3.15M * 100),

                Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayPlusSMSSubscriptionFeeReversal),
                Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayPlusSMSSubscriptionFeeReversal),

                TerminalId = TransactionMerchantCodeService.GetMerchantCode(cardHolder.BelongsToExchangeHouse, MobileApplicationId.C3Pay, TransactionMerchantCodeFeature.C3PayPlusSMSSubscriptionFeeReversal),
                ReferenceNumber = referenceNumber
            };

            ServiceResponse<PPSWebAuthResponseModel> tryTopUpUser;

            try
            {
                tryTopUpUser = await _ppsWebAuthService.DoTopUp(topUpRequest);
            }
            catch (Exception)
            {
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlus.CantPerformCredit);
            }

            if (tryTopUpUser.IsSuccessful == false)
            {

                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlus.CantPerformCredit);
            }

            if (tryTopUpUser.Data.StatusCode != "00")
            {

                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlus.CantPerformCredit);
            }

            return new ServiceResponse<C3PayPlusMembershipTransaction>();
        }
        #endregion



        private async Task<ServiceResponse> CheckIfInDiscountExperiment(User user)
        {
            var userInDiscountExperiment = await this._unitOfWork.C3PayPlusMembershipExperimentUsers.FirstOrDefaultAsync(x => x.CardholderId == user.CardHolderId
                                                                                                               && x.IsActive == true
                                                                                                               && x.C3PayPlusMembershipExperiment.IsActive == true,
                                                                                                               include => include.C3PayPlusMembershipExperiment);

            if (userInDiscountExperiment is null)
            {
                _logger.LogInformation("User not in discount exp.");
                return new ServiceResponse(false, "User not in discount exp.");
            }



            // Here, we have a discount for this user. We need to check if discount has expired or not.
            // Get and parse experiment data.
            if (string.IsNullOrEmpty(userInDiscountExperiment.C3PayPlusMembershipExperiment.ExperimentJsonData))
            {
                // Fatal error, should seed data.
                _logger.LogError("C3Pay+ discount data not found.");
                return new ServiceResponse(false, "C3Pay+ discount data not found.");
            }


            var experimentData = JsonConvert.DeserializeObject<C3PayPlusMembershipTargetedDiscountExperimentData>(userInDiscountExperiment.C3PayPlusMembershipExperiment.ExperimentJsonData);
            if (experimentData is null)
            {
                // Fatal error, should seed data.
                _logger.LogError("C3Pay+ discount data not found.");
                return new ServiceResponse(false, "C3Pay+ discount data not found.");
            }


            if (string.IsNullOrEmpty(userInDiscountExperiment.ExperimentJsonData))
            {
                // Fatal error, should seed data.
                _logger.LogError("user data for exp not found");
                return new ServiceResponse(false, "user data for exp not found");
            }

            var experimentUserData = JsonConvert.DeserializeObject<C3PayPlusMembershipTargetedDiscountExperimentUserData>(userInDiscountExperiment.ExperimentJsonData);
            if (experimentUserData is null)
            {
                // Fatal error, should seed data.
                _logger.LogError("user data for exp not found");
                return new ServiceResponse(false, "user data for exp not found");
            }


            if (DateTime.Now > experimentUserData.OfferEndDate)
            {
                // Fatal error, should seed data.
                _logger.LogError("offer expired.");
                return new ServiceResponse(false, "offer expired.");
            }

            return new ServiceResponse();
        }
    }
}
