﻿using AutoMapper;
using C3Pay.API.Resources.UnemploymentInsurance;
using C3Pay.Core.Models;
using C3Pay.Core;
using C3Pay.Core.Models.C3Pay.UnEmpInsurance;
using C3Pay.API.Resources.UnEmpInsurance;
using System.Collections.Generic;
using System.Linq;
using System;
using System.Globalization;
using Edenred.Common.Core;
using C3Pay.API.Resources;
using Newtonsoft.Json;

namespace C3Pay.API.Mapping.UnemploymentInsurance
{
    public class UnEmpInsuranceMappingProfile : Profile
    {
        public UnEmpInsuranceMappingProfile()
        {
            CreateMap<UnEmpInsurancePaymentOption, UnEmpInsurancePaymentOptionDto>()
                .ForMember(dest => dest.TotalAmount, opt => opt.MapFrom(src => src.Fee + src.Amount + src.AmountVAT))
                .ForMember(dest => dest.PaymentMode, opt => opt.MapFrom(src => Enum.GetName(typeof(UnEmpInsurancePaymentOptionFromExternal), src.Frequency)))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src =>
                         src.Frequency == UnEmpInsurancePaymentOptionFromExternal.Monthly ? Enum.GetName(typeof(InsurancePaymentOption), InsurancePaymentOption.Monthly)
                         : "Annual")
                         )
                .ForMember(dest => dest.TotalAmountCurrency, opt => opt.MapFrom(_ => ConstantParam.DefaultCurrency));

            CreateMap<UnEmpInsurancePayment, UnEmpInsuranceSubscriptionDetailsDto>()
                .ForMember(dest => dest.SubscriptionId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.User.CardHolder.FirstName + " " + src.User.CardHolder.LastName))
                .ForMember(dest => dest.PhoneNumber, opt => opt.MapFrom(src => $"+ {src.User.PhoneNumber.Substring(2, src.User.PhoneNumber.Length - 2)}"))
                .ForMember(dest => dest.PaymentOption, opt => opt.MapFrom(src => src.Option != null ?
                (src.Option.Frequency == UnEmpInsurancePaymentOptionFromExternal.Annual ?
                     "Annual" :
                    src.Option.Frequency == UnEmpInsurancePaymentOptionFromExternal.Monthly ?
                    Enum.GetName(typeof(InsurancePaymentOption), InsurancePaymentOption.Monthly) :
                    src.Option.Frequency == UnEmpInsurancePaymentOptionFromExternal.SemiAnnual ?
                    Enum.GetName(typeof(InsurancePaymentOption), InsurancePaymentOption.SemiAnnual) :
                    src.Option.Frequency == UnEmpInsurancePaymentOptionFromExternal.Quarterly ?
                    Enum.GetName(typeof(InsurancePaymentOption), InsurancePaymentOption.Quarterly) : String.Empty
                 ) : String.Empty))
                .ForMember(dest => dest.PolicyNumber, opt => opt.MapFrom(src => src.PolicyNumber))
                .ForMember(dest => dest.IsAutoPayment, opt => opt.MapFrom(src => src.IsAutoPayment))
                .ForMember(dest => dest.IsSubscribedExternally, opt => opt.MapFrom(src => src.IsSubscribedExternally))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.ExpiryDate))
                .ForMember(dest => dest.IsFullyPaid, opt => opt.MapFrom(src => src.TotalInstallments == src.PaidInstallments))
                .ForPath(dest => dest.Installment.Total, opt => opt.MapFrom(src => src.TotalInstallments))
                .ForPath(dest => dest.Installment.Paid, opt => opt.MapFrom(src => src.PaidInstallments))
                .ForPath(dest => dest.Installment.NextDueDate, opt => opt.MapFrom(src => src.NextDueDate))
                .ForPath(dest => dest.Installment.NextDueAmount, opt => opt.MapFrom(src => $"{src.TotalAmountCurrency} {src.NextDueAmount + src.Fee}"));

            CreateMap<EnquiryResponseDto, UnEmpInsuranceSubscriptionDetailsDto>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Worker != null ? src.Worker.EmployeeNameEn : string.Empty))
                .ForMember(dest => dest.PaymentOption, opt => opt.MapFrom(src => src.Policy.PaymentSchedule))
                .ForMember(dest => dest.PolicyNumber, opt => opt.MapFrom(src => src.Policy != null ? src.Policy.COI : String.Empty))
                .ForMember(dest => dest.IsAutoPayment, opt => opt.MapFrom(_ => false))
                .ForMember(dest => dest.IsSubscribedExternally, opt => opt.MapFrom(_ => true))
                .ForMember(dest => dest.IsFullyPaid, opt => opt.MapFrom(src => src.TotalInstallments == src.PaidInstallmentPremium))
                .ForPath(dest => dest.Installment.Total, opt => opt.MapFrom(src => src.TotalInstallments))
                .ForPath(dest => dest.Installment.Paid, opt => opt.MapFrom(src => src.Installments.Any() ? src.Installments.Count(a => a.PaymentStatus == "PAID") : 0))
                .ForPath(dest => dest.Installment.NextDueDate, opt => opt.MapFrom(src => MapNextDueDate(src.Installments)))
                .ForPath(dest => dest.Installment.NextDueAmount, opt => opt.MapFrom(src => MapNextDueAmount(src.Installments)))
                .ForMember(dest => dest.PolicyNumber, opt => opt.MapFrom(src => src.Policy.COI))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => DateTime.ParseExact(src.Policy.ExpiryDate, "dd-MM-yyyy", CultureInfo.InvariantCulture)));

            CreateMap<EnquiryResponseDto, UnEmpInsurancePayment>()
                .ForMember(dest => dest.PolicyNumber, opt => opt.MapFrom(src => src.Policy != null ? src.Policy.COI : string.Empty))
                .ForMember(dest => dest.Remarks, opt => opt.MapFrom(src => src.Policy != null ? src.Policy.PaymentSchedule : string.Empty))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => DateTime.ParseExact(src.Policy.ExpiryDate, "dd-MM-yyyy", CultureInfo.InvariantCulture)))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Policy != null && src.HasPolicy == "Yes" ? UnEmpInsuranceStatus.Subscribed : UnEmpInsuranceStatus.InProgress))
                .ForMember(dest => dest.TotalInstallments, opt => opt.MapFrom(src => src.TotalInstallments))
                .ForMember(dest => dest.PaidInstallments, opt => opt.MapFrom(src => src.Installments.Count(a => a.PaymentStatus == "PAID")))
                .ForMember(dest => dest.NextDueAmount, opt => opt.MapFrom(src => src.Installments.Count > 1 ?
                  (
                    src.Installments.Any(a => string.Equals(a.PaymentStatus, "NOT PAID", StringComparison.OrdinalIgnoreCase)) ?
                            src.Installments.FirstOrDefault(a => string.Equals(a.PaymentStatus, "NOT PAID", StringComparison.OrdinalIgnoreCase)).Premium
                            : (decimal?)null
                  )
                 : (decimal?)null))
                 .ForMember(dest => dest.NextDueDate, opt => opt.MapFrom(src => src.Installments.Count > 1 ?
                  (
                    src.Installments.Any(a => string.Equals(a.PaymentStatus, "NOT PAID", StringComparison.OrdinalIgnoreCase)) ?
                            DateTime.ParseExact(src.Installments.FirstOrDefault(a => string.Equals(a.PaymentStatus, "NOT PAID", StringComparison.OrdinalIgnoreCase)).DueDate, "dd-MM-yyyy", CultureInfo.InvariantCulture)
                            : (DateTime?)null
                  )
                 : (DateTime?)null))
                .ForMember(dest => dest.NextDueAmountCurrency, opt => opt.MapFrom(_ => ConstantParam.DefaultCurrency))
                .ForMember(dest => dest.IsAutoPayment, opt => opt.MapFrom(_ => false))
                .ForMember(dest => dest.Source, opt => opt.MapFrom(_ => UnEmpInsuranceSource.C3Pay))
                .ForMember(dest => dest.Category, opt => opt.MapFrom(src => src.Category))
                .ForMember(dest => dest.IsAutoPayment, opt => opt.MapFrom(_ => false))
                .ForMember(dest => dest.LastSyncDate, opt => opt.MapFrom(_ => DateTime.Now))
                .ForMember(dest => dest.InstallmentJson, opt => opt.MapFrom(dest => JsonConvert.SerializeObject(dest.Installments)));

            CreateMap<List<Installment>, UnempInsuranceInstallmentDto>()
                .ForMember(dest => dest.TotalInstallmentsCount, opt => opt.MapFrom(src => src.Count))
                .ForMember(dest => dest.PaidInstallmentsCount, opt => opt.MapFrom(src => src.Count(installment => installment.PaymentStatus == "PAID")))
                .ForMember(dest => dest.RemainingAmount, opt => opt.MapFrom(src =>
                new AmountDto()
                {
                    Amount = src.Any(a => a.PaymentStatus == "NOT PAID") ? Convert.ToDouble(src.Where(installment => installment.PaymentStatus == "NOT PAID").Sum(installment => installment.InstallmentPremium)) : 0,
                    Currency = ConstantParam.DefaultCurrency
                })) 
                .ForMember(dest => dest.Installments, opt => opt.MapFrom(src => src.ConvertAll(installment => new InstallmentDetailsDto
                {
                    DueDate = DateTime.ParseExact(installment.DueDate, "dd-MM-yyyy", CultureInfo.InvariantCulture),
                    DueAmount = new AmountDto { Amount = Convert.ToDouble(installment.Premium + installment.VatAmount), Currency = ConstantParam.DefaultCurrency },
                    Status = GetInstallmentStatus(installment.PaymentStatus, installment.DueDate)
                })))
                .ForMember(dest => dest.OverallStatus, opt => opt.MapFrom((src, dest) =>
                   {
                       if (dest.Installments.Any(installment => installment.Status == EnumUtility.GetDescriptionFromEnumValue(UnEmpInsuranceInstallmentStatus.OverDue)))
                       {
                           return Enum.GetName(typeof(UnEmpInsuranceInstallmentStatus), UnEmpInsuranceInstallmentStatus.OverDue);
                       }
                       else if (dest.Installments.Any(installment => installment.Status == EnumUtility.GetDescriptionFromEnumValue(UnEmpInsuranceInstallmentStatus.Upcoming)))
                       {
                           return Enum.GetName(typeof(UnEmpInsuranceInstallmentStatus), UnEmpInsuranceInstallmentStatus.Upcoming);
                       }
                       else
                       {
                           return string.Empty;
                       }
                   }));

        }

        private string GetInstallmentStatus(string paymentStatus, string dueDate)
        {
            if (!DateTime.TryParseExact(dueDate, "dd-MM-yyyy", CultureInfo.InvariantCulture,
                DateTimeStyles.None, out DateTime dueDateTime))
            {
                return "Invalid due date format";
            }

            switch (paymentStatus)
            {
                case "PAID":
                    return EnumUtility.GetDescriptionFromEnumValue(UnEmpInsuranceInstallmentStatus.Paid);

                case "NOT PAID" when dueDateTime > DateTime.Now.AddDays(5):
                    return EnumUtility.GetDescriptionFromEnumValue(UnEmpInsuranceInstallmentStatus.InFuture);

                case "NOT PAID" when dueDateTime <= DateTime.Now.AddDays(5) && dueDateTime >= DateTime.Now:
                    return EnumUtility.GetDescriptionFromEnumValue(UnEmpInsuranceInstallmentStatus.Upcoming);

                case "NOT PAID" when dueDateTime < DateTime.Now:
                    return EnumUtility.GetDescriptionFromEnumValue(UnEmpInsuranceInstallmentStatus.OverDue);

                default:
                    return EnumUtility.GetDescriptionFromEnumValue(UnEmpInsuranceInstallmentStatus.InFuture);
            }
        }

        public static DateTime MapNextDueDate(List<Edenred.Common.Core.Installment> Installments)
        {
            var nextDueInstallment = Installments.Where(ins => ins.PaymentStatus.Contains("NOT PAID")).OrderBy(or => or.InstallmentNo).FirstOrDefault();

            var nextDueDate = DateTime.ParseExact(nextDueInstallment.DueDate, "dd-MM-yyyy", CultureInfo.InvariantCulture);

            return nextDueDate;
        }

        public static string MapNextDueAmount(List<Edenred.Common.Core.Installment> Installments)
        {
            var nextDueInstallment = Installments.Where(ins => ins.PaymentStatus.Contains("NOT PAID")).OrderBy(or => or.InstallmentNo).FirstOrDefault();

            return $"{ConstantParam.DefaultCurrency} {nextDueInstallment.InstallmentPremium.ToString()}";
        }

    }
}
