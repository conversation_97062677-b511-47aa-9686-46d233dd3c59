﻿using C3Pay.Core.Models;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.BillPayments;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace C3Pay.Core.Services.C3Pay
{
    public interface IBillPaymentProcessingService
    {
        Task<ServiceResponse<bool>> SyncData();
        Task<ServiceResponse<IEnumerable<BillPaymentCategory>>> GetCategories(string countryCode);
        Task<ServiceResponse<IEnumerable<BillPaymentProvider>>> GetProviders(int categoryId, string countryCode);
        Task<ServiceResponse<BillPaymentProvider>> GetProvider(int productId);
        Task<ServiceResponse<IEnumerable<BillPaymentProductIO>>> GetFields(int categoryId, int billerId, int subProviderId);
        Task<ServiceResponse<BillPaymentBiller>> AddBiller(BillPaymentBiller inputBillPaymentBiller);
        Task<ServiceResponse<bool>> DeleteBiller(Guid billerId);
        Task<ServiceResponse<List<BillPaymentBiller>>> GetBillers(Guid userId, string providerCode);
        Task<ServiceResponse<bool>> SynchronizeBillPaymentFxRates();
        Task<ServiceResponse<BillPaymentsAmountDueDto>> GetAmountDue(BillPaymentBiller thisLocalBillerItem, string transactionId, Guid loggedInUserId);
        Task<ServiceResponse<List<BillPaymentTransaction>>> GetTransactions(Guid userId, string providerCode, int? pageSize, int? pageNumber);
        Task<ServiceResponse<BillPaymentTransaction>> GetTransactionDetail(Guid transactionId, Guid userId);

        Task<ServiceResponse<BillPaymentTransaction>> GetSummary(Guid billerId, Guid userId, int productId, string entityTransactionId, decimal? inputAmount = default,
                                                   string inputAmountCurrency = default);
        Task<ServiceResponse<BillPaymentTransaction>> CapturePayment(Guid billerId, User user, int productId, decimal? inputAmount = default,
                                         string inputAmountCurrency = default);
        Task<ServiceResponse<BillPaymentTransaction>> ProcessQueuePayment(Guid transactionId, int productId, decimal? inputAmount, string inputAmountCurrency);
        Task<ServiceResponse<bool>> FetchAmountDueForBiller(Guid billerId);
        Task<ServiceResponse<bool>> CheckForAmountDueExpiration();
        Task<ServiceResponse<BillPaymentBiller>> InitiateAmountFetch(Guid billerId, Guid userId);
        Task<ServiceResponse<IEnumerable<BillPaymentProviderField>>> GetNolFields();
        Task<ServiceResponse<BillPaymentTransaction>> PostNolTransaction(User user, Guid billerId, int productId);

    }
}
