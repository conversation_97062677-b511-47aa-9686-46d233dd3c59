﻿using AutoMapper;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using C3Pay.Core.Models.Settings.Membership;
using C3Pay.Core.Repositories;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay.Membership;
using C3Pay.Services.Helper.Membership;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.ESMO;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Services.Membership.Queries
{
    public class GetC3PayPlusMembershipDetailsQuery : IRequest<Result<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>>
    {
        public string LanguageCode { get; set; }
        public string UserPhoneNumber { get; set; }

        public static Result IsQueryValid(GetC3PayPlusMembershipDetailsQuery query)
        {
            if (query is null)
            {
                return Result.Failure(Errors.C3PayPlus.EmptyRequest);
            }

            if (string.IsNullOrWhiteSpace(query.UserPhoneNumber))
            {
                return Result.Failure(Errors.C3PayPlus.NoPhoneNumberSent);
            }
            return Result.Success();
        }
    }

    public class GetC3PayPlusMembershipDetailsQueryHandler : IRequestHandler<GetC3PayPlusMembershipDetailsQuery, Result<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>>
    {
        private readonly IC3PayPlusMembershipLookupService _lookupService;
        private readonly IBlobStorageService _blobStorageService;
        private readonly C3PayPlusMembershipSettings _settings;
        private readonly IFeatureManager _featureManager;
        private readonly IUserRepository _userRepository;
        private readonly IESMOWebService _esmoWebService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IC3PayPlusMembershipTranslationService _translationService;
        private readonly IC3PayPlusTargetedDiscountService _targetedDiscountService;
        private readonly ILogger _logger;

        private readonly string _selfieContainer = "selfies";

        public GetC3PayPlusMembershipDetailsQueryHandler(ILogger<GetC3PayPlusMembershipDetailsQueryHandler> logger,
                                                         IC3PayPlusMembershipLookupService lookupService,
                                                         IOptions<C3PayPlusMembershipSettings> settings,
                                                         IBlobStorageService blobStorageService,
                                                         IFeatureManager featureManager,
                                                         IUserRepository userRepository,
                                                         IESMOWebService esmoWebService,
                                                         IUnitOfWork unitOfWork,
                                                         IMapper mapper,
                                                         IC3PayPlusMembershipTranslationService translationService,
                                                         IC3PayPlusTargetedDiscountService targetedDiscountService)
        {
            _blobStorageService = blobStorageService;
            _featureManager = featureManager;
            _userRepository = userRepository;
            _esmoWebService = esmoWebService;
            _lookupService = lookupService;
            _settings = settings.Value;
            _unitOfWork = unitOfWork;
            _logger = logger;
            _mapper = mapper;
            _translationService = translationService;
            _targetedDiscountService = targetedDiscountService;
        }
        public async Task<Result<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>> Handle(GetC3PayPlusMembershipDetailsQuery request, CancellationToken ct)
        {
            // If C3Pay+ is unavailable, exit.
            var isEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.GlobalC3PayPlus);
            if (isEnabled == false)
            {
                _logger.LogError(Errors.C3PayPlus.Unavailable.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(Errors.C3PayPlus.Unavailable);
            }



            // Validate query.
            var isQueryValid = GetC3PayPlusMembershipDetailsQuery.IsQueryValid(request);
            if (isQueryValid.IsFailure)
            {
                _logger.LogError(isQueryValid.Error.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(isQueryValid.Error);
            }



            // Prepare request input.
            request.UserPhoneNumber = request.UserPhoneNumber.Trim();
            request.LanguageCode ??= "en";



            // If user is not found, deleted, blocked, or is not a C3Pay app user, exit.
            var user = await this._userRepository.GetUserForC3PayPlus(request.UserPhoneNumber, ct);
            if (user is null)
            {
                _logger.LogError(Errors.C3PayPlus.UserNotFound.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(Errors.C3PayPlus.UserNotFound);
            }


            // Prepare the membership object to return.
            var membershipDto = new C3PayPlusMembershipDto();

            // We will have 2 flows:
            // Flow 1: User has subscribed to C3Pay+.       In this flow, we don't have to perform all eligibility checks, return no login videos, and we will show all benefits.
            // Flow 2: User has NOT subscribed to C3Pay+.   In this flow, we will have to perfom all eligibility checks, return the login videos and show the benefits based on the membership type.
            var membershipUser = await this._unitOfWork.C3PayPlusMembershipUsers.FirstOrDefaultAsync(x => x.UserId == user.Id
                                                                                                          && x.IsActive == true,
                                                                                                          i => i.C3PayPlusMembership.Videos,
                                                                                                          i => i.User.Identifications,
                                                                                                          i => i.User.CardHolder,
                                                                                                          i => i.C3PayPlusMembershipLifeInsuranceNominees);

            if (membershipUser != null)
            {
                // Flow 1: User has subscribed to C3Pay+. 
                await GetBenefits(membershipUser, request.LanguageCode);

                // If a user is part of the C3Pay+ Surcharge experiment, we need to show an extra benefit.
                var surchargeExperiemnt = await this._lookupService.GetExperimentDetails(C3PayPlusMembershipExperiments.Surcharge);
                if (surchargeExperiemnt != null)
                {
                    var inSurchargeExperiemnt = await this._unitOfWork.C3PayPlusMembershipExperimentUsers
                   .FirstOrDefaultAsync(x => x.CorporateId == user.CardHolder.CorporateId && x.C3PayPlusMembershipExperimentId == surchargeExperiemnt.Id);

                    if (inSurchargeExperiemnt != null)
                    {
                        // User is part of the surcharge experiment.
                        // We need to show additional benefits (remove the extra benefits)
                        membershipUser.C3PayPlusMembership.Benefits = membershipUser.C3PayPlusMembership.Benefits.Where(x => x.BenefitId != C3PayPlusMembershipBenefitType.Cashback).ToList();

                        // Set the dynamic icon of the money transfer benefit to match the user's nationality.
                        var nationality = user.CardHolder.Nationality;

                        // Find the free money transfer benefit.
                        var moneyTransferBenefit = membershipUser.C3PayPlusMembership.Benefits.FirstOrDefault(x => x.BenefitId == C3PayPlusMembershipBenefitType.FreeMoneyTransfer);
                        if (moneyTransferBenefit != null)
                        {
                            if (nationality == "IND")
                            {
                                moneyTransferBenefit.IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-india.png";
                            }
                            else if (nationality == "PAK")
                            {
                                moneyTransferBenefit.IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-pak.png";
                            }
                            else if (nationality == "LKA")
                            {
                                moneyTransferBenefit.IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-lk.png";
                            }
                            else if (nationality == "PHL")
                            {
                                moneyTransferBenefit.IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-ph.png";
                            }
                            else if (nationality == "BGD")
                            {
                                moneyTransferBenefit.IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-bgd.png";
                            }
                            else if (nationality == "NPL")
                            {
                                moneyTransferBenefit.IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-npl.png";
                            }
                            else
                            {
                                moneyTransferBenefit.IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-money.png";
                            }
                        }
                    }
                    else
                    {
                        // Remove any extra benefits.
                        membershipUser.C3PayPlusMembership.Benefits = membershipUser.C3PayPlusMembership.Benefits.Where(x => x.BenefitId != C3PayPlusMembershipBenefitType.FreeMoneyTransfer && x.BenefitId != C3PayPlusMembershipBenefitType.Cashback).ToList();
                    }
                }
                else
                {
                    // Remove any extra benefits.
                    membershipUser.C3PayPlusMembership.Benefits = membershipUser.C3PayPlusMembership.Benefits.Where(x => x.BenefitId != C3PayPlusMembershipBenefitType.FreeMoneyTransfer && x.BenefitId != C3PayPlusMembershipBenefitType.Cashback).ToList();
                }

                // Map basic result and initialize videos.
                membershipDto = C3PayPlusMembershipDto.Map(membershipUser.C3PayPlusMembership);


                // Map membership dates such as start date, end date and other details.
                MapMembershipDates(membershipDto, membershipUser);


                // Map lucky draw details, this include the users ticket number.
                MapLuckyDrawDetails(membershipDto, membershipUser);


                // Personal details.
                await MapPersonalDetails(membershipDto, membershipUser);


                // Savings.
                await MapSavings(membershipDto, membershipUser, user, request.LanguageCode);


                // Headlines.
                MapHeadlines(membershipDto, membershipUser, request.LanguageCode);


                // Banner benefits.
                MapBannerBenefits(membershipDto, membershipUser, user, request.LanguageCode);
            }
            else
            {
                // Flow 2: User has NOT subscribed to C3Pay+. 
                // Check if the user is Eligible to subscribe to C3Pay+.
                var isC3PayPlusEligible = user.IsC3PayPlusEligible();
                if (isC3PayPlusEligible.IsFailure)
                {
                    _logger.LogError(isC3PayPlusEligible.Error.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(isC3PayPlusEligible.Error);
                }

                // If we reach this far, the user can view C3Pay+ subscription details.
                // Based on what the user has subscribed to, we will determine the membership the user is allowed to view and subscribe to.
                IList<Subscription> subscriptions = await _lookupService.GetCachedSubscriptions();


                // Check if the user has subscribed to Balance Enquiry.
                UserSubscription userBalanceEnquirySubscription = null;
                bool hasBalanceEnquirySubscription = false;

                var balanceEnquirySubscription = subscriptions.FirstOrDefault(x => x.Code == SMSSubscriptionType.BE.ToString());
                if (balanceEnquirySubscription != null)
                {
                    userBalanceEnquirySubscription = await this._unitOfWork.UserSubscriptions.FirstOrDefaultAsync(x => x.SubscriptionId == balanceEnquirySubscription.Id
                                                                                                                      && x.UserId == user.Id
                                                                                                                      && x.EndDate.HasValue == false);
                    if (userBalanceEnquirySubscription != null)
                    {
                        hasBalanceEnquirySubscription = true;
                    }
                }

                // Check if the user has subscribed to Security SMS and/or salary alerts.
                var hasSecuritySmsSubscription = false;
                var hasSalaryAlertSmsSubscription = false;
                var tryGetSMSSubscriptionMode = await _esmoWebService.GetSMSSubscriptionMode(new GetSMSSubscriptionModeRequest()
                {
                    CardSerialNo = user.CardHolder.CardSerialNumber,
                    CorporateId = int.Parse(user.CardHolder.CorporateId)
                });


                if (tryGetSMSSubscriptionMode.IsSuccessful == false)
                {
                    _logger.LogError(Errors.C3PayPlus.CantGetEsmoSubscriptionDetails.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(Errors.C3PayPlus.CantGetEsmoSubscriptionDetails);
                }

                hasSecuritySmsSubscription = tryGetSMSSubscriptionMode.Data.SubscribedToSecurityAlerts;
                hasSalaryAlertSmsSubscription = tryGetSMSSubscriptionMode.Data.SubscribedToSalaryAlert;


                // We can now tell which membership we can show to the user.
                var c3PayPlusTypeId = C3PayPlusMembershipType.None;
                if (hasBalanceEnquirySubscription == true)
                {
                    if (hasSecuritySmsSubscription == true)
                    {
                        c3PayPlusTypeId = C3PayPlusMembershipType.UsersWithBalanceEnquiryAndSecuritySms;
                    }
                    else if (hasSecuritySmsSubscription == true && hasSalaryAlertSmsSubscription == true)
                    {
                        _logger.LogError(Errors.C3PayPlus.NoMembershipFound.Code);
                        return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(Errors.C3PayPlus.NoMembershipFound);
                    }
                    else if (hasSecuritySmsSubscription == true && hasSalaryAlertSmsSubscription == false)
                    {
                        c3PayPlusTypeId = C3PayPlusMembershipType.UsersWithBalanceEnquiryAndSecuritySms;
                    }
                    else if (hasSecuritySmsSubscription == false && hasSalaryAlertSmsSubscription == true)
                    {
                        c3PayPlusTypeId = C3PayPlusMembershipType.UsersWithBalanceEnquiryAndSalaryAlert;
                    }
                    else if (hasSecuritySmsSubscription == false && hasSalaryAlertSmsSubscription == false)
                    {
                        c3PayPlusTypeId = C3PayPlusMembershipType.UsersWithBalanceEnquiryOnly;
                    }
                }
                else
                {
                    if (hasSecuritySmsSubscription == true)
                    {
                        c3PayPlusTypeId = C3PayPlusMembershipType.UsersWithSecuritySmsOnly;
                    }
                    else if (hasSecuritySmsSubscription == true && hasSalaryAlertSmsSubscription == true)
                    {
                        _logger.LogError(Errors.C3PayPlus.NoMembershipFound.Code);
                        return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(Errors.C3PayPlus.NoMembershipFound);
                    }
                    else if (hasSecuritySmsSubscription == true && hasSalaryAlertSmsSubscription == false)
                    {
                        c3PayPlusTypeId = C3PayPlusMembershipType.UsersWithSecuritySmsOnly;
                    }
                    else if (hasSecuritySmsSubscription == false && hasSalaryAlertSmsSubscription == true)
                    {
                        c3PayPlusTypeId = C3PayPlusMembershipType.UsersWithSalaryAlertsOnly;
                    }
                    else if (hasSecuritySmsSubscription == false && hasSalaryAlertSmsSubscription == false)
                    {
                        c3PayPlusTypeId = C3PayPlusMembershipType.UsersWithNoSubscription;
                    }
                }

                // Get membership details based on the type.
                var membership = await this._unitOfWork.C3PayPlusMemberships.FirstOrDefaultAsync(x => x.C3PayPlusTypeId == c3PayPlusTypeId
                                                                                                      && x.IsAvailable == true,
                                                                                                      include => include.Videos);
                if (membership is null)
                {
                    _logger.LogError(Errors.C3PayPlus.NoMembershipFound.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(Errors.C3PayPlus.NoMembershipFound);
                }

                // Load membership benefits with translations
                IList<C3PayPlusMembershipBenefit> benefits = await _lookupService.GetBenefitsByMembershipTypeWithTranslation(membership.Id, request.LanguageCode);
                membership.Benefits = benefits?.ToList();

                membership.Benefits = membership.Benefits.Where(x => x.BenefitId != C3PayPlusMembershipBenefitType.FreeMoneyTransfer
                                                                     && x.BenefitId != C3PayPlusMembershipBenefitType.Cashback).ToList();

                // Map result.
                membershipDto = C3PayPlusMembershipDto.Map(membership);


                // For lucky draw details, we need to pass the user's name and a placeholder ID.
                membershipDto.LuckyDrawDetailsDto = membershipDto.GetLuckyDrawDetails($"{user.CardHolder.FirstName}", "XX-XXXX", this._settings.OverrideLuckyDrawDate, this._settings.OverrideLuckyDrawTime);

                // Show estimated savings while subscribing
                membershipDto.MembershipEstimatedSavings = await _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.WinAndSaveMoney.ToString(), request.LanguageCode);// "Win and Save Money";                                                                                

                // Remove headlines.
                membershipDto.Headline = null;


                // Check if we should use the new targeted discount service or the old experiment approach
                var useNewTargetedDiscountService = await _featureManager.IsEnabledAsync(FeatureFlags.Memberships_C3PayPlus_UseNewTargetedDiscountService);

                if (useNewTargetedDiscountService)
                {
                    // NEW APPROACH: Use targeted discount service
                    // Get C3Pay+ video view count (LoginVideo.Id = 4 for all C3Pay+ videos)
                    var c3PayPlusVideoViewCount = await _unitOfWork.UserLoginVideoDetailRepository.GetUserVideoViewCount(user.Id, 4);

                    // Check for targeted discount eligibility using our consolidated service
                    var eligibilityResult = await _targetedDiscountService.CheckEligibilityAsync(
                        user,
                        hasBalanceEnquirySubscription,
                        hasSecuritySmsSubscription,
                        hasSalaryAlertSmsSubscription,
                        c3PayPlusVideoViewCount,
                        ct);
                    if (eligibilityResult.IsSuccess && eligibilityResult.Value.IsEligible)
                    {
                        // User is eligible for targeted discount
                        membershipDto.DiscountAvailable = true;

                        // Get discount pricing details
                        var pricingResult = await _targetedDiscountService.CalculateDiscountAsync(user, eligibilityResult.Value, membership, ct);
                        if (pricingResult.IsSuccess)
                        {
                            // Populate the TargetedDiscount DTO with full discount details
                            membershipDto.TargetedDiscount = new C3PayPlusMembershipDiscountDto
                            {
                                DiscountIdentifier = pricingResult.Value.DiscountIdentifier,
                                PriceAfterDiscount = pricingResult.Value.PriceAfterDiscount.ToString("0.##"),
                                PriceAfterDiscountVatInclusive = pricingResult.Value.PriceAfterDiscountVatInclusive.ToString("0.##"),
                                OfferStartDate = eligibilityResult.Value.OfferStartDate ?? DateTime.Now,
                                OfferEndDate = eligibilityResult.Value.OfferEndDate ?? DateTime.Now.AddHours(24),
                                HasOfferExpired = (eligibilityResult.Value.OfferEndDate ?? DateTime.Now.AddHours(24)) <= DateTime.Now
                            };

                            // Show targeted discount videos if offer is active and not expired
                            if (eligibilityResult.Value.HasActiveOffer && !membershipDto.TargetedDiscount.HasOfferExpired)
                            {
                                membershipDto.LoginVideos = new List<C3PayPlusMembershipVideoDto>()
                                {
                                    new C3PayPlusMembershipVideoDto()
                                    {
                                        Url = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Targeted_english_1.mp4",
                                        IsDefault = false,
                                        LanguageCode = "en"
                                    },
                                    new C3PayPlusMembershipVideoDto()
                                    {
                                        Url = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/targeted_Hindi_1.mp4",
                                        IsDefault = true,
                                        LanguageCode = "hi"
                                    }
                                };
                            }
                        }
                        else
                        {
                            _logger.LogWarning("Failed to calculate discount pricing for user {UserId}: {Error}", user.Id, pricingResult.Error.Code);
                            membershipDto.DiscountAvailable = false;
                        }
                    }
                    else
                    {
                        membershipDto.DiscountAvailable = false;
                        if (eligibilityResult.IsFailure)
                        {
                            _logger.LogDebug("User {UserId} is not eligible for targeted discount: {Reason}", user.Id, eligibilityResult.Error.Code);
                        }
                        else
                        {
                            _logger.LogDebug("User {UserId} is not eligible for targeted discount: {Reason}", user.Id, eligibilityResult.Value.EligibilityReason);
                        }
                    }
                }
                else
                {
                    // OLD APPROACH: Use original experiment logic
                    // Check if there is an available discount.
                    // Here, we need to check if a user is part of a C3Pay+ experiment or not.
                    // These experiments will act as modifiers to what we return and show to the user.
                    var inDiscountExperiment = await this._unitOfWork.C3PayPlusMembershipExperimentUsers.FirstOrDefaultAsync(x => x.CardholderId == user.CardHolderId && x.IsActive == true
                    && x.C3PayPlusMembershipExperiment.ExperimentId == C3PayPlusMembershipExperiments.TargetedDiscount && x.C3PayPlusMembershipExperiment.IsActive == true,
                    i => i.C3PayPlusMembershipExperiment);

                    if (inDiscountExperiment != null)
                    {
                        if (inDiscountExperiment.C3PayPlusMembershipExperiment.ExperimentCode == "TD")
                        {
                            membershipDto.DiscountAvailable = true;
                            // Only show the video if the offer has not expired yet.
                            if (string.IsNullOrEmpty(inDiscountExperiment.ExperimentJsonData))
                            {
                                // Here, we can show the new video to the user because they have not claimed it yet.
                                membershipDto.LoginVideos = new List<C3PayPlusMembershipVideoDto>()
                                {
                                    new C3PayPlusMembershipVideoDto()
                                    {
                                        Url = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Targeted_english_1.mp4",
                                        IsDefault = false,
                                        LanguageCode = "en"
                                    },
                                    new C3PayPlusMembershipVideoDto()
                                    {
                                        Url = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/targeted_Hindi_1.mp4",
                                        IsDefault = true,
                                        LanguageCode = "hi"
                                    }
                                };
                            }
                            else
                            {
                                // Here, the user might have the offer expired, we need to parse and check.
                                var experimentUserData = JsonConvert.DeserializeObject<C3PayPlusMembershipTargetedDiscountExperimentUserData>(inDiscountExperiment.ExperimentJsonData);
                                if (experimentUserData is null || (experimentUserData != null && DateTime.Now < experimentUserData.OfferEndDate))
                                {
                                    membershipDto.DiscountAvailable = true;
                                    // Here, we can show the new video to the user because they have not claimed it yet.
                                    membershipDto.LoginVideos = new List<C3PayPlusMembershipVideoDto>()
                                    {
                                        new C3PayPlusMembershipVideoDto()
                                        {
                                            Url = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Targeted_english_1.mp4",
                                            IsDefault = false,
                                            LanguageCode = "en"
                                        },
                                        new C3PayPlusMembershipVideoDto()
                                        {
                                            Url = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/targeted_Hindi_1.mp4",
                                            IsDefault = true,
                                            LanguageCode = "hi"
                                        }
                                    };
                                }
                                else
                                {
                                    // Here, the user has seen the discount before, so we need to check if it had expired or not.
                                    if (DateTime.Now > experimentUserData.OfferEndDate)
                                    {
                                        membershipDto.DiscountAvailable = false;
                                        // Don't return the video because the offer has expired.
                                    }
                                }
                            }
                        }
                        else if (inDiscountExperiment.C3PayPlusMembershipExperiment.ExperimentCode == "TD_C")
                        {
                            membershipDto.LoginVideos =
                                [
                                new C3PayPlusMembershipVideoDto()
                                {
                                    Url = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Control_Eng_1.mp4",
                                    IsDefault = false,
                                    LanguageCode = "en"
                                },
                                new C3PayPlusMembershipVideoDto()
                                {
                                    Url = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Control_Eng_1.mp4",
                                    IsDefault = true,
                                    LanguageCode = "hi"
                                },
                            ];
                        }
                    }
                    else
                    {
                        membershipDto.DiscountAvailable = false;
                    }
                }
            }

            // Computed properties:
            // For life insurance policy details, we need to pass the user's name and some sort of ID.
            // The ID for the policy is something we will have to create and it should be unique to each user. We will user the cardholder ID for this purpose.
            var nomineeDto = _mapper.Map<C3PayPlusMembershipLifeInsuranceNomineeResponseDto>(membershipUser?.C3PayPlusMembershipLifeInsuranceNominees?.FirstOrDefault(i => i.IsDeleted == false));


            // add insurance policy details to the nominee
            if (nomineeDto != null)
            {
                nomineeDto.PolicyDetails = await _lookupService.GetPolicyDetailsWithTranslation(request.LanguageCode);
            }

            membershipDto.LifeInsurancePolicyDetails = membershipDto.GetLifeInsurancePolicyDetails($"{user.CardHolder.FirstName} {user.CardHolder.LastName}", $"LIF{user.CardHolder.Id}", nomineeDto);



            return Result.Success(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>()
            {
                IsSuccessful = true,
                Data = membershipDto
            });
        }

        private async Task GetBenefits(C3PayPlusMembershipUser membershipUser, string userLanguage)
        {
            // Get all benefits regardless of the membership type.
            IList<C3PayPlusMembershipBenefit> benefits = await _lookupService.GetCachedBenefits(userLanguage);
            membershipUser.C3PayPlusMembership.Benefits = benefits.ToList();
        }

        private void MapLuckyDrawDetails(C3PayPlusMembershipDto membershipDto, C3PayPlusMembershipUser membershipUser)
        {

            // For lucky draw details, we need to pass the user's name and the ticket number.
            membershipDto.LuckyDrawDetailsDto = membershipDto.GetLuckyDrawDetails($"{membershipUser.User.CardHolder.FirstName}", $"{membershipUser.TicketNumber}", this._settings.OverrideLuckyDrawDate, this._settings.OverrideLuckyDrawTime);
        }

        private static void MapMembershipDates(C3PayPlusMembershipDto membershipDto, C3PayPlusMembershipUser membershipUser)
        {

            // "IsActive" is the key we will use to know if the membership is active (i.e. the benefits of this membership are available for the user).
            // "IsActive" is true =>    Benefits are available for the user.
            // "IsActive" is false =>   Benefits are not available for the user.
            // The user can cancel their membership, but it will stay active until it has expired.
            // "IsActive" will still be true, until the end date of the membership.
            membershipDto.IsActive = membershipUser.IsActive;


            // The property "ValidUntill" will tell us for how long the membership is valid for.
            // "ValidUntill" will always have a value, and it will keep updating whenever we renew the user's membership.
            // If the user has canceled their membership, "ValidUntill" will reflect the end date of the membership.
            membershipDto.ValidUntill = membershipUser.ValidUntill.ToString("dd MMMM yyyy");


            // To know if the user has canceled their membership, "UserHasCanceled" will be true.
            membershipDto.UserHasCanceled = membershipUser.UserHasCanceled;


            // The cancellation date (if there is one) will be stored in "UserCanceledOn".
            membershipDto.UserCanceledOn = membershipUser.UserCanceledOn.HasValue ? membershipUser.UserCanceledOn.Value.ToString("dd MMMM yyyy") : null;


            // "NextBilling" will tell us when the next billing will occur.
            // If the user has canceled their membership, and the final billing did not happen yet, this property will have a value,
            // otherwise, if the user has canceled thier membership and has paid the final amount, this will be null.
            membershipDto.NextBilling = membershipUser.NextBillingDate.HasValue ? membershipUser.NextBillingDate.Value.ToString("dd MMMM yyyy") : null;


            // "ValidFrom" will tell us when the user has subscribed.
            membershipDto.ValidFrom = membershipUser.StartDate.ToString("dd MMMM yyyy");
        }

        private void MapBannerBenefits(C3PayPlusMembershipDto membershipDto, C3PayPlusMembershipUser membershipUser, User user, string userLanguage)
        {
            // For banner benefits, we need to display a mix of static benefits, along with calculated benefits.
            // For now, we will hard-code the static benefits.
            // Case 1: Nominee pending, ATM available.
            if ((membershipUser.C3PayPlusMembershipLifeInsuranceNominees is null
                || membershipUser.C3PayPlusMembershipLifeInsuranceNominees.Count == 0)
                && membershipUser.HasClaimedAtmWithdrawalFeeReversalForThisMonth == false)
            {
                membershipDto.BannerBenefits.Add(new C3PayPlusMembershipBannerBenefitDto()
                {
                    Id = C3PayPlusMembershipBannerBenefit.NoNomineeAtmAvailable,
                    Style = C3PayPlusMembershipBannerBenefitsStyle.Gold,
                    Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsAddInsuranceNominee.ToString(), userLanguage).Result,
                    SubTitle = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsAddInsuranceNomineeSubtitle.ToString(), userLanguage).Result,
                    IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/gold-warning-icon.png"
                });

                membershipDto.BannerBenefits.Add(new C3PayPlusMembershipBannerBenefitDto()
                {
                    Id = C3PayPlusMembershipBannerBenefit.PickNewDrawTicket,
                    Style = C3PayPlusMembershipBannerBenefitsStyle.Yellow,
                    Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsPickNewDrawTicket.ToString(), userLanguage).Result,
                    SubTitle = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsPickNewDrawTicketSubtitle.ToString(), userLanguage).Result,
                    IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/yellow-coins-icon.png"
                });

                membershipDto.BannerBenefits.Add(new C3PayPlusMembershipBannerBenefitDto()
                {
                    Id = C3PayPlusMembershipBannerBenefit.OneFreeAtm,
                    Style = C3PayPlusMembershipBannerBenefitsStyle.Green,
                    Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsOneFreeAtmAvailable.ToString(), userLanguage).Result,
                    SubTitle = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsOneFreeAtmAvailableSubtitle.ToString(), userLanguage).Result,
                    IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/green-notification-icon.png"
                });
            }

            // Case 2: Nominee is added, ATM available.
            else if (membershipUser.C3PayPlusMembershipLifeInsuranceNominees != null
               && membershipUser.C3PayPlusMembershipLifeInsuranceNominees.Count > 0
               && membershipUser.HasClaimedAtmWithdrawalFeeReversalForThisMonth == false)
            {
                membershipDto.BannerBenefits.Add(new C3PayPlusMembershipBannerBenefitDto()
                {
                    Id = C3PayPlusMembershipBannerBenefit.PickNewDrawTicket,
                    Style = C3PayPlusMembershipBannerBenefitsStyle.Yellow,
                    Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsPickNewDrawTicket.ToString(), userLanguage).Result,
                    SubTitle = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsPickNewDrawTicketSubtitle.ToString(), userLanguage).Result,
                    IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/yellow-coins-icon.png"
                });

                membershipDto.BannerBenefits.Add(new C3PayPlusMembershipBannerBenefitDto()
                {
                    Id = C3PayPlusMembershipBannerBenefit.OneFreeAtm,
                    Style = C3PayPlusMembershipBannerBenefitsStyle.Green,
                    Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsOneFreeAtmAvailable.ToString(), userLanguage).Result,
                    SubTitle = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsOneFreeAtmAvailableSubtitle.ToString(), userLanguage).Result,
                    IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/green-notification-icon.png"
                });

                membershipDto.BannerBenefits.Add(new C3PayPlusMembershipBannerBenefitDto()
                {
                    Id = C3PayPlusMembershipBannerBenefit.WinnersAreOut,
                    Style = C3PayPlusMembershipBannerBenefitsStyle.Normal,
                    Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsWinnerOutForTheWeek.ToString(), userLanguage).Result,
                    SubTitle = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsWinnerOutForTheWeekSubtitle.ToString(), userLanguage).Result,
                    IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/blue-announcement-icon.png"
                });
            }

            // Case 3: User added a nominee and used the free ATM.
            else if (membershipUser.C3PayPlusMembershipLifeInsuranceNominees != null
               && membershipUser.C3PayPlusMembershipLifeInsuranceNominees.Count > 0
               && membershipUser.HasClaimedAtmWithdrawalFeeReversalForThisMonth == true)
            {
                membershipDto.BannerBenefits.Add(new C3PayPlusMembershipBannerBenefitDto()
                {
                    Id = C3PayPlusMembershipBannerBenefit.PickNewDrawTicket,
                    Style = C3PayPlusMembershipBannerBenefitsStyle.Yellow,
                    Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsPickNewDrawTicket.ToString(), userLanguage).Result,
                    SubTitle = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsPickNewDrawTicketSubtitle.ToString(), userLanguage).Result,
                    IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/yellow-coins-icon.png"
                });

                membershipDto.BannerBenefits.Add(new C3PayPlusMembershipBannerBenefitDto()
                {
                    Id = C3PayPlusMembershipBannerBenefit.WinnersAreOut,
                    Style = C3PayPlusMembershipBannerBenefitsStyle.Normal,
                    Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsWinnerOutForTheWeek.ToString(), userLanguage).Result,
                    SubTitle = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsWinnerOutForTheWeekSubtitle.ToString(), userLanguage).Result,
                    IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/blue-announcement-icon.png"
                });

                membershipDto.BannerBenefits.Add(new C3PayPlusMembershipBannerBenefitDto()
                {
                    Id = C3PayPlusMembershipBannerBenefit.HasLifeInsurance,
                    Style = C3PayPlusMembershipBannerBenefitsStyle.Normal,
                    Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsSecuredWithLifeInsurance.ToString(), userLanguage).Result,
                    SubTitle = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsSecuredWithLifeInsuranceSubtitle.ToString(), userLanguage).Result,
                    IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/pink-heart-icon.png"
                });
            }
        }

        private void MapHeadlines(C3PayPlusMembershipDto membershipDto, C3PayPlusMembershipUser membershipUser, string userLanguage)
        {
            // Headlines are a list of announcements or stats we need to show to the user.
            // We need to know what day it is now because some of these rules are date dependant.
            var today = DateTime.Now.DayOfWeek;

            membershipDto.Headline ??= new C3PayPlusMembershipHeadlineDto();

            // If the user has cancelled their subscription,
            // then we should show them the "Expires Soon" message.
            if (membershipUser.UserHasCanceled == true)
            {
                membershipDto.Headline.Id = C3PayPlusMembershipHeadline.ExpiresSoon;
                membershipDto.Headline.Text = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.HeadlinesExpiresSoon.ToString(), userLanguage).Result;
                membershipDto.Headline.UrgencyLevel = C3PayPlusMembershipHeadlineUrgencyLevel.Danger;
                membershipDto.Headline.IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/red-warning-icon.png";
                membershipDto.Headline.DeeplinkUrl = "/c3payplus/renewal";
            }
            else
            {
                // Weekdays.
                if (today >= DayOfWeek.Monday && today <= DayOfWeek.Friday)
                {
                    // The Update nominee CTA should be shown if the user has not added a nominee,
                    // this should show during the weekdays (Monday - Friday). If the user clicks on this CTA, they should be taken to the onboarding flow of the add life insurance nominee.
                    if (membershipUser.C3PayPlusMembershipLifeInsuranceNominees is null
                       || membershipUser.C3PayPlusMembershipLifeInsuranceNominees.Count == 0)
                    {
                        membershipDto.Headline.Id = C3PayPlusMembershipHeadline.UpdateNominee;
                        membershipDto.Headline.Text = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.HeadlinesUpdateNominee.ToString(), userLanguage).Result;
                        membershipDto.Headline.UrgencyLevel = C3PayPlusMembershipHeadlineUrgencyLevel.Warning;
                        membershipDto.Headline.IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/yellow-warning-icon.png";
                        membershipDto.Headline.DeeplinkUrl = "/C3PayPlus/LifeInsuranceOnBoardingVideo";
                    }

                    else
                    {
                        if (membershipUser.HasClaimedAtmWithdrawalFeeReversalForThisMonth == false)
                        {
                            membershipDto.Headline.Id = C3PayPlusMembershipHeadline.ClaimFreeAtm;
                            membershipDto.Headline.Text = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.BenefitsOneFreeAtmAvailable.ToString(), userLanguage).Result;
                            membershipDto.Headline.UrgencyLevel = C3PayPlusMembershipHeadlineUrgencyLevel.Normal;
                            membershipDto.Headline.DeeplinkUrl = "/C3PayPlus/Onboarding";
                        }
                        else
                        {
                            membershipDto.Headline.Id = C3PayPlusMembershipHeadline.Active;
                            membershipDto.Headline.Text = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.HeadlinesStatusActive.ToString(), userLanguage).Result;
                            membershipDto.Headline.UrgencyLevel = C3PayPlusMembershipHeadlineUrgencyLevel.Normal;
                            membershipDto.Headline.IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/green-check-icon.png";
                            membershipDto.Headline.DeeplinkUrl = "/C3PayPlus/Onboarding";
                        }
                    }
                }
                else
                {
                    // Weekends
                    membershipDto.Headline.Id = C3PayPlusMembershipHeadline.ChangeLuckyDrawTicket;
                    membershipDto.Headline.Text = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.HeadlinesSelectLuckyDraw.ToString(), userLanguage).Result;
                    membershipDto.Headline.UrgencyLevel = C3PayPlusMembershipHeadlineUrgencyLevel.Normal;
                    membershipDto.Headline.DeeplinkUrl = "/c3payplus/luckydrawticketdashboard";
                }
            }
        }

        private async Task MapSavings(C3PayPlusMembershipDto membershipDto, C3PayPlusMembershipUser membershipUser, User user, string userLanguage)
        {
            // For total savings, we need to calculate how much the user has saved so far.
            // Savings are: 1- Balance Enquiry subscriptions.
            //              2- Security SMS subscriptions.
            //              3- Free ATM transactions.

            var membershipSavingsDto = await MembershipHelper.GetMembershipUserAllSavingsAmount(_unitOfWork, membershipUser);


            // Calculate subscription savings.
            var balanceEnquirySubscriptionSavings = membershipSavingsDto.BalanceEnquirySubscriptionSavings;
            var securitySmsSubscriptionSavings = membershipSavingsDto.SecuritySmsSubscriptionSavings;

            // Calculate free ATM withdrawal savings.
            var freeAtmWithdrawalSavings = membershipSavingsDto.FreeAtmWithdrawalSavings;


            // Add all savings together.
            var totalSavings = membershipSavingsDto.TotalSavingsAmount.ToString();

            membershipDto.TotalSavings = "AED " + totalSavings;

            // We need to breakdown the savings and show them to the user.
            membershipDto.Savings.Add(new C3PayPlusMembershipSavingsDto()
            {
                Id = C3PayPlusMembershipSavingType.FreeAtmWithdrawal,
                Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.SavingFreeAtmWithdrawal.ToString(), userLanguage).Result,
                Amount = freeAtmWithdrawalSavings.ToString(),
                IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/atm_machine.png"
            });

            membershipDto.Savings.Add(new C3PayPlusMembershipSavingsDto()
            {
                Id = C3PayPlusMembershipSavingType.SecuritySms,
                Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.SavingSecuritySms.ToString(), userLanguage).Result,
                Amount = securitySmsSubscriptionSavings.ToString(),
                IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/security-sms.png"
            });

            membershipDto.Savings.Add(new C3PayPlusMembershipSavingsDto()
            {
                Id = C3PayPlusMembershipSavingType.BalanceEnquiry,
                Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.SavingBalanceEnquiry.ToString(), userLanguage).Result,
                Amount = balanceEnquirySubscriptionSavings.ToString(),
                IconUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/check-balance.png"
            });


            // Check if the user has won in the lucky draw before or not.
            var luckyDrawWinnings = await this._unitOfWork.C3PayPlusMembershipLuckyDrawWinners.FindAsync(x => x.C3PayPlusMembershipUserId == membershipUser.Id);
            if (luckyDrawWinnings != null && luckyDrawWinnings.Count > 0)
            {
                var goldCoinsWinnings = luckyDrawWinnings.Where(x => x.PrizeTypeId == C3PayPlusMembershipPrizeType.GoldCoin).ToList();
                if (goldCoinsWinnings != null && goldCoinsWinnings.Count > 0)
                {
                    membershipDto.Savings.Add(new C3PayPlusMembershipSavingsDto()
                    {
                        Id = C3PayPlusMembershipSavingType.LuckyDrawGoldCoinWinner,
                        Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.SavingLuckyDraw.ToString(), userLanguage).Result,
                        Amount = (goldCoinsWinnings.Count * 250).ToString() // TODO-Raed: Move the value of the gold coin prize to settings.
                    });
                }

                var grandPrizeWinnings = luckyDrawWinnings.Where(x => x.PrizeTypeId == C3PayPlusMembershipPrizeType.CashPrizeAed10K).ToList();
                if (grandPrizeWinnings != null && grandPrizeWinnings.Count > 0)
                {
                    membershipDto.Savings.Add(new C3PayPlusMembershipSavingsDto()
                    {
                        Id = C3PayPlusMembershipSavingType.LuckyDrawGrandPrizeWinner,
                        Title = _translationService.GetTranslatedText(C3PayPlusMembershipTranslationTextCode.SavingLuckyDraw.ToString(), userLanguage).Result,
                        Amount = (grandPrizeWinnings.Count * 10000).ToString() // TODO-Raed: Move the value of the grand prize to settings.
                    });
                }
            }
        }

        private async Task MapPersonalDetails(C3PayPlusMembershipDto membershipDto, C3PayPlusMembershipUser membershipUser)
        {
            // Here, we will return the first name, as well as the profile picture of the current C3Pay+ subscriber.
            membershipDto.FirstName = membershipUser.User.CardHolder.FirstName.Split(' ').First();

            // Generate profile picture read URL.
            var identification = membershipUser.User.Identifications.FirstOrDefault(x => x.Type == Enums.IdentificationType.EmiratesId);
            if (identification != null && string.IsNullOrEmpty(identification.SelfieFileName) == false)
            {
                var profileImageName = string.Join(".", identification.SelfieFileName, Enums.IdentificationDocumentMimeType.jpg.ToString());

                var tryGetBlobReadURLAsync = await _blobStorageService.GetBlobReadURLAsync(profileImageName, _selfieContainer);

                // Parse the original URL
                Uri originalUri = new Uri(tryGetBlobReadURLAsync.Data);

                // Parse the new base URL
                Uri newUri = new Uri(this._settings.CdnUrl);

                // Combine the new base URL with the path and query of the original URL.
                UriBuilder uriBuilder = new UriBuilder(newUri)
                {
                    Path = originalUri.AbsolutePath.Split("/").Last(),
                    Query = originalUri.Query,
                };

                // Use "AbsoluteUri" to remove the port number.
                membershipDto.ProfileImageUrl = uriBuilder.Uri.AbsoluteUri;
            }
            else
            {
                // TODO-Raed: Move this to settings.
                membershipDto.ProfileImageUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/default-winner-c3p.png";
            }

            // Membership duration.
            membershipDto.MembershipDuration = membershipUser.UserSubscribedOn.ToString("dd MMM") + " - " + membershipUser.ValidUntill.ToString("dd MMM");
        }

    }
}
