﻿using C3Pay.Core;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Requests;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using C3Pay.Core.Models.DTOs.Membership.VPN.Base;
using C3Pay.Core.Models.DTOs.Membership.VPN.Requests;
using C3Pay.Core.Models.DTOs.Membership.VPN.Responses;
using C3Pay.Core.Models.DTOs.Membership.Benefit;
using C3Pay.Services.Membership.Commands;
using C3Pay.Services.Membership.Queries;
using C3Pay.Services.Membership.BenefitsShell.Commands;
using C3Pay.Services.Membership.VPN.Commands;
using C3Pay.Services.Membership.VPN.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [InputValidation]
    [Authorize]
    public class MembershipController : Controller
    {

        /// <summary>
        /// 
        /// </summary>
        private readonly IHttpContextAccessor _httpContextAccessor;


        /// <summary>
        /// 
        /// </summary>
        private readonly IMediator _mediator;


        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpContextAccessor"></param>
        /// <param name="mediator"></param>
        public MembershipController(IHttpContextAccessor httpContextAccessor, IMediator mediator)
        {
            _httpContextAccessor = httpContextAccessor;
            _mediator = mediator;
        }

        #region C3Pay+
        /// <summary>
        /// Query: <see cref="GetC3PayPlusMembershipDetailsQuery" />
        /// Handler: <see cref="GetC3PayPlusMembershipDetailsQueryHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("c3-pay-plus")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>> GetC3PayPlusMembershipDetails(CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var tryGetC3PayPlusMembershipDetails = await _mediator.Send(new GetC3PayPlusMembershipDetailsQuery()
            {
                LanguageCode = languageCode,
                UserPhoneNumber = userPhoneNumber
            }, ct);

            if (tryGetC3PayPlusMembershipDetails.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryGetC3PayPlusMembershipDetails.Error.Code
                });
            }


            return Ok(tryGetC3PayPlusMembershipDetails.Value);
        }



        /// <summary>
        /// Command: <see cref="SubscribeToC3PayPlusCommand" />
        /// Handler: <see cref="SubscribeToC3PayPlusCommandHandler" />
        /// </summary>
        /// <param name="request"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpPost("c3-pay-plus/subscribe")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>>> SubscribeToC3PayPlus(SubscribeToC3PayPlusMembershipRequestDto request, CancellationToken ct = default)
        {
            request.UserPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;
            var languageCode = Request.Headers["x-lang-code"];

            var trySubscribeToC3PayPlusMembership = await _mediator.Send(new SubscribeToC3PayPlusCommand()
            {
                UserPhoneNumber = request.UserPhoneNumber,
                C3PayPlusTypeId = request.C3PayPlusTypeId,
                UserLocale = languageCode
            }, ct);


            if (trySubscribeToC3PayPlusMembership.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = trySubscribeToC3PayPlusMembership.Error.Code
                });
            }

            return Ok(trySubscribeToC3PayPlusMembership.Value);
        }


        /// <summary>
        /// Command: <see cref="UnsubscribeFromC3PayPlusCommand" />
        /// Handler: <see cref="UnsubscribeFromC3PayPlusCommandHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpPut("c3-pay-plus/unsubscribe")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<C3PayPlusMembershipUnsubscribeDto>>> UnsubscribeFromC3PayPlus(CancellationToken ct = default)
        {
            var request = new UnsubscribeToC3PayPlusMembershipRequestDto
            {
                UserPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value
            };

            var tryUnsubscribeFromC3PayPlusMembership = await _mediator.Send(new UnsubscribeFromC3PayPlusCommand()
            {
                UserPhoneNumber = request.UserPhoneNumber,
                CancellationToken = ct
            });


            if (tryUnsubscribeFromC3PayPlusMembership.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipUnsubscribeDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryUnsubscribeFromC3PayPlusMembership.Error.Code
                });
            }

            return Ok(tryUnsubscribeFromC3PayPlusMembership.Value);
        }

        /// <summary>
        /// Command: <see cref="RemoveC3PayPlusMembershipCommand" />
        /// Handler: <see cref="RemoveC3PayPlusMembershipCommandHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpDelete("c3-pay-plus/remove")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<C3PayPlusMembershipUnsubscribeDto>>> RemoveC3PayPlusMembership(CancellationToken ct = default)
        {
            var request = new UnsubscribeToC3PayPlusMembershipRequestDto
            {
                UserPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value
            };

            var tryRemoveC3PayPlusMembershipCommand = await _mediator.Send(new RemoveC3PayPlusMembershipCommand()
            {
                UserPhoneNumber = request.UserPhoneNumber,
                CancellationToken = ct
            });


            if (tryRemoveC3PayPlusMembershipCommand.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryRemoveC3PayPlusMembershipCommand.Error.Code
                });
            }

            return Ok();
        }


        /// <summary>
        /// Query: <see cref="GetC3PayPlusMembershipLuckyDrawWinnersQuery" />
        /// Handler: <see cref="GetC3PayPlusMembershipLuckyDrawWinnersQueryHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("c3-pay-plus/lucky-draw-winners")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipLuckyDrawWinnerDto>>>> GetC3PayPlusMembershipLuckyDrawWinners(string date, string week, CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var tryGetC3PayPlusMembershipLuckyDrawWinnersQuery = await _mediator.Send(new GetC3PayPlusMembershipLuckyDrawWinnersQuery()
            {
                UserPhoneNumber = userPhoneNumber,
                DateComponent = date,
                WeekId = week,
                CancellationToken = ct
            });

            if (tryGetC3PayPlusMembershipLuckyDrawWinnersQuery.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipLuckyDrawWinnerDto>>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryGetC3PayPlusMembershipLuckyDrawWinnersQuery.Error.Code
                });
            }


            return Ok(tryGetC3PayPlusMembershipLuckyDrawWinnersQuery.Value);
        }

        /// <summary>
        /// Command: <see cref="AddInsuranceNomineeToC3PayPlusCommand" />
        /// Handler: <see cref="AddInsuranceNomineeToC3PayPlusHandler" />
        /// </summary>
        /// <param name="request"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpPost("c3-pay-plus/life-insurance/nominees")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<C3PayPlusMembershipLifeInsuranceNomineeResponseDto>>> AddInsuranceNominee(C3PayPlusMembershipLifeInsuranceNomineeRequestDto request, CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var addInsuranceNomineeResult = await _mediator.Send(new AddInsuranceNomineeToC3PayPlusCommand()
            {
                CancellationToken = ct,
                UserPhoneNumber = userPhoneNumber,
                NomineeDetails = request,
                LanguageCode = languageCode
            });


            if (addInsuranceNomineeResult.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipLifeInsuranceNomineeResponseDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = addInsuranceNomineeResult.Error.Code
                });
            }

            return Ok(addInsuranceNomineeResult.Value);
        }

        /// <summary>
        /// Command: <see cref="UpdateInsuranceNomineeToC3PayPlusCommand" />
        /// Handler: <see cref="UpdateInsuranceNomineeToC3PayPlusHandler" />
        /// </summary>
        /// <param name="nomineeId"></param>
        /// <param name="request"></param>
        /// <param name="ct"></param>
        /// <returns></returns>

        [HttpPatch("c3-pay-plus/life-insurance/nominees/{nomineeId}")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<C3PayPlusMembershipLifeInsuranceNomineeResponseDto>>> UpdateInsuranceNominee(Guid nomineeId, C3PayPlusMembershipLifeInsuranceNomineeRequestDto request, CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var updateInsuranceNomineeResult = await _mediator.Send(new UpdateInsuranceNomineeToC3PayPlusCommand()
            {
                CancellationToken = ct,
                UserPhoneNumber = userPhoneNumber,
                NomineeId = nomineeId,
                NomineeDetails = request,
                LanguageCode = languageCode
            });


            if (updateInsuranceNomineeResult.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipLifeInsuranceNomineeResponseDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = updateInsuranceNomineeResult.Error.Code
                });
            }

            return Ok(updateInsuranceNomineeResult.Value);
        }

        /// <summary>
        /// Command: <see cref="C3PayPlusMembershipLifeInsuranceNomineeRelationshipLookupQuery" />
        /// Handler: <see cref="GetC3PayPlusMembershipLifeInsuranceNomineeRelationshipLookupQueryHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("c3-pay-plus/life-insurance/relationships")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeDto>>>> GetInsuranceNomineeRelationships(CancellationToken ct = default)
        {
            string languageCode = Request.Headers["x-lang-code"];

            var lookupQueryResult = await _mediator.Send(new C3PayPlusMembershipLifeInsuranceNomineeRelationshipLookupQuery()
            {
                LanguageCode = languageCode,
                CancellationToken = ct
            });

            if (lookupQueryResult.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeDto>>()
                {
                    IsSuccessful = false,
                    ErrorMessage = lookupQueryResult.Error.Code
                });
            }
            return Ok(lookupQueryResult.Value);

        }

        /// <summary>
        /// Command: <see cref="C3PayPlusMembershipSupportedCountryLookupQuery" />
        /// Handler: <see cref="C3PayPlusMembershipSupportedCountryLookupQueryHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("c3-pay-plus/life-insurance/supported-countries")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipSupportedCountryDto>>>> GetInsuranceSupportedCountries(CancellationToken ct = default)
        {
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var lookupQueryResult = await _mediator.Send(new C3PayPlusMembershipSupportedCountryLookupQuery()
            {
                CancellationToken = ct,
                UserPhoneNumber = userPhoneNumber
            });

            if (lookupQueryResult.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipSupportedCountryDto>>()
                {
                    IsSuccessful = false,
                    ErrorMessage = lookupQueryResult.Error.Code
                });
            }
            return Ok(lookupQueryResult.Value);

        }


        /// <summary>
        /// Command: <see cref="C3PayPlusMembershipSupportedCountryLookupQuery" />
        /// Handler: <see cref="C3PayPlusMembershipSupportedCountryLookupQueryHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("c3-pay-plus/life-insurance/certificate")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<byte[]>>> DownloadLifeInsuranceCertificate(CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var tryDownloadC3PayPlusMembershipLifeInsuranceCertificateQuery = await _mediator.Send(new DownloadC3PayPlusMembershipLifeInsuranceCertificateQuery()
            {
                UserPhoneNumber = userPhoneNumber,
                CancellationToken = ct
            });

            if (tryDownloadC3PayPlusMembershipLifeInsuranceCertificateQuery.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<byte[]>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryDownloadC3PayPlusMembershipLifeInsuranceCertificateQuery.Error.Code
                });
            }


            return Ok(tryDownloadC3PayPlusMembershipLifeInsuranceCertificateQuery.Value);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("c3-pay-plus/lucky-draw/tickets")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipLuckyDrawTicket>>>> GetLuckyDrawTickets(CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var tryGetLuckyDrawTicketsQuery = await _mediator.Send(new GetLuckyDrawTicketsQuery()
            {
                UserPhoneNumber = userPhoneNumber,
                CancellationToken = ct
            });

            if (tryGetLuckyDrawTicketsQuery.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipLuckyDrawTicket>>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryGetLuckyDrawTicketsQuery.Error.Code
                });
            }


            return Ok(tryGetLuckyDrawTicketsQuery.Value);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpPut("c3-pay-plus/lucky-draw")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<C3PayPlusMembershipTicketUpdateDto>>> UpdateLuckyDrawTicket(C3PayPlusMembershipUpdateLuckyDrawTicketRequestDto request, CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var tryUpdateLuckyDrawTicketCommand = await _mediator.Send(new UpdateLuckyDrawTicketCommand()
            {
                UserPhoneNumber = userPhoneNumber,
                CancellationToken = ct,
                NewTicket = request.TicketNumber
            });

            if (tryUpdateLuckyDrawTicketCommand.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipTicketUpdateDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryUpdateLuckyDrawTicketCommand.Error.Code
                });
            }


            return Ok(tryUpdateLuckyDrawTicketCommand.Value);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("c3-pay-plus/lucky-draw/winning-periods")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipLuckyDrawWinningsPeriodDto>>>> GetLuckyDrawWinningsPeriods(CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var tryGetLuckyDrawWinningsPeriodsQuery = await _mediator.Send(new GetLuckyDrawWinningsPeriodsQuery()
            {
                UserPhoneNumber = userPhoneNumber,
                CancellationToken = ct
            });

            if (tryGetLuckyDrawWinningsPeriodsQuery.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipLuckyDrawWinningsPeriodDto>>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryGetLuckyDrawWinningsPeriodsQuery.Error.Code
                });
            }


            return Ok(tryGetLuckyDrawWinningsPeriodsQuery.Value);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("c3-pay-plus/savings")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipSavingsDetailsDto>>>> GetC3PayPlusMembershipSavings(CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var tryGetC3PayPlusMembershipSavingsDetails = await _mediator.Send(new GetC3PayPlusMembershipSavingsDetailsQuery()
            {
                UserPhoneNumber = userPhoneNumber,
                CancellationToken = ct,
                LanguageCode = languageCode
            });

            if (tryGetC3PayPlusMembershipSavingsDetails.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipSavingsDetailsDto>>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryGetC3PayPlusMembershipSavingsDetails.Error.Code
                });
            }


            return Ok(tryGetC3PayPlusMembershipSavingsDetails.Value);
        }

        /// <summary>
        /// Query: <see cref=C3PayPlusMembershipCancellationDetailsQuery" />
        /// Handler: <see cref="C3PayPlusMembershipCancellationDetailsQueryHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("c3-pay-plus/cancellation-details")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipCancellationDetailsDto>>>> GetCancellationDetails(CancellationToken ct = default)
        {
            string languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value?.Trim();


            var lookupQueryResult = await _mediator.Send(new C3PayPlusMembershipCancellationDetailsQuery()
            {
                LanguageCode = languageCode,
                UserPhoneNumber = userPhoneNumber,
                CancellationToken = ct
            });

            if (lookupQueryResult.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<IEnumerable<C3PayPlusMembershipCancellationDetailsDto>>()
                {
                    IsSuccessful = false,
                    ErrorMessage = lookupQueryResult.Error.Code
                });
            }
            return Ok(lookupQueryResult.Value);

        }

        /// <summary>
        /// Query: <see cref="C3PayPlusMembershipDiscountDetailsQuery" />
        /// Handler: <see cref="C3PayPlusMembershipDiscountDetailsQueryHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("c3-pay-plus/discount")]
        public async Task<ActionResult<C3PayPlusMembershipBaseDto<C3PayPlusMembershipCancellationDetailsDto>>> GetDiscountDetails(CancellationToken ct = default)
        {
            string languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value?.Trim();

            var tryC3PayPlusMembershipDiscountDetailsQuery = await _mediator.Send(new C3PayPlusMembershipDiscountDetailsQuery()
            {
                LanguageCode = languageCode,
                UserPhoneNumber = userPhoneNumber,
                CancellationToken = ct
            });

            if (tryC3PayPlusMembershipDiscountDetailsQuery.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipCancellationDetailsDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryC3PayPlusMembershipDiscountDetailsQuery.Error.Code
                });
            }
            return Ok(tryC3PayPlusMembershipDiscountDetailsQuery.Value);

        }

        #endregion

        #region VPN
        /// <summary>
        /// Query: <see cref="GetVpnMembershipDetailsQuery" />
        /// Handler: <see cref="GetVpnMembershipDetailsQueryHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("vpn")]
        public async Task<ActionResult<VpnMembershipBaseDto<VpnMembershipDto>>> GetVpnMembershipDetails(CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var tryGetVpnMembershipDetailsQuery = await _mediator.Send(new GetVpnMembershipDetailsQuery()
            {
                LanguageCode = languageCode,
                UserPhoneNumber = userPhoneNumber
            }, ct);

            if (tryGetVpnMembershipDetailsQuery.IsFailure)
            {
                return BadRequest(new VpnMembershipBaseDto<VpnMembershipDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = tryGetVpnMembershipDetailsQuery.Error.Code
                });
            }


            return Ok(tryGetVpnMembershipDetailsQuery.Value);
        }



        /// <summary>
        /// Command: <see cref="SubscribeToVpnMembershipRequestCommand" />
        /// Handler: <see cref="SubscribeToVpnMembershipRequestCommandHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpPost("vpn/subscribe")]
        public async Task<ActionResult<VpnMembershipBaseDto<VpnMembershipDto>>> SubscribeToVpn(CancellationToken ct = default)
        {
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;
            
            var languageCode = Request.Headers["x-lang-code"];

            var trySubscribeToVpnMembershipRequestDto = await _mediator.Send(new SubscribeToVpnMembershipRequestCommand()
            {
                UserPhoneNumber = userPhoneNumber,
                LanguageCode = languageCode
            }, ct);


            if (trySubscribeToVpnMembershipRequestDto.IsFailure)
            {
                return BadRequest(new C3PayPlusMembershipBaseDto<C3PayPlusMembershipSubscribeDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = trySubscribeToVpnMembershipRequestDto.Error.Code
                });
            }

            return Ok(trySubscribeToVpnMembershipRequestDto.Value);
        }

        #endregion

        #region Benefits Shell (Generic)

        /// <summary>
        /// Benefits subscription endpoint - currently supports VPN (benefitId=1) only
        /// Uses generic benefits command that preserves all VPN data in standardized format
        /// </summary>
        /// <param name="benefitId"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpPost("benefits/{benefitId}/subscribe")]
        public async Task<ActionResult<Core.Models.DTOs.Membership.Benefit.BenefitBaseDto<Core.Models.DTOs.Membership.Benefit.BenefitSubscribeDto>>> SubscribeToBenefit(int benefitId, CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            // Only handle VPN for now (benefitId = 1) - use generic benefits command
            if (benefitId == ConstantParam.VpnBenefitId)
            {
                // Use generic benefits command (already handles VPN conversion properly)
                var result = await _mediator.Send(new SubscribeToBenefitCommand()
                {
                    LanguageCode = languageCode,
                    UserPhoneNumber = userPhoneNumber,
                    BenefitId = benefitId
                }, ct);

                if (result.IsFailure)
                {
                    return BadRequest(new Core.Models.DTOs.Membership.Benefit.BenefitBaseDto<Core.Models.DTOs.Membership.Benefit.BenefitSubscribeDto>()
                    {
                        IsSuccessful = false,
                        ErrorMessage = result.Error.Code
                    });
                }

                return Ok(result.Value);
            }

            // For all other benefits, return NotImplemented until they're added
            return StatusCode(501, new { Message = "Benefit subscription not implemented for this benefit type" });
        }

        #endregion
    }}
