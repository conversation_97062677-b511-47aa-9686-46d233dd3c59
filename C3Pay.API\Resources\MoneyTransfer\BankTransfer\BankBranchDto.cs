﻿using C3Pay.Core;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace C3Pay.API.Resources.MoneyTransfer
{
    public class BankBranchDto
    {
        public int BankId { get; set; }
        public int BranchId { get; set; }
        public string BankName { get; set; }
        public string BankBranchName { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string BankCode { get; set; }
        public string BankBranchCode { get; set; }
        public string BankLogoUrl { get; set; }
        public AccountNumberLengthLimitType? AccountNumberLengthLimitType { get; set; }
        public List<string> AccountNumberLengthLimitValues { get; set; }

    }
}
