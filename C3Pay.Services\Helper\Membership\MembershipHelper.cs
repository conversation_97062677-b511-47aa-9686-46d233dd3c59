﻿using C3Pay.Core;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using C3Pay.Core.Models.Settings.Membership;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace C3Pay.Services.Helper.Membership
{
    public static class MembershipHelper
    {
        private const double BALANCE_ENQUIRY_SAVINGS_VALUE = 1.05;
        private const double SECURITY_SMS_SAVINGS_VALUE = 3.5;
        private const double FREE_ATM_WITHDRAWAL_SAVINGS_VALUE = 2.5;

        //Money Transfer Icon URL
        public const string NATIONALITY_ICON_URL_DEFAULT = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-money.png";
        public const string NATIONALITY_ICON_URL_IND = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-india.png";
        public const string NATIONALITY_ICON_URL_PAK = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-pak.png";
        public const string NATIONALITY_ICON_URL_LKA = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-lk.png";
        public const string NATIONALITY_ICON_URL_PHL = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-ph.png";
        public const string NATIONALITY_ICON_URL_BGD = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-bgd.png";
        public const string NATIONALITY_ICON_URL_NPL = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/Mid_Nav_Icons-npl.png";


        //Icon Url
        public const string CASHBACK_BENEFIT_ICON_URL = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/canellation-details-cashback.png";
        public const string INSURANCE_BENEFIT_ICON_URL = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/canellation-details-life-insurance.png";
        public const string LUCKYDRAW_BENEFIT_ICON_URL = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/canellation-details-lucky-draw.png";
        public const string SAVEMONEY_BENEFIT_ICON_URL = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/canellation-details-savings.png";
        public const string STOP_BENEFIT_ICON_URL = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/stop-icon.png";


        public static string GetMoneyTransferIconURL(string nationality)
        {
            // Set the dynamic icon of the money transfer benefit to match the user's nationality.
            var iconUrl = NATIONALITY_ICON_URL_DEFAULT;
            if (nationality == "IND")
            {
                iconUrl = NATIONALITY_ICON_URL_IND;
            }
            else if (nationality == "PAK")
            {
                iconUrl = NATIONALITY_ICON_URL_PAK;
            }
            else if (nationality == "LKA")
            {
                iconUrl = NATIONALITY_ICON_URL_LKA;
            }
            else if (nationality == "PHL")
            {
                iconUrl = NATIONALITY_ICON_URL_PHL;
            }
            else if (nationality == "BGD")
            {
                iconUrl = NATIONALITY_ICON_URL_BGD;
            }
            else if (nationality == "NPL")
            {
                iconUrl = NATIONALITY_ICON_URL_NPL;
            }

            return iconUrl;
        }

        public static async Task<double> GetCashbackSavingsAmount(IUnitOfWork unitOfWork, Guid userId)
        {
            double cashbackSavingsAmount = 0;
            // Get cashback savings done so far.
            var cashbackClaims = await unitOfWork.C3PayPlusMembershipCashbackClaims.FindAsync(x => x.UserId == userId);
            if (cashbackClaims != null && cashbackClaims.Count > 0)
            {
                cashbackSavingsAmount = Convert.ToDouble(cashbackClaims.Sum(x => x.RefundAmount));
            }

            return cashbackSavingsAmount;
        }

        public static async Task<double> GetFreeMoneyTransfersSavingsAmount(IUnitOfWork unitOfWork, Guid userId)
        {
            double freeMoneyTransfersSavingsAmount = 0;

            var freeMoneyTransfers = await unitOfWork.C3PayPlusMembershipFreeMoneyTransferClaims.FindAsync(x => x.UserId == userId);
            if (freeMoneyTransfers != null && freeMoneyTransfers.Count > 0)
            {
                freeMoneyTransfersSavingsAmount = Convert.ToDouble(freeMoneyTransfers.Sum(x => x.RefundAmount));
            }
            return freeMoneyTransfersSavingsAmount;
        }

        public static async Task<C3PayPlusMembershipUserSavingDetailsDto> GetMembershipUserAllSavingsAmount(IUnitOfWork unitOfWork, C3PayPlusMembershipUser membershipUser)
        {

            // For total savings, we need to calculate how much the user has saved so far.
            // Savings are: 1- Balance Enquiry subscriptions.
            //              2- Security SMS subscriptions.
            //              3- Free ATM transactions.

            // To get how much the user has saved for subscriptions, we need to calculate how many months the user has been subscribed for.
            var subscriptionDuration = GetMonthsDifference(membershipUser.UserSubscribedOn);

            // Calculate subscription savings.
            var balanceEnquirySubscriptionSavings = Math.Round(subscriptionDuration * BALANCE_ENQUIRY_SAVINGS_VALUE, 2);
            var securitySmsSubscriptionSavings = Math.Round(subscriptionDuration * SECURITY_SMS_SAVINGS_VALUE, 2);

            // Calculate free ATM withdrawal savings.
            var freeAtmWithdrawals = await unitOfWork.C3PayPlusMembershipTransactions.CountAsync(x => x.C3PayPlusMembershipUserId == membershipUser.Id
                                                                                                            && x.TransactionType == C3PayPlusMembershipTransactionType.AtmWithdrawalFeeReversal);
            var freeAtmWithdrawalSavings = Math.Round(freeAtmWithdrawals * FREE_ATM_WITHDRAWAL_SAVINGS_VALUE, 2);


            var userSavingsDto = new C3PayPlusMembershipUserSavingDetailsDto()
            {
                BalanceEnquirySubscriptionSavings = balanceEnquirySubscriptionSavings,
                SecuritySmsSubscriptionSavings = securitySmsSubscriptionSavings,
                FreeAtmWithdrawalSavings = freeAtmWithdrawalSavings,
                FreeMoneyTransfersSavings = 0,
                CashbackSavings = 0
            };

            return userSavingsDto;
        }

        public static async Task<string> GetUserProfileImageUrl(IBlobStorageService blobStorageService, string appSettingsCdnUrl, string profileImageUrl)
        {
            string _defaultWinnerImageUrl = "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/default-winner-c3p.png";
            string _selfieContainer = "selfies";

            if (profileImageUrl != _defaultWinnerImageUrl)
            {
                var tryGetBlobReadURLAsync = await blobStorageService.GetBlobReadURLAsync(profileImageUrl, _selfieContainer);

                // Parse the original URL
                Uri originalUri = new Uri(tryGetBlobReadURLAsync.Data);

                // Parse the new base URL
                Uri newUri = new Uri(appSettingsCdnUrl);

                // Combine the new base URL with the path and query of the original URL.
                UriBuilder uriBuilder = new UriBuilder(newUri)
                {
                    Path = originalUri.AbsolutePath.Split("/").Last(),
                    Query = originalUri.Query,
                };

                // Use "AbsoluteUri" to remove the port number.
                profileImageUrl = uriBuilder.Uri.AbsoluteUri;
            }

            return profileImageUrl;
        }
        private static int GetMonthsDifference(DateTime startDate)
        {
            var now = DateTime.Now;
            int yearsDifference = now.Year - startDate.Year;
            int monthsDifference = now.Month - startDate.Month;

            // Calculate the total months difference.
            int totalMonthsDifference = yearsDifference * 12 + monthsDifference;

            // If the dates are in the same year and month, the difference should be 1.
            if (yearsDifference == 0 && monthsDifference == 0)
            {
                return 1;
            }

            return Math.Abs(totalMonthsDifference) + 1;
        }
    }
}
