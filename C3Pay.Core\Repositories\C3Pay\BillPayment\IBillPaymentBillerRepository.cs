﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.Structs;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace C3Pay.Core.Repositories.C3Pay.BillPayment
{ 
    public interface IBillPaymentBillerRepository : IRepository<BillPaymentBiller>
    {
        Task<List<BillPaymentBiller>> FindBillerAsync(Guid userId = default, string providerCode = default);
        Task<BillPaymentBiller> FindBillerByIdAsync(Guid billerId);
        Task<BillPaymentBiller> FindBillerByIdAndUserAsync(Guid billerId, Guid userId);
        Task<Tuple<List<BillPaymentBillerStruct>, int>> Search(List<Expression<Func<BillPaymentBiller, bool>>> searchBillPaymentParameters,string searchCategory, int? page, int? size);  
        Task<int> GetLocalBillerCountByUserAsync(Guid userId = default);
        Task<int> GetInternationalBillerCountByUserAsync(Guid userId = default);

        Task<List<BillPaymentBiller>> FindExpiredAmountBillerAsync(int expirationHours);
        Task<List<BillPaymentBiller>> OptimizedFindBillerAsync(Guid userId = default, string providerCode = default);
        Task<List<BillPaymentBiller>> FindExistingBillersAsync(Guid userId = default);
    }
}
