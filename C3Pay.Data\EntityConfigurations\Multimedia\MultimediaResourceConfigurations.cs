﻿using C3Pay.Core;
using C3Pay.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Collections.Generic;

namespace C3Pay.Data.EntityConfigurations
{
    public class MultimediaResourceConfigurations : IEntityTypeConfiguration<MultimediaResource>
    {
        public void Configure(EntityTypeBuilder<MultimediaResource> builder)
        {
            builder.ToTable("MultimediaResources");

            builder.HasKey(c => c.Id);

            builder.Property(c => c.FeatureId)
                .IsRequired();

            builder.Property(c => c.NationalityCode)
                .HasColumnType("varchar(3)")
                .HasMaxLength(3);

            builder.Property(c => c.Language)
                .HasColumnType("varchar(5)")
                .HasMaxLength(5);

            builder.Property(c => c.Type)
                .HasColumnType("int")
                .IsRequired();

            builder.Property(c => c.ThumbnailUrl)
                .HasMaxLength(250);

            builder.Property(c => c.Url)
                .HasMaxLength(250);

            builder.Property(c => c.Identifier)
                .HasColumnType("varchar(100)")
                .HasMaxLength(100);

            var multimediaData = new List<MultimediaResource>()
            {
                new MultimediaResource() { Id = 1,  FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Video, Language = "hi", ThumbnailUrl = "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/638332475/rendition/360p/file.mp4?loc=external&signature=78f4cbd07481885778965da0f1098683c16bf5bd8b4b9d46312caf3ee6285c72" },
                new MultimediaResource() { Id = 2,  FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "PAK", Type = MultimediaType.Video, Language = "hi", ThumbnailUrl = "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/638332791/rendition/360p/file.mp4?loc=external&signature=bcdf80cb45792dd654e02b9eef808af17da01b7a6902895f721e4f22f8f10cbe" },
                new MultimediaResource() { Id = 3,  FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "NPL", Type = MultimediaType.Video, Language = "en", ThumbnailUrl = "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/638332739/rendition/360p/file.mp4?loc=external&signature=c9861055f0846001b86cc2559442000a5b92a3a333e91931704810f025a8e70a" },
                new MultimediaResource() { Id = 4,  FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "PHL", Type = MultimediaType.Video, Language = "en", ThumbnailUrl = "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/638333304/rendition/360p/file.mp4?loc=external&signature=02ed7a9cf3acfd2fe5f238b20308f3afc1ec5c07ea098177916127732185b12f" },
                new MultimediaResource() { Id = 5,  FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "LKA", Type = MultimediaType.Video, Language = "en", ThumbnailUrl = "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/638333368/rendition/360p/file.mp4?loc=external&signature=3620db85a3c10c4c8e355e7bb74984149bbc5c7cb7cca020ba54f26c1ec65261" },
                new MultimediaResource() { Id = 6,  FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "BGD", Type = MultimediaType.Video, Language = "bn", ThumbnailUrl = "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/638332062/rendition/360p/file.mp4?loc=external&signature=c3d681362a341967310e3eb433ac97bb49f358a47d0512824143aa0d5493d5a9" },
                new MultimediaResource() { Id = 7,  FeatureId = (int)FeatureType.ReferralProgram, NationalityCode = "IND", Type = MultimediaType.Video, Language = "hi", ThumbnailUrl = "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/644071052/rendition/360p/file.mp4?loc=external&signature=ba3ec9b4c997591270dc2b0e9c0d476f2585fb82c554aa7bd4be2434a7671535" },
                new MultimediaResource() { Id = 8,  FeatureId = (int)FeatureType.ReferralProgram, NationalityCode = "PAK", Type = MultimediaType.Video, Language = "hi", ThumbnailUrl = "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/644071052/rendition/360p/file.mp4?loc=external&signature=ba3ec9b4c997591270dc2b0e9c0d476f2585fb82c554aa7bd4be2434a7671535" },
                new MultimediaResource() { Id = 9,  FeatureId = (int)FeatureType.ReferralProgram, NationalityCode = "NPL", Type = MultimediaType.Video, Language = "en", ThumbnailUrl = "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/644071052/rendition/360p/file.mp4?loc=external&signature=ba3ec9b4c997591270dc2b0e9c0d476f2585fb82c554aa7bd4be2434a7671535" },
                new MultimediaResource() { Id = 10, FeatureId = (int)FeatureType.ReferralProgram, NationalityCode = "PHL", Type = MultimediaType.Video, Language = "en", ThumbnailUrl = "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/644071251/rendition/360p/file.mp4?loc=external&signature=03d7efa87ea502df255f80b0d4bc34a4958e2dde4b9f1b9690ce3b24feb744ec" },
                new MultimediaResource() { Id = 11, FeatureId = (int)FeatureType.ReferralProgram, NationalityCode = "LKA", Type = MultimediaType.Video, Language = "en", ThumbnailUrl = "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/644071251/rendition/360p/file.mp4?loc=external&signature=03d7efa87ea502df255f80b0d4bc34a4958e2dde4b9f1b9690ce3b24feb744ec" },
                new MultimediaResource() { Id = 12, FeatureId = (int)FeatureType.ReferralProgram, NationalityCode = "BGD", Type = MultimediaType.Video, Language = "bn", ThumbnailUrl = "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/644071052/rendition/360p/file.mp4?loc=external&signature=ba3ec9b4c997591270dc2b0e9c0d476f2585fb82c554aa7bd4be2434a7671535" },
                new MultimediaResource() { Id = 13, FeatureId = (int)FeatureType.BillPayments, NationalityCode = "IND", Type = MultimediaType.Video, Language = "hi", ThumbnailUrl = "***********************************************************************?sp=r&st=2022-03-17T07:04:24Z&se=2025-12-31T15:04:24Z&spr=https&sv=2020-08-04&sr=b&sig=6FesClJTvTEH5GWpDAmkpiG%2BiIUBgQwxbwyvxsgkp3c%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/710823432/rendition/360p/file.mp4?loc=external&signature=bc5dbc45a1afd3ff9c5a7537eda576d1cbbf46c82edb222f5aa13af57b0ff936" },
                new MultimediaResource() { Id = 14, FeatureId = (int)FeatureType.BillPayments, NationalityCode = "PAK", Type = MultimediaType.Video, Language = "hi", ThumbnailUrl = "***********************************************************************?sp=r&st=2022-03-17T07:07:13Z&se=2025-03-17T15:07:13Z&spr=https&sv=2020-08-04&sr=b&sig=VHtQmfSzUOou%2Fn1xwKrDwZQjoWhwH1fBAV05Ab6J2G8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/710822242/rendition/360p/file.mp4?loc=external&signature=dd707730fc4125e547323be1d546fda1fbc7f65ca51900d4ba66fcdce9703cc8" },
                new MultimediaResource() { Id = 15, FeatureId = (int)FeatureType.BillPayments, NationalityCode = "NPL", Type = MultimediaType.Video, Language = "en", ThumbnailUrl = "***********************************************************************?sp=r&st=2022-03-17T07:07:46Z&se=2025-03-17T15:07:46Z&spr=https&sv=2020-08-04&sr=b&sig=Mcq041UQ7AEGJrOlS0DC8YBxzJZ2W7C7%2FVe5TwBaF%2B8%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/710777315/rendition/360p/file.mp4?loc=external&signature=427f91fe9f3a35a9585f630758dbb80f2a014a2f2d11599da3e080fa682aa45d" },
                new MultimediaResource() { Id = 16, FeatureId = (int)FeatureType.BillPayments, NationalityCode = "PHL", Type = MultimediaType.Video, Language = "en", ThumbnailUrl = "***********************************************************************?sp=r&st=2022-03-17T07:08:16Z&se=2025-03-17T15:08:16Z&spr=https&sv=2020-08-04&sr=b&sig=z8bSU5YbioY%2BioXX629pV%2BF7vrMQ8xNr7LspcyOjiJY%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/710823250/rendition/360p/file.mp4?loc=external&signature=97135538aeb127d8fdac343c7f7dba2ab5c705d6e4d7b3347f8ca8f58853a764" },
                new MultimediaResource() { Id = 17, FeatureId = (int)FeatureType.BillPayments, NationalityCode = "LKA", Type = MultimediaType.Video, Language = "en", ThumbnailUrl = "***********************************************************************?sp=r&st=2022-03-17T12:01:41Z&se=2025-03-17T20:01:41Z&spr=https&sv=2020-08-04&sr=b&sig=1Ga10HwKBkHuQ5SpjVMmQmZis9GKDGGqPRKmy4%2FkYpE%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/710822089/rendition/360p/file.mp4?loc=external&signature=d637a95a186c7831b0358870c828b57378e2afb3c5f5a3ece6a291776c133b8f" },
                new MultimediaResource() { Id = 18, FeatureId = (int)FeatureType.BillPayments, NationalityCode = "BGD", Type = MultimediaType.Video, Language = "bn", ThumbnailUrl = "***********************************************************************?sp=r&st=2022-03-17T07:04:24Z&se=2025-12-31T15:04:24Z&spr=https&sv=2020-08-04&sr=b&sig=6FesClJTvTEH5GWpDAmkpiG%2BiIUBgQwxbwyvxsgkp3c%3D", Url = "https://player.vimeo.com/progressive_redirect/playback/710823432/rendition/360p/file.mp4?loc=external&signature=bc5dbc45a1afd3ff9c5a7537eda576d1cbbf46c82edb222f5aa13af57b0ff936" },
                new MultimediaResource() { Id = 19, FeatureId = (int)FeatureType.UnEmpInsurance, NationalityCode = "IND", Type = MultimediaType.Video, Language = "hi", Url = "https://player.vimeo.com/progressive_redirect/playback/786255453/rendition/360p/file.mp4?loc=external&signature=20c3820b7d2a6f9836b3b90e41ee727394a74f0810174109b97c24707c9258eb" },
                new MultimediaResource() { Id = 20, FeatureId = (int)FeatureType.UnEmpInsurance, NationalityCode = "PHL", Type = MultimediaType.Video, Language = "en", Url = "https://player.vimeo.com/progressive_redirect/playback/786493764/rendition/360p/file.mp4?loc=external&signature=8e65c18fcdd646448fe799c83c2668dca72799c5cdbf53f2af9f7296189ae125" },
                new MultimediaResource() { Id = 21, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Audio, Language = "hi", Url = "*************************************************************************************", Identifier = "AddBeneficiary_Main" },
                new MultimediaResource() { Id = 22, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Audio, Language = "hi", Url = "*************************************************************************", Identifier = "AddBeneficiary_PersonalDetail" },
                new MultimediaResource() { Id = 23, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Audio, Language = "hi", Url = "*************************************************************************", Identifier = "AddBeneficiary_SelectCountry" },
                new MultimediaResource() { Id = 24, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Audio, Language = "hi", Url = "************************************************************************", Identifier = "AddBeneficiary_IFSC" },
                new MultimediaResource() { Id = 25, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Audio, Language = "hi", Url = "************************************************************************", Identifier = "AddBeneficiary_AccountNumber" },
                new MultimediaResource() { Id = 26, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Audio, Language = "hi", Url = "************************************************************************", Identifier = "AddBeneficiary_Review" },
                new MultimediaResource() { Id = 27, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Audio, Language = "hi", Url = "************************************************************************", Identifier = "AddBeneficiary_SendNow" },
                new MultimediaResource() { Id = 28, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Text, Language = "hi", Url = "**********************************************************************", Identifier = "AddBeneficiary_Main" },
                new MultimediaResource() { Id = 29, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Text, Language = "hi", Url = "**********************************************************************", Identifier = "AddBeneficiary_PersonalDetail" },
                new MultimediaResource() { Id = 30, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Text, Language = "hi", Url = "**********************************************************************", Identifier = "AddBeneficiary_SelectCountry" },
                new MultimediaResource() { Id = 31, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Text, Language = "hi", Url = "**********************************************************************", Identifier = "AddBeneficiary_IFSC" },
                new MultimediaResource() { Id = 32, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Text, Language = "hi", Url = "**********************************************************************", Identifier = "AddBeneficiary_AccountNumber" },
                new MultimediaResource() { Id = 33, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Text, Language = "hi", Url = "**********************************************************************", Identifier = "AddBeneficiary_Review" },
                new MultimediaResource() { Id = 34, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Text, Language = "hi", Url = "**********************************************************************", Identifier = "AddBeneficiary_SendNow" },
                new MultimediaResource() { Id = 35, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Image, Language = "hi", Url = "*********************************************************************", Identifier = "AddBeneficiary_Main" },
                new MultimediaResource() { Id = 36, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Image, Language = "hi", Url = "*********************************************************************", Identifier = "AddBeneficiary_PersonalDetail" },
                new MultimediaResource() { Id = 37, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Image, Language = "hi", Url = "*********************************************************************", Identifier = "AddBeneficiary_SelectCountry" },
                new MultimediaResource() { Id = 38, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Image, Language = "hi", Url = "*********************************************************************", Identifier = "AddBeneficiary_IFSC" },
                new MultimediaResource() { Id = 39, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Image, Language = "hi", Url = "*********************************************************************", Identifier = "AddBeneficiary_AccountNumber" },
                new MultimediaResource() { Id = 40, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Image, Language = "hi", Url = "*********************************************************************", Identifier = "AddBeneficiary_Review" },
                new MultimediaResource() { Id = 41, FeatureId = (int)FeatureType.MoneyTransfer, NationalityCode = "IND", Type = MultimediaType.Image, Language = "hi", Url = "*********************************************************************", Identifier = "AddBeneficiary_SendNow" },
                new MultimediaResource() { Id = 42, FeatureId = (int)FeatureType.UnEmpInsurance_MySalary, NationalityCode = "IND", Type = MultimediaType.Video, Language = "hi", Url = "https://player.vimeo.com/progressive_redirect/playback/*********/rendition/360p/file.mp4?loc=external&signature=bd613942410360fc89de7f427b757303f3b9046c2a145e5d87eb0cb3555fadec" },
                new MultimediaResource() { Id = 43, FeatureId = (int)FeatureType.UnEmpInsurance_MySalary, NationalityCode = "PHL", Type = MultimediaType.Video, Language = "en", Url = "https://player.vimeo.com/progressive_redirect/playback/803790226/rendition/360p/file.mp4?loc=external&signature=b65a91f6301811778c021b67d980010c1f8ad7a8d3ca5c204e87d27b72432cfc" },
                new MultimediaResource() { Id = 44, FeatureId = (int)FeatureType.Subscription_SecuritySMS, NationalityCode = "IND", Type = MultimediaType.Video, Language = "hi", Url = " https://player.vimeo.com/progressive_redirect/playback/816877470/rendition/360p/file.mp4?loc=external&signature=18aa360dc76d720d73d150c8655ca9ec38d2464a51995fc6997053884044fa1d" },
                new MultimediaResource() { Id = 45, FeatureId = (int)FeatureType.Subscription_SecuritySMS, NationalityCode = "PHL", Type = MultimediaType.Video, Language = "en", Url = "https://player.vimeo.com/progressive_redirect/playback/816877509/rendition/360p/file.mp4?loc=external&signature=60f788ad44ab2c74a6792b40d43f19d286be4f64c25c0b48626e40ff2a797389" },
                new MultimediaResource() { Id = 46, FeatureId = (int)FeatureType.MobileRecharge_Promotion, NationalityCode = "PAK", Type = MultimediaType.Image, Language = "en", Url = "***********************************************************************************" },
                new MultimediaResource() { Id = 48, FeatureId = (int)FeatureType.ForgotPassword, NationalityCode = "IND", Type = MultimediaType.Video, Language = "hi", Url = "https://eae-c3pay-cdn-p.azureedge.net/money-transfer-videos/ILOE_English_tutorial.mp4" },
                new MultimediaResource() { Id = 49, FeatureId = (int)FeatureType.ForgotPassword, NationalityCode = "PHL", Type = MultimediaType.Video, Language = "en", Url = "https://eae-c3pay-cdn-p.azureedge.net/money-transfer-videos/hindi_manahil_iloe_(c3pay).mp4" },
            };

            builder.HasData(multimediaData.ToArray());
        }
    }
}
