﻿using AutoMapper;
using C3Pay.API.Resources;
using C3Pay.API.Resources.BillPayment;
using C3Pay.API.Resources.BillPayment.BillPaymentProcessPaymentResponseObjects;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay;
using Edenred.Common.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Documents;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using User = C3Pay.Core.Models.User;
using System.Resources;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// Bill Payment Controller
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    [Authorize]
    //[DeviceAuthorize] 
    public class BillPaymentController : BaseController
    {
        private readonly IMapper _mapper;
        private readonly IBillPaymentProcessingService _billPaymentProcessingService;
        private readonly ILookupService _lookupService;
        private readonly BillPaymentSettings _billPaymentSettings;
        private readonly GeneralSettings _generalSettings;
        private readonly string _successfulStatus = "Success";
        private readonly string _failedStatus = "Failure";

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="billPaymentProcessingService"></param>
        /// <param name="mapper"></param>
        /// <param name="lookupService"></param>
        /// <param name="httpContextAccessor"></param>
        /// <param name="userService"></param> 
        /// <param name="logger"></param>
        public BillPaymentController(IBillPaymentProcessingService billPaymentProcessingService,
            IMapper mapper,
            ILookupService lookupService,
            IHttpContextAccessor httpContextAccessor,
            IUserService userService,
            IOptions<BillPaymentSettings> billPaymentSettings,
            IOptions<GeneralSettings> generalSettings,
            ILogger<BillPaymentController> logger) : base(httpContextAccessor, userService, logger)
        {
            this._mapper = mapper;
            this._billPaymentProcessingService = billPaymentProcessingService;
            this._lookupService = lookupService;
            this._billPaymentSettings = billPaymentSettings.Value;
            this._generalSettings = generalSettings.Value;
        }



        #region Bill Payment Module 

        /// <summary>
        /// Get All Countries for Bill Payment
        /// </summary>
        /// <returns></returns>
        [HttpGet("countries")]
        public async Task<ActionResult<IEnumerable<BillPaymentCountryDto>>> GetCountries()
        {
            await GetLoggedInUser();

            if (_user == null) return this.Unauthorized("");

            List<string> qAUserPhoneNumbers = _generalSettings.QAUserPhoneNumbers.Split(",").Select(a => a).ToList();

            bool isQaUser = false;
            if (qAUserPhoneNumbers.Contains(_user.PhoneNumber))
                isQaUser = true;

            if (_billPaymentSettings.RemoveCountrywiseFilter)
                isQaUser = true;

            var countries = await this._lookupService.GetBillPaymentCountries(isQaUser);

            var countryResources = _mapper.Map<IEnumerable<Country>, IEnumerable<BillPaymentCountryDto>>(countries.Data);

            return Ok(countryResources);
        }

        /// <summary>
        ///  Get Categories 
        /// </summary>
        /// <returns></returns>
        [HttpGet("categories/{countryCode}")]
        public async Task<ActionResult<IEnumerable<BillPaymentCategoryDto>>> GetCategories(string countryCode)
        {
            var catgories = await this._billPaymentProcessingService.GetCategories(countryCode);

            var categoryDtos = _mapper.Map<IEnumerable<BillPaymentCategory>, IEnumerable<BillPaymentCategoryDto>>(catgories.Data);

            return Ok(categoryDtos);
        }

        /// <summary>
        ///  Get Bill Providers based on Category and Country 
        /// </summary>
        /// <param name="categoryId"></param>
        /// <param name="countryCode"></param> 
        /// <returns></returns>
        [HttpGet("providers/{countryCode}/{categoryId}")]
        public async Task<ActionResult<IEnumerable<BillPaymentProviderDto>>> GetProviders(string countryCode, int categoryId)
        {
            var providers = await this._billPaymentProcessingService.GetProviders(categoryId, countryCode);

            var providerDtos = _mapper.Map<IEnumerable<BillPaymentProvider>, IEnumerable<BillPaymentProviderDto>>(providers.Data);

            return Ok(providerDtos);
        }

        /// <summary>
        ///  Get Fields (Text Inputs) based on Category and Biller 
        /// </summary>
        /// <param name="categoryId"></param>
        /// <param name="providerId"></param> 
        /// <param name="subProviderId"></param>  
        /// <returns></returns>
        [HttpGet("fields/{categoryId}/{providerId}")]
        public async Task<ActionResult<IEnumerable<BillPaymentFieldDto>>> GetFields(int categoryId, int providerId, int subProviderId = 0)
        {
            var fields = await this._billPaymentProcessingService.GetFields(categoryId, providerId, subProviderId);

            if (fields.IsSuccessful)
            {
                var fieldsDtos = _mapper.Map<IEnumerable<BillPaymentProductIO>, IEnumerable<BillPaymentFieldDto>>(fields.Data);

                return Ok(fieldsDtos);
            }
            else
            {
                return BadRequest(fields.ErrorMessage);
            }
        }


        /// <summary>
        /// Add / Update biller 
        /// Possible results: InvalidCategory, InvalidProvider, UserBillerExistsAlready, FieldNotFound, FieldMinValueDoesnotMet, FieldMaxValueExceeded, FieldInvalidDataType, MaximumBillersLimitReached
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("user/biller")]

        public async Task<ActionResult> AddBiller(BillPaymentAddBillerRequestDto request)
        {
            await GetLoggedInUser();

            if (_user == null) return this.Unauthorized("");

            var billPaymentAddingBillerResource = _mapper.Map<BillPaymentAddBillerRequestDto, BillPaymentBiller>(request);

            billPaymentAddingBillerResource.UserId = _loggedInUserId;

            var addBillerResponse = await _billPaymentProcessingService.AddBiller(billPaymentAddingBillerResource);

            if (!addBillerResponse.IsSuccessful)
            {
                return BadRequest(addBillerResponse.ErrorMessage);
            }

            var newBillerDto = _mapper.Map<BillPaymentBiller, BillPaymentBillerDto>(addBillerResponse.Data);

            bool isNol = addBillerResponse.Data.Provider.Code == _billPaymentSettings.NolProviderCode;

            // Nol Card 
            if (isNol)
            {
                SettingsForNolProvider(newBillerDto);

                newBillerDto.ProductDisplayType = BaseEnums.BillPaymentProductDisplayType.Topup.ToString();

            }

            bool isSalik = addBillerResponse.Data.Provider.Code == _billPaymentSettings.SalikProviderCode;

            if (isSalik)
            {
                if (newBillerDto.AmountDue != null)
                {
                    newBillerDto.AvailableBalance = new AmountDto()
                    {
                        Amount = newBillerDto.AmountDue.Amount,
                        Currency = newBillerDto.AmountDue.Currency
                    };
                }
                newBillerDto.ProductDisplayType = BaseEnums.BillPaymentProductDisplayType.Topup.ToString();
            }

            return Ok(newBillerDto);
        }

        /// <summary>
        ///  Get User Billers
        ///  Possible results: Invalid Biller, Invalid SKU, Invalid Reference
        /// </summary> 
        /// <returns></returns>
        [HttpGet("user/billers")]
        public async Task<ActionResult<IEnumerable<BillPaymentBillerDto>>> GetBillers(string providerCode)
        {
            await GetLoggedInUser();

            if (_user == null) return this.Unauthorized("");


            var billers = await this._billPaymentProcessingService.GetBillers(_loggedInUserId, providerCode);

            var billersDtos = _mapper.Map<List<BillPaymentBiller>, List<BillPaymentBillerDto>>(billers.Data.ToList());

            // For Amount Due
            if (billers.Data.Any())
            {
                foreach (var _thisBiller in billers.Data.Where(a => a.Provider.Code == _billPaymentSettings.NolProviderCode))
                {
                    int index = billers.Data.IndexOf(_thisBiller);

                    var _thisBillerDto = billersDtos[index];

                    SettingsForNolProvider(_thisBillerDto);

                    _thisBillerDto.ProductDisplayType = BaseEnums.BillPaymentProductDisplayType.Topup.ToString();

                    billersDtos[index] = _thisBillerDto;
                }

                foreach (var _thisBiller in billers.Data.Where(a => a.Provider.Code == _billPaymentSettings.SalikProviderCode))
                {
                    int index = billers.Data.IndexOf(_thisBiller);

                    var _thisBillerDto = billersDtos[index];

                    _thisBillerDto.IsInquiryAvailable = false;


                    if (_thisBillerDto.AmountDue != null)
                    {
                        _thisBillerDto.AvailableBalance = new AmountDto()
                        {
                            Amount = _thisBillerDto.AmountDue.Amount,
                            Currency = _thisBillerDto.AmountDue.Currency
                        };
                    }

                    _thisBillerDto.ProductDisplayType = BaseEnums.BillPaymentProductDisplayType.Topup.ToString();

                    billersDtos[index] = _thisBillerDto;
                }
            }

            return Ok(billersDtos);
        }

        /// <summary>
        /// Deleting Existing Biller 
        /// Possible results: InvalidUserBiller
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns> 
        [HttpDelete("user/biller/{id}")]
        public async Task<ActionResult> DeleteBiller(Guid id)
        {
            await GetLoggedInUserId();

            if (_loggedInUserId == Guid.Empty) return this.Unauthorized("");


            var deleteBiller = await _billPaymentProcessingService.DeleteBiller(id);

            if (!deleteBiller.IsSuccessful)
            {
                return BadRequest(deleteBiller.ErrorMessage);
            }

            return Ok();
        }

        /// <summary>
        ///  Get Bill Summary 
        /// Possible results:  InvalidUserBiller
        /// </summary>
        /// <returns></returns>
        [HttpGet("user/biller/fetch-amount-due/{id}")]
        public async Task<ActionResult<BillPaymentBillSummaryResponseDto>> FetchAmountDue(Guid id)
        {
            await GetLoggedInUserId();
            if (_loggedInUserId == Guid.Empty) return this.Unauthorized("");

            var response = await _billPaymentProcessingService.InitiateAmountFetch(id, _loggedInUserId);

            if (!response.IsSuccessful)
            {
                return BadRequest(response.ErrorMessage);
            }

            return Ok(new BillPaymentBillSummaryResponseDto());
        }

        /// <summary>
        ///  Get Bill Summary 
        /// Possible results: InvalidUserBiller, InvalidProduct, AmountOrProductNotNeeded, NeedAmountOrProduct, NoAmountDue, InvalidAmount,
        /// InvalidCurrency, MaximumAllowedAmountPerTransactionExceeded, PaymentInProgress
        /// </summary>
        /// <returns></returns>
        [HttpPost("user/biller/bill-summary")]
        public async Task<ActionResult<BillPaymentBillSummaryResponseDto>> BillPaymentSummary(BillPaymentBillSummaryRequestDto request)
        {
            await GetLoggedInUserId();

            if (_loggedInUserId == Guid.Empty) return this.Unauthorized("");


            var response = new BillPaymentBillSummaryResponseDto()
            {
                BillAmount = new AmountDto(),
                FeeAmount = new AmountDto(),
                ToBePaidAmount = new AmountDto(),
                TotalAmount = new AmountDto(),
                Fields = new List<BillPaymentBillSummaryFieldDto>(),
                ProviderCharge = new AmountDto()
            };


            decimal? amount = null;
            string currency = null;
            if (request.TobePaidAmount != null)
            {
                amount = Convert.ToDecimal(request.TobePaidAmount.Amount);
                currency = request.TobePaidAmount.Currency;
            }
            var summaryResponse = await _billPaymentProcessingService.GetSummary(request.BillerId, _loggedInUserId, request.ProductId, Guid.NewGuid().ToString(),
                amount, currency);

            if (summaryResponse.IsSuccessful)
            {
                var result = _mapper.Map<BillPaymentBillSummaryResponseDto>(summaryResponse.Data);
                return Ok(result);
            }
            return BadRequest(summaryResponse.ErrorMessage);
        }

        /// <summary>
        /// Process Bill Payment 
        /// Possible results:  InvalidUserBiller, InvalidProduct, AmountOrProductNotNeeded, NeedAmountOrProduct, NoAmountDue, InvalidAmount,
        /// InvalidCurrency, MaximumAllowedAmountPerTransactionExceeded, UnblockYourCard, ActivateYourCard, InsufficientBalance, InvalidFieldValue, PaymentFailed 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("user/biller/process-payment")]
        public async Task<ActionResult<BillPaymentProcessPaymentResponseDto>> ProcessPayment(BillPaymentProcessPaymentRequestDto request)
        {
            string languageCode = Request.Headers["x-lang-code"];

            await GetLoggedInUser();

            if (_user == null) return this.Unauthorized("");

            decimal? amount = null;
            string currency = null;
            if (request.ToBePaidAmount != null)
            {
                amount = Convert.ToDecimal(request.ToBePaidAmount.Amount);
                currency = request.ToBePaidAmount.Currency;
            }
            var paymentResponse = await _billPaymentProcessingService.CapturePayment(request.BillerId, _user, request.ProductId, amount, currency);

            var provider = await _billPaymentProcessingService.GetProvider(request.ProductId);

            bool isNol = provider.Data.Code == _billPaymentSettings.NolProviderCode;

            if (isNol)
            {
                var nolResponse = await PopulateNolBillPaymentResponse(paymentResponse, _user, languageCode);
                return Ok(nolResponse);
            }
            else
            {
                if (!paymentResponse.IsSuccessful)
                {
                    return BadRequest(paymentResponse.ErrorMessage);
                }

                return Ok(new BillPaymentProcessPaymentResponseDto()
                {
                    DaysToProcessBill = paymentResponse.Data.DaysToPost,
                    Status = paymentResponse.Data.Status.ToString()
                });
            }
        }


        /// <summary>
        ///  Get User Billers
        ///  Possible results: Invalid Biller, Invalid SKU, Invalid Reference
        /// </summary> 
        /// <returns></returns>
        [HttpGet("transactions")]
        public async Task<ActionResult<IEnumerable<BillPaymentTransactionDto>>> GetMonthlyTransactions(int? pageSize = null, int? pageNumber = null, string providerCode = default)
        {
            await GetLoggedInUserId();

            if (_loggedInUserId == Guid.Empty) return this.Unauthorized("");

            var transactions = await _billPaymentProcessingService.GetTransactions(_loggedInUserId, providerCode, pageSize, pageNumber);
            if (transactions.IsSuccessful == false)
            {
                return BadRequest(transactions.ErrorMessage);
            }

            var result = _mapper.Map<List<BillPaymentTransactionDto>>(transactions.Data);

            return Ok(result);
        }

        /// <summary>
        ///  Get Transaction Detail
        /// </summary>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        [HttpGet("transaction-detail/{transactionId}")]
        public async Task<ActionResult<BillPaymentTransactionDetailDto>> GetTransactionDetail(Guid transactionId)
        {
            await GetLoggedInUserId();

            if (_loggedInUserId == Guid.Empty) return this.Unauthorized("");


            var transactions = await _billPaymentProcessingService.GetTransactionDetail(transactionId, _loggedInUserId);
            if (transactions.IsSuccessful == false)
            {
                return BadRequest(transactions.ErrorMessage);
            }

            var result = _mapper.Map<BillPaymentTransactionDetailDto>(transactions.Data);
            return Ok(result);

        }

        [HttpGet("nol-fields")]
        public async Task<ActionResult<IEnumerable<BillPaymentFieldDto>>> GetNolFields()
        {
            var fields = await this._billPaymentProcessingService.GetNolFields();

            if (fields.IsSuccessful)
            {
                var fieldsDtos = _mapper.Map<IEnumerable<BillPaymentProviderField>, IEnumerable<BillPaymentFieldDto>>(fields.Data);

                return Ok(fieldsDtos);
            }
            else
            {
                return BadRequest(fields.ErrorMessage);
            }
        }

        [HttpPost("user/nol-payment")] 
        public async Task<ActionResult<BillPaymentProcessPaymentResponseDto>> ProcessNolPayment(BillPaymentNolPaymentRequestDto request)
        { 
            string languageCode = Request.Headers["x-lang-code"];

            await GetLoggedInUser();

            if (_user == null) return this.Unauthorized(""); 

            var paymentResponse = await _billPaymentProcessingService.PostNolTransaction(_user, request.BillerId, request.ProductId);

            var provider = await _billPaymentProcessingService.GetProvider(request.ProductId);

            bool isNol = provider.Data.Code == _billPaymentSettings.NolProviderCode;

            if (isNol)
            {
                var nolResponse = await PopulateNolBillPaymentResponse(paymentResponse, _user, languageCode);
                return Ok(nolResponse);
            }
            else
            {
                if (!paymentResponse.IsSuccessful)
                {
                    return BadRequest(paymentResponse.ErrorMessage);
                }

                return Ok(new BillPaymentProcessPaymentResponseDto()
                {
                    DaysToProcessBill = paymentResponse.Data.DaysToPost,
                    Status = paymentResponse.Data.Status.ToString()
                });
            }
        }

        private void SettingsForNolProvider(BillPaymentBillerDto billersDto)
        {
            if (billersDto != null)
            {
                if (!_user.IsVerified)
                {
                    List<decimal> _allowedAmounts = !string.IsNullOrEmpty(_billPaymentSettings.AllowedNolAmountForNotVerified) ?
                        JsonConvert.DeserializeObject<List<decimal>>(_billPaymentSettings.AllowedNolAmountForNotVerified) : new List<decimal>();

                    if (billersDto.Products.Any())
                    {
                        billersDto.Products.ForEach(_product =>
                        {
                            if (!_allowedAmounts.Contains((decimal)_product.ProductAmount.Amount))
                            {
                                _product.ProductEnabled = false;
                            }
                        });
                    }
                }
            }
        }

        private async Task <BillPaymentProcessPaymentResponseDto> PopulateNolBillPaymentResponse(ServiceResponse<BillPaymentTransaction> paymentResponse, User user, string languageCode)
        {
            if (!paymentResponse.IsSuccessful)
            {
                return new BillPaymentProcessPaymentResponseDto()
                {
                    TransactionResult = new TransactionResult()
                    {
                        Status = _failedStatus,
                        Title = "Payment Failed"
                    }
                };
            }

            var resourceManager = languageCode == "hi-en" ? new ResourceManager("C3Pay.API.Resources.BillPayment.BillPaymentResources.NolGuide.Resource.hi-en", typeof(Program).Assembly)
                             : new ResourceManager("C3Pay.API.Resources.BillPayment.BillPaymentResources.NolGuide.Resource", typeof(Program).Assembly);

            return new BillPaymentProcessPaymentResponseDto()
            {
                DaysToProcessBill = paymentResponse.Data.DaysToPost,
                Status = paymentResponse.Data.Status.ToString(),

                TransactionResult = new TransactionResult()
                {
                    Status = _successfulStatus,
                },

                SuccessInfo = new SuccessInfo()
                {
                    Video = await GetVideoUrls(user),

                    ActionPoints = new List<string>
                    {
                        resourceManager.GetString("NolRechargeGuide1", new CultureInfo(languageCode)),
                        resourceManager.GetString("NolRechargeGuide2", new CultureInfo(languageCode))
                    }
                },
            };
        }

        private async Task<List<Video>> GetVideoUrls(User user)
        {
            var hindiNationalities = new HashSet<string> { nameof(BaseEnums.MultimediaCountry.PAK), nameof(BaseEnums.MultimediaCountry.IND) };
            var userShouldSeeHindiVideo = hindiNationalities.Contains(user.CardHolder.Nationality);

            var nolPaymentMultimediaResources = await _lookupService.GetMultimediaResources(feature: (int)FeatureType.BillPaymentNol).ConfigureAwait(false);

            bool defaultAlreadySet = false;

            return nolPaymentMultimediaResources.Data
                .Select(resource => new Video
                {
                    LanguageCode = resource.Language,
                    Url = resource.Url,
                    IsDefault = ((resource.Language == "hi" && userShouldSeeHindiVideo)
                                    || (resource.Language != "hi" && !userShouldSeeHindiVideo))
                                && !defaultAlreadySet && (defaultAlreadySet = true)
                })
                .ToList();
        }

        #endregion
    }

}
