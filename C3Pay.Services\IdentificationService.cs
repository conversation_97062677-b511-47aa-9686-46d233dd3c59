﻿using AutoMapper;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.Messages.Signup;
using C3Pay.Core.Models.Messages.User;
using C3Pay.Core.Services;
using C3Pay.Services.Filters;
using C3Pay.Services.IntegrationEvents.Out.Enums;
using Edenred.Common.Core;
using Edenred.Common.Core.Services.Integration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using static Edenred.Common.Core.Enums;
using Gender = C3Pay.Core.BaseEnums.Gender;

namespace C3Pay.Services
{
    public class IdentificationService : IIdentificationService
    {
        private readonly IPrimaryIndividualIdentificationService _signzyIdentificationService;
        private readonly ISecondaryIndividualIdentificationService _secondaryIdentificationService;
        private readonly AzureIdentificationServiceSettings _azureIdentificationSettings;
        private readonly IMapper _mapper;
        private readonly IPPSService _ppsService;
        private readonly IUserService _userService;
        private readonly IKYCService _kycService;
        private readonly IBlobStorageService _blobStorageService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;
        private readonly IPushNotificationSenderService _pushNotificationSenderService;
        private readonly IEmailSenderService _emailSenderService;
        private readonly IIdentityService _identityService;
        private readonly IAuditTrailService _auditTrailService;
        private readonly IAnalyticsPublisherService _analyticsPublisherService;
        private readonly ILookupService _lookupService;
        private readonly IMoneyTransferService _moneyTransferService;
        private readonly IESMOWebService _eSMOWebService;
        private readonly IPartnerCorporateService _partnerCorporateService;
        private readonly IExperimentService _experimentService;
        private readonly IFeatureManager _featureManager;
        private readonly MoneyTransferServiceSettings _moneyTransferServiceSettings;
        private readonly IUploadedDocumentService _uploadedDocumentService;
        private readonly ICardHolderService _cardHolderService;
        private readonly IMessagingQueueService _messagingQueueService;
        private readonly IOptions<KycUnblockByPassportSettings> _kycUnblockByPassportSettings;
        private readonly ISanctionScreeningService _sanctionScreeningService;
        private readonly string _emiratesIdContainerName = "emirates-id";
        private readonly string _passportContainerName = "passport";
        private readonly string _emailAttachmentContainerName = "email-attachments";
        private readonly decimal _maxDocumentSize = 500;
        private readonly string _frontFileName = "emirates_id_front_scan_";
        private readonly string _backFileName = "emirates_id_back_scan_";
        private readonly string _passportFileName = "passport_";

        /// <summary>
        /// Identification Service
        /// </summary>
        /// <param name="ppsService"></param>
        /// <param name="mapper"></param>
        /// <param name="signzyIdentificationService"></param>
        /// <param name="azureIdentificationService"></param>
        /// <param name="azureIdentificationSettings"></param>
        /// <param name="kycService"></param>
        /// <param name="blobStorageService"></param>
        /// <param name="auditTrailService"></param>
        /// <param name="userService"></param>
        /// <param name="unitOfWork"></param>
        /// <param name="logger"></param>
        /// <param name="pushNotificationSenderService"></param>
        /// <param name="emailSenderService"></param>
        /// <param name="identityService"></param>
        /// <param name="analyticsPublisherService"></param>
        /// <param name="lookupService"></param>
        /// <param name="moneyTransferService"></param>
        /// <param name="eSMOWebService"></param>
        /// <param name="partnerCorporateService"></param>
        /// <param name="experimentService"></param>
        /// <param name="sanctionScreeningService"></param>
        public IdentificationService(IPPSService ppsService,
            IMapper mapper,
            IPrimaryIndividualIdentificationService signzyIdentificationService,
            ISecondaryIndividualIdentificationService azureIdentificationService,
            IOptions<AzureIdentificationServiceSettings> azureIdentificationSettings,
            IKYCService kycService,
            IBlobStorageService blobStorageService,
            IAuditTrailService auditTrailService,
            IUserService userService,
            IUnitOfWork unitOfWork,
            ILogger<IdentificationService> logger,
            IPushNotificationSenderService pushNotificationSenderService,
            IEmailSenderService emailSenderService,
            IIdentityService identityService,
            IAnalyticsPublisherService analyticsPublisherService,
            ILookupService lookupService,
            IMoneyTransferService moneyTransferService,
            IESMOWebService eSMOWebService,
            IPartnerCorporateService partnerCorporateService,
            IExperimentService experimentService,
            IFeatureManager featureManager,
            IOptions<MoneyTransferServiceSettings> moneyTransferServiceSettings,
            IUploadedDocumentService uploadedDocumentService,
            ICardHolderService cardHolderService,
            IMessagingQueueService messagingQueueService,
            IOptions<KycUnblockByPassportSettings> KycUnblockByPassportSettings,
            ISanctionScreeningService sanctionScreeningService)
        {
            _signzyIdentificationService = signzyIdentificationService;
            _secondaryIdentificationService = azureIdentificationService;
            _azureIdentificationSettings = azureIdentificationSettings.Value;
            _kycService = kycService;
            _userService = userService;
            _blobStorageService = blobStorageService;
            _unitOfWork = unitOfWork;
            _logger = logger;
            _pushNotificationSenderService = pushNotificationSenderService;
            _emailSenderService = emailSenderService;
            _identityService = identityService;
            _ppsService = ppsService;
            _auditTrailService = auditTrailService;
            _analyticsPublisherService = analyticsPublisherService;
            _lookupService = lookupService;
            _moneyTransferService = moneyTransferService;
            _mapper = mapper;
            _eSMOWebService = eSMOWebService;
            _partnerCorporateService = partnerCorporateService;
            _experimentService = experimentService;
            _featureManager = featureManager;
            _moneyTransferServiceSettings = moneyTransferServiceSettings.Value;
            _uploadedDocumentService = uploadedDocumentService;
            _cardHolderService = cardHolderService;
            _messagingQueueService = messagingQueueService;
            _kycUnblockByPassportSettings = KycUnblockByPassportSettings;
            _sanctionScreeningService = sanctionScreeningService;
        }

        public async Task<ServiceResponse<string>> UploadIdentificationDocument(Guid? userId, string content, string documentName, Enums.DocumentType documentType)
        {
            this._logger.LogDebug(ConstantParam.UploadingUserIdentificationDocumentToAzureStorageAccount, documentType, userId, documentName);

            var uploadResult = await _secondaryIdentificationService.UploadDocument(content, documentName, IdentificationDocumentMimeType.jpg);

            if (!uploadResult.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.UploadingUserIdentificationDocumentToAzureStorageAccountFailed, documentType, userId, documentName, uploadResult.ErrorMessage);

                return new ServiceResponse<string>(false, uploadResult.ErrorMessage);
            }

            var upload = uploadResult.Data;


            return new ServiceResponse<string>(upload);
        }

        public async Task<ServiceResponse<bool>> CheckIdentificationDocumentQuality(Guid? userId, string fileName, string documentUrl, Enums.DocumentType documentType)
        {
            this._logger.LogDebug(ConstantParam.CheckingUserIdentificationDocumentQuality, documentType, userId, documentUrl);

            try
            {
                var qualityResult = await this._signzyIdentificationService.CheckDocumentQuality(documentUrl, documentType);

                if (!qualityResult.IsSuccessful)
                {
                    this._logger.LogWarning(ConstantParam.CheckingUserIdentificationDocumentQualityFailed, documentType, userId, fileName, qualityResult.ErrorMessage);

                    return new ServiceResponse<bool>(false, qualityResult.ErrorMessage);
                }

                var quality = qualityResult.Data;


                return new ServiceResponse<bool>(quality);
            }

            catch (OperationCanceledException ex)
            {
                this._logger.LogError(ConstantParam.ImageQualityTimeout, userId, ex.Message);
                return new ServiceResponse<bool>(true);
            }
        }

        public async Task<ServiceResponse<object>> ReadIdentificationDocument(Guid? userId, string frontScanFileName, string backScanFileName, string nameToMatch, IdentificationType identificationType)
        {
            User user = null;

            if (userId.HasValue)
            {
                var userResult = await this._userService.GetUserById(userId.Value);
                if (!userResult.IsSuccessful)
                {
                    return new ServiceResponse<object>(false, userResult.ErrorMessage);
                }

                user = userResult.Data;

                nameToMatch = user.CardHolder.FirstName;

                var employeeId = user.CardHolder.EmployeeId;

                if (!string.IsNullOrEmpty(employeeId) && !string.IsNullOrEmpty(nameToMatch) && nameToMatch.Contains(employeeId))
                {
                    nameToMatch = nameToMatch.Replace(employeeId.Trim(), string.Empty).Trim();
                }
            }

            this._logger.LogDebug(ConstantParam.ReadingUserIdentificationDocumentStarted, userId, frontScanFileName, backScanFileName, nameToMatch);
            ServiceResponse<BaseIdDocumentDto> identificationResult;
            bool isTimeout = false;
            try
            {
                identificationResult = await this._signzyIdentificationService.ReadDocument(frontScanFileName, backScanFileName, identificationType, nameToMatch);
            }
            catch (OperationCanceledException ex)
            {
                isTimeout = true;

                this._logger.LogError(ConstantParam.ReadingUserIdentificationDocumentTimeout, userId, frontScanFileName, backScanFileName, nameToMatch, ex.Message);
                identificationResult = await this._secondaryIdentificationService.ReadDocument(frontScanFileName, backScanFileName, identificationType, nameToMatch);
            }

            if (!identificationResult.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.ReadingUserIdentificationDocumentFailed, userId, frontScanFileName, backScanFileName, nameToMatch, identificationResult.ErrorMessage);

                if (identificationResult.Data != null)
                {
                    await AddToEmiratesIdLog(userId, frontScanFileName, backScanFileName, identificationType, identificationResult, IdentificationServiceVendor.Signzy);
                }

                var checkIfFirstNameIsNull = false;
                var featureFlagCheckAzureFirstName = await _featureManager.IsEnabledAsync(FeatureFlags.IdentificationCheckWithAzureIfFirstNameNull);

                if (featureFlagCheckAzureFirstName && identificationType == IdentificationType.EmiratesId &&
                identificationResult.ErrorMessage == SystemMessages.UnableToExtractDocumentDetials && identificationResult.Data != null)
                {
                    var emiratesId = (EmiratesIdLogDto)identificationResult.Data;
                    if (string.IsNullOrEmpty(emiratesId.FirstName))
                    {
                        checkIfFirstNameIsNull = true;
                    }
                }

                //fallback to azure
                if ((_azureIdentificationSettings.UseAzureOCR && !isTimeout) || checkIfFirstNameIsNull)
                {
                    identificationResult = await _secondaryIdentificationService.ReadDocument(frontScanFileName, backScanFileName, identificationType, nameToMatch);
                    if (!identificationResult.IsSuccessful)
                    {
                        // Create empty Emirates ID data instance to prevent error when logging
                        if (identificationResult.Data == null)
                        {
                            identificationResult.Data = new EmiratesIdLogDto();
                        }

                        await AddToEmiratesIdLog(userId, frontScanFileName, backScanFileName, identificationType, identificationResult, IdentificationServiceVendor.Azure);
                        await _unitOfWork.CommitAsync();
                        return new ServiceResponse<object>(false, identificationResult.ErrorMessage);
                    }

                }
                else
                {
                    await _unitOfWork.CommitAsync();
                    return new ServiceResponse<object>(false, identificationResult.ErrorMessage);
                }
            }


            if (identificationType == IdentificationType.EmiratesId)
            {
                var emiratesId = (EmiratesIdDto)identificationResult.Data;

                //Eligibility
                if (userId.HasValue)
                {
                    // Recheck Emirates Id details
                    await RecheckEmiratesIdDetails(userId, frontScanFileName, backScanFileName, nameToMatch, identificationType, user, identificationResult, emiratesId);

                    var idNumber = emiratesId.IdNumber;
                    string eligibility = null;
                    var isAddition = !await _unitOfWork.Identifications.Any(record => record.UserId == userId && record.VerificationStatus == IdentificationVerificationStatus.Approved && record.Type == IdentificationType.EmiratesId);
                    var isUpdate = !isAddition;
                    var userExistsWithTheSameEmiratesId = await _unitOfWork.Users.Any(record => !record.IsDeleted && record.Id != userId.Value && record.CardHolder.EmiratesId == idNumber && record.ApplicationId == BaseEnums.MobileApplicationId.C3Pay);
                    var emiratesIdMatchesUserEmiratesId = user.CardHolder.EmiratesId == idNumber;

                    var allowEmiratesIdIfAssociatedWithAnotherUser = await _featureManager.IsEnabledAsync(FeatureFlags.CardholderAllowEmiratesIdAssociatedWithAnotherUser);
                    if (allowEmiratesIdIfAssociatedWithAnotherUser)
                    {
                        userExistsWithTheSameEmiratesId = false;
                        _logger.LogWarning(ConstantParam.AllowEmiratesIdAssociatedWithAnotherUser, userId, idNumber);
                    }

                    if (isAddition && userExistsWithTheSameEmiratesId)
                    {
                        eligibility = EmiratesIdEligibilityResult.AssociatedWithAnotherUser.ToString();
                    }
                    else if (isUpdate && !emiratesIdMatchesUserEmiratesId)
                    {
                        eligibility = EmiratesIdEligibilityResult.Mismatch.ToString();
                    }
                    else
                    {
                        eligibility = EmiratesIdEligibilityResult.Eligible.ToString();
                    }

                    if (await _featureManager.IsEnabledAsync(FeatureFlags.BlockEIDsUpdateIfDifferentNationality) && isUpdate)
                    {
                        try
                        {
                            var identificationWithADifferentNationalityExist = await _unitOfWork.Identifications.Any(record => record.UserId == userId.Value && record.Type == IdentificationType.EmiratesId &&
                                record.VerificationStatus == IdentificationVerificationStatus.Approved
                                && record.Nationality != emiratesId.NationalityCode);

                            if (identificationWithADifferentNationalityExist)
                            {
                                eligibility = EmiratesIdEligibilityResult.Mismatch.ToString();

                                _logger.LogWarning("BlockEIDsUpdateIfDifferentNationality: blocked different nationality Emirates Id update {ExistingNationality}, {NewNationality}", user.CardHolder.Nationality, emiratesId.NationalityCode);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("BlockEIDsUpdateIfDifferentNationality: Exception {emiratesId}", emiratesId.IdNumber);
                        }
                    }


                    emiratesId.IsExpired = false;

                    if (await _featureManager.IsEnabledAsync(FeatureFlags.BlockEIDsExpiredForMoreThan90Days))
                    {
                        if (emiratesId.IsValid)
                        {
                            try
                            {
                                var expiryDate = DateTime.ParseExact(emiratesId.ExpiryDate.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture);

                                if (expiryDate < DateTime.Now)
                                {
                                    emiratesId.IsExpired = true;
                                    emiratesId.IsValid = false;

                                    if (isAddition)
                                    {
                                        if (!user.IsVerified)
                                        {
                                            emiratesId.RedirectToPassportFlow = true;
                                        }
                                    }
                                }

                                _logger.LogWarning("90days: blocked expired Emirates Id");
                            }
                            catch (Exception)
                            {
                                _logger.LogError("90days: Couldn't extract expiry date");
                            }
                        }
                    }

                    emiratesId.Eligibility = eligibility;
                }

                return new ServiceResponse<object>(emiratesId);
            }
            else if (identificationType == IdentificationType.Passport)
            {
                var passport = (PassportDto)identificationResult.Data;

                // Recheck Passport details
                await RecheckPassportDetails(userId, frontScanFileName, backScanFileName, nameToMatch, identificationType, user, identificationResult, passport);

                //Eligibility
                if (userId.HasValue)
                {
                    var passportNumber = passport.PassportNumber;

                    string eligibility;

                    var userExistsWithTheSamePassportNumber = await _unitOfWork.Users.Any(record => !record.IsDeleted && record.Id != userId.Value && record.CardHolder.PassportNumber == passportNumber);
                    var passportNumberMatchesUserPassportNumber = user.CardHolder.PassportNumber == passportNumber;

                    if (userExistsWithTheSamePassportNumber)
                    {
                        eligibility = EmiratesIdEligibilityResult.AssociatedWithAnotherUser.ToString();
                    }
                    else
                    {
                        eligibility = EmiratesIdEligibilityResult.Eligible.ToString();
                    }

                    passport.Eligibility = eligibility;
                }

                return new ServiceResponse<object>(passport);
            }
            else
            {
                return new ServiceResponse<object>(false, ConstantParam.InvalidDocumentType);
            }
        }

        private async Task RecheckEmiratesIdDetails(Guid? userId, string frontScanFileName, string backScanFileName, string nameToMatch, IdentificationType identificationType, User user, ServiceResponse<BaseIdDocumentDto> identificationResult, EmiratesIdDto emiratesId)
        {
            var idNumber = emiratesId.IdNumber;
            EmiratesIdDto emiratesIdFromSecondary = null;
            bool primaryAndSecondaryFoundSameEmiratesId = false;
            bool primaryAndSecondaryFoundSameNationalityCode = false;
            bool primaryAndSecondaryFoundSameDateOfBirth = false;

            var differenceInDigitsCount = CountDifferentDigits(user.CardHolder.EmiratesId, idNumber);
            var maxDifferenceInDigit = _azureIdentificationSettings.MaxDifferenceInDigitsForRecheck;
            var expiryDate = DateTime.ParseExact(emiratesId.ExpiryDate.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture);
            var birthDate = DateTime.ParseExact(emiratesId.DOB.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture);

            var shouldRecheckEmiratesIdDetails = await _featureManager.IsEnabledAsync(FeatureFlags.UserRecheckEmiratesIdDetails);
            ServiceResponse<BaseIdDocumentDto> identificationResultFromSecondary;

            if (shouldRecheckEmiratesIdDetails &&
                (differenceInDigitsCount > 0 && differenceInDigitsCount <= maxDifferenceInDigit) ||
                user.CardHolder.Nationality != emiratesId.NationalityCode ||
                expiryDate.Year.ToString().StartsWith("19") ||
                user.CardHolder.Birthdate != birthDate)
            {
                try
                {
                    identificationResultFromSecondary = await _secondaryIdentificationService.ReadDocument(frontScanFileName, backScanFileName, identificationType, nameToMatch);
                    if (identificationResultFromSecondary.IsSuccessful && identificationResultFromSecondary.Data != null)
                    {
                        emiratesIdFromSecondary = (EmiratesIdDto)identificationResultFromSecondary.Data;
                    }
                    else
                    {
                        if (identificationResultFromSecondary.Data != null)
                        {
                            await AddToEmiratesIdLog(userId, frontScanFileName, backScanFileName, identificationType, identificationResultFromSecondary, IdentificationServiceVendor.Azure);
                        }

                        return;
                    }
                }
                catch (Exception)
                {
                    _logger.LogWarning("Unable to recheck emirates Id for CardholderId: {cardholderId}", user.CardHolderId);
                    return;
                }

                if (idNumber != emiratesIdFromSecondary.IdNumber)
                {
                    _logger.LogWarning(ConstantParam.UpdatingEmiratesIdIdentificationDueToIdNo, userId, frontScanFileName, backScanFileName, user.CardHolder.EmiratesId, idNumber, emiratesIdFromSecondary.IdNumber);
                    emiratesId.IdNumber = emiratesIdFromSecondary.IdNumber;
                }
                else
                {
                    primaryAndSecondaryFoundSameEmiratesId = true;
                }

                if (emiratesId.NationalityCode != emiratesIdFromSecondary.NationalityCode)
                {
                    _logger.LogWarning(ConstantParam.UpdatingEmiratesIdIdentificationDueToNationality, userId, frontScanFileName, backScanFileName, user.CardHolder.Nationality, emiratesId.NationalityCode, emiratesIdFromSecondary.NationalityCode);

                    emiratesId.Nationality = emiratesIdFromSecondary.Nationality;
                    emiratesId.NationalityCode = emiratesIdFromSecondary.NationalityCode;
                }
                else
                {
                    primaryAndSecondaryFoundSameNationalityCode = true;
                }

                if (emiratesId.DOB != emiratesIdFromSecondary.DOB)
                {
                    _logger.LogWarning(ConstantParam.UpdatingPassportIdentificationDueToDateOfBirth, userId, frontScanFileName, backScanFileName, user.CardHolder.Birthdate, emiratesId.DOB, emiratesIdFromSecondary.DOB);

                    emiratesId.DOB = emiratesIdFromSecondary.DOB;
                }
                else
                {
                    primaryAndSecondaryFoundSameDateOfBirth = true;
                }

                if (emiratesId.ExpiryDate != emiratesIdFromSecondary.ExpiryDate)
                {
                    _logger.LogWarning(ConstantParam.UpdatingEmiratesIdIdentificationDueToExpiryDate, userId, frontScanFileName, backScanFileName, user.CardHolder.EmiratesIdExpiryDate, emiratesId.ExpiryDate, emiratesIdFromSecondary.ExpiryDate);
                    emiratesId.ExpiryDate = emiratesIdFromSecondary.ExpiryDate;
                }
            }

            var canUpdateCardholderDetails = await _featureManager.IsEnabledAsync(FeatureFlags.CardholderUpdateDetails);
            if (canUpdateCardholderDetails)
            {
                //Check the difference between the extracted Emirates id number and cardholders' emirates id number
                differenceInDigitsCount = CountDifferentDigits(user.CardHolder.EmiratesId, idNumber);
                if (primaryAndSecondaryFoundSameEmiratesId &&
                    differenceInDigitsCount >= 0 && differenceInDigitsCount <= maxDifferenceInDigit)
                {
                    user.CardHolder.EmiratesId = idNumber;
                }

                //Check nationality
                if (primaryAndSecondaryFoundSameNationalityCode &&
                    user.CardHolder.Nationality != emiratesId.NationalityCode)
                {
                    _logger.LogWarning(ConstantParam.UpdatingCardholderNationality, userId, frontScanFileName, backScanFileName, user.CardHolder.Nationality, emiratesId.NationalityCode);
                    user.CardHolder.Nationality = emiratesId.NationalityCode;
                }

                //Check Date of birth
                if (primaryAndSecondaryFoundSameDateOfBirth &&
                    user.CardHolder.Birthdate != birthDate)
                {
                    _logger.LogWarning(ConstantParam.UpdatingCardholderDateOfBirth, userId, frontScanFileName, backScanFileName, user.CardHolder.Birthdate, birthDate);
                    user.CardHolder.Birthdate = birthDate;
                }

                await _unitOfWork.CommitAsync();
            }
        }

        private async Task RecheckPassportDetails(Guid? userId, string frontScanFileName, string backScanFileName, string nameToMatch, IdentificationType identificationType, User user, ServiceResponse<BaseIdDocumentDto> identificationResult, PassportDto passport)
        {
            var shouldRecheckPassportExpiryDate = await _featureManager.IsEnabledAsync(FeatureFlags.UserRecheckPassportExpiryDate);
            if (shouldRecheckPassportExpiryDate && passport.DateOfExpiry != null)
            {
                var expiryDate = DateTime.ParseExact(passport.DateOfExpiry.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                var dateOfBirth = DateTime.ParseExact(passport.DateOfBirth.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture);

                if (expiryDate.Year.ToString().StartsWith("19") ||
                    dateOfBirth != user.CardHolder.Birthdate)
                {
                    PassportDto passportFromSecondary;
                    try
                    {
                        ServiceResponse<BaseIdDocumentDto> identificationResultFromSecondary = await _secondaryIdentificationService.ReadDocument(frontScanFileName, backScanFileName, identificationType, nameToMatch);
                        if (identificationResultFromSecondary.IsSuccessful && identificationResultFromSecondary.Data != null)
                        {
                            passportFromSecondary = (PassportDto)identificationResultFromSecondary.Data;
                        }
                        else
                        {
                            if (identificationResultFromSecondary.Data != null)
                            {
                                await AddToEmiratesIdLog(userId, frontScanFileName, backScanFileName, identificationType, identificationResultFromSecondary, IdentificationServiceVendor.Azure);
                            }

                            return;
                        }
                    }
                    catch (Exception)
                    {
                        _logger.LogWarning("Unable to recheck passport for CardholderId: {cardholderId}", user == null ? passport.PassportNumber : user.CardHolderId);
                        return;
                    }

                    if (passport.DateOfExpiry != passportFromSecondary.DateOfExpiry)
                    {
                        _logger.LogWarning(ConstantParam.UpdatingPassportIdentificationDueToExpiryDate, userId, frontScanFileName, backScanFileName, passport.DateOfExpiry, passportFromSecondary.DateOfExpiry);
                        passport.DateOfExpiry = passportFromSecondary.DateOfExpiry;
                    }

                    if (passport.DateOfBirth != passportFromSecondary.DateOfBirth)
                    {
                        _logger.LogWarning(ConstantParam.UpdatingPassportIdentificationDueToDateOfBirth, userId, frontScanFileName, backScanFileName, passport.DateOfBirth, passportFromSecondary.DateOfBirth);
                        passport.DateOfBirth = passportFromSecondary.DateOfBirth;
                    }
                }
            }
        }

        private async Task AddToEmiratesIdLog(Guid? userId, string frontScanFileName, string backScanFileName, IdentificationType identificationType, ServiceResponse<BaseIdDocumentDto> identificationResult, IdentificationServiceVendor vendor)
        {
            if (identificationType == IdentificationType.EmiratesId)
            {
                    var log = _mapper.Map<EmiratesIdLog>((EmiratesIdLogDto)identificationResult.Data);
                    await AddEmiratesIdIdentificationLog(userId, frontScanFileName, backScanFileName, log, vendor);
                }
            else
            {
                var log = _mapper.Map<PassportLog>((PassportLogDto)identificationResult.Data);
                await AddPassportIdentificationLog(userId, frontScanFileName, log, vendor);
            }
        }

        private static int CountDifferentDigits(string num1, string num2)
        {
            int count = 0;

            if (num1 == null || num2 == null) return count;

            int length = num1.Length;

            if (num1.Length != num2.Length) return count;

            for (int i = 0; i < length; i++)
            {
                if (num1[i] != num2[i])
                {
                    count++;
                }
            }

            return count;
        }

        public async Task<ServiceResponse<FaceMatchResultDto>> CheckFaceMatch(Guid? userId, string selfieFileName, string frontScanFileName)
        {
            this._logger.LogDebug(ConstantParam.CheckingUserFaceMatch, userId, frontScanFileName, selfieFileName);

            ServiceResponse<FaceMatchResultDto> faceMatchResult;
            bool isTimeout = false;
            try
            {
                faceMatchResult = await _signzyIdentificationService.CheckFaceMatch(selfieFileName, frontScanFileName);
            }

            catch (OperationCanceledException ex)
            {
                isTimeout = true;
                this._logger.LogError(ConstantParam.FaceMatchTimeout, userId, frontScanFileName, selfieFileName, ex.Message);
                faceMatchResult = await _secondaryIdentificationService.CheckFaceMatch(selfieFileName, frontScanFileName);

            }

            if (!faceMatchResult.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.CheckingUserFaceMatchFailed, userId, frontScanFileName, selfieFileName, faceMatchResult.ErrorMessage);

                if (_azureIdentificationSettings.UseAzureFace && !isTimeout)
                {
                    faceMatchResult = await _secondaryIdentificationService.CheckFaceMatch(selfieFileName, frontScanFileName);

                    if (!faceMatchResult.IsSuccessful)
                    {
                        this._logger.LogWarning(ConstantParam.CheckingUserFaceMatchFailed, userId, frontScanFileName, selfieFileName, faceMatchResult.ErrorMessage);
                        return new ServiceResponse<FaceMatchResultDto>(false, faceMatchResult.ErrorMessage);

                    }


                }
                else
                {
                    return new ServiceResponse<FaceMatchResultDto>(false, faceMatchResult.ErrorMessage);
                }
            }

            var faceMatch = faceMatchResult.Data;


            return new ServiceResponse<FaceMatchResultDto>(faceMatch);
        }

        //we save documents into azure storage account
        public async Task<ServiceResponse> SaveIdentificationDocuments(Guid? userId, List<IdentificationDocumentDto> documents)
        {
            var documentsNames = string.Join(" ", documents.Select(record => record.DocumentName).ToArray());

            this._logger.LogDebug(ConstantParam.SavingUserIdentificationDocumentsToStorage, userId, documentsNames);

            var saveResult = await this._secondaryIdentificationService.SaveDocuments(documents);

            if (!saveResult.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.SavingUserIdentificationDocumentsToStorageFailed, userId, documentsNames, saveResult.ErrorMessage);

                return new ServiceResponse<string>(false, saveResult.ErrorMessage);
            }


            return new ServiceResponse();
        }

        public async Task<ServiceResponse> AddIdentification(Identification identification, bool autoVerify = false, Guid? portalUserId = null, string portalEmailId = null)
        {
            if (await _featureManager.IsEnabledAsync(FeatureFlags.BlockEIDsExpiredForMoreThan90Days))
            {
                if (identification.Type == IdentificationType.EmiratesId)
                {
                    try
                    {
                        if (identification.ExpiryDate.AddDays(90) < DateTime.Now)
                        {
                            return new ServiceResponse(false, ConstantParam.EmiratesIdExpired);
                        }

                        _logger.LogWarning("90days: blocked expired Emirates Id");
                    }
                    catch (Exception)
                    {
                        _logger.LogError("90days: Couldn't extract expiry date");
                    }
                }
            }

            this._logger.LogDebug(ConstantParam.AddingIdentificationDocument, identification.UserId, identification.DocumentNumber);

            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(record => record.Id == identification.UserId, x => x.CardHolder);

            if (user is null)
            {
                this._logger.LogWarning(ConstantParam.AddingIdentificationDocumentFailed, identification.UserId, identification.DocumentNumber, ConstantParam.UserNotFound);

                return new ServiceResponse(ConstantParam.UserNotFound);
            }

            var userIdentifications = await this._unitOfWork.Identifications.FindAsync(record => record.UserId == identification.UserId);

            if (userIdentifications.Where(i => i.Type == identification.Type).Count() > 0)
            {
                if (userIdentifications.Where(i => i.Type == identification.Type).OrderByDescending(e => e.CreatedDate)
                    .First().VerificationStatus == IdentificationVerificationStatus.Pending)
                {
                    return new ServiceResponse(false, "User has a pending update.");
                }
            }

            var userWasVerifiedWithDtool =
                user.IsVerified
                &&
                !userIdentifications.Any(r => r.VerificationStatus == IdentificationVerificationStatus.Approved);

            if (identification.Type == IdentificationType.EmiratesId)
            {

                var userHasApprovedEmiratesIdUpdates = userIdentifications.Any(record =>
                    record.Type == IdentificationType.EmiratesId &&
                    record.VerificationStatus == IdentificationVerificationStatus.Approved);

                if (await _featureManager.IsEnabledAsync(FeatureFlags.BlockUploadingExistingEmiratesId))
                {
                    if (userHasApprovedEmiratesIdUpdates && identification.ExpiryDate == user.CardHolder.EmiratesIdExpiryDate && identification.DocumentNumber == user.CardHolder.EmiratesId)
                    {
                        this._logger.LogWarning($"{ConstantParam.EmiratesIdAlreadyExists} {user.Id} {identification.ExpiryDate} {user.CardHolder.EmiratesIdExpiryDate}");

                        return new ServiceResponse(false, ConstantParam.EmiratesIdAlreadyExists);
                    }
                }

                identification.UpdateType = userHasApprovedEmiratesIdUpdates || userWasVerifiedWithDtool ? IdentificationUpdateType.Update : IdentificationUpdateType.Add;
            }
            else if (identification.Type == IdentificationType.Passport)
            {
                var userHasPendingOrApprovedPassportIdentifications = userIdentifications.Any(record =>
                   record.Type == IdentificationType.Passport &&
                   record.VerificationStatus != IdentificationVerificationStatus.Rejected);

                if (userHasPendingOrApprovedPassportIdentifications)
                {
                    return new ServiceResponse(false, ConstantParam.UserAlreadyUploadedAPassportIdentification);
                }

                identification.UpdateType = IdentificationUpdateType.Add;
            }

            identification.VerificationStatus = IdentificationVerificationStatus.Pending;

            identification.Vendor = identification.ServiceReference == IdentificationServiceVendor.Azure.ToString() ? IdentificationServiceVendor.Azure : IdentificationServiceVendor.Signzy;

            await this._unitOfWork.Identifications.AddAsync(identification);

            try
            {
                if (identification.Type == IdentificationType.EmiratesId)
                {
                    var rmtKycRefinementRecords = await _unitOfWork.RmtKycRefinement.GetUnprocessedRmtForEmirateId(identification.DocumentNumber);
                    foreach (var item in rmtKycRefinementRecords)
                    {
                        item.MarkAsDeletedByUser(nameof(AddIdentification));
                        _unitOfWork.RmtKycRefinement.Update(item);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error while trying to update records in RmtKycRefinement: {ex.Message}");
            }

            await this._unitOfWork.CommitAsync();

            if (portalUserId != null)
            {
                var saveAuditTrail = await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId, user.Id, ConstantParam.AuditTrailInsertIdentification, user.PhoneNumber, null);
            }

            try
            {
                await _experimentService.AddUserToNoLoyaltyExperiment(identification);
            }
            catch (Exception ex)
            {
                this._logger.LogWarning(ConstantParam.UnableToInsertUserInExperiment, identification.UserId, ex.Message);
            }

            var isAddition = identification.UpdateType == IdentificationUpdateType.Add;
            var isUpdate = identification.UpdateType == IdentificationUpdateType.Update;

            var isIdealKycResult = identification.NameMatchIsIdeal && identification.FaceMatchIsIdeal;

            var isEligibleForAutoVerification = isUpdate ||
                userWasVerifiedWithDtool ||
                (identification.Vendor == IdentificationServiceVendor.Signzy && isIdealKycResult) ||
                autoVerify;

            if (isEligibleForAutoVerification)
            {
                var approvalResult = await ApproveIdentification(identification.Id, null);

                if (!approvalResult.IsSuccessful)
                {
                    return new ServiceResponse(false, approvalResult.ErrorMessage);
                }
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> UpdateIdentification(Identification identification, Guid? portalUserId = null, string portalEmailId = null)
        {
            this._logger.LogDebug(ConstantParam.UpdatingIdentificationDocument, identification.UserId, identification.DocumentNumber);

            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(record => record.Id == identification.UserId);

            var updateIdentification = await this._unitOfWork.Identifications.FirstOrDefaultAsync(record => record.Id == identification.Id);

            if (user is null)
            {
                this._logger.LogWarning(ConstantParam.UpdatingIdentificationDocumentFailed, identification.UserId, identification.DocumentNumber, ConstantParam.UserNotFound);

                return new ServiceResponse(ConstantParam.UserNotFound);
            }

            if (updateIdentification is null)
            {
                this._logger.LogWarning(ConstantParam.UpdatingIdentificationDocumentFailed, identification.UserId, identification.DocumentNumber, string.Format(ConstantParam.IdentificationNotFound, identification.Id));

                return new ServiceResponse(string.Format(ConstantParam.IdentificationNotFound, identification.Id));
            }

            updateIdentification.FirstName = identification.FirstName;
            updateIdentification.LastName = identification.LastName;
            updateIdentification.FullName = identification.FullName;
            updateIdentification.Birthdate = identification.Birthdate;
            updateIdentification.DocumentNumber = identification.DocumentNumber;
            updateIdentification.Nationality = identification.Nationality;
            updateIdentification.NationalityCode = identification.NationalityCode;
            updateIdentification.Gender = identification.Gender;
            updateIdentification.ExpiryDate = identification.ExpiryDate;
            updateIdentification.FaceMatchPercentage = identification.FaceMatchPercentage;
            updateIdentification.FaceMatchIsIdeal = identification.FaceMatchIsIdeal;
            updateIdentification.NameMatchPercentage = identification.NameMatchPercentage;
            updateIdentification.NameMatchIsIdeal = identification.NameMatchIsIdeal;
            updateIdentification.FrontScanFileName = identification.FrontScanFileName;
            updateIdentification.BackScanFileName = identification.BackScanFileName;
            updateIdentification.SelfieFileName = identification.SelfieFileName;
            updateIdentification.UpdatedDate = DateTime.Now;

            updateIdentification.Vendor = identification.ServiceReference == IdentificationServiceVendor.Azure.ToString() ? IdentificationServiceVendor.Azure : IdentificationServiceVendor.Signzy;
            await this._unitOfWork.CommitAsync();

            if (portalUserId != null)
            {
                var saveAuditTrail = await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId, user.Id, ConstantParam.AuditTrailUpdateIdentification, user.PhoneNumber, null);
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> UpdateSelfie(Identification identification)
        {

            var updateIdentification = await this._unitOfWork.Identifications.FirstOrDefaultAsync(record => record.Id == identification.Id);

            if (updateIdentification is null)
            {
                this._logger.LogWarning(ConstantParam.IdentificationNotFound, identification.Id);
                return new ServiceResponse(string.Format(ConstantParam.IdentificationNotFound, identification.Id));
            }

            this._logger.LogDebug(ConstantParam.AddingIdentificationDocument, updateIdentification.UserId, updateIdentification.DocumentNumber);

            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(record => record.Id == updateIdentification.UserId);

            if (user is null)
            {
                this._logger.LogWarning(ConstantParam.AddingIdentificationDocumentFailed, updateIdentification.UserId, updateIdentification.DocumentNumber, ConstantParam.UserNotFound);

                return new ServiceResponse(ConstantParam.UserNotFound);
            }

            updateIdentification.SelfieFileName = identification.SelfieFileName;

            await this._unitOfWork.CommitAsync();


            return new ServiceResponse();
        }

        public async Task<ServiceResponse> ApproveIdentification(Guid identificationId, string verifiedBy, Guid? portalUserId = null, string portalEmailId = null)
        {
            var identification = await this._unitOfWork.Identifications.FirstOrDefaultAsync(record => record.Id == identificationId);

            var secondIdentification = new Identification();

            this._logger.LogDebug(string.Format(ConstantParam.ApprovingIdentificationDocument, identification.DocumentNumber));

            if (identification == null)
            {
                this._logger.LogWarning(string.Format(ConstantParam.ApprovingIdentificationDocumentFailed, identification.DocumentNumber));

                return new ServiceResponse(string.Format(ConstantParam.IdentificationDocumentNotFound, identificationId));
            }


            var userId = identification.UserId;

            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(record => record.Id == userId, user => user.CardHolder, user => user.Documents);

            if (await _featureManager.IsEnabledAsync(FeatureFlags.SanctionScreeningValidateScreeningStatus))
            {
                var validateSanctionScreeningResult =
                    await _sanctionScreeningService.ValidateUserSanctionScreening(user.CardHolder.C3RegistrationId);
                if (validateSanctionScreeningResult.IsValid == false)
                {
                    return new ServiceResponse(IdentificationVerificationResult.SanctionScreeningInvalid.ToString());
                }
            }

            var userProfileResult = await this._userService.GetUserByIdWithIdentifications(identification.UserId);

            if (identification.Type == IdentificationType.EmiratesId
                    && userProfileResult.Data != null
                    && userProfileResult.Data.Identifications != null
                    && userProfileResult.Data.Identifications.Count > 0
                    && userProfileResult.Data.Identifications.Any(x => x.VerificationStatus == BaseEnums.IdentificationVerificationStatus.Pending
                    && x.Type == IdentificationType.Passport))
            {
                secondIdentification = userProfileResult.Data.Identifications.FirstOrDefault(x => x.VerificationStatus == BaseEnums.IdentificationVerificationStatus.Pending
                      && x.Type == IdentificationType.Passport);
            }
            else
            {
                secondIdentification = null;
            }

            if (identification.Type == IdentificationType.Passport
                    && userProfileResult.Data != null
                    && userProfileResult.Data.Identifications != null
                    && userProfileResult.Data.Identifications.Count > 0
                    && userProfileResult.Data.Identifications.Any(x => x.VerificationStatus == BaseEnums.IdentificationVerificationStatus.Pending
                    && x.Type == IdentificationType.EmiratesId))
            {
                secondIdentification = new Identification();
                secondIdentification = userProfileResult.Data.Identifications.FirstOrDefault(x => x.VerificationStatus == BaseEnums.IdentificationVerificationStatus.Pending
                      && x.Type == IdentificationType.Passport);
            }
            else
            {
                secondIdentification = null;
            }


            if (identification.Type == IdentificationType.EmiratesId)
            {
                var cardHolder = user.CardHolder;

                var frontScanUrl = await this._blobStorageService.GetBlobReadURLAsync(string.Join(".", identification.FrontScanFileName, Enums.IdentificationDocumentMimeType.jpg.ToString()), _emiratesIdContainerName);

                var backScanUrl = await this._blobStorageService.GetBlobReadURLAsync(string.Join(".", identification.BackScanFileName, Enums.IdentificationDocumentMimeType.jpg.ToString()), _emiratesIdContainerName);

                var expiryDate = identification.ExpiryDate;

                var emiratesIdWithinExpiry = expiryDate <= DateTime.Today.AddDays(35);

                var allowedCorporates = await this._partnerCorporateService.GetPartnerCorporates();
                var partnerCode = PartnerType.DirectClient.ToString();
                var partnerCorporate = allowedCorporates.FirstOrDefault(x => x.CorporateId == cardHolder.CorporateId);
                if (partnerCorporate != null)
                {
                    partnerCode = partnerCorporate.Partner.Code;
                }

                var isExchangeHouseUser = cardHolder.BelongsToExchangeHouse && partnerCode != PartnerType.DirectClient.ToString();

                string fullName = identification.FirstName?.Trim() + " " + identification.LastName?.Trim();

                // Split the full name into words
                string[] words = fullName.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

                string firstName = words[0]; // First word as first name
                string lastName = words[^1]; // Last word as last name
                string middleName = string.Join(" ", words, 1, words.Length - 2); // Join remaining words as middle name


                var updateKYCResult = await this._kycService.UpdateEmiratesId(new UpdateEmiratesIdRequestDto()
                {
                    Mobile = user.PhoneNumber,
                    FirstName = firstName,
                    MiddleName = middleName,
                    LastName = lastName,
                    Birthdate = identification.Birthdate,
                    CardNumber = identification.CardNumber,
                    CitizenId = user.CardHolderId,
                    EmiratesId = identification.DocumentNumber,
                    EmiratesIdExpiryDate = identification.ExpiryDate,
                    FullName = identification.FullName,
                    Gender = identification.Gender == Gender.Male ? "M" : "F",
                    Nationality = identification.Nationality,
                    City = cardHolder.City,
                    PassportId = cardHolder.PassportNumber,
                    POBOX = cardHolder.ZipCode,
                    FrontUrl = frontScanUrl.Data,
                    BackUrl = backScanUrl.Data,
                    AcceptExpiredEmiratesId = emiratesIdWithinExpiry,
                    IsExchangeHouseUser = isExchangeHouseUser,
                    FrontFileName = identification.FrontScanFileName,
                    BackFileName = identification.BackScanFileName
                });

                identification.Posted = updateKYCResult.IsSuccessful;

                if (updateKYCResult.IsSuccessful)
                {
                    if (emiratesIdWithinExpiry)
                    {
                        identification.PostRemarks = ConstantParam.EmiratesIdNotPostedToPartnerBankDueExpiry;
                    }

                    await KycUnblock(cardHolder, expiryDate);

                    user.IsVerified = true;
                    await _userService.AddOutboxMessage(user, OutboxMessageTypeEnum.UserVerifiedEvent.ToString());

                    identification.VerificationDate = DateTime.Now;
                    identification.VerificationStatus = IdentificationVerificationStatus.Approved;

                    // Insert into Tracker
                    string emiratesId = string.IsNullOrEmpty(identification.DocumentNumber) ? user.CardHolder.EmiratesId : identification.DocumentNumber;
                    var trackerLog = new MoneyTransferProfileTracker(user.Id, user.CardHolder.Id, emiratesId);
                    trackerLog.EmiratesIdVerifiedAndRequestSubmitted(updateKYCResult.Data.Action);
                    await _unitOfWork.MoneyTransferProfileTracker.AddAsync(trackerLog);

                    var hasOldTransactions = await this._moneyTransferService.HasOldTransactions(identification.DocumentNumber, identification.UserId);

                    if (hasOldTransactions)
                    {
                        user.HasOldTransactions = true;

                    }

                    if (secondIdentification != null)
                    {
                        secondIdentification.VerificationDate = DateTime.Now;
                        secondIdentification.VerificationStatus = IdentificationVerificationStatus.Rejected;
                    }

                    if (!string.IsNullOrEmpty(verifiedBy))
                    {
                        var portalVerifierId = new Guid(verifiedBy);

                        identification.VerifierId = portalVerifierId;
                        if (secondIdentification != null)
                        {
                            secondIdentification.VerifierId = portalVerifierId;
                        }
                    }

                    var userFrontScanDocument = user.Documents.FirstOrDefault(record => record.Type == BaseEnums.DocumentType.EmiratesIdFrontScan);

                    if (userFrontScanDocument == null)
                    {
                        user.Documents.Add(new Document()
                        {
                            Name = identification.FrontScanFileName,
                            Type = BaseEnums.DocumentType.EmiratesIdFrontScan
                        });
                    }
                    else
                    {
                        userFrontScanDocument.Name = identification.FrontScanFileName;
                    }

                    var userBackScanDocument = user.Documents.FirstOrDefault(record => record.Type == BaseEnums.DocumentType.EmiratesIdBackScan);

                    if (userBackScanDocument == null)
                    {
                        user.Documents.Add(new Document()
                        {
                            Name = identification.BackScanFileName,
                            Type = BaseEnums.DocumentType.EmiratesIdBackScan
                        });
                    }
                    else
                    {
                        userBackScanDocument.Name = identification.BackScanFileName;
                    }

                    var userSelfieDocument = user.Documents.FirstOrDefault(record => record.Type == BaseEnums.DocumentType.Selfie);

                    if (userSelfieDocument == null)
                    {
                        user.Documents.Add(new Document()
                        {
                            Name = identification.SelfieFileName,
                            Type = BaseEnums.DocumentType.Selfie
                        });
                    }
                    else
                    {
                        userSelfieDocument.Name = identification.SelfieFileName;
                    }

                    if (!isExchangeHouseUser && user.MoneyTransferProfileStatus == MoneyTransferProfileStatus.Missing && identification.PostRemarks != ConstantParam.EmiratesIdNotPostedToPartnerBankDueExpiry)
                    {
                        user.MoneyTransferProfileStatus = MoneyTransferProfileStatus.Pending;
                    }

                    // MoneyTransferProfileStatus from Created to Pending
                    if (_moneyTransferServiceSettings.RMTStatusFromCreatedToPendingEnabled)
                    {
                        //IN, PK, NP, BD, SL, PH
                        var nationalities = string.IsNullOrEmpty(_moneyTransferServiceSettings.NonWUCorridors) ? new string[] { "" }
                         : _moneyTransferServiceSettings.NonWUCorridors.Split(',').Select(a => a).ToArray();
                        if (nationalities.Contains(user.CardHolder.Nationality.ToUpper()))
                        {
                            int configGraceDays = _moneyTransferServiceSettings.NonWUEmiratesIdExpiryGracePeriodDays;
                            var eIdExpiryDate = Convert.ToDateTime(user.CardHolder.EmiratesIdExpiryDate);
                            if (DateTime.Now.Date >= eIdExpiryDate.Date.AddDays(configGraceDays))
                            {
                                user.MoneyTransferProfileStatus = MoneyTransferProfileStatus.Pending;
                                identification.UpdatePostedDate = DateTime.Now;
                            }
                        }
                        else
                        {
                            int wuConfigGraceDays = _moneyTransferServiceSettings.WUEmiratesIdExpiryGracePeriodDays;
                            var eIdExpiryDate = Convert.ToDateTime(user.CardHolder.EmiratesIdExpiryDate);
                            if (DateTime.Now.Date >= eIdExpiryDate.Date.AddDays(wuConfigGraceDays))
                            {
                                user.MoneyTransferProfileStatus = MoneyTransferProfileStatus.Pending;
                                identification.UpdatePostedDate = DateTime.Now;
                            }
                        }
                    }

                    user.UpdatedDate = DateTime.Now;

                    await this._ppsService.ActivateCard(user.CardHolder.CardSerialNumber);

                    if (identification.UpdateType == IdentificationUpdateType.Add)
                    {
                        this._logger.LogDebug(ConstantParam.UploadingEmiratesIdVerifiedEvent);

                        var uploadResult = await this._analyticsPublisherService.PublishEmiratesIdVerifiedEvent(user.CardHolderId, new Core.Models.Messages.Analytics.EmiratesIdVerifiedEvent()
                        {
                            Country = identification.Nationality
                        });

                        if (!uploadResult.IsSuccessful)
                        {
                            this._logger.LogWarning(ConstantParam.UploadingEmiratesIdVerifiedEventFailed, uploadResult.ErrorMessage);
                        }

                    }

                    this._logger.LogDebug(ConstantParam.SendingRegistrationApprovalPushNotification, user.Id);

                    if (string.IsNullOrEmpty(user.DeviceToken))
                    {
                        this._logger.LogWarning(ConstantParam.SendingRegistrationApprovalPushNotificationFailed, user.Id, ConstantParam.UserDeviceNotRegistred);
                    }
                    else
                    {
                        var sendPushNotificationResult = (identification.Type == IdentificationType.EmiratesId) ? await this._pushNotificationSenderService.SendRegistrationEmiratesIdApprovalNotification(user.DeviceToken)
                                                                                                        : await this._pushNotificationSenderService.SendRegistrationPassportApprovalNotification(user.DeviceToken);

                        if (!sendPushNotificationResult.IsSuccessful)
                        {
                            this._logger.LogWarning(ConstantParam.SendingRegistrationApprovalPushNotificationFailed, user.Id, sendPushNotificationResult.ErrorMessage);
                        }

                    }


                    await _unitOfWork.CommitAsync();

                    if (portalUserId != null)
                    {
                        var saveAuditTrail = await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId, user.Id, ConstantParam.AuditTrailVerifyRegistration, user.PhoneNumber, null);
                    }

                    return new ServiceResponse();
                }
                else
                {
                    if (identification.UpdateType == IdentificationUpdateType.Update)
                    {
                        this._unitOfWork.Identifications.Remove(identification);
                        if (secondIdentification != null && secondIdentification.UpdateType == IdentificationUpdateType.Update)
                        {
                            this._unitOfWork.Identifications.Remove(secondIdentification);
                        }
                    }
                    else
                    {
                        identification.PostRemarks = updateKYCResult.ErrorMessage;

                        // Insert into Tracker
                        var trackerLog = new MoneyTransferProfileTracker(user.Id, user.CardHolder.Id, user.CardHolder.EmiratesId);
                        trackerLog.ProfileRequestSubmissionFailed(updateKYCResult.ErrorMessage);
                        await _unitOfWork.MoneyTransferProfileTracker.AddAsync(trackerLog);
                    }


                    await _unitOfWork.CommitAsync();

                    return new ServiceResponse(false, IdentificationVerificationResult.VerificationFailed.ToString());
                }
            }
            else if (identification.Type == IdentificationType.Passport)
            {
                user.IsVerified = true;
                await _userService.AddOutboxMessage(user, OutboxMessageTypeEnum.UserVerifiedEvent.ToString());
                identification.VerificationDate = DateTime.Now;
                identification.VerificationStatus = IdentificationVerificationStatus.Approved;

                if (secondIdentification != null)
                {
                    secondIdentification.VerificationDate = DateTime.Now;
                    secondIdentification.VerificationStatus = IdentificationVerificationStatus.Approved;
                }

                if (!string.IsNullOrEmpty(verifiedBy))
                {
                    var portalVerifierId = new Guid(verifiedBy);

                    identification.VerifierId = portalVerifierId;
                    if (secondIdentification != null)
                    {
                        secondIdentification.VerifierId = portalVerifierId;
                    }
                }

                var userFrontScanDocument = user.Documents.FirstOrDefault(record => record.Type == BaseEnums.DocumentType.Passport);

                if (userFrontScanDocument == null)
                {
                    user.Documents.Add(new Document()
                    {
                        Name = identification.FrontScanFileName,
                        Type = BaseEnums.DocumentType.Passport
                    });
                }
                else
                {
                    userFrontScanDocument.Name = identification.FrontScanFileName;
                }

                var userSelfieDocument = user.Documents.FirstOrDefault(record => record.Type == BaseEnums.DocumentType.Selfie);

                if (userSelfieDocument == null)
                {
                    user.Documents.Add(new Document()
                    {
                        Name = identification.SelfieFileName,
                        Type = BaseEnums.DocumentType.Selfie
                    });
                }
                else
                {
                    userSelfieDocument.Name = identification.SelfieFileName;
                }

                if (identification.UpdateType == IdentificationUpdateType.Add)
                {
                    await this._ppsService.ActivateCard(user.CardHolder.CardSerialNumber);
                }

                this._logger.LogDebug(ConstantParam.SendingRegistrationApprovalPushNotification, user.Id);

                if (string.IsNullOrEmpty(user.DeviceToken))
                {
                    this._logger.LogWarning(ConstantParam.SendingRegistrationApprovalPushNotificationFailed, user.Id, ConstantParam.UserDeviceNotRegistred);
                }
                else
                {
                    var sendPushNotificationResult = (identification.Type == IdentificationType.EmiratesId) ? await this._pushNotificationSenderService.SendRegistrationEmiratesIdApprovalNotification(user.DeviceToken)
                                                                                                        : await this._pushNotificationSenderService.SendRegistrationPassportApprovalNotification(user.DeviceToken);

                    if (!sendPushNotificationResult.IsSuccessful)
                    {
                        this._logger.LogWarning(ConstantParam.SendingRegistrationApprovalPushNotificationFailed, user.Id, sendPushNotificationResult.ErrorMessage);
                    }

                }

                this._logger.LogInformation(string.Format(ConstantParam.IdentificationDocumentApproved, identification.DocumentNumber));

                await _unitOfWork.CommitAsync();

                if (portalUserId != null)
                {
                    var saveAuditTrail = await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId, user.Id, ConstantParam.AuditTrailVerifyRegistration, user.PhoneNumber, null);
                }

                return new ServiceResponse();
            }
            else
            {
                return new ServiceResponse(false, ConstantParam.InvalidDocumentType);
            }
        }

        private async Task KycUnblock(CardHolder cardHolder, DateTime expiryDate)
        {
            if (expiryDate.AddDays(90) < DateTime.Now.Date)
                return;


            // Check that the user is part of KYC Unblock.
            var kycBlocked = await _unitOfWork.MissingKycCardholders.FirstOrDefaultAsync(u => u.CitizenId == cardHolder.Id &&
                                                                                              (u.Status == KycStatus.NotSubmittedKycBlocked || u.Status == KycStatus.Blocked));
            // User not found or was already unblocked.
            if (kycBlocked is null)
            {
                return;
            }

            // Unblock card.
            var tryUnblockCard = await _ppsService.UnblockCard(cardHolder.CardSerialNumber);
            if (tryUnblockCard.IsSuccessful == false)
            {
                // Can't unblock card.
                kycBlocked.Remarks += $"| Can't unblock card. Error: {tryUnblockCard.ErrorMessage}";
            }
            else
            {
                // Card was unblocked.
                kycBlocked.Status = KycStatus.Valid;
                kycBlocked.UnblockDate = DateTime.Now;
                kycBlocked.Remarks += $"| Card was unblocked";

                // Add entry to ESMO.
                var tryAddEntry = await _eSMOWebService.MarkCardAsKycUnblockedV2(new MarkUnblockCardRequest
                {
                    C3RegistrationId = cardHolder.C3RegistrationId,
                    Remarks = ConstantParam.UnblockingCardWithValidEidRemark,
                    Username = ConstantParam.SenderUsername
                });
                if (tryAddEntry.IsSuccessful == false)
                {
                    kycBlocked.Remarks += $"| Unable to add entry to ESMO. Error: {tryAddEntry.ErrorMessage}";
                }
                else
                {
                    kycBlocked.Remarks += $"| Card was marked as unblocked on ESMO";
                }

                _logger.LogInformation("CardHolderId: {cardHolderId} was unblocked", cardHolder.Id);

                if (await _featureManager.IsEnabledAsync(FeatureFlags.KycUnblockAllCards))
                {
                    if (cardHolder.EmiratesId != null && cardHolder.EmiratesId != "111111111111111")
                    {
                        //Reschedule message in the queue            
                        await _messagingQueueService.SendAsync<UnblockKycMessageDto>(new UnblockKycMessageDto { Id = Guid.NewGuid(), CardholderId = cardHolder.Id, PassportId = cardHolder.PassportNumber, EmiratesId = cardHolder.EmiratesId },
                                                                                    this._kycUnblockByPassportSettings.Value.QueueConnectionString,
                                                                                    this._kycUnblockByPassportSettings.Value.QueueName, null);

                    }
                    else
                    {
                        _logger.LogWarning($"Emirates Id is not valid {cardHolder.Id}");
                    }
                }

            }
        }

        public async Task<ServiceResponse> RejectIdentification(Guid identificationId, string rejectedBy, int userRegistrationRejectionReasonId, Guid? portalUserId = null, string portalEmailId = null)
        {
            var identification = await this._unitOfWork.Identifications.FirstOrDefaultAsync(record => record.Id == identificationId);

            if (identification == null)
            {
                return new ServiceResponse(string.Format(ConstantParam.IdentificationDocumentNotFound, identificationId));
            }

            var userId = identification.UserId;

            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(record => record.Id == userId);

            if (user == null)
            {
                return new ServiceResponse(string.Format(ConstantParam.UserNotFound, userId));
            }

            identification.VerificationDate = DateTime.Now;
            identification.VerificationStatus = IdentificationVerificationStatus.Rejected;
            identification.UserRegistrationRejectionReasonId = userRegistrationRejectionReasonId;

            if (!string.IsNullOrEmpty(rejectedBy))
            {
                identification.VerifierId = new Guid(rejectedBy);
            }

            await _unitOfWork.CommitAsync();

            var userIdentityResult = await this._identityService.GetUserAccountAsync(user.PhoneNumber.ToZeroPrefixedPhoneNumber());

            if (!userIdentityResult.IsSuccessful)
            {
                return new ServiceResponse(string.Format(ConstantParam.UserNotFound, userId));
            }

            if (string.IsNullOrEmpty(userIdentityResult.Data.Email))
            {
                this._logger.LogError(ConstantParam.SendingRegistrationRejectionEmailFailed, userId, ConstantParam.UserHasNoEmail);
            }
            else
            {
                this._logger.LogDebug(ConstantParam.SendingRegistrationRejectionEmail, userId, userIdentityResult.Data.Email);

                var emailAttachment = await _blobStorageService.GetBlobWithContentAsync(ConstantParam.EmiratesIdGuideFileName, _emailAttachmentContainerName);

                if (!emailAttachment.IsSuccessful)
                {
                    this._logger.LogError(ConstantParam.SendingRegistrationRejectionEmailFailed, userId, userIdentityResult.Data.Email);

                    return new ServiceResponse(emailAttachment.ErrorMessage);
                }

                var sendRegistrationRejectEmailRequest = new RegistrationRejectionEmailRequest()
                {
                    Recipients = new List<string>() { userIdentityResult.Data.Email },
                    Attachment = new List<SendGrid.Helpers.Mail.Attachment>()
                    {
                        new SendGrid.Helpers.Mail.Attachment()
                        {
                            Content = emailAttachment.Data.Base64Content,
                            Type = EnumUtility.GetDescriptionFromEnumValue(BaseEnums.FileMediaType.PDF),
                            Filename = ConstantParam.EmiratesIdGuideFileName
                        }
                    }
                };

                var emailResult = await this._emailSenderService.SendRegistrationRejectionEmail(sendRegistrationRejectEmailRequest);

                if (!emailResult.IsSuccessful)
                {
                    this._logger.LogError(ConstantParam.SendingRegistrationRejectionEmailFailed, userId, userIdentityResult.Data.Email);

                    return new ServiceResponse(emailResult.ErrorMessage);
                }

            }

            var userRejectionReason = await _lookupService.GetUserRegistrationRejectionReasons();

            this._logger.LogDebug(ConstantParam.UploadingEmiratesIdRejectedEvent);

            var uploadResult = await this._analyticsPublisherService.PublishEmiratesIdRejectedEvent(user.CardHolderId, new Core.Models.Messages.Analytics.EmiratesIdRejectedEvent()
            {
                Country = identification.Nationality,
                RejectionReason = userRejectionReason != null && userRejectionReason.Data.Any(x => x.Id == userRegistrationRejectionReasonId) ?
                                    userRejectionReason.Data.FirstOrDefault(x => x.Id == userRegistrationRejectionReasonId).Name : string.Empty

            });

            if (!uploadResult.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.UploadingEmiratesIdRejectedEventFailed, uploadResult.ErrorMessage);
            }


            if (portalUserId != null)
            {
                var saveAuditTrail = await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId, user.Id, ConstantParam.AuditTrailRejectRegistration, user.PhoneNumber, null);
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse<Tuple<List<Identification>, int>>> SearchRejectedIdentification(SearchUserParameters searchUserParameters)
        {
            var responseIdentifications = await this._unitOfWork.Identifications.SearchRejectedIdentification(searchUserParameters);
            return new ServiceResponse<Tuple<List<Identification>, int>>(responseIdentifications);
        }

        private async Task AddEmiratesIdIdentificationLog(Guid? userId, string frontScanFileName, string backScanFileName, EmiratesIdLog log, IdentificationServiceVendor vendor)
        {

            log.Vendor = vendor;

            if (userId.HasValue)
                log.UserId = userId.Value;

            log.FrontScanUrl = frontScanFileName;
            log.BackScanUrl = backScanFileName;
            await _unitOfWork.EmiratesIdLogs.AddAsync(log);
        }

        private async Task AddPassportIdentificationLog(Guid? userId, string frontScanFileName, PassportLog log, IdentificationServiceVendor vendor)
        {
            log.Vendor = vendor;

            if (userId.HasValue)
                log.UserId = userId.Value;

            log.FrontScanUrl = frontScanFileName;
            await _unitOfWork.PassportLogs.AddAsync(log);
        }

        #region NonRegisteredIdentifications

        public async Task<ServiceResponse<object>> ReadNonRegisteredIdentificationDocument(Guid? userId, string frontScanFileName, string backScanFileName, string nameToMatch, IdentificationType identificationType)
        {
            this._logger.LogDebug(ConstantParam.ReadingUserIdentificationDocumentStarted, userId, frontScanFileName, backScanFileName, nameToMatch);
            ServiceResponse<BaseIdDocumentDto> identificationResult = new ServiceResponse<BaseIdDocumentDto>();
            try
            {
                identificationResult = await this._secondaryIdentificationService.ReadDocument(frontScanFileName, backScanFileName, identificationType, "");
            }
            catch (Exception ex)
            {
                this._logger.LogError(ex, "Failure to read");
                return new ServiceResponse<object>(false, identificationResult.ErrorMessage);
            }

            if (!identificationResult.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.ReadingUserIdentificationDocumentFailed, userId, frontScanFileName, backScanFileName, nameToMatch, identificationResult.ErrorMessage);

                if (identificationResult.Data != null)
                {
                    var vendor = IdentificationServiceVendor.Azure;
                    await AddToEmiratesIdLog(userId, frontScanFileName, backScanFileName, identificationType, identificationResult, vendor);
                }

                await _unitOfWork.CommitAsync();
                return new ServiceResponse<object>(false, identificationResult.ErrorMessage);
            }


            if (identificationType == IdentificationType.EmiratesId)
            {
                var emiratesId = (EmiratesIdDto)identificationResult.Data;
                return new ServiceResponse<object>(emiratesId);
            }
            else if (identificationType == IdentificationType.Passport)
            {
                var passport = (PassportDto)identificationResult.Data;
                return new ServiceResponse<object>(passport);
            }
            else
            {
                return new ServiceResponse<object>(false, ConstantParam.InvalidDocumentType);
            }
        }

        public async Task<ServiceResponse> AddNonRegisteredIdentification(NonRegisteredIdentification identification, CardHolder cardHolder)
        {
            if (await _featureManager.IsEnabledAsync(FeatureFlags.BlockEIDsExpiredForMoreThan90Days))
            {
                if (identification.Type == IdentificationType.EmiratesId)
                {
                    try
                    {
                        if (identification.ExpiryDate.AddDays(90) < DateTime.Now)
                        {
                            return new ServiceResponse(false, ConstantParam.EmiratesIdExpired);
                        }

                        _logger.LogWarning("90days: blocked expired Emirates Id");
                    }
                    catch (Exception)
                    {
                        _logger.LogError("90days: Couldn't extract expiry date");
                    }
                }
            }

            this._logger.LogDebug(ConstantParam.AddingIdentificationDocument, "", identification.DocumentNumber);

            var userIdentifications = await this._unitOfWork.NonRegisteredIdentifications.FindAsync(record => record.CardHolderId == identification.CardHolderId);

            if (identification.Type == IdentificationType.EmiratesId)
            {

                var userHasApprovedEmiratesIdUpdates = userIdentifications.Any(record =>
                    record.Type == IdentificationType.EmiratesId &&
                    record.VerificationStatus == IdentificationVerificationStatus.Approved);

                identification.UpdateType = userHasApprovedEmiratesIdUpdates ? IdentificationUpdateType.Update : IdentificationUpdateType.Add;
            }
            else if (identification.Type == IdentificationType.Passport)
            {
                var userHasPendingOrApprovedPassportIdentifications = userIdentifications.Any(record =>
                   record.Type == IdentificationType.Passport &&
                   record.VerificationStatus != IdentificationVerificationStatus.Rejected);

                if (userHasPendingOrApprovedPassportIdentifications)
                {
                    return new ServiceResponse(false, ConstantParam.UserAlreadyUploadedAPassportIdentification);
                }

                identification.UpdateType = IdentificationUpdateType.Add;
            }

            identification.VerificationStatus = IdentificationVerificationStatus.Pending;

            identification.Vendor = identification.ServiceReference == IdentificationServiceVendor.Azure.ToString() ? IdentificationServiceVendor.Azure : IdentificationServiceVendor.Signzy;
            await this._unitOfWork.NonRegisteredIdentifications.AddAsync(identification);

            await this._unitOfWork.CommitAsync();



            var isAddition = identification.UpdateType == IdentificationUpdateType.Add;
            var isUpdate = identification.UpdateType == IdentificationUpdateType.Update;

            var approvalResult = await ApproveNonRegisteredIdentification(identification.Id, cardHolder);
            if (!approvalResult.IsSuccessful)
            {
                return new ServiceResponse(false, approvalResult.ErrorMessage);
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> ApproveNonRegisteredIdentification(Guid identificationId, CardHolder cardHolder)
        {
            var identification = await this._unitOfWork.NonRegisteredIdentifications.FirstOrDefaultAsync(record => record.Id == identificationId);

            if (await _featureManager.IsEnabledAsync(FeatureFlags.SanctionScreeningValidateScreeningStatus))
            {
                var validateSanctionScreeningResult =
                    await _sanctionScreeningService.ValidateUserSanctionScreening(identification.C3RegistrationId);
                if (validateSanctionScreeningResult.IsValid == false)
                {
                    return new ServiceResponse(IdentificationVerificationResult.SanctionScreeningInvalid.ToString());
                }
            }


            if (identification.Type == IdentificationType.EmiratesId)
            {
                var expiryDate = identification.ExpiryDate;

                await KycUnblock(cardHolder, expiryDate);

                identification.VerificationDate = DateTime.Now;
                identification.VerificationStatus = IdentificationVerificationStatus.Approved;

                await this._ppsService.ActivateCard(identification.CardSerialNumber);
            }
            else if (identification.Type == IdentificationType.Passport)
            {
                identification.VerificationDate = DateTime.Now;
                identification.VerificationStatus = IdentificationVerificationStatus.Approved;
            }
            else
            {
                return new ServiceResponse(false, ConstantParam.InvalidDocumentType);
            }

            await _unitOfWork.CommitAsync();

            return new ServiceResponse();
        }

        public async Task<ServiceResponse<NonRegisteredDetailsDto>> GetNonRegisteredDocuments(string cardHolderId)
        {
            var todaysDate = DateTime.Now.Date;
            var nonRegisteredDetailsDto = new NonRegisteredDetailsDto();
            nonRegisteredDetailsDto.CardHolderId = cardHolderId;
            nonRegisteredDetailsDto.NonRegisteredDocuments = new List<NonRegisteredDocument>();

            //Check if cardHolder is a registered user
            var userData = (await _userService.GetUserByCitizenId(cardHolderId))?.Data;

            //User is not registered
            if (userData is null)
            {
                var getLatestEmiratesIdIdentifications = (await _unitOfWork.NonRegisteredIdentifications.FindAsync(f => f.CardHolderId == cardHolderId && f.Type == IdentificationType.EmiratesId &&
                                                                                                                        f.VerificationStatus != IdentificationVerificationStatus.Rejected &&
                                                                                                                        f.IsDeleted == false,
                                                                                                                   order => order.CreatedDate, true, null, null, true)).FirstOrDefault();

                var getLatestPassportIdentifications = (await _unitOfWork.NonRegisteredIdentifications.FindAsync(f => f.CardHolderId == cardHolderId && f.Type == IdentificationType.Passport &&
                                                                                                                      f.VerificationStatus != IdentificationVerificationStatus.Rejected &&
                                                                                                                      f.IsDeleted == false,
                                                                                                                 order => order.CreatedDate, true, null, null, true)).FirstOrDefault();

                if (getLatestEmiratesIdIdentifications != null && getLatestEmiratesIdIdentifications.ExpiryDate >= todaysDate)
                {
                    nonRegisteredDetailsDto.EmiratesId = getLatestEmiratesIdIdentifications.DocumentNumber;
                    nonRegisteredDetailsDto.EmiratesIdExpiryDate = getLatestEmiratesIdIdentifications.ExpiryDate;

                    //Set the front image details
                    var nonRegisteredDocument = new NonRegisteredDocument();
                    nonRegisteredDocument.DocumentTypeId = BaseEnums.DocumentType.EmiratesIdFrontScan;

                    if (!string.IsNullOrEmpty(getLatestEmiratesIdIdentifications.FrontScanFileName))
                    {
                        var frontScanUrl = await _blobStorageService.GetBlobReadURLAsync($"{getLatestEmiratesIdIdentifications.FrontScanFileName}.{IdentificationDocumentMimeType.jpg}", _emiratesIdContainerName);
                        nonRegisteredDocument.DocumentUrl = frontScanUrl.Data;
                    }

                    nonRegisteredDetailsDto.NonRegisteredDocuments.Add(nonRegisteredDocument);

                    //Set the back image details
                    nonRegisteredDocument = new NonRegisteredDocument();
                    nonRegisteredDocument.DocumentTypeId = BaseEnums.DocumentType.EmiratesIdBackScan;

                    if (!string.IsNullOrEmpty(getLatestEmiratesIdIdentifications.BackScanFileName))
                    {
                        var backScanUrl = await _blobStorageService.GetBlobReadURLAsync($"{getLatestEmiratesIdIdentifications.BackScanFileName}.{IdentificationDocumentMimeType.jpg}", _emiratesIdContainerName);
                        nonRegisteredDocument.DocumentUrl = backScanUrl.Data;
                    }

                    nonRegisteredDetailsDto.NonRegisteredDocuments.Add(nonRegisteredDocument);
                }

                //Do not send back if only passport image is uploaded AND only if the expiry date of the passport is greater than today
                if (!(getLatestEmiratesIdIdentifications == null && getLatestPassportIdentifications != null && (todaysDate - getLatestPassportIdentifications.CreatedDate.Date).TotalDays > 90) &&
                    getLatestPassportIdentifications != null && getLatestPassportIdentifications.ExpiryDate >= todaysDate)
                {
                    nonRegisteredDetailsDto.PassportNumber = getLatestPassportIdentifications.DocumentNumber;
                    nonRegisteredDetailsDto.PassportExpiryDate = getLatestPassportIdentifications.ExpiryDate;
                    nonRegisteredDetailsDto.PassportUploadedDate = getLatestPassportIdentifications.CreatedDate;

                    //Set the passport image details
                    var nonRegisteredDocument = new NonRegisteredDocument();
                    nonRegisteredDocument.DocumentTypeId = BaseEnums.DocumentType.Passport;

                    if (!string.IsNullOrEmpty(getLatestPassportIdentifications.FrontScanFileName))
                    {
                        var frontScanUrl = await _blobStorageService.GetBlobReadURLAsync($"{getLatestPassportIdentifications.FrontScanFileName}.{IdentificationDocumentMimeType.jpg}", _passportContainerName);
                        nonRegisteredDocument.DocumentUrl = frontScanUrl.Data;
                    }

                    nonRegisteredDetailsDto.NonRegisteredDocuments.Add(nonRegisteredDocument);
                }
            }
            else //User is registered
            {
                var userId = userData.Id;
                nonRegisteredDetailsDto.IsRegistered = true;
                nonRegisteredDetailsDto.IsVerified = userData.IsVerified;

                var getLatestEmiratesIdIdentifications = (await _unitOfWork.Identifications.FindAsync(f => f.UserId == userId && f.Type == IdentificationType.EmiratesId &&
                                                                                                           f.VerificationStatus != IdentificationVerificationStatus.Rejected &&
                                                                                                           f.IsDeleted == false,
                                                                                                      order => order.CreatedDate, true, null, null, true)).FirstOrDefault();

                var getLatestPassportIdentifications = (await _unitOfWork.Identifications.FindAsync(f => f.UserId == userId && f.Type == IdentificationType.Passport &&
                                                                                                         f.VerificationStatus != IdentificationVerificationStatus.Rejected &&
                                                                                                         f.IsDeleted == false,
                                                                                                    order => order.CreatedDate, true, null, null, true)).FirstOrDefault();

                if (getLatestEmiratesIdIdentifications != null && getLatestEmiratesIdIdentifications.ExpiryDate >= todaysDate)
                {
                    nonRegisteredDetailsDto.EmiratesId = getLatestEmiratesIdIdentifications.DocumentNumber;
                    nonRegisteredDetailsDto.EmiratesIdExpiryDate = getLatestEmiratesIdIdentifications.ExpiryDate;

                    //Set the front image details
                    var nonRegisteredDocument = new NonRegisteredDocument();
                    nonRegisteredDocument.DocumentTypeId = BaseEnums.DocumentType.EmiratesIdFrontScan;

                    if (!string.IsNullOrEmpty(getLatestEmiratesIdIdentifications.FrontScanFileName))
                    {
                        var frontScanUrl = await _blobStorageService.GetBlobReadURLAsync($"{getLatestEmiratesIdIdentifications.FrontScanFileName}.{IdentificationDocumentMimeType.jpg}", _emiratesIdContainerName);
                        nonRegisteredDocument.DocumentUrl = frontScanUrl.Data;
                    }

                    nonRegisteredDetailsDto.NonRegisteredDocuments.Add(nonRegisteredDocument);

                    //Set the back image details
                    nonRegisteredDocument = new NonRegisteredDocument();
                    nonRegisteredDocument.DocumentTypeId = BaseEnums.DocumentType.EmiratesIdBackScan;

                    if (!string.IsNullOrEmpty(getLatestEmiratesIdIdentifications.BackScanFileName))
                    {
                        var backScanUrl = await _blobStorageService.GetBlobReadURLAsync($"{getLatestEmiratesIdIdentifications.BackScanFileName}.{IdentificationDocumentMimeType.jpg}", _emiratesIdContainerName);
                        nonRegisteredDocument.DocumentUrl = backScanUrl.Data;
                    }

                    nonRegisteredDetailsDto.NonRegisteredDocuments.Add(nonRegisteredDocument);
                }

                //Do not send back if only passport image is uploaded AND only if the expiry date of the passport is greater than today
                if (!(getLatestEmiratesIdIdentifications == null && getLatestPassportIdentifications != null && (todaysDate - getLatestPassportIdentifications.CreatedDate.Date).TotalDays > 90) &&
                    getLatestPassportIdentifications != null && getLatestPassportIdentifications.ExpiryDate >= todaysDate)
                {
                    nonRegisteredDetailsDto.PassportNumber = getLatestPassportIdentifications.DocumentNumber;
                    nonRegisteredDetailsDto.PassportExpiryDate = getLatestPassportIdentifications.ExpiryDate;
                    nonRegisteredDetailsDto.PassportUploadedDate = getLatestPassportIdentifications.CreatedDate;

                    //Set the passport image details
                    var nonRegisteredDocument = new NonRegisteredDocument();
                    nonRegisteredDocument.DocumentTypeId = BaseEnums.DocumentType.Passport;

                    if (!string.IsNullOrEmpty(getLatestPassportIdentifications.FrontScanFileName))
                    {
                        var frontScanUrl = await _blobStorageService.GetBlobReadURLAsync($"{getLatestPassportIdentifications.FrontScanFileName}.{IdentificationDocumentMimeType.jpg}", _passportContainerName);
                        nonRegisteredDocument.DocumentUrl = frontScanUrl.Data;
                    }

                    nonRegisteredDetailsDto.NonRegisteredDocuments.Add(nonRegisteredDocument);
                }
            }

            return new ServiceResponse<NonRegisteredDetailsDto>(nonRegisteredDetailsDto);
        }

        #endregion

        #region Private Methods

        public async Task<ServiceResponse> UploadNonRegisteredDocuments(NonRegisteredIdentificationDto request)
        {

            var ticks = DateTime.UtcNow.Ticks;
            var frontFileName = $"{_frontFileName}{ticks}";
            var backFileName = $"{_backFileName}{ticks}";
            var passportFileName = $"{_passportFileName}{ticks}";

            var cardHolder = new CardHolder
            {
                Id = request.CardHolderId,
                CardSerialNumber = request.CardSerialNumber,
                C3RegistrationId = request.C3RegistrationId

            };

            if (string.IsNullOrEmpty(request.PassportScanBase64String) && ((string.IsNullOrEmpty(request.EmiratesIdFrontScanBase64String) && string.IsNullOrEmpty(request.EmiratesIdBackScanBase64String)) ||
                                                      (!(string.IsNullOrEmpty(request.EmiratesIdFrontScanBase64String)) && string.IsNullOrEmpty(request.EmiratesIdBackScanBase64String)) ||
                                                      (string.IsNullOrEmpty(request.EmiratesIdFrontScanBase64String) && !(string.IsNullOrEmpty(request.EmiratesIdBackScanBase64String)))))
            {
                return new ServiceResponse(ConstantParam.DocumentMissing);
            }

            try
            {
                // Get details about the cardholder.
                var tryGetCardHolder = await this._cardHolderService.GetCardHolderByCitizenId(request.CardHolderId);

                if (!tryGetCardHolder.IsSuccessful)
                {
                    return new ServiceResponse(ConstantParam.CardHolderNotFound);
                }

                var cardHolderDetails = tryGetCardHolder.Data;

                //Check if cardHolder is a registered user
                var userData = (await _userService.GetUserByCitizenId(cardHolder.Id))?.Data;
                var userId = userData?.Id;

                //Not to allow upload of passport a second time or user is verified
                if (string.IsNullOrEmpty(request.EmiratesIdFrontScanBase64String) && string.IsNullOrEmpty(request.EmiratesIdBackScanBase64String) && !string.IsNullOrEmpty(request.PassportScanBase64String))
                {
                    var passportCannotBeUploaded = false;
                    if (userId is null)
                    {
                        passportCannotBeUploaded = await _unitOfWork.NonRegisteredIdentifications.Any(f => f.CardHolderId == request.CardHolderId && f.Type == IdentificationType.Passport && f.IsDeleted == false);
                    }
                    else
                    {
                        passportCannotBeUploaded = userData.IsVerified;
                    }

                    if (passportCannotBeUploaded)
                    {
                        return new ServiceResponse("Not allowed to upload a passport for the second time.");
                    }
                }

                //FRONT PAGE
                if (!string.IsNullOrEmpty(request.EmiratesIdFrontScanBase64String))
                {
                    var frontScanResult = await UploadDocumentToTempBlob(userId, request.EmiratesIdFrontScanBase64String, frontFileName, BaseEnums.DocumentType.EmiratesIdFrontScan, true);

                    if (!frontScanResult.IsSuccessful)
                    {
                        return new ServiceResponse(frontScanResult.ErrorMessage);
                    }
                }
                //BACK PAGE
                if (!string.IsNullOrEmpty(request.EmiratesIdBackScanBase64String))
                {
                    var backScanResult = await UploadDocumentToTempBlob(userId, request.EmiratesIdBackScanBase64String, backFileName, BaseEnums.DocumentType.EmiratesIdBackScan);

                    if (!backScanResult.IsSuccessful)
                    {
                        return new ServiceResponse(backScanResult.ErrorMessage);
                    }
                }

                //PASSPORT
                if (!string.IsNullOrEmpty(request.PassportScanBase64String))
                {
                    var passportResult = await UploadDocumentToTempBlob(userId, request.PassportScanBase64String, passportFileName, BaseEnums.DocumentType.Passport, true);
                    if (!passportResult.IsSuccessful)
                    {
                        return new ServiceResponse(passportResult.ErrorMessage);
                    }
                }

                var documents = new List<IdentificationDocumentDto>();
                //Save the Files to Blob
                if (!(string.IsNullOrEmpty(request.EmiratesIdFrontScanBase64String)) && !(string.IsNullOrEmpty(request.EmiratesIdBackScanBase64String)))
                {
                    var frontScanUrl = await GetUrlIfFileExists(frontFileName);
                    if (string.IsNullOrEmpty(frontScanUrl))
                    {
                        return new ServiceResponse(string.Format(ConstantParam.DocumentNotFound, frontFileName));
                    }

                    var backScanUrl = await GetUrlIfFileExists(backFileName);
                    if (string.IsNullOrEmpty(backScanUrl))
                    {
                        return new ServiceResponse(string.Format(ConstantParam.DocumentNotFound, backFileName));
                    }

                    //Read Data
                    var extractDataResult = await ReadDataFromEmiratesIdImage(userId, frontScanUrl, backScanUrl);
                    if (!extractDataResult.IsSuccessful)
                    {
                        return new ServiceResponse(extractDataResult.ErrorMessage);
                    }

                    //Validate the Read Data
                    var emiratesDetails = ((ServiceResponse<EmiratesIdDto>)extractDataResult).Data;

                    var emiratesIdValidResult = ValidateEmiratesIdDetails(emiratesDetails);
                    if (!emiratesIdValidResult.IsSuccessful)
                    {
                        return new ServiceResponse(emiratesIdValidResult.ErrorMessage);
                    }
                    //Save in EmiratesIdUpdate
                    var updateEmiratesId = _mapper.Map<UpdateIdentificationRequestDto>(emiratesDetails);
                    if (userId != null)
                    {
                        updateEmiratesId.UserId = userId.Value;
                    }
                    updateEmiratesId.FrontScanFileName = frontFileName;
                    updateEmiratesId.BackScanFileName = backFileName;

                    ServiceResponse saveUpdateEmiratesIdResult = null;
                    if (userId is null)
                    {
                        saveUpdateEmiratesIdResult = await SaveNonRegisteredIdentification(updateEmiratesId, IdentificationType.EmiratesId, new CardHolder
                        {
                            Id = request.CardHolderId,
                            CardSerialNumber = request.CardSerialNumber,
                            C3RegistrationId = request.C3RegistrationId,
                            EmiratesId = updateEmiratesId.IdNumber,
                            PassportNumber = cardHolderDetails.PassportNumber
                        });
                    }
                    else
                    {
                        saveUpdateEmiratesIdResult = await SaveIdentification(updateEmiratesId, IdentificationType.EmiratesId);
                    }

                    if (!saveUpdateEmiratesIdResult.IsSuccessful)
                    {
                        return new ServiceResponse(saveUpdateEmiratesIdResult.ErrorMessage);
                    }

                    documents = new List<IdentificationDocumentDto>()
                    {
                        new IdentificationDocumentDto() { DocumentName = string.Concat(frontFileName, ".", IdentificationDocumentMimeType.jpg), DocumentType = Enums.DocumentType.emirates_id_front_scan, DocumentUrl = frontScanUrl},
                        new IdentificationDocumentDto() { DocumentName = string.Concat(backFileName, ".", IdentificationDocumentMimeType.jpg), DocumentType = Enums.DocumentType.emirates_id_back_scan, DocumentUrl = backScanUrl},
                    };
                }
                if (!string.IsNullOrEmpty(request.PassportScanBase64String))
                {
                    var passportScanUrl = await GetUrlIfFileExists(passportFileName);
                    if (string.IsNullOrEmpty(passportScanUrl))
                    {
                        return new ServiceResponse(string.Format(ConstantParam.DocumentNotFound, backFileName));
                    }

                    //Read Data
                    var extractDataResult = await ReadDataFromPassportImage(userId, passportScanUrl);
                    if (!extractDataResult.IsSuccessful)
                    {
                        return new ServiceResponse(extractDataResult.ErrorMessage);
                    }

                    //Validate the Read Data
                    var passportDetails = ((ServiceResponse<DataExtractionDto>)extractDataResult).Data;

                    var passportValidResult = ValidatePassportDetails(passportDetails);
                    if (!passportValidResult.IsSuccessful)
                    {
                        return new ServiceResponse(passportValidResult.ErrorMessage);
                    }
                    //Save in Passport
                    var updatePassport = _mapper.Map<UpdateIdentificationRequestDto>(passportDetails);

                    if (userId != null)
                    {
                        updatePassport.UserId = userId.Value;
                    }

                    updatePassport.FrontScanFileName = passportFileName;

                    if (!documents.Any(x => x.DocumentType == Enums.DocumentType.selfie))
                    {
                        ServiceResponse saveUpdatePassportResult;
                        if (userId is null)
                        {
                            saveUpdatePassportResult = await SaveNonRegisteredIdentification(updatePassport, IdentificationType.Passport, new CardHolder
                            {
                                Id = request.CardHolderId,
                                CardSerialNumber = request.CardSerialNumber,
                                C3RegistrationId = request.C3RegistrationId,
                                PassportNumber = cardHolderDetails.PassportNumber
                            });
                        }
                        else
                        {
                            saveUpdatePassportResult = await SaveIdentification(updatePassport, IdentificationType.Passport);
                        }

                        if (!saveUpdatePassportResult.IsSuccessful)
                        {
                            return new ServiceResponse(saveUpdatePassportResult.ErrorMessage);
                        }
                    }
                    if (documents != null)
                    {
                        documents.Add(new IdentificationDocumentDto() { DocumentName = string.Concat(passportFileName, ".", IdentificationDocumentMimeType.jpg), DocumentType = Enums.DocumentType.passport, DocumentUrl = passportScanUrl });
                    }
                    else
                    {
                        documents = new List<IdentificationDocumentDto>()
                        {
                        new IdentificationDocumentDto() { DocumentName =string.Concat(passportFileName, ".", IdentificationDocumentMimeType.jpg), DocumentType = Enums.DocumentType.passport, DocumentUrl = passportScanUrl},
                        };
                    }
                }

                var saveDocumentsResult = await SaveIdentificationDocuments(userId, documents);
                if (!saveDocumentsResult.IsSuccessful)
                {
                    return new ServiceResponse(saveDocumentsResult.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Non Registered Upload Kyc failed");
                return new ServiceResponse("Non Registered Upload Kyc failed");
            }

            return new ServiceResponse();
        }

        /// <summary>
        /// Check for Existing files
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private async Task<string> GetUrlIfFileExists(string fileName)
        {
            string url = default;

            var uploadedFiles = await this._uploadedDocumentService.GetUploadedDocumentByName(fileName);

            if (uploadedFiles.Data.Any())
            {
                url = uploadedFiles.Data.First().Url;
            }

            return url;
        }
        /// <summary>
        /// Upload and Check Quality
        /// </summary>
        /// <param name="imageBase64"></param>
        /// <param name="fileName"></param>
        /// <param name="documentType"></param>
        /// <param name="shouldCheckQuality"></param>
        /// <returns></returns>
        private async Task<ServiceResponse> UploadDocumentToTempBlob(Guid? userId, string imageBase64, string fileName, BaseEnums.DocumentType documentType, bool shouldCheckQuality = false)
        {
            var documentSize = TypeUtility.GetBase64StringSize(imageBase64);

            if (documentSize > _maxDocumentSize)
            {
                return new ServiceResponse<string>(false, string.Format(ConstantParam.DocumentSizeLimitExceeded, documentSize, _maxDocumentSize));
            }

            //Delete the file if already exists
            await this._uploadedDocumentService.DeleteIfExists(fileName);


            Enums.DocumentType? document = null;
            switch (documentType)
            {
                case BaseEnums.DocumentType.EmiratesIdFrontScan:
                    document = Enums.DocumentType.emirates_id_front_scan;
                    break;
                case BaseEnums.DocumentType.EmiratesIdBackScan:
                    document = Enums.DocumentType.emirates_id_back_scan;
                    break;
                case BaseEnums.DocumentType.Selfie:
                    document = Enums.DocumentType.selfie;
                    break;
                case BaseEnums.DocumentType.Passport:
                    document = Enums.DocumentType.passport;
                    break;
                default:
                    break;
            }

            var uploadResult = await UploadIdentificationDocument(userId, imageBase64, fileName, document.Value);

            if (!uploadResult.IsSuccessful)
            {
                return new ServiceResponse<string>(false, uploadResult.ErrorMessage);
            }

            var uploadDocumentsResult = await this._uploadedDocumentService.CreateUploadedDocument(new UploadedDocument()
            {
                UserId = userId,
                Name = fileName,
                Type = documentType,
                Url = uploadResult.Data
            });

            if (!uploadDocumentsResult.IsSuccessful)
            {
                return new ServiceResponse<string>(false, uploadDocumentsResult.ErrorMessage);
            }

            return new ServiceResponse();
        }

        private async Task<ServiceResponse> ReadDataFromEmiratesIdImage(Guid? userId, string frontScanUrl, string backScanUrl)
        {
            string firstname = null;

            if (userId != null)
            {
                var userResult = await this._userService.GetUserById(userId.Value);
                if (!userResult.IsSuccessful)
                {
                    return new ServiceResponse(false, userResult.ErrorMessage);
                }

                firstname = userResult.Data.CardHolder.FirstName;
            }

            var extractDataResult = await ReadNonRegisteredIdentificationDocument(userId, frontScanUrl, backScanUrl, firstname, IdentificationType.EmiratesId);
            if (!extractDataResult.IsSuccessful)
            {
                return new ServiceResponse<string>(false, extractDataResult.ErrorMessage);
            }

            var emiratesId = _mapper.Map<EmiratesIdDto>(extractDataResult.Data);

            return new ServiceResponse<EmiratesIdDto>(emiratesId);
        }

        private async Task<ServiceResponse> ReadDataFromPassportImage(Guid? userId, string passportScanUrl)
        {
            string firstname = null;

            if (userId != null)
            {
                var userResult = await this._userService.GetUserById(userId.Value);
                if (!userResult.IsSuccessful)
                {
                    return new ServiceResponse(false, userResult.ErrorMessage);
                }

                firstname = userResult.Data.CardHolder.FirstName;
            }

            var extractDataResult = await ReadNonRegisteredIdentificationDocument(userId, passportScanUrl, string.Empty, firstname, IdentificationType.Passport);
            if (!extractDataResult.IsSuccessful)
            {
                return new ServiceResponse<string>(false, extractDataResult.ErrorMessage);
            }

            var passport = _mapper.Map<DataExtractionDto>(extractDataResult.Data);

            return new ServiceResponse<DataExtractionDto>(passport);
        }

        private async Task<ServiceResponse> SaveNonRegisteredIdentification(UpdateIdentificationRequestDto updateIdentificationRequest,
            IdentificationType identificationType,
            CardHolder cardHolder)
        {
            var identificationUpdateRecord = _mapper.Map<NonRegisteredIdentification>(updateIdentificationRequest);

            identificationUpdateRecord.FullName = updateIdentificationRequest.FullName;
            identificationUpdateRecord.Nationality = identificationUpdateRecord.NationalityCode = updateIdentificationRequest.NationalityCode;
            identificationUpdateRecord.Type = identificationType;
            identificationUpdateRecord.CardHolderId = cardHolder.Id;
            identificationUpdateRecord.CardSerialNumber = cardHolder.CardSerialNumber;
            identificationUpdateRecord.PassportNumber = cardHolder.PassportNumber;
            identificationUpdateRecord.C3RegistrationId = cardHolder.C3RegistrationId;


            var addIdentificationUpdateResult = await AddNonRegisteredIdentification(identificationUpdateRecord, cardHolder);
            if (!addIdentificationUpdateResult.IsSuccessful)
            {
                return new ServiceResponse<string>(false, addIdentificationUpdateResult.ErrorMessage);
            }
            return new ServiceResponse();
        }

        private async Task<ServiceResponse> SaveIdentification(UpdateIdentificationRequestDto updateIdentificationRequest, IdentificationType identificationType)
        {
            var identificationUpdateRecord = _mapper.Map<Identification>(updateIdentificationRequest);

            identificationUpdateRecord.FullName = updateIdentificationRequest.FullName;
            identificationUpdateRecord.Nationality = identificationUpdateRecord.NationalityCode = updateIdentificationRequest.NationalityCode;
            identificationUpdateRecord.Type = identificationType;

            var addIdentificationUpdateResult = await AddIdentification(identificationUpdateRecord, true);
            if (!addIdentificationUpdateResult.IsSuccessful)
            {
                return new ServiceResponse<string>(false, addIdentificationUpdateResult.ErrorMessage);
            }
            return new ServiceResponse();
        }

        private ServiceResponse ValidateEmiratesIdDetails(EmiratesIdDto emiratesDetails)
        {
            //Validate Emirates
            if (!emiratesDetails.IsValid)
            {
                return new ServiceResponse(false, ConstantParam.ReadingUserIdentificationDocumentInvalid);
            }
            if (emiratesDetails.IsExpired)
            {
                return new ServiceResponse(false, ConstantParam.EmiratesIdExpired);
            }

            return new ServiceResponse();
        }

        private ServiceResponse ValidatePassportDetails(DataExtractionDto emiratesDetails)
        {
            //Validate Emirates
            if (!emiratesDetails.IsValid)
            {
                return new ServiceResponse(false, ConstantParam.ReadingUserIdentificationDocumentInvalid);
            }
            if (emiratesDetails.IsExpired)
            {
                return new ServiceResponse(false, ConstantParam.PassportExpired);
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse<Tuple<List<VerificationUser>, int>>> SearchVerificationUsers(SearchUserParameters searchUserParameters)
        {
            var filters = IdentificationFilter.GetFilter(searchUserParameters, SearchType.Verification);
            var responseUser = await _unitOfWork.Identifications.SearchVerificationUser(filters, searchUserParameters.RegistrationType);
            return new ServiceResponse<Tuple<List<VerificationUser>, int>>(responseUser);
        }
        #endregion
    }
}
