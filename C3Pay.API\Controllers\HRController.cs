﻿using AutoMapper;
using C3Pay.API.Resources.HR;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.HR.Requests;
using Edenred.Common.Core.Models.Messages.Integration.Payroll.Requests;
using Edenred.Common.Core.Models.Messages.Integration.Payroll.Responses;
using Edenred.Common.Core.Services.Integration;
using Edenred.Common.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace C3Pay.API.Controllers
{
    [ApiExplorerSettings(IgnoreApi = false)]
    [Route("api/[controller]")]
    [ApiController]
    [InputValidationAttribute]
    [Authorize]
    [DeviceAuthorize]
    public class HRController : BaseController
    {
        private readonly IPayrollService _payrollService;
        private readonly IHRService _hrService;
        private readonly IMapper _mapper;
        private readonly IDistributedCache _cacheService;
        private readonly IFeatureManager _featureManager;
        private readonly string _corporateSubscriptionCachePrefix = "Corporate_{0}_{1}_Status";
        private readonly HRServiceSettings _hrServiceSettings;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpContextAccessor"></param>
        /// <param name="userService"></param>
        /// <param name="payrollService"></param>
        /// <param name="logger"></param>
        public HRController(
         IHttpContextAccessor httpContextAccessor,
         IPayrollService payrollService,
         IUserService userService,
         IHRService hrService,
         IMapper mapper,
         IDistributedCache cacheService,
         IOptions<HRServiceSettings> hrServiceSettings,
         ILogger<HRController> logger,
         IFeatureManager featureManager) : base(httpContextAccessor, userService, logger)
        {
            _payrollService = payrollService;
            _hrService = hrService;
            _mapper = mapper;
            _cacheService = cacheService;
            this._hrServiceSettings = hrServiceSettings.Value;
            _featureManager = featureManager;
        }

        /// <summary>
        /// subscriptions
        /// </summary>
        /// <returns></returns>
        [HttpGet("user/corporate/subscription/status")]

        public async Task<ActionResult<bool>> GetUserCorporateSubscriptionStatus()
        {
            await GetLoggedInUser();

            var testVariableEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.TestVariableFlag);

            if (testVariableEnabled)
            {
                var testVariable = 5;
            }

            var corporateId = _user.CardHolder.CorporateId;

            bool? subscriptionEnabled;

            var cacheName = string.Format(_corporateSubscriptionCachePrefix, corporateId, Enums.CorporateSubscriptionName.HR.ToString());

            subscriptionEnabled = await this._cacheService.GetRecordAsync<bool?>(cacheName);

            if (subscriptionEnabled is null)
            {
                var corporateSubscriptionStatusResult = await this._payrollService.GetCorporateSubscriptionStatus(new GetCorporateSubscriptionStatusRequestDto()
                {
                    CorporateId = corporateId,
                    SubscriptionName = Enums.CorporateSubscriptionName.HR
                });

                if (!corporateSubscriptionStatusResult.IsSuccessful)
                {
                    return this.BadRequest(corporateSubscriptionStatusResult.ErrorMessage);
                }

                subscriptionEnabled = corporateSubscriptionStatusResult.Data;

                await this._cacheService.SetRecordAsync(cacheName, subscriptionEnabled, TimeSpan.FromMinutes(this._hrServiceSettings.CacheInMinutes));
            }

            return Ok(subscriptionEnabled);
        }

        /// <summary>
        ///  All Payslips
        /// </summary>
        /// <returns></returns>
        [HttpGet("user/payslips/all")]

        public async Task<ActionResult<List<PayslipDto>>> GetUserPayslips()
        {
            await GetLoggedInUser();

            var corporateId = _user.CardHolder.CorporateId;

            var employeeId = _user.CardHolder.EmployeeId;

            var payslipsResult = await this._payrollService.GetEmployeePayslipsList(new Edenred.Common.Core.Models.Messages.Integration.HR.Requests.GetPayslipsRequestDto()
            {
                CorporateId = corporateId,
                EmployeeId = employeeId
            });

            if (!payslipsResult.IsSuccessful)
            {
                return this.BadRequest(payslipsResult.ErrorMessage);
            }

            var payslips = payslipsResult.Data;

            return Ok(payslips);
        }

        /// <summary>
        ///  Payslips
        /// </summary>
        /// <returns></returns>
        [HttpGet("user/payslips/{salaryMonth}/{salaryYear}/attachment")]

        public async Task<ActionResult<string>> GetUserPayslipAttachment(int salaryMonth, int salaryYear)
        {
            await GetLoggedInUser();

            var corporateId = _user.CardHolder.CorporateId;

            var employeeId = _user.CardHolder.EmployeeId;

            var payslipAttachmentResult = await this._payrollService.GetEmployeePayslipAttachmentURL(new Edenred.Common.Core.Models.Messages.Integration.HR.Requests.GetPayslipAttachmentRequestDto()
            {
                CorporateId = corporateId,
                EmployeeId = employeeId,
                SalaryMonth = salaryMonth,
                SalaryYear = salaryYear
            });

            if (!payslipAttachmentResult.IsSuccessful)
            {
                return this.BadRequest(payslipAttachmentResult.ErrorMessage);
            }

            var payslips = payslipAttachmentResult.Data;

            return Ok(payslips);
        }

        /// <summary>
        ///  Get Leave Balances
        /// </summary>
        /// <returns></returns>
        [HttpGet("user/leaves/balances")]

        public async Task<ActionResult<List<LeaveBalanceDto>>> GetUserLeavesBalances()
        {
            await GetLoggedInUser();

            var corporateId = _user.CardHolder.CorporateId;

            var employeeId = _user.CardHolder.EmployeeId;

            var balancesResult = await this._hrService.GetLeaveBalance(new GetLeaveBalanceRequestDto()
            {
                CorporateId = corporateId,
                EmployeeId = employeeId
            });

            if (!balancesResult.IsSuccessful)
            {
                return BadRequest(balancesResult.ErrorMessage);
            }

            var balances = balancesResult.Data;

            if (balances.Count != 0)
            {
                balances = balances.Where(b => b.LeaveTypeId < 3).ToList();
            }

            var mappedBalances = this._mapper.Map<List<LeaveBalanceDto>>(balances);

            return this.Ok(mappedBalances);
        }

        /// <summary>
        ///  Get All User Leaves
        /// </summary> 
        /// <returns></returns>
        [HttpGet("user/leaves")]

        public async Task<ActionResult<List<LeaveReadDto>>> GetUserLeaves(int? pageSize = null, int? pageNumber = null)
        {
            await GetLoggedInUser();

            var corporateId = _user.CardHolder.CorporateId;

            var employeeId = _user.CardHolder.EmployeeId;

            var leavesResult = await this._hrService.GetLeaves(new GetLeavesRequestDto()
            {
                CorporateId = corporateId,
                EmployeeId = employeeId,
                PageSize = pageSize,
                PageNumber = pageNumber
            });

            if (!leavesResult.IsSuccessful)
            {
                return BadRequest(leavesResult.ErrorMessage);
            }

            var leaves = leavesResult.Data;

            var mappedLeaves = this._mapper.Map<List<LeaveReadDto>>(leaves);

            return this.Ok(mappedLeaves);
        }

        /// <summary>
        /// Request new Leave
        /// Possible results: DepartmentDoesntExists, LeavePolicyDoesntExists, AlreadyRequestedForSameDays, LeaveApprovalWorkflowDoesntExists,
        /// NoManagerFoundForThisCorporate, ApprovalManagerDoesntExists, PendingLeaveRequestInSameDays, ErrorInConnectingHRService
        /// CannotApplyinProbationPeriod
        /// </summary> 
        /// <returns></returns>
        [HttpPost("user/leaves")]

        public async Task<ActionResult> RequestLeave([FromForm] LeaveDto leave)
        { 
            await GetLoggedInUser();

            var corporateId = _user.CardHolder.CorporateId;

            var employeeId = _user.CardHolder.EmployeeId;

            var request = new AddLeaveRequestDto()
            {
                CorporateId = corporateId,
                EmployeeId = employeeId,
                StartDate = leave.StartDate,
                EndDate = leave.EndDate,
                LeaveType = leave.TypeId,
                Note = string.IsNullOrEmpty(leave.Note) ? "" : leave.Note
            };

            if (leave.Attachment != null)
            { 
                request.Attachment = leave.Attachment; 
            }

            // Check for Overlapping of leaves  

            var overLappingResult = await this._hrService.GetOverLappingLeaves(request); 
            if (overLappingResult.IsSuccessful) 
            {
                if(Regex(overLappingResult.Data) != Regex(ConstantParam.IsValidLeaves))
                    return BadRequest(nameof(ConstantParam.PendingLeaveRequestInSameDays));
            }
            else
                return BadRequest(nameof(ConstantParam.ErrorInConnectingHRService));


            var leavesResult = await this._hrService.AddLeave(request);

            if (!leavesResult.IsSuccessful)
            {
                string error = string.Empty;

                error = HandleErrorMessages(leavesResult, error);

                return BadRequest(error);
            }

            return this.Ok();

        }

        /// <summary>
        ///  Get All User Leaves
        /// </summary> 
        /// <returns></returns>
        [HttpGet("user/probation-status")]

        public async Task<ActionResult<ProbationStatusDto>> GetProbationStatus()
        { 
            await GetLoggedInUser();

            var corporateId = _user.CardHolder.CorporateId;

            var employeeId = _user.CardHolder.EmployeeId;

            var leavesResult = await this._hrService.GetProbationStatus(new GetProbabtionStatusRequestDto()
            {
                CorporateId = corporateId,
                EmployeeId = employeeId, 
            });

            if (!leavesResult.IsSuccessful)
            {
                string error = string.Empty;

                error = HandleErrorMessages(leavesResult, error);

                return BadRequest(error);
            }

            var response = new ProbationStatusDto()
            {
                IsInProbation = false,
                DepartmentNotExists = false,
            };
            if (leavesResult.IsSuccessful)
            {
                if(Regex(leavesResult.Data) == Regex(ConstantParam.ProbationPeriodExist))
                {
                    response.IsInProbation = true;
                }
                else if (Regex(leavesResult.Data) == Regex(ConstantParam.ProbationPeriodNotExist))
                {
                    response.IsInProbation = false;
                }
                else if(Regex(leavesResult.Data) == Regex(ConstantParam.DepartmentDoesntExists))
                {
                    response.DepartmentNotExists = true;
                }
            }
           
            return this.Ok(response);
        }


        private static string HandleErrorMessages(ServiceResponse leavesResult, string error)
        {
            string errorMessage = Regex(leavesResult.ErrorMessage);

            if (Regex(ConstantParam.DepartmentDoesntExists) == errorMessage)
                error = nameof(ConstantParam.DepartmentDoesntExists);
            else if (errorMessage == Regex(ConstantParam.AlreadyRequestedForSameDays1) ||
                errorMessage == Regex(ConstantParam.AlreadyRequestedForSameDays2) ||
                errorMessage == Regex(ConstantParam.AlreadyRequestedForSameDays3) ||
                errorMessage == Regex(ConstantParam.AlreadyRequestedForSameDays4) ||
                errorMessage == Regex(ConstantParam.AlreadyRequestedForSameDays5) ||
                errorMessage == Regex(ConstantParam.AlreadyRequestedForSameDays6))
                error = nameof(ConstantParam.AlreadyRequestedForSameDays4);
            else if (errorMessage == Regex(ConstantParam.LeaveApprovalWorkflowDoesntExists))
                error = nameof(ConstantParam.LeaveApprovalWorkflowDoesntExists);
            else if (errorMessage == Regex(ConstantParam.NoManagerFoundForThisCorporate))
                error = nameof(ConstantParam.NoManagerFoundForThisCorporate);
            else if (errorMessage == Regex(ConstantParam.ApprovalManagerDoesntExists))
                error = nameof(ConstantParam.ApprovalManagerDoesntExists); 
            else
                error = String.Empty;
            error = System.Text.RegularExpressions.Regex.Replace(error, @"[\d-]", string.Empty);
            return error;
        }

        private static string Regex(string str)
        {
            return System.Text.RegularExpressions.Regex.Replace(str.ToLower(), "[^a-zA-Z0-9_.]+", "", RegexOptions.Compiled);
        }
    }
}
