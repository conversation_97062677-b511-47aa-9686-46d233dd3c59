﻿namespace C3Pay.Core
{
    public static class Errors
    {
        public static class Common
        {
            public static Error FailedWith500 => new Error("Common.FailedWith500", "Failed with 500");
            public static Error NoContent => new Error("Common.NoContent", "NoContent");
            public static Error InvalidRequest => new Error("Common.InvalidRequest", "InvalidRequest");
            public static Error UnAuthorized => new Error("Common.UnAuthorized", "UnAuthorized");
            public static Error UserNotExists => new Error("Common.UserNotExists", "UserNotExists");
            public static Error NotFound => new Error("Common.NotFound", "NotFound");
            public static Error UnableToDeleteCacheKey => new Error("Common.UnableToDeleteCacheKey", "UnableToDeleteCacheKey");
            public static Error ExternalServiceFailedWithException => new Error("Common.ExternalServiceFailedWithException", "External service failed with exception");
            public static Error ExternalServiceFailed => new Error("Common.ExternalServiceFailed", "External service failed");
        }
        public static class UIElement
        {
            public static Error NonActionableUIElement => new Error("UIElement.NonActionableUIElement", "NonActionableUIElement");
            public static Error InvalidUIElement => new Error("UIElement.InvalidUIElement", "InvalidUIElement");
        }
        public static class RAKService
        {
            public static class BankBranch
            {
                public static Error InvalidBankName => new Error("RAKService.BankBranch.InvalidBankName", "Invalid bank name");
                public static Error InvalidCountryCode => new Error("RAKService.BankBranch.InvalidCountryCode", "Invalid country code");
            }
            public static Error FailedToRetrieveBankDetails => new Error("RAKService.FailedToRetrieveBankDetails", "Failed to retrieve bank details from RAK Service");
        }
        public static class SecuritySMSAwareness
        {
        }

        public static class DeviceBinding
        {
            public static Error InvalidOTP => new Error("DeviceBinding.InvalidOTP", "InvalidOTP");
            public static Error BlockedTooManyFailedAttempts => new Error("DeviceBinding.BlockedTooManyFailedAttempts", "BlockedTooManyFailedAttempts");
            public static Error WrongCombination => new Error("DeviceBinding.WrongCombination", "WrongCombination");
            public static Error InvalidCard => new Error("DeviceBinding.InvalidCard", "InvalidCard");

        }

        public static class User
        {
            public static Error UserNotExists => new Error("User.UserNotExists", "UserNotExists");
        }

        public static class MoneyTransfer
        {
            public static Error GetFieldGroupsError => new Error("MoneyTransfer.GetFieldGroupsError", "GetFieldGroupsError");
            public static Error MinumumTransactionLimitExceededForWalletTransferType => new Error("MoneyTransfer.WalletTransactionLimitError", "TransactionLimitError####Sending amount should be {0} AED or more");
            public static Error MaximumTransactionLimitExceededForWalletTransferType => new Error("MoneyTransfer.WalletTransactionLimitError", "TransactionLimitError####Please enter a transfer amount lesser than {0} AED");
            public static Error MaximumTransactionLimitExceededInReceiverCurrency => new Error("MoneyTransfer.MaximumTransactionLimitExceededInReceiverCurrency",
                     "TransactionLimitError####Sending amount should be {0} {1} or less");
            public static Error MinimumTransactionLimitExceededInReceiverCurrency => new Error("MoneyTransfer.MinimumTransactionLimitExceededInReceiverCurrency",
                    "TransactionLimitError####Sending amount should be {0} {1} or more");
            public static Error TransferMethodNotExists => new Error("MoneyTransfer.TransferMethodNotExists", "TransferMethodNotExists");
            public static Error FieldsNotFound => new Error("MoneyTransfer.FieldsNotFound", "FieldsNotFound");
            public static Error ExceedBeneficiaryCountLimit => new Error("MoneyTransfer.ExceedBeneficiaryCountLimit", "ExceedBeneficiaryCountLimit");
            public static Error ExchangeHouseUser => new Error("MoneyTransfer.ExchangeHouseUser", "ExchangeHouseUser");
            public static Error ReasonNotExists => new Error("MoneyTransfer.ReasonNotExists", "ReasonNotExists");
            public static Error FieldFullNameNotFound => new Error("MoneyTransfer.FieldFullNameNotFound", "FieldFullNameNotFound");
            public static Error FirstNameNotExists => new Error("MoneyTransfer.FirstNameNotExists", "FirstNameNotExists");
            public static Error ExceedFirstNameLength => new Error("MoneyTransfer.ExceedFirstNameLength", "ExceedFirstNameLength");
            public static Error LastNameNotExists => new Error("MoneyTransfer.LastNameNotExists", "LastNameNotExists");
            public static Error ExceedLastNameLength => new Error("MoneyTransfer.ExceedLastNameLength", "ExceedLastNameLength");
            public static Error ExceedMiddleNameLength => new Error("MoneyTransfer.ExceedMiddleNameLength", "ExceedMiddleNameLength");
            public static Error AccountNumberNotExists => new Error("MoneyTransfer.AccountNumberNotExists", "AccountNumberNotExists");
            public static Error BeneficiaryAlreadyExists => new Error("MoneyTransfer.BeneficiaryAlreadyExists", "BeneficiaryAlreadyExists");
            public static Error BankCodeNotExists => new Error("MoneyTransfer.BankCodeNotExists", "BankCodeNotExists");
            public static Error BankNotExists => new Error("MoneyTransfer.BankNotExists", "BankNotExists");
            public static Error AddressLine1NotFound => new Error("MoneyTransfer.AddressLine1NotFound", "AddressLine1NotFound");
            public static Error CityNotFound => new Error("MoneyTransfer.CityNotFound", "CityNotFound");
            public static Error PostalCodeNotFound => new Error("MoneyTransfer.PostalCodeNotFound", "PostalCodeNotFound");
            public static Error PhoneNumberIsRequired => new Error("MoneyTransfer.PhoneNumberIsRequired", "PhoneNumberIsRequired");
            public static Error UserBlocked => new Error("MoneyTransfer.UserBlocked", "UserBlocked");
            public static Error GetDynamicScreenError => new Error("MoneyTransfer.GetDynamicScreenError", "GetDynamicScreenError");
            public static Error GetDynamicScreenErrorInvalidDeliveryMethod => new Error("MoneyTransfer.GetDynamicScreenError.InvalidDeliveryMethod", "InvalidDeliveryMethodId");
            public static Error BeneficiaryDetailsValidationError => new Error("MoneyTransfer.BeneficiaryDetailsValidationError", "BeneficiaryDetailsValidationError");
        }

        public static class C3PayPlus
        {
            public static Error Unavailable => new Error("C3_DEV_C3P_NOT_ENABLED", "C3_DEV_C3P_NOT_ENABLED");
            public static Error EmptyRequest => new Error("C3_DEV_C3P_EMPTY_REQUEST", "C3_DEV_C3P_EMPTY_REQUEST");
            public static Error NoPhoneNumberSent => new Error("C3_DEV_C3P_NO_PHONE_NUMBER_SENT", "C3_DEV_C3P_NO_PHONE_NUMBER_SENT");
            public static Error UserNotFound => new Error("C3_DEV_C3P_USER_NOT_FOUND", "C3_DEV_C3P_USER_NOT_FOUND");
            public static Error UserBelongsToExchangeHouse => new Error("C3_DEV_C3P_USER_BELONGS_TO_EXCHANGE_HOUSE", "C3_DEV_C3P_USER_BELONGS_TO_EXCHANGE_HOUSE");
            public static Error UserIsTooOldOrTooYoung => new Error("C3_DEV_C3P_USER_USER_TOO_OLD_OR_TOO_YOUNG", "C3_DEV_C3P_USER_USER_TOO_OLD_OR_TOO_YOUNG");
            public static Error UserNotVerified => new Error("C3_DEV_C3P_USER_NOT_VERIFIED", "C3_DEV_C3P_USER_NOT_VERIFIED");
            public static Error NoKycFound => new Error("C3_DEV_C3P_NO_KYC_FOUND", "C3_DEV_C3P_NO_KYC_FOUND");
            public static Error CantGetEsmoSubscriptionDetails => new Error("C3_DEV_C3P_CANT_GET_ESMO_SUBSCRIPTION_DETAILS", "C3_DEV_C3P_CANT_GET_ESMO_SUBSCRIPTION_DETAILS");
            public static Error CantGetCurrentSubscriptionDetails => new Error("C3_DEV_C3P_CANT_GET_CURRENT_SUBSCRIPTION_DETAILS", "C3_DEV_C3P_CANT_GET_CURRENT_SUBSCRIPTION_DETAILS");
            public static Error NoMembershipFound => new Error("C3_DEV_C3P_NO_MEMBERSHIP_FOUND", "C3_DEV_C3P_NO_MEMBERSHIP_FOUND");
            public static Error NoTypeIdSent => new Error("C3_DEV_C3P_NO_C3PAY_PLUS_TYPE_ID_SENT", "C3_DEV_C3P_NO_C3PAY_PLUS_TYPE_ID_SENT");
            public static Error MembershipSubscriptionMismatch => new Error("C3_DEV_C3P_MEMBERSHIP_SUBSCRIPTION_MISMATCH", "C3_DEV_C3P_MEMBERSHIP_SUBSCRIPTION_MISMATCH");
            public static Error MembershipDidNotEndYet => new Error("C3_DEV_C3P_MEMBERSHIP_DID_NOT_END_YET", "C3_DEV_C3P_MEMBERSHIP_DID_NOT_END_YET");
            public static Error CantFindTransaction => new Error("C3_DEV_C3P_CANT_FIND_TRANSACTION", "C3_DEV_C3P_CANT_FIND_TRANSACTION");
            public static Error OrionTransactionResultMismatch => new Error("C3_DEV_C3P_ORION_TRANSACTION_RESULT_MISMATCH", "C3_DEV_C3P_ORION_TRANSACTION_RESULT_MISMATCH");
            public static Error CantPerformCredit => new Error("C3_DEV_C3P_CANT_CREDIT", "C3_DEV_C3P_CANT_CREDIT");
            public static Error AtmWithdrawalFeeReversalException => new Error("C3_DEV_C3P_ATM_WITHDRAWAL_FEE_REVERSAL_EXCEPTION", "C3_DEV_C3P_ATM_WITHDRAWAL_FEE_REVERSAL_EXCEPTION");
            public static Error EsmoSubscriptionUpdateFailed => new Error("C3_DEV_C3P_ESMO_SUBSCRIPTION_UPDATE_FAILED", "C3_DEV_C3P_ESMO_SUBSCRIPTION_UPDATE_FAILED");
            public static Error CantSubscribe => new Error("C3_DEV_C3P_CANT_SUBSCRIBE", "C3_DEV_C3P_CANT_SUBSCRIBE");
            public static Error CantPerformDebit => new Error("C3_DEV_C3P_CANT_DEBIT", "C3_DEV_C3P_CANT_DEBIT");
            public static Error CantFindEnoughWinners => new Error("C3_DEV_C3P_CANT_FIND_ENOUGH_WINNERS", "C3_DEV_C3P_CANT_FIND_ENOUGH_WINNERS");
            public static Error CantGetBalance => new Error("C3_DEV_C3P_CANT_GET_BALANCE", "C3_DEV_C3P_CANT_GET_BALANCE");
            public static Error CantCheckIfUserGotSalary => new Error("C3_DEV_C3P_CANT_CHECK_IF_USER_GOT_SALARY", "C3_DEV_C3P_CANT_CHECK_IF_USER_GOT_SALARY");
            public static Error UserDidNotGetSalary => new Error("C3_DEV_C3P_USER_DID_NOT_GET_SALARY", "C3_DEV_C3P_USER_DID_NOT_GET_SALARY");
            public static Error UserAlreadyHasAnActiveOlderSubscription => new Error("C3_DEV_C3P_USER_ALREADY_HAS_AN_ACTIVE_OLDER_SUBSCRIPTION", "C3_DEV_C3P_USER_ALREADY_HAS_AN_ACTIVE_OLDER_SUBSCRIPTION");
            public static Error CantGetC3PayPlusTransactions => new Error("C3_DEV_C3P_INLINE_BILLING_CANT_GET_C3P_TRX", "C3_DEV_C3P_INLINE_BILLING_CANT_GET_C3P_TRX");
            public static Error CantGetSubscriptionsTransactions => new Error("C3_DEV_C3P_CANT_GET_SUBSCRIPTIONS_TRANSACTIONS", "C3_DEV_C3P_CANT_GET_SUBSCRIPTIONS_TRANSACTIONS");
            public static Error CantPerformDebitInline => new Error("C3_DEV_C3P_CANT_DEBIT_INLINE", "C3_DEV_C3P_CANT_DEBIT_INLINE");
            public static Error DoubleDebitInInlineBillingDetected => new Error("C3_DEV_C3P_DOUBLE_DEBIT_IN_INLINE_BILLING_DETECTED", "C3_DEV_C3P_DOUBLE_DEBIT_IN_INLINE_BILLING_DETECTED");
            public static Error DoubleCreditInBalanceEnquiryDetected => new Error("C3_DEV_C3P_DOUBLE_CREDIT_IN_BALANCE_ENQUIRYD_ETECTED", "C3_DEV_C3P_DOUBLE_CREDIT_IN_BALANCE_ENQUIRYD_ETECTED");
            public static Error DoubleCreditInSmsDetected => new Error("C3_DEV_C3P_DOUBLE_CREDIT_IN_SMS_DETECTED", "C3_DEV_C3P_DOUBLE_CREDIT_IN_SMS_DETECTED");
            public static Error CantPerformBalanceEnquiryRefund => new Error("C3_DEV_C3P_CANT_PERFORM_BALANCE_ENQUIRY_REFUND", "C3_DEV_C3P_CANT_PERFORM_BALANCE_ENQUIRY_REFUND");
            public static Error CantPerformSmsRefund => new Error("C3_DEV_C3P_CANT_PERFORM_SMS_REFUND", "C3_DEV_C3P_CANT_PERFORM_SMS_REFUND");
            public static Error LuckyDrawWinnerFilterNotValid => new Error("C3_DEV_C3P_LUCKY_DRAW_WINNER_FILTER_NOT_VALID", "C3_DEV_C3P_LUCKY_DRAW_WINNER_FILTER_NOT_VALID");
            public static Error NoTicketNumberSent => new Error("C3_DEV_C3P_NO_TICKET_NUMBER_SENT", "C3_DEV_C3P_NO_TICKET_NUMBER_SENT");
            public static Error TicketAlreadyUsed => new Error("C3_DEV_C3P_TICKET_ALREADY_USED", "C3_DEV_C3P_TICKET_ALREADY_USED");
            public static Error CantSendSubscriptionSms => new Error("C3_DEV_C3P_CANT_SEND_SUBSCRIPTION_SMS", "C3_DEV_C3P_CANT_SEND_SUBSCRIPTION_SMS");
            public static Error CantConfirmDebit => new Error("C3_DEV_C3P_CANT_CONFIRM_DEBIT", "C3_DEV_C3P_CANT_CONFIRM_DEBIT");
            public static Error BillingNotFound => new Error("C3_DEV_C3P_BILLING_NOT_FOUND", "C3_DEV_C3P_BILLING_NOT_FOUND");
            public static Error UserNotInExperiment => new Error("C3_DEV_C3P_USER_NOT_IN_EXPERIMENT", "C3_DEV_C3P_USER_NOT_IN_EXPERIMENT");
            public static Error C3PayAutomatedCallsUnavailable => new Error("C3_DEV_AUTOMATED_CALLS_NOT_ENABLED", "C3_DEV_AUTOMATED_CALLS_NOT_ENABLED");
            public static Error CashbackDisabled => new Error("C3_DEV_C3P_CASHBACK_DISABLED", "C3_DEV_C3P_CASHBACK_DISABLED");
            public static Error MoneyTransferRefundDisabled => new Error("C3_DEV_C3P_MONEY_TRANSFER_REFUND_DISABLED", "C3_DEV_C3P_MONEY_TRANSFER_REFUND_DISABLED");
            public static Error NoUserIdSent => new Error("C3_DEV_C3P_NO_USER_ID_SENT", "C3_DEV_C3P_NO_USER_ID_SENT");
            public static Error NoMoneyTransferReferenceNumberSent => new Error("C3_DEV_C3P_NO_MONEY_TRANSFER_REFERENCE_SENT", "C3_DEV_C3P_NO_MONEY_TRANSFER_REFERENCE_SENT");
            public static Error UserAlreadyClaimedFreeMoneyTransferBenefitForThisMonth => new Error("C3_DEV_C3P_ALREADY_CLAIMED_FREE_MONEY_TRANSFER_BENEFIT_FOR_THIS_MONTH", "C3_DEV_C3P_ALREADY_CLAIMED_FREE_MONEY_TRANSFER_BENEFIT_FOR_THIS_MONTH");
            public static Error TransactionNotFound => new Error("C3_DEV_C3P_TRANSACTION_NOT_FOUND", "C3_DEV_C3P_TRANSACTION_NOT_FOUND");
            public static Error TransactionTypeNotSupported => new Error("C3_DEV_C3P_TRANSACTION_TYPE_NOT_SUPPORTED", "C3_DEV_C3P_TRANSACTION_TYPE_NOT_SUPPORTED");
            public static Error TransactionNotSuccessful => new Error("C3_DEV_C3P_TRANSACTION_NOT_SUCCESSFUL", "C3_DEV_C3P_TRANSACTION_NOT_SUCCESSFUL");
            public static Error TransactionAlreadyFree => new Error("C3_DEV_C3P_TRANSACTION_ALREADY_FREE", "C3_DEV_C3P_TRANSACTION_ALREADY_FREE");
            public static Error ErrorPerformingMoneyTransferRefund => new Error("C3_DEV_C3P_ERROR_PERFORMING_MONEY_TRANSFER_REFUND", "C3_DEV_C3P_ERROR_PERFORMING_MONEY_TRANSFER_REFUND");
            public static Error MoneyTransferRefundNoSubscribersFound => new Error("C3_DEV_C3P_MONEY_TRANSFER_REFUND_NO_SUBSCRIBERS_FOUND", "C3_DEV_C3P_MONEY_TRANSFER_REFUND_NO_SUBSCRIBERS_FOUND");

            #region ATM Withdrawal Refunds.
            public static Error AtmWithdrawalRefunds_FeatureNotEnabled => new Error("C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_FEATURE_NOT_ENABLED", "C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_FEATURE_NOT_ENABLED");
            public static Error AtmWithdrawalRefunds_CardholderIdNotSent => new Error("C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_CARDHOLDER_ID_NOT_SENT", "C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_CARDHOLDER_ID_NOT_SENT");
            public static Error AtmWithdrawalRefunds_FeeNotSent => new Error("C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_FEE_NOT_SENT", "C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_FEE_NOT_SENT");
            public static Error AtmWithdrawalRefunds_CantParseFee => new Error("C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_CANT_PARSE_FEE", "C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_CANT_PARSE_FEE");
            public static Error AtmWithdrawalRefunds_UserNotFound => new Error("C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_USER_NOT_FOUND", "C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_USER_NOT_FOUND");
            public static Error AtmWithdrawalRefunds_MembershipUserNotFound => new Error("C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_MEMBERSHIP_USER_NOT_FOUND", "C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_MEMBERSHIP_USER_NOT_FOUND");
            public static Error AtmWithdrawalRefunds_MembershipIsNotActive => new Error("C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_MEMBERSHIP_IS_NOT_ACTIVE", "C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_MEMBERSHIP_IS_NOT_ACTIVE");
            public static Error AtmWithdrawalRefunds_UserAlreadyClaimedBenefit => new Error("C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_USER_ALREADY_CLAIMED_BENEFIT", "C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_USER_ALREADY_CLAIMED_BENEFIT");
            public static Error AtmWithdrawalRefunds_Exception => new Error("C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_EXCEPTION", "C3_DEV_C3P_ATM_WITHDRAWAL_REFUNDS_EXCEPTION");
            public static Error C3PayPlusCantPerformDomantCheck => new Error("C3_DEV_C3P_CANT_PERFORM_DORMANT_CHECK", "C3_DEV_C3P_CANT_PERFORM_DORMANT_CHECK");
            public static Error C3PayPlusNoBESubscriptionFoundInRenewal => new Error("C3_DEV_C3P_NO_BE_SUBSCRIPTION_FOUND_IN_RENEWAL", "C3_DEV_C3P_NO_BE_SUBSCRIPTION_FOUND_IN_RENEWAL");
            public static Error C3PayPlusNoCardIsDormant => new Error("C3_DEV_C3P_CARD_IS_DORMANT", "C3_DEV_C3P_CARD_IS_DORMANT");

            #endregion

            #region Life Insurance Nominees
            public static Error LifeInsuranceNomieeAlreadyExists => new Error("C3_DEV_C3P_INSURANCE_NOMINEE_ALREADY_EXISTS", "C3_DEV_C3P_INSURANCE_NOMINEE_ALREADY_EXISTS");
            public static Error LifeInsuranceNomieeNameNotFound => new Error("C3_DEV_C3P_INSURANCE_NOMINEE_NAME_NOT_FOUND", "C3_DEV_C3P_INSURANCE_NOMINEE_NAME_NOT_FOUND");
            public static Error LifeInsuranceNomieeRelationTypeInvalid => new Error("C3_DEV_C3P_INSURANCE_NOMINEE_RELATIONSHIP_TYPE_INVALID", "C3_DEV_C3P_INSURANCE_NOMINEE_RELATIONSHIP_TYPE_INVALID");
            public static Error LifeInsuranceNomieeIdInvalid => new Error("C3_DEV_C3P_INSURANCE_NOMINEE_ID_INVALID", "C3_DEV_C3P_INSURANCE_NOMINEE_ID_INVALID");
            public static Error LifeInsuranceNomieeNotFound => new Error("C3_DEV_C3P_INSURANCE_NOMINEE_NOT_FOUND", "C3_DEV_C3P_INSURANCE_NOMINEE_NOT_FOUND");
            public static Error LifeInsuranceNomieeUpdateModelInvalid => new Error("C3_DEV_C3P_INSURANCE_NOMINEE_UPDATE_MODEL_INVALID", "C3_DEV_C3P_INSURANCE_NOMINEE_UPDATE_MODEL_INVALID");
            public static Error LifeInsuranceNomieeRelationshipLookupNotAvailable => new Error("C3_DEV_C3P_INSURANCE_NOMINEE_RELATIONSHIP_TYPE_LOOKUP_NOT_AVAILABLE", "C3_DEV_C3P_INSURANCE_NOMINEE_RELATIONSHIP_TYPE_LOOKUP_NOT_AVAILABLE");
            public static Error LifeInsuranceSupportedCountryLookupNotAvailable => new Error("C3_DEV_C3P_INSURANCE_SUPPORTED_COUNTRY_LOOKUP_NOT_AVAILABLE", "C3_DEV_C3P_INSURANCE_SUPPORTED_COUNTRY_LOOKUP_NOT_AVAILABLE");
            public static Error LifeInsuranceNomineeChangesNotificationToNomineeError => new Error("C3_DEV_C3P_INSURANCE_NOMINEE_CHANGE_CANT_SEND_SMS_TO_NOMINEE", "C3_DEV_C3P_INSURANCE_NOMINEE_CHANGE_CANT_SEND_SMS_TO_NOMINEE");
            public static Error LifeInsuranceNomineeChangesNotificationToCardHolderError => new Error("C3_DEV_C3P_INSURANCE_NOMINEE_CHANGE_CANT_SEND_SMS_TO_CARDHOLDER", "C3_DEV_C3P_INSURANCE_NOMINEE_CHANGE_CANT_SEND_SMS_TO_CARDHOLDER");
            public static Error CantGenerateLifeInsuranceReport => new Error("C3_DEV_C3P_CANT_GENERATE_LIFE_INSURANCE_REPORT", "C3_DEV_C3P_CANT_GENERATE_LIFE_INSURANCE_REPORT");
            #endregion


            public static Error DashboardIconNotLoaded => new Error("C3_DEV_C3P_DASHBOARD_ICON_NOT_LOADED", "C3_DEV_C3P_DASHBOARD_ICON_NOT_LOADED");
            public static Error CantGetAtmTransactionDetails => new Error("C3_DEV_C3P_CANT_GET_ATM_TRANSACTION_DETAILS", "C3_DEV_C3P_CANT_GET_ATM_TRANSACTION_DETAILS");
            public static Error AlreadyHasActiveMembership => new Error("C3_DEV_C3P_ALREADY_HAS_ACTIVE_MEMBERSHIP", "C3_DEV_C3P_ALREADY_HAS_ACTIVE_MEMBERSHIP");
            public static Error NoDiscountAvailable => new Error("C3_DEV_C3P_NO_DISCOUNT_AVAILABLE", "C3_DEV_C3P_NO_DISCOUNT_AVAILABLE");
            public static Error MembershipNotAvailable => new Error("C3_DEV_C3P_MEMBERSHIP_NOT_AVAILABLE", "C3_DEV_C3P_MEMBERSHIP_NOT_AVAILABLE");
            public static Error UnknownError => new Error("C3_DEV_C3P_UNKNOWN_ERROR", "C3_DEV_C3P_UNKNOWN_ERROR");
        }

        public static class LoginVideos
        {
            public static Error LoginVideoFeatureNotEnabled => new Error("LOGIN_VIDEO_FEATURE_NOT_ENABLED", "LOGIN_VIDEO_FEATURE_NOT_ENABLED");
            public static Error EmptyRequest => new Error("LOGIN_VIDEO_EMPTY_REQUEST", "LOGIN_VIDEO_EMPTY_REQUEST");
            public static Error NoPhoneNumberSent => new Error("LOGIN_VIDEO_NO_PHONE_NUMBER_SENT", "LOGIN_VIDEO_NO_PHONE_NUMBER_SENT");
            public static Error UserNotFound => new Error("LOGIN_VIDEO_USER_NOT_FOUND", "LOGIN_VIDEO_USER_NOT_FOUND");
            public static Error NoVideosForLoginSlot => new Error("LOGIN_VIDEO_NO_VIDEO_FOR_THIS_SLOT", "LOGIN_VIDEO_NO_VIDEO_FOR_THIS_SLOT");

            public static Error LoginCountIsInvalid => new Error("LOGIN_VIDEO_LOGIN_COUNT_INVALID", "LOGIN_VIDEO_LOGIN_COUNT_INVALID");

            public static Error SpecifiedLoginVideoValidatorNotFound => new Error("LOGIN_VIDEO_SPECIFIED_VIDEO_VALIDATOR_NOT_FOUND", "LOGIN_VIDEO_SPECIFIED_VIDEO_VALIDATOR_NOT_FOUND");
        }

        public static class InAppAuth
        {
            public static Error PaymentAuthFeatureNotEnabled => new Error("INAPP_AUTH_FEATURE_NOT_ENABLED", "INAPP_AUTH_FEATURE_NOT_ENABLED");
            public static Error NoPhoneNumberSent => new Error("INAPP_AUTH_NO_PHONE_NUMBER_SENT", "INAPP_AUTH_NO_PHONE_NUMBER_SENT");
            public static Error UserNotFound => new Error("INAPP_AUTH_USER_NOT_FOUND", "INAPP_AUTH_USER_NOT_FOUND");
            public static Error PaymentAuthIdIsInvalid => new Error("INAPP_AUTH_PAYMENT_AUTHID_INVALID", "INAPP_AUTH_PAYMENT_AUTHID_INVALID");
        }

        public static class Infobip
        {
            public static Error CallbackDataInvalid => new Error("INFOBIP_CALLBACK_DATA_INVALID", "INFOBIP_CALLBACK_DATA_INVALID");
        }

        public static class DashboardErrors
        {
            public static Error Unavailable => new Error("C3_DEV_DSHBRD_POPUP_NOT_ENABLED", "C3_DEV_DSHBRD_POPUP_NOT_ENABLED");
            public static Error EmptyRequest => new Error("C3_DEV_DSHBRD_POPUP_EMPTY_REQUEST", "C3_DEV_DSHBRD_POPUP_EMPTY_REQUEST");
            public static Error NoUserSent => new Error("C3_DEV_DSHBRD_POPUP_NO_USER_SENT", "C3_DEV_DSHBRD_POPUP_NO_USER_SENT");
            public static Error CorporateNotFound => new Error("C3_DEV_DSHBRD_POPUP_CORPORATE_NOT_FOUND", "C3_DEV_DSHBRD_POPUP_CORPORATE_NOT_FOUND");
            public static Error LastSeenPopupDateIntervalFailed => new Error("C3_DEV_DSHBRD_POPUP_LAST_SEEN_DATE_INTERVAL_FAILED", "C3_DEV_DSHBRD_POPUP_LAST_SEEN_DATE_INTERVAL_FAILED");
        }



        public static class C3PayPlusRenewalsErrors
        {
            public static Error FeatureDisabled => new(
                code: "C3_DEV_C3P_RNWLS_FEATURE_DISABLED",
                message: "This feature is currently disabled."
                );

            public static Error BalanceEnquirySubscriptionNotFound => new(
                code: "C3_DEV_C3P_RNWL_BE_SUBSCRIPTION_NOT_FOUND",
                message: "Failed to locate the original Balance Enquiry subscription created by C3Pay+ during the subscription process."
                );

            public static Error CantGetSecuritySmsSubscriptionCode => new(
                code: "C3_DEV_C3P_RNWLS_CANT_GET_SECURITY_SMS_SUBSCRIPTION_CODE",
                message: "Unable to retrieve the security SMS subscription code."
                );

            public static Error CantGetSecuritySmsSubscriptionFee => new(
                code: "C3_DEV_C3P_RNWLS_CANT_GET_SECURITY_SMS_SUBSCRIPTION_FEE",
                message: "Failed to retrieve the security SMS subscription fee."
                );

            public static Error CantSubscribeBackToSecuritySms => new(
                code: "C3_DEV_C3P_RNWLS_CANT_SUBSCRIBE_BACK_TO_SECURITY_SMS",
                message: "Unable to resubscribe to the security SMS service."
                );

            public static Error CantGetSalaryAlertSubscriptionCode => new(
                code: "C3_DEV_C3P_RNWLS_CANT_GET_SALARY_ALERT_SUBSCRIPTION_CODE",
                message: "Unable to retrieve the salary alert subscription code."
                );

            public static Error CantGetSalaryAlertSubscriptionFees => new(
                code: "C3_DEV_C3P_RNWLS_CANT_GET_SALARY_ALERT_SUBSCRIPTION_FEES",
                message: "Unable to retrieve the salary alerts subscription fees."
                );

            public static Error CantSubscribeBackToSalaryAlerts => new(
                code: "C3_DEV_C3P_RNWLS_CANT_SUBSCRIBE_BACK_TO_SALARY_ALERTS",
                message: "Unable to resubscribe to salary alerts."
                );

            public static Error CantDebitUser => new(
                code: "C3_DEV_C3P_RNWLS_CANT_DEBIT_USER",
                message: "Unable to debit the user's account."
                );

            public static Error CantFindBillingRecordInC3PayDB => new(
                code: "C3_DEV_C3P_RNWLS_CANT_FIND_BILLING_RECORD",
                message: "Unable to find the billing record in the C3Pay database."
            );

        }


        public static class UserAlertPreferencesErrors
        {
            public static Error EmptyRequest => new Error("C3_DEV_ALERTPREF_EMPTY_REQUEST", "C3_DEV_ALERTPREF_EMPTY_REQUEST");

            public static Error NoPhoneNumberSent => new(
                code: "C3_DEV_ALERTPREF_NO_PHONE_NUMBER_SENT",
                message: "C3_DEV_ALERTPREF_NO_PHONE_NUMBER_SENT"
                );

            public static Error Unavailable => new Error("C3_DEV_ALERTPREF_NOT_ENABLED", "C3_DEV_ALERTPREF_NOT_ENABLED");

            public static Error UserNotFound => new Error("C3_DEV_ALERTPREF_USER_NOT_FOUND", "C3_DEV_ALERTPREF_USER_NOT_FOUND");

            public static Error InvalidEmailFormat => new Error("C3_DEV_ALERTPREF_INVALID_EMAIL_FORMAT", "C3_DEV_ALERTPREF_INVALID_EMAIL_FORMAT");

            public static Error CantGetEsmoSubscriptionDetails => new Error("C3_DEV_ALERTPREF_CANT_GET_ESMO_SUBSCRIPTION_DETAILS", "C3_DEV_ALERTPREF_CANT_GET_ESMO_SUBSCRIPTION_DETAILS");

            public static Error UserSubscribedToSecuritySms => new Error("C3_DEV_ALERTPREF_USER_SUBSCRIBED_TO_SECURITY_SMS", "C3_DEV_ALERTPREF_USER_SUBSCRIBED_TO_SECURITY_SMS");
            public static Error UserSubscribedToC3PayPlus => new Error("C3_DEV_ALERTPREF_USER_SUBSCRIBED_TO_C3PAY_PLUS", "C3_DEV_ALERTPREF_USER_SUBSCRIBED_TO_C3PAY_PLUS");
            public static Error InvalidAlertChannelFound => new Error("C3_DEV_ALERTPREF_INVALID_ALERT_CHANNEL_FOUND", "C3_DEV_ALERTPREF_INVALID_ALERT_CHANNEL_FOUND");

            public static Error NoEmailSent => new(
               code: "C3_DEV_ALERTPREF_NO_EMAIL_SENT",
               message: "C3_DEV_ALERTPREF_NO_EMAIL_SENT"
               );

            public static Error CantUpdateEmail => new(
            code: "C3_DEV_ALERTPREF_CANT_UPDATE_EMAIL",
            message: "C3_DEV_ALERTPREF_CANT_UPDATE_EMAIL"
            );

        }

        public static class MfaErrors
        {
            public static Error Invalid2fa => new Error("2fa_failed", "2fa_failed");
        }

        public static class IdempotencyErrors
        {
            public static Error AlreadyProcessed => new Error("RequestAlreadyProcessed", "RequestAlreadyProcessed");
            public static Error RequestInProgress => new Error("RequestInProgress", "Request is already in progress");
        }

        #region Benefits

        public static class BenefitShellErrors
        {
            public static Error UserNotEntitled => new("USER_NOT_ENTITLED_TO_THE_BENEFIT", "USER_NOT_ENTITLED_TO_THE_BENEFIT");
            public static Error Unavailable => new Error("C3_DEV_BENEFITSHELL_NOT_ENABLED", "C3_DEV_BENEFITSHEL_NOT_ENABLED");
        }


        #endregion


        #region VPN
        public static class VpnMembershipErrors
        {
            public static Error EmptyRequest => new("C3_DEV_VPN_EMPTY_REQUEST", "C3_DEV_VPN_EMPTY_REQUEST");
            public static Error NoPhoneNumberSent => new("C3_DEV_VPN_NO_PHONE_NUMBER_SENT", "C3_DEV_VPN_NO_PHONE_NUMBER_SENT");
            public static Error Unavailable => new Error("C3_DEV_VPN_NOT_ENABLED", "C3_DEV_VPN_NOT_ENABLED");
            public static Error UserNotFound => new Error("C3_DEV_VPN_USER_NOT_FOUND", "C3_DEV_VPN_USER_NOT_FOUND");
            public static Error CardHolderIdNotFoundForBenefit => new Error("C3_DEV_VPN_CARDHOLDERID_NOT_FOUND", "C3_DEV_VPN_CARDHOLDERID_NOT_FOUND");
            public static Error UnavailableForUser => new Error("C3_DEV_VPN_UNAVAILABLE_FOR_USER", "C3_DEV_VPN_UNAVAILABLE_FOR_USER");
            public static Error MembershipNotActive => new Error("C3_DEV_VPN_MEMBERSHIP_NOT_ACTIVE", "C3_DEV_VPN_MEMBERSHIP_NOT_ACTIVE");
            public static Error MembershipNotAvailable => new Error("C3_DEV_VPN_MEMBERSHIP_NOT_AVAILABLE", "C3_DEV_VPN_MEMBERSHIP_NOT_AVAILABLE");
            public static Error MembershipStillActive => new Error("C3_DEV_VPN_MEMBERSHIP_STILL_ACTIVE", "C3_DEV_VPN_MEMBERSHIP_STILL_ACTIVE");
            public static Error CantPerformDomantCheck => new Error("C3_DEV_VPN_CANT_PERFORM_DORMANT_CHECK", "C3_DEV_VPN_CANT_PERFORM_DORMANT_CHECK");
            public static Error CardIsDormant => new Error("C3_DEV_VPN_CARD_IS_DORMANT", "C3_DEV_VPN_CARD_IS_DORMANT");
            public static Error CantGetBalance => new Error("C3_DEV_VPN_CANT_GET_BALANCE", "C3_DEV_VPN_CANT_GET_BALANCE");
            public static Error CantCheckIfUserGotSalary => new Error("C3_DEV_VPN_CANT_CHECK_IF_USER_GOT_SALARY", "C3_DEV_VPN_CANT_CHECK_IF_USER_GOT_SALARY");
            public static Error UserDidNotGetSalary => new Error("C3_DEV_VPN_USER_DID_NOT_GET_SALARY", "C3_DEV_VPN_USER_DID_NOT_GET_SALARY");
            public static Error NoAvailableVpnCodes => new Error("C3_DEV_VPN_NO_AVAILABLE_CODES", "C3_DEV_VPN_NO_AVAILABLE_CODES");
            public static Error CantPerformDebit => new Error("C3_DEV_VPN_CANT_PERFORM_DEBIT", "C3_DEV_VPN_CANT_PERFORM_DEBIT");
            public static Error CantCreateSubscription => new Error("C3_DEV_VPN_CANT_CREATE_SUBSCRIPTION", "C3_DEV_VPN_CANT_CREATE_SUBSCRIPTION");
            public static Error CantAssignVpnCode => new Error("C3_DEV_VPN_CANT_ASSIGN_CODE", "C3_DEV_VPN_CANT_ASSIGN_CODE");
            public static Error FeatureDisabled => new Error("C3_DEV_VPN_RENEWALS_DISABLED", "VPN renewals feature is disabled");
            public static Error RenewalJobFailed => new Error("C3_DEV_VPN_RENEWAL_JOB_FAILED", "VPN renewal job failed");
            public static Error NoAvailableCodesForRenewal => new Error("C3_DEV_VPN_NO_AVAILABLE_CODES_RENEWAL", "No available VPN codes for renewal");
            public static Error CodeAssignmentFailed => new Error("C3_DEV_VPN_CODE_ASSIGNMENT_FAILED", "VPN code assignment failed during renewal");
        }
        #endregion




    }

    public static class C3PayPlusErrors
    {
        public static Error RenewalsFeatureDisabled => new(
            code: "C3_DEV_C3P_",
            message: "This feature is currently disabled."
            );

        public static Error BalanceEnquirySubscriptionNotFound => new(
            code: "C3_DEV_C3P_RNWL_BE_SUBSCRIPTION_NOT_FOUND",
            message: "Failed to locate the original Balance Enquiry subscription created by C3Pay+ during the subscription process."
            );

        public static Error CantGetSecuritySmsSubscriptionCode => new(
            code: "C3_DEV_C3P_RNWLS_CANT_GET_SECURITY_SMS_SUBSCRIPTION_CODE",
            message: "Unable to retrieve the security SMS subscription code."
            );

        public static Error CantGetSecuritySmsSubscriptionFee => new(
            code: "C3_DEV_C3P_RNWLS_CANT_GET_SECURITY_SMS_SUBSCRIPTION_FEE",
            message: "Failed to retrieve the security SMS subscription fee."
            );

        public static Error CantSubscribeBackToSecuritySms => new(
            code: "C3_DEV_C3P_RNWLS_CANT_SUBSCRIBE_BACK_TO_SECURITY_SMS",
            message: "Unable to resubscribe to the security SMS service."
            );

        public static Error CantGetSalaryAlertSubscriptionCode => new(
            code: "C3_DEV_C3P_RNWLS_CANT_GET_SALARY_ALERT_SUBSCRIPTION_CODE",
            message: "Unable to retrieve the salary alert subscription code."
            );

        public static Error CantGetSalaryAlertSubscriptionFees => new(
            code: "C3_DEV_C3P_RNWLS_CANT_GET_SALARY_ALERT_SUBSCRIPTION_FEES",
            message: "Unable to retrieve the salary alerts subscription fees."
            );

        public static Error CantSubscribeBackToSalaryAlerts => new(
            code: "C3_DEV_C3P_RNWLS_CANT_SUBSCRIBE_BACK_TO_SALARY_ALERTS",
            message: "Unable to resubscribe to salary alerts."
            );

        public static Error CannotSubscribeBackToNone => new(
            code: "C3P_RNWLS_CANNOT_REVERT_TO_PREVIOUS_SUB_NONE",
            message: "Cannot revert to previous subscription because it was set to 'None'."
            );

        public static Error CantDebitUser => new(
            code: "C3_DEV_C3P_RNWLS_CANT_DEBIT_USER",
            message: "Unable to debit the user's account."
            );

        public static Error GeneralException => new(
          code: "C3_DEV_C3P_RNWLS_CANT_FIND_BILLING_RECORD",
          message: "Unable to find the billing record in the C3Pay database."
      );

    }
}
