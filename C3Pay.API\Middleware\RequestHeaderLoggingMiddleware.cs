﻿using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Linq;
using C3Pay.API.Models;
using System.Diagnostics;

namespace C3Pay.API.Middleware
{
    public class RequestHeaderLoggingMiddleware : IMiddleware
    {
        public List<RequestHeader> RequestHeaders { get; set; } = new List<RequestHeader>()
        {
            new RequestHeader()
            {
                Code = "x-source-client",
                Name = "Client name"
            },
            new RequestHeader()
            {
                Code = "x-lang-code",
                Name = "Client language"
            },
            new RequestHeader()
            {
                Code = "x-source-client-version",
                Name = "Client version"
            },
            new RequestHeader()
            {
                Code = "x-source-client-os",
                Name = "Client os"
            },
            new RequestHeader()
            {
                Code = "x-source-client-os-version",
                Name = "Client os version"
            }
        };

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            context.Request.EnableBuffering();

            var activity = Activity.Current;

            if (activity != null)
            {
                foreach (var header in RequestHeaders)
                {
                    var headerValue = context.Request.Headers[header.Code];
                    if (headerValue.Any())
                    {
                        activity.SetTag($"http.request.header.{header.Code}", string.Join(Environment.NewLine, headerValue));
                    }
                }
            }

            await next(context);
        }
    }
}
