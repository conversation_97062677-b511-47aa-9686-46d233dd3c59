﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace C3Pay.Data.Migrations
{
    /// <inheritdoc />
    public partial class Modified_RenewalsLog_Tbl : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "JobDurationInSeconds",
                table: "C3PayPlusMembershipRenewalsDailyLogs");

            migrationBuilder.RenameColumn(
                name: "UnsubscribedDueToDormantCardCheckFailure",
                table: "C3PayPlusMembershipRenewalsDailyLogs",
                newName: "SkippedDueToDormantCardCheckFailure");

            migrationBuilder.AddColumn<double>(
                name: "JobDurationInMinutes",
                table: "C3PayPlusMembershipRenewalsDailyLogs",
                type: "float",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "JobDurationInMinutes",
                table: "C3PayPlusMembershipRenewalsDailyLogs");

            migrationBuilder.RenameColumn(
                name: "SkippedDueToDormantCardCheckFailure",
                table: "C3PayPlusMembershipRenewalsDailyLogs",
                newName: "UnsubscribedDueToDormantCardCheckFailure");

            migrationBuilder.AddColumn<int>(
                name: "JobDurationInSeconds",
                table: "C3PayPlusMembershipRenewalsDailyLogs",
                type: "int",
                nullable: true);
        }
    }
}
