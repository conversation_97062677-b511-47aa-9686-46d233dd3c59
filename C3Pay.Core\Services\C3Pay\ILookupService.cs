﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay;
using C3Pay.Core.Models.C3Pay.Lookup;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using C3Pay.Core.Models.C3Pay.UnEmpInsurance;
using C3Pay.Core.Models.DTOs.Lookup;
using C3Pay.Core.Models.DTOs.SignUp;
using C3Pay.Core.Models.Messages.MoneyTransfer;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Services
{
    public interface ILookupService
    {
        Task<ServiceResponse<IEnumerable<SecurityQuestion>>> GetAllSecurityQuestions();
        Task<ServiceResponse<IEnumerable<SecurityQuestion>>> GetSecurityQuestionsByIdList(List<int> securityQuestionIdList);
        Task<ServiceResponse<Country>> GetCountryByName(string name);
        Task<ServiceResponse<Country>> GetCountryByNameReadOnly(string name);
        Task<ServiceResponse<Country>> GetCountryByCode(string code);
        Task<ServiceResponse<IEnumerable<Country>>> GetAllCountriesReadOnly();
        Task<ServiceResponse<IEnumerable<Country>>> GetMoneyTransferCountries();
        Task<ServiceResponse<IEnumerable<MoneyTransferBank>>> GetMoneyTransferBanksByCountryCode(string countryCode);
        Task<ServiceResponse<IEnumerable<MoneyTransferBranch>>> GetMoneyTransferBranchesByBankId(int bankId);
        Task<ServiceResponse<IEnumerable<Country>>> GetMobileRechargeCountries();
        Task<ServiceResponse<IList<MoneyTransferReason>>> GetMoneyTransferReasons(string language);
        Task<ServiceResponse<IEnumerable<Country>>> GetPopularCountriesReadOnly();
        Task<ServiceResponse<IEnumerable<Subscription>>> GetSubscriptions(string corporateId, string language);
        Task<ServiceResponse<IEnumerable<Subscription>>> GetSubscriptionsReadOnly();
        Task<ServiceResponse<IList<UserBlockReason>>> GetBlockReasons();
        Task<ServiceResponse<IList<UserRegistrationRejectionReason>>> GetUserRegistrationRejectionReasons();
        Task<ServiceResponse<IEnumerable<Country>>> GetBillPaymentCountries(bool isTestUser = false);
        Task<ServiceResponse<IList<BillPaymentCategory>>> GetBillPaymentCategories();
        Task<ServiceResponse<IList<BillPaymentSubCategory>>> GetBillPaymentSubCategories(int categoryId);
        Task<ServiceResponse<IEnumerable<BillPaymentFee>>> GetBillPaymentFees();
        Task<ServiceResponse<IEnumerable<BillPaymentCategoryFee>>> GetBillPaymentCategoryAndCountryFees(int categoryId, string countryCode);
        Task<ServiceResponse<IEnumerable<BillPaymentProviderFee>>> GetBillPaymentProviderFees();
        Task<ServiceResponse<IEnumerable<Language>>> GetSupportedLanguages(string product = "");
        Task<ServiceResponse<List<string>>> GetEligibilityCriteria(string languageCode, string type);
        Task<ServiceResponse<IList<MoneyTransferReason>>> GetPaymentPurposes();
        Task<ServiceResponse<IList<MoneyTransferCorridor>>> GetEHCorridors(string clientName);
        Task<ServiceResponse<Country>> GetCountryByCurrency(string currency);
        ServiceResponse<List<EHRequiredField>> GetEHRequiredFields(string countryCode);
        Task<ServiceResponse<IList<Phone>>> GetAllStorePhones();
        Task<ServiceResponse<IList<City>>> GetStoreEnabledCities();
        Task<ServiceResponse<IList<City>>> GetCities(int stateId);
        Task<ServiceResponse<IList<State>>> GetStates(string countryCode);
        Task<ServiceResponse<bool>> CheckIsEligibleForBNPL(string cardHolderId);
        Task<ServiceResponse<IList<DashboardSection>>> GetSections();
        Task<ServiceResponse<IList<DashboardElement>>> GetQuickActions(string languageCode, string nationality, bool belongsToExchangeHouse);
        Task<ServiceResponse<IList<MultimediaResource>>> GetMultimediaResources(string cardholderId = null, string nationality = null, string identifier = null, MultimediaType? type = null, string languageCode = null, int? feature = null);
        Task<ServiceResponse<IList<DashboardQuickActionElementTag>>> GetQuickActionTags(string languageCode);
        Task<ServiceResponse<IEnumerable<UnemploymentInsurancePaymentOption>>> GetUnemploymentInsurancePaymentOptions(string partnerCode);
        Task<ServiceResponse<IEnumerable<UnEmpInsurancePaymentOption>>> GetUserUnEmpInsurancePaymentOptions(User user, UnEmpInsurancePartnerType partnerCode);
        Task<ServiceResponse<IEnumerable<UnemploymentInsurancePaymentOption>>> GetAllUnemploymentInsurancePaymentOptions();
        Task<ServiceResponse<IEnumerable<MobileRechargeProvider>>> GetMobileRechargeProviders(string countryCode);
        Task<ServiceResponse<decimal?>> GetTransferFees(string countryCode, TransferMethod transferMethod, decimal sendAmount, Guid userId);
        Task<ServiceResponse<List<MobileRechargeProduct>>> GetMobileRechargeProducts(List<string> providerCodes, int skipValue, int pageRecords);
        Task<ServiceResponse<List<TextContent>>> GetDeviceAdditionTextMessage(string type);
        Task<ServiceResponse<List<MoneyTransferBeneficiary>>> GetLinkedBeneficiaries(Guid? linkedUserId, MoneyTransferType? transferType);
        Task<ServiceResponse<IList<Feature>>> GetFeatures();
        Task<ServiceResponse<IList<Experiment>>> GetActiveExperiments();
        Task<ServiceResponse<bool>> IsCorporateC3PayPlusEnabled(string corporateId);

        Task<ServiceResponse<IEnumerable<ProvinceDto>>> GetProvincesByCountry(string countryCode);
        Task<bool> IsFeatureEnabled(string featureName, string targetingContextValue = "");
        Task<bool> IsSmsPlusEnabled(string corporateId);


        #region Sign-up
        Task<ServiceResponse<IEnumerable<SignUpCountryDto>>> GetSignUpCountries();
        #endregion

        #region Benefit Allowed Users
        Task<ServiceResponse<HashSet<string>>> GetBenefitAllowedCardholderIds(int benefitId);
        #endregion
    }
}
