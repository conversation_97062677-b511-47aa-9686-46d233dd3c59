﻿using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using C3Pay.Core.ResourceFile;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;
using System.Linq;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models
{
    public class MoneyTransferTransaction : BaseModel
    {
        public Guid Id { get; set; }

        public Guid UserId { get; set; } // User who has placed the transfer

        public Guid MoneyTransferBeneficiaryId { get; set; } // Beneficiary ID

        public string ReferenceNumber { get; set; } // PPS Transaction ref number TypeUtility.GetReferenceNumber(TransactionPrefix.EMT.ToString(), 12) (always 12)

        public int? MoneyTransferReasonId { get; set; } // mapping from benef.

        public TransactionType TransferType { get; set; } // enum (rak or index)

        /// <summary>
        /// Always AED.
        /// </summary>
        public string SendCurrency { get; set; }

        public string ReceiveCurrency { get; set; } // Benf country currency.

        /// <summary>
        /// Always AED.
        /// </summary>
        public string ChargesCurrency { get; set; }

        /// <summary>
        /// Amount to be transfered in AED.
        /// </summary>
        public decimal SendAmount { get; set; }

        /// <summary>
        /// Amount to be transfered after being converted to 'ReceiveCurrency'.
        /// </summary>
        public decimal ReceiveAmount { get; set; }

        public decimal? ChargesAmount { get; set; } // This is returned from Index (for EH money transfer)

        public decimal? WaivedCharge { get; set; } // Charges we removed (first time, loyality, promotion etc) WaivedCharge: is always 100% of TotalCharges if we have a waive

        public decimal? TotalCharges { get; set; } // Charges (from index) + VAT (0.05 * charges) - WaivedCharge

        public decimal ChargeVat { get; set; } // 0.05 of TotalCharges

        public decimal? ConversionRate { get; set; } // 1 / AED TO INR

        public WaiveType WaiveType { get; set; } //FirstTransaction excluding failed transactions

        public string ReferralCode { get; set; } // can only be placed for the first transaction (from API)

        public bool UsedForReferral { get; set; }

        public int? TriesCount { get; set; } // No. of times sent to PPS

        public string Remarks { get; set; } // logs for failed attempts or any notes

        public string CashPickupPin { get; set; }

        public string CashPickUpPoint { get; set; }
        public DateTime? ReversalDate { get; set; }

        public Status Status { get; set; } // pending, failed, SUCCESSFUL 

        public bool IsSuspiciousInformationSent { get; set; }
        public DateTime? SuspiciousInformationSentDateTime { get; set; }

        public string WUMsgId { get; set; }
        public bool IsRewardAssigned { get; set; } = false;
        public bool IsRewardAssignmentFailed { get; set; } = false;
        public int RewardAssignmentRetryCount { get; set; } = 0;

        public int StatusCheckRetryCount { get; set; } = 0;
        /// <summary>
        /// Gets or sets the place of birth for the transaction. (For Tracking Purpose)
        /// </summary>
        public string TransactionPlaceOfBirth { get; set; }

        /// <summary>
        /// To capture the FX rate displayed to the user at the time of transaction.
        /// </summary>
        public string DisplayedFxRate { get; set; }

        #region Not Mapped

        public int ExternalUserId { get; set; } // ? ignore this fields
        public string ExternalBeneficiaryId { get; set; } //? ignore this fields
        public Status InitialStatus { get; set; }

        public string Rate { get; set; }

        public string SMVValidationMessageId { get; set; }
        public string SMVValidationRemarks { get; set; }
        public string TempHostTxnId { get; set; }

        #endregion Not Mapped

        #region Navigation Properties

        public User User { get; set; }

        public MoneyTransferReason MoneyTransferReason { get; set; }

        public MoneyTransferExternalTransaction ExternalTransaction { get; set; }

        public MoneyTransferBeneficiary MoneyTransferBeneficiary { get; set; }

        public Transaction Transaction { get; set; }

        public List<MoneyTransferStatusStep> MoneyTransferStatusSteps { get; set; }

        #endregion

        [NotMapped]
        public string StatusMessage { get; private set; }

        public void SetTransactionStatusMessage(string languageCode)
        {
            var statusInfo = GetStatusInfo(Status, Remarks?.ToLower() ?? string.Empty, languageCode);
            StatusMessage = statusInfo;
        }

        private string GetStatusInfo(Status status, string remarks, string languageCode)
        {
            var statusKey = DetermineStatusKey(status, remarks);
            string statusMessage = string.Empty;
            if (!string.IsNullOrEmpty(statusKey))
            {
                string currency = string.Empty;
                string max_amount = string.Empty;
                var match = System.Text.RegularExpressions.Regex.Match(remarks, @"(\d+)-transaction amount must not exceed (\w+) (\d+)");
                if (match.Success)
                {
                    currency = match.Groups[2]?.Value?.ToUpper() + " ";
                    max_amount = match.Groups[3]?.Value ?? string.Empty;
                }

                statusMessage = GetLocalizedString($"mt_statusdescription_for_{statusKey}", languageCode);
                if (statusMessage.Contains("<currency>") && statusMessage.Contains("<max_amount>"))
                    statusMessage = statusMessage.Replace("<currency>", currency).Replace("<max_amount>", max_amount);
            }
            return statusMessage;
        }

        private string DetermineStatusKey(Status status, string remarks)
        {
            if (status == Status.SUCCESSFUL) return "success";
            if (status == Status.PENDING)
            {
                if (string.IsNullOrEmpty(remarks) || remarks == "waiting for correspondent bank authorization")
                    return "pendinggeneric";
                if (remarks == "waiting for suspicious transaction authorization")
                    return "pendingduetomissingdocuments";
            }
            if (status == Status.FAILED)
            {
                string normalizedRemarks = System.Text.RegularExpressions.Regex.Replace(remarks.Replace(" ", ""), "[^a-zA-Z0-9]", "").ToLower();
                var bestMatch = MoneyTransferStatusMappings.FailedStatusMap
                    .Select(entry => new
                    {
                        Key = entry.Key,
                        Value = entry.Value,
                        Similarity = CalculateSimilarity(
                            normalizedRemarks,
                            System.Text.RegularExpressions.Regex.Replace(entry.Key.Replace(" ", ""), "[^a-zA-Z0-9]", "").ToLower()
                        )
                    })
                    .OrderByDescending(x => x.Similarity)
                    .FirstOrDefault();

                if (bestMatch?.Similarity >= 0.7)
                    return bestMatch.Value;

                if (IsFailedActionRequired(status, remarks))
                    return "failedactionrequired";

                return "failednoactionrequired";
            }

            if (status == Status.REVERSED)
            {
                string normalizedRemarks = System.Text.RegularExpressions.Regex.Replace(remarks.Replace(" ", ""), "[^a-zA-Z0-9]", "").ToLower();
                var bestMatch = MoneyTransferStatusMappings.ReversedStatusMap
                    .Select(entry => new
                    {
                        Key = entry.Key,
                        Value = entry.Value,
                        Similarity = CalculateSimilarity(
                            normalizedRemarks,
                            System.Text.RegularExpressions.Regex.Replace(entry.Key.Replace(" ", ""), "[^a-zA-Z0-9]", "").ToLower()
                        )
                    })
                    .OrderByDescending(x => x.Similarity)
                    .FirstOrDefault();

                if (bestMatch?.Similarity >= 0.7)
                    return bestMatch.Value;

                return "reversednoactionrequired";
            }
            return string.Empty;
        }
        public static double CalculateSimilarity(string str1, string str2)
        {
            if (string.IsNullOrEmpty(str1) || string.IsNullOrEmpty(str2))
                return 0;
            int maxLength = Math.Max(str1.Length, str2.Length);
            int minLength = Math.Min(str1.Length, str2.Length);
            int diffCount = maxLength - minLength;
            for (int i = 0; i < minLength; i++)
            {
                if (str1[i] != str2[i])
                    diffCount++;
            }
            return 1 - (double)diffCount / maxLength;
        }

        private bool IsFailedActionRequired(Status status, string remarks) =>
            status == Status.FAILED && Array.Exists(MoneyTransferStatusMappings.FailedActionRequiredRemarks, r => StringComparer.OrdinalIgnoreCase.Equals(r, remarks));

        private string GetLocalizedString(string key, string languageCode)
        {
            return UIResource.ResourceManager.GetString(key, Culture.Get(languageCode)) ?? string.Empty;
        }
    }
}
