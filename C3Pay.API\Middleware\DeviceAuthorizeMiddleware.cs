﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using C3Pay.Services.Helper;
using DocumentFormat.OpenXml.Office2013.Drawing.ChartStyle;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace C3Pay.API
{
    public class DeviceAuthorizeMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly GeneralSettings _generalSettings;
        private readonly IFeatureManager _featureManager;
        private readonly ILogger _logger;

        public DeviceAuthorizeMiddleware(RequestDelegate next, IOptions<GeneralSettings> generalSettings, IFeatureManager featureManager, ILogger<DeviceAuthorizeMiddleware> logger)
        {
            _next = next;
            _generalSettings = generalSettings.Value;
            _featureManager = featureManager;
            _logger = logger;
        }

        public async Task Invoke(HttpContext context)
        {
            var mfaDeviceAuthorizeEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MFADeviceAuthorize);
            if (mfaDeviceAuthorizeEnabled)
            {
                var endpoint = context.GetEndpoint();
                var hasCustomAuthorizationAttribute = endpoint?.Metadata.GetMetadata<DeviceAuthorizeAttribute>() != null;
                if (hasCustomAuthorizationAttribute)
                {
                    var userName = context.User.FindFirst(ConstantParam.Username)?.Value;

                    if (!context.Request.Headers.ContainsKey("X-Identifier"))
                        _logger.LogWarning("Request is missing the 'X-Identifier' header."); // Use injected logger

                    var xIdentifier = context.Request.Headers["X-Identifier"].ToString();
                    _logger.LogInformation("X-Identifier header value: {XIdentifier}", xIdentifier); // Use injected logger

                    bool tokenValidationEnabled = ValidateMFATokenValidation(userName);

                    if (tokenValidationEnabled)
                    {
                        if (!context.Request.Headers.ContainsKey("X-Identifier"))
                        {
                            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                            return;
                        }
                        if (string.IsNullOrEmpty(userName))
                        {
                            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                            context.Response.ContentType = "application/json";
                        }
                        var _userService = context.RequestServices.GetService(typeof(IUserService)) as IUserService;
                        var isValidToken = await _userService.ValidateDeviceToken(userName, context.Request.Headers["X-Identifier"]);
                        if (!isValidToken)
                        {
                            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                            context.Response.ContentType = "application/json";
                            _logger.LogError($"{userName} is unauthorized."); 
                            return;
                        }
                    }
                }
            }

            await _next(context);
        }

        private bool ValidateMFATokenValidation(string userName)
        {
            bool validationEnabled = true;

            var _uatPentestPhoneNumbers = _generalSettings.UATPentestPhoneNumbers.Split(";").ToList();

            if (_uatPentestPhoneNumbers.Count() > 0) // Check if the list has any items
            {
                if (_uatPentestPhoneNumbers.Contains(userName)) // Check if the userName is in the list
                {
                    validationEnabled = true;
                }
                else
                {
                    validationEnabled = false;
                }
            }
            return validationEnabled;
        }
    }

    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class DeviceAuthorizeAttribute : Attribute
    {
    }
}
