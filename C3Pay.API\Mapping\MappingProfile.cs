﻿using AutoMapper;
using C3Pay.API.Resources;
using C3Pay.API.Resources.Balance;
using C3Pay.API.Resources.Card;
using C3Pay.API.Resources.HR;
using C3Pay.API.Resources.Login;
using C3Pay.API.Resources.MobileRecharge;
using C3Pay.API.Resources.MoneyTransfer;
using C3Pay.API.Resources.SignUp;
using C3Pay.API.Resources.Subscriptions;
using C3Pay.API.Resources.UserProfile;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.LoginVideos.Cached;
using C3Pay.Core.Models.C3Pay.Lookup;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using C3Pay.Core.Models.C3Pay.SignUp;
using C3Pay.Core.Models.DTOs;
using C3Pay.Core.Models.DTOs.ExternalService;
using C3Pay.Core.Models.DTOs.LoginVideos;
using C3Pay.Core.Models.DTOs.Lookup;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Requests;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using C3Pay.Core.Models.DTOs.MoneyTransfer;
using C3Pay.Core.Models.DTOs.MoneyTransfer.Lookups;
using C3Pay.Core.Models.DTOs.SignUp;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Messages.Login;
using C3Pay.Core.Models.Messages.MoneyTransfer;
using C3Pay.Core.Models.Messages.Signup;
using C3Pay.Services.Commands;
using C3Pay.Services.Helper;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.HR;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.API.Mapping
{
    /// <summary>
    /// 
    /// </summary>
    public class MappingProfile : Profile
    {
        /// <summary>
        /// 
        /// </summary>
        public MappingProfile()
        {
            // Domain to Resource
            CreateMap<Core.Models.User, UserDto>()
                .ForMember(dest => dest.FirstName, opt => opt.MapFrom(src => src.CardHolder.FirstName))
                .ForMember(dest => dest.LastName, opt => opt.MapFrom(src => src.CardHolder.LastName))
                .ForMember(dest => dest.Nationality, opt => opt.MapFrom(src => src.CardHolder.Nationality))
                .ForMember(dest => dest.EmiratesId, opt => opt.MapFrom(src => src.CardHolder.EmiratesId));

            List<string> enableNationalitiesForStories = new List<string>() { "IND", "PAK", "NPL" };
            CreateMap<UserProfile, UserDto>()
                .ForMember(dest => dest.ATMPinBlockEndDate, opt => opt.MapFrom(src => src.ATMPinBlockEndDate.HasValue ? src.ATMPinBlockEndDate.Value.ToUniversalTime() : (DateTime?)null))
                .ForMember(dest => dest.DateOfBirth, opt => opt.MapFrom(src => src.DateOfBirth.HasValue ? src.DateOfBirth.Value.ToString("dd/MM/yyyy") : null))
                .ForMember(dest => dest.RegistrationDate, opt => opt.MapFrom(src => src.RegistrationDate.HasValue ? src.RegistrationDate.Value.ToString("dd/MM/yyyy") : null))
                .ForMember(dest => dest.EmiratesIdExpiryDate, opt => opt.MapFrom(src => src.EmiratesIdExpiryDate.HasValue ? src.EmiratesIdExpiryDate.Value.ToString("dd/MM/yyyy") : null))
                .ForMember(dest => dest.ShowDashboardNavigation, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.CitizenId) ? (Convert.ToDouble(src.CitizenId) % 2 == 0 ? true : false) : false))
                .ForMember(dest => dest.CardPlasticType, opt => opt.MapFrom(src => (int)src.CardPlasticType))
                .ForMember(dest => dest.Role, opt => opt.MapFrom(src => src.Role))
                .ForMember(dest => dest.IsMTStoriesEnabled, opt => opt.MapFrom(src => enableNationalitiesForStories.Contains(src.Nationality) && src.RMTProfileStatus == "Created" && src.MoneyTransferTransactionsCount == 0))
                .ForMember(dest => dest.EligibleForUnemploymentInsurance, opt => opt.MapFrom(src =>
                (src.IsFreeZoneEmployee == null || (src.IsFreeZoneEmployee != true))
                && (src.EmiratesIdStatus == Enum.GetName(typeof(IdentificationStatus), IdentificationStatus.Valid) ||
                src.EmiratesIdStatus == Enum.GetName(typeof(IdentificationStatus), IdentificationStatus.ValidUpdatable))
                ))
                .AfterMap((src, dest, context) =>
                {
                    dest.Experiments = new List<ExperimentGroupsDto>();

                    if (src.ExperimentUsers != null)
                    {
                        var experimentGroupListByFeature = src.ExperimentUsers.Select(s => new { s.Experiment.Feature.Id, s.Experiment.Feature.Name }).Distinct();
                        foreach (var item in experimentGroupListByFeature)
                        {
                            var experimentGroup = new ExperimentGroupsDto();
                            experimentGroup.FeatureId = item.Id;
                            experimentGroup.FeatureName = item.Name;

                            var experimentData = src.ExperimentUsers.Where(x => x.Experiment.FeatureId == item.Id);
                            experimentGroup.Experiments = context.Mapper.Map<List<ExperimentDto>>(experimentData);

                            dest.Experiments.Add(experimentGroup);
                        }
                    }
                });

            //CreateMap<SocialProofing, SocialProofingDto>()
            //    .ForMember(dest => dest.FeatureName, opt => opt.MapFrom(src => src.Feature.Name))
            //    .ForMember(dest => dest.CountryName, opt => opt.MapFrom(src => src.Country.Name));

            CreateMap<C3Pay.Core.Models.User, ExternalUserDto>();

            CreateMap<SecurityQuestion, SecurityQuestionDto>();

            CreateMap<EmiratesIdDto, C3Pay.API.Resources.EmiratesId.IdentificationDto>()
                .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.DateOfBirth, opt => opt.MapFrom(src => src.DOB.Replace("/", "-")))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.ExpiryDate.Replace("/", "-")));

            CreateMap<PassportDto, C3Pay.API.Resources.EmiratesId.IdentificationDto>()
               .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => string.Join(" ", src.Name, src.Surname)))
               .ForMember(dest => dest.DateOfBirth, opt => opt.MapFrom(src => src.DateOfBirth.Replace("/", "-")))
               .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.DateOfExpiry.Replace("/", "-")))
               .ForMember(dest => dest.FirstName, opt => opt.MapFrom(src => src.Name))
               .ForMember(dest => dest.LastName, opt => opt.MapFrom(src => src.Surname))
               .ForMember(dest => dest.NationalityCode, opt => opt.MapFrom(src => src.CountryCode))
               .ForMember(dest => dest.Nationality, opt => opt.MapFrom(src => src.Country));

            CreateMap<EmiratesIdDto, DataExtractionDto>()
                .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.IdNumber))
                .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.DateOfBirth, opt => opt.MapFrom(src => src.DOB.Replace("/", "-")))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.ExpiryDate.Replace("/", "-")));

            CreateMap<PassportDto, DataExtractionDto>()
                .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.PassportNumber))
               .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => string.Join(" ", src.Name, src.Surname)))
               .ForMember(dest => dest.DateOfBirth, opt => opt.MapFrom(src => src.DateOfBirth.Replace("/", "-")))
               .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.DateOfExpiry.Replace("/", "-")))
               .ForMember(dest => dest.FirstName, opt => opt.MapFrom(src => src.Name))
               .ForMember(dest => dest.LastName, opt => opt.MapFrom(src => src.Surname))
               .ForMember(dest => dest.NationalityCode, opt => opt.MapFrom(src => src.CountryCode))
               .ForMember(dest => dest.Nationality, opt => opt.MapFrom(src => src.Country));

            CreateMap<FaceMatchResultDto, C3Pay.API.Resources.FaceMatchResponseDto>()
                .ForMember(dest => dest.MatchPercentage, opt => opt.MapFrom(src => decimal.Parse(src.MatchPercentage.Replace("%", ""))));

            CreateMap<Subscription, SubscriptionDto>()
                .ForMember(dest => dest.FeeIteration, opt => opt.MapFrom(src => Regex.Replace(src.PaymentFrequency.ToString(), @"((?<=\p{Ll})\p{Lu})|((?!\A)\p{Lu}(?>\p{Ll}))", " $0")));

            CreateMap<Subscription, UserSubscriptionDetailsDto>()
                .ForMember(dest => dest.FeeIteration, opt => opt.MapFrom(src => Regex.Replace(src.PaymentFrequency.ToString(), @"((?<=\p{Ll})\p{Lu})|((?!\A)\p{Lu}(?>\p{Ll}))", " $0")));

            CreateMap<UserSubscriptionDetailsDto, SubscriptionDto>();
            CreateMap<UserSubscriptionDetailsFeatureDto, SubscriptionFeatureDto>();

            CreateMap<UserSubscription, UserSubscriptionDto>()
                .ForMember(dest => dest.Active, opt => opt.MapFrom(src => src.StartDate <= DateTime.Now && (!src.EndDate.HasValue || src.EndDate > DateTime.Now)));

            CreateMap<TransactionDetailsDto, CardTransactionDto>()
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => new AmountDto() { Amount = double.Parse(src.TransactionAmount), Currency = "AED" }))
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateTime.ParseExact(src.TransactionDate, "dd/MM/yyyy", CultureInfo.InvariantCulture)))
                .ForMember(dest => dest.MerchantName, opt => opt.MapFrom(src => src.TransactionDescription))
                .ForMember(dest => dest.IsCharge, opt => opt.MapFrom(src => double.Parse(src.TransactionAmount) < 0));

            CreateMap<SubscriptionFeature, SubscriptionFeatureDto>();
            CreateMap<SubscriptionFeature, UserSubscriptionDetailsFeatureDto>();

            CreateMap<Edenred.Common.Core.PPSTransaction, CardTransactionDto>();

            CreateMap<PPSAmount, AmountDto>();

            CreateMap<PPSAmount, AmountDto>();

            CreateMap<ResetPasswordEligibility, CheckResetPasswordEligbilityResponseDto>()
                .ForMember(dest => dest.Eligibility, opt => opt.MapFrom(src => src.Eligibility.ToString()));

            CreateMap<VerifyAutoUnblockDetailsRequestDto, VerifyAutoUnblockDetailsCommand>();
            CreateMap<VerifyAutoUnblockDetailsByCardRequestDto, VerifyAutoUnblockDetailsByCardCommand>();

            // Resource to Domain          
            CreateMap<RegisterOrUpdateExternalUserRequestDto, RegisterOrUpdateExternalUserCommand>();

            CreateMap<EmiratesIdDto, UpdateIdentificationRequestDto>()
                 .ForMember(dest => dest.DateOfBirth, opt => opt.MapFrom(src => src.DOB));

            CreateMap<DataExtractionDto, UpdateIdentificationRequestDto>()
                .ForMember(dest => dest.IdNumber, opt => opt.MapFrom(src => src.DocumentNumber));

            CreateMap<UpdateIdentificationRequestDto, Identification>()
                .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.IdNumber) ? src.IdNumber : src.PassportNumber))
                .ForMember(dest => dest.FaceMatchIsIdeal, opt => opt.MapFrom(src => src.IsIdealFaceMatchPercentage))
                .ForMember(dest => dest.NameMatchIsIdeal, opt => opt.MapFrom(src => src.IsIdealNameMatchPercentage))
                .ForMember(dest => dest.Birthdate, opt => opt.MapFrom(src => DateTime.ParseExact(src.DateOfBirth.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture)))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => DateTime.ParseExact(src.ExpiryDate.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture)));

            CreateMap<UpdateIdentificationRequestDto, NonRegisteredIdentification>()
              .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.IdNumber))
              .ForMember(dest => dest.FaceMatchIsIdeal, opt => opt.MapFrom(src => src.IsIdealFaceMatchPercentage))
              .ForMember(dest => dest.NameMatchIsIdeal, opt => opt.MapFrom(src => src.IsIdealNameMatchPercentage))
              .ForMember(dest => dest.Birthdate, opt => opt.MapFrom(src => DateTime.ParseExact(src.DateOfBirth.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture)))
              .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => DateTime.ParseExact(src.ExpiryDate.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture)));

            CreateMap<UpdateIdentificationDto, Identification>()
                .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.DocumentNumber))
                .ForMember(dest => dest.FaceMatchIsIdeal, opt => opt.MapFrom(src => src.IsIdealFaceMatchPercentage))
                .ForMember(dest => dest.NameMatchIsIdeal, opt => opt.MapFrom(src => src.IsIdealNameMatchPercentage))
                .ForMember(dest => dest.Birthdate, opt => opt.MapFrom(src => DateTime.ParseExact(src.DateOfBirth.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture)))
                .ForMember(dest => dest.Gender, opt => opt.MapFrom(src => src.Gender == "MALE" ? Gender.Male : Gender.Female))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => DateTime.ParseExact(src.ExpiryDate.Replace("-", "/"), "dd/MM/yyyy", CultureInfo.InvariantCulture)));

            CreateMap<SecurityAnswerDto, SecretAnswer>()
                .ForMember(dest => dest.Answer, opt => opt.MapFrom(src => src.SecurityAnswer));

            //Country            
            CreateMap<Country, CountryDto>()
                .ForMember(dest => dest.ISDCode, opt => opt.MapFrom(src => src.STDCode))
                .ForMember(dest => dest.CashPickUpProvider, opt => opt.MapFrom(src => src.EligibleForCashPickUp ? src.CashPickUpProvider : string.Empty))
                .ForMember(dest => dest.IsBankTransfer, opt => opt.MapFrom(src => src.EligibleForBankTransfer))
                .ForMember(dest => dest.IsCashPickup, opt => opt.MapFrom(src => src.EligibleForCashPickUp))
                .ForMember(dest => dest.IsPopular, opt => opt.MapFrom(src => src.IsPapularCountry))
                .ForPath(dest => dest.Rates.CashPickUpRate, opt => opt.MapFrom(src => src.CashPickUpLatestRate == 0 ? 0 : 1 / src.CashPickUpLatestRate))
                .ForPath(dest => dest.Rates.BankTransferRate, opt => opt.MapFrom(src => src.BankTransferLatestRate == 0 ? 0 : 1 / src.BankTransferLatestRate))
                .ForPath(dest => dest.Rates.Expiry, opt => opt.MapFrom(src => src.RatesLastUpdatedDate.HasValue ? src.RatesLastUpdatedDate.Value.AddMinutes(35) : (DateTime?)null));

            ////Banks
            CreateMap<BankBranchDetailsRakModel, C3Pay.API.Resources.MoneyTransfer.BankBranchDto>();

            // CasePickup Transfer base.
            CreateMap<C3Pay.Core.Models.MoneyTransferBeneficiary, C3Pay.API.Resources.MoneyTransfer.CashPickupBeneficiaryDto>();

            //Money Transfer Reason
            CreateMap<C3Pay.Core.Models.MoneyTransferReason, C3Pay.API.Resources.MoneyTransfer.MoneyTransferReasonDto>();

            // Add Money Transfer Beneficiary
            CreateMap<PostMoneyTransferBeneficiaryRequestDto, MoneyTransferBeneficiary>()
                .ForMember(src => src.Address1, opt => opt.MapFrom(dest => dest.BankDetails.Address1))
                .ForMember(src => src.Address2, opt => opt.MapFrom(dest => dest.BankDetails.Address2))
                .ForMember(src => src.CountryCode, opt => opt.MapFrom(dest => dest.Beneficiary.Country))
                .ForMember(src => src.FirstName, opt => opt.MapFrom(dest => dest.Beneficiary.FirstName))
                .ForMember(src => src.MiddleName, opt => opt.MapFrom(dest => dest.Beneficiary.MiddleName))
                .ForMember(src => src.LastName, opt => opt.MapFrom(dest => dest.Beneficiary.LastName))
                .ForMember(src => src.MoneyTransferReasonId, opt => opt.MapFrom(dest => dest.ReasonId))
                .ForMember(src => src.BankName, opt => opt.MapFrom(dest => dest.BankDetails.BankName))
                .ForMember(src => src.AccountNumber, opt => opt.MapFrom(dest => dest.BankDetails.AccountNumber))
                .ForMember(src => src.BankBranchName, opt => opt.MapFrom(dest => dest.BankDetails.BankBranchName))
                .ForMember(src => src.IdentifierCode1, opt => opt.MapFrom(dest => dest.BankDetails.BankCode))
                .ForMember(src => src.IdentifierCode2, opt => opt.MapFrom(dest => dest.BankDetails.BankBranchCode))
                .ForMember(src => src.TransferType, opt => opt.MapFrom(dest => MapFromTransferMethodToTransferType(dest.TransferMethod)))
                .ForPath(src => src.Country.Name, opt => opt.MapFrom(dest => dest.Beneficiary.CountryName));

            // Add Money Transfer Beneficiary
            CreateMap<PostMoneyTransferBeneficiaryV2RequestDto, MoneyTransferBeneficiary>()
                .ForMember(src => src.CountryCode, opt => opt.MapFrom(dest => dest.Beneficiary.Country))
                .ForMember(src => src.FirstName, opt => opt.MapFrom(dest => dest.Beneficiary.FirstName))
                .ForMember(src => src.MiddleName, opt => opt.MapFrom(dest => dest.Beneficiary.MiddleName))
                .ForMember(src => src.LastName, opt => opt.MapFrom(dest => dest.Beneficiary.LastName))
                .ForMember(src => src.MoneyTransferReasonId, opt => opt.MapFrom(dest => dest.ReasonId))
                .ForMember(src => src.AccountNumber, opt => opt.MapFrom(dest => dest.BankDetails.AccountNumber))
                .ForMember(src => src.BankId, opt => opt.MapFrom(dest => dest.BankDetails.BankId))
                .ForMember(src => src.BranchId, opt => opt.MapFrom(dest => dest.BankDetails.BranchId))
                .ForMember(src => src.TransferType, opt => opt.MapFrom(dest => MapFromTransferMethodToTransferType(dest.TransferMethod)))
                .ForPath(src => src.Country.Name, opt => opt.MapFrom(dest => dest.Beneficiary.CountryName))
                .AfterMap((src, dest, context) =>
                {
                    if (!string.IsNullOrEmpty(src.Beneficiary.FullName))
                    {
                        List<string> names = src.Beneficiary.FullName.Trim().Split(' ').ToList();
                        if (names.Count == 1)
                        {
                            dest.FirstName = names[0].Trim();
                        }
                        else if (names.Count == 2)
                        {
                            dest.FirstName = names.First().Trim();
                            dest.LastName = names.Last().Trim();
                        }
                        else
                        {
                            dest.FirstName = names.First().Trim();
                            dest.LastName = names.Last().Trim();

                            names.RemoveAt(0);
                            names.RemoveAt(names.Count - 1);

                            dest.MiddleName = string.Join(" ", names);
                        }
                    }
                });

            // Bank Transfer beneficiary
            CreateMap<MoneyTransferBeneficiary, BankTransferBeneficiaryDto>()
                .ForPath(src => src.MoneyTransferPurpose, opt => opt.MapFrom(dest => dest.MoneyTransferReason.Reason))
                .ForPath(src => src.BankDetails.AccountNumber, opt => opt.MapFrom(dest => string.IsNullOrEmpty(dest.AccountNumber) ? dest.LinkedUser.PhoneNumber : dest.AccountNumber))
                .ForPath(src => src.CountryName, opt => opt.MapFrom(dest => dest.Country.Name))
                .ForPath(src => src.CashPickupProvider, opt => opt.MapFrom(dest => dest.TransferType == MoneyTransferType.RAKMoneyCashPayout ? dest.Country.CashPickUpProvider : string.Empty))
                .ForPath(src => src.Country, opt => opt.MapFrom(dest => dest.CountryCode))
                .ForPath(src => src.BankDetails.BankCode, opt => opt.MapFrom(dest => dest.IdentifierCode1))
                .ForPath(src => src.BankDetails.BankBranchCode, opt => opt.MapFrom(dest => dest.IdentifierCode2))
                .ForPath(src => src.BankDetails.BankName, opt => opt.MapFrom(dest => dest.BankName))
                .ForPath(src => src.BankDetails.BankBranchName, opt => opt.MapFrom(dest => dest.BankBranchName))
                .ForPath(src => src.BankDetails.Address1, opt => opt.MapFrom(dest => dest.Address1))
                .ForPath(src => src.BankDetails.Address2, opt => opt.MapFrom(dest => dest.Address2))
                .ForPath(src => src.BankDetails.IsBranchSelected, opt => opt.MapFrom(dest => dest.IsBranchSelected))
                .ForMember(src => src.Status, opt => opt.MapFrom(dest => dest.Status))
                .ForMember(src => src.IsManualApproval, opt => opt.MapFrom(dest => dest.RequiresApproval))
                .ForMember(src => src.BeneficiaryDate, opt => opt.MapFrom(dest => dest.CreatedDate))
                .ForMember(src => src.TransferMethod, opt => opt.MapFrom(dest => MapFromTransferTypeToTransferMethod(dest.TransferType)))
                .ForMember(src => src.RemarksForFailed, opt => opt.MapFrom(dest => dest.Remarks));

            CreateMap<MoneyTransferBeneficiaryDetails, BankTransferBeneficiaryDto>()
                .ForPath(src => src.CashPickupProvider, opt => opt.MapFrom(dest => dest.CashPickupProvider))
                .ForPath(src => src.BankDetails.AccountNumber, opt => opt.MapFrom(dest => dest.AccountNumber))
                .ForPath(src => src.BankDetails.BankName, opt => opt.MapFrom(dest => dest.BankName))
                .ForPath(src => src.BankDetails.BankBranchName, opt => opt.MapFrom(dest => dest.BankBranchName))
                .ForPath(src => src.BankDetails.BankCode, opt => opt.MapFrom(dest => dest.BankCode))
                .ForPath(src => src.BankDetails.BankBranchCode, opt => opt.MapFrom(dest => dest.BankBranchCode))
                .ForPath(src => src.BankDetails.Address1, opt => opt.MapFrom(dest => dest.Address1))
                .ForPath(src => src.BankDetails.Address2, opt => opt.MapFrom(dest => dest.Address2))
                .ForPath(src => src.BankDetails.IsBranchSelected, opt => opt.MapFrom(dest => dest.IsBranchSelected))
                .ForMember(src => src.CanPerformTransaction, opt => opt.MapFrom(dest => dest.CanPerformTransaction))
                .ForMember(src => src.TransactionFailureReason, opt => opt.MapFrom(dest => dest.TransactionFailureReason))
                .ForMember(src => src.Status, opt => opt.MapFrom(dest => dest.Status.ToString()))
                .ForMember(src => src.TransferMethod, opt => opt.MapFrom(dest => MapFromTransferTypeToTransferMethod(dest.TransferMethod)))
                .ForMember(src => src.PhoneNumber, opt => opt.MapFrom(dest => dest.PhoneNumber))
                .ForMember(src => src.ProviderName, opt => opt.MapFrom(dest => dest.ProviderName))
                .ForMember(src => src.ProviderLogo, opt => opt.MapFrom(dest => dest.ProviderLogoUrl))
                .AfterMap((src, dest, context) =>
                {
                    // If the user has the same first name and last name, and the transfer type, then only show 1 name.
                    // This is because the wallet transfer page has only one input (full name).
                    if (dest.TransferMethod == TransferMethod.WALLET.ToString())
                    {
                        if (dest.FirstName == dest.LastName)
                        {
                            dest.LastName = string.Empty;
                        }
                    }
                });


            // Fx Request mapping
            CreateMap<C3Pay.API.Resources.MoneyTransfer.MoneyTransferEstimateRequestDto, FxRateRequestRakModel>()
                .ForPath(src => src.Fxvalue.Amount, opt => opt.MapFrom(dest => dest.Amount));

            CreateMap<MoneyTransferFreeTransferEligiblity, C3Pay.API.Resources.MoneyTransfer.MoneyTransferFreeTransferDetailsDto>();

            // Fx Response mapping           
            CreateMap<FxConversionRakModel, C3Pay.API.Resources.MoneyTransfer.FxConversionRatesDto>()
                .ForMember(src => src.ConvertedAmount, opt => opt.MapFrom(dest => dest.ConvertedAmount))
                .ForMember(src => src.Currency, opt => opt.MapFrom(dest => dest.Currency))
                .ForMember(src => src.Rate, opt => opt.MapFrom(dest => dest.Rate));

            CreateMap<FxRateResponseRakModel, C3Pay.API.Resources.MoneyTransfer.MoneyTransferFxRateResponseDto>()
                 .ForMember(src => src.FxRates, opt => opt.MapFrom(dest => dest.FxRates));

            //Mapping for Fx Rate for the amount
            CreateMap<FxRateConversionResponseRakModel, C3Pay.API.Resources.MoneyTransfer.MoneyTransfeferFxRateDto>()
               .ForPath(src => src.Amount, opt => opt.MapFrom(dest => dest.Fxvalue.Amount))
               .ForPath(src => src.TotalFeeAmount, opt => opt.MapFrom(dest => dest.ConversionCharges.TotalCharges))
               .ForMember(src => src.TransferType, opt => opt.MapFrom(dest => (TransferMethod)Enum.Parse<MoneyTransferType>(dest.TransferType)))
               .ForMember(src => src.FxRate, opt => opt.MapFrom(dest => dest.FxConversionRates != null && dest.FxConversionRates.Count > 0 && !string.IsNullOrEmpty(dest.FxConversionRates[0].Rate) ? Math.Round(1 / Convert.ToDouble(dest.FxConversionRates[0].Rate), 3) : 0));

            //Mapping for Estimate rate for transactions.
            CreateMap<FxRateConversionResponseRakModel, C3Pay.API.Resources.MoneyTransfer.MoneyTransferEstimateDto>()
               .ForPath(src => src.Amount, opt => opt.MapFrom(dest => dest.Fxvalue.Amount))
               .ForMember(src => src.TransferType, opt => opt.MapFrom(dest => dest.TransferType == BaseEnums.MoneyTransferType.OutsideUAE.ToString() ? BaseEnums.TransferMethod.BANKTRANSFER.ToString() : BaseEnums.TransferMethod.CASHPICKUP.ToString()))
               .ForPath(src => src.TotalFeeAmount, opt => opt.MapFrom(dest => dest.ConversionCharges.TotalCharges))
               .ForMember(src => src.FxRate, opt => opt.MapFrom(dest => dest.FxConversionRates != null && dest.FxConversionRates.Count > 0 && !string.IsNullOrEmpty(dest.FxConversionRates[0].Rate) ? Math.Round(1 / Convert.ToDouble(dest.FxConversionRates[0].Rate), 3) : 0))
               .ReverseMap();

            // Add Money Transfer Transaction.
            CreateMap<PostMoneyTransferRequestDto, MoneyTransferTransaction>()
              .ForMember(src => src.MoneyTransferBeneficiaryId, opt => opt.MapFrom(dest => dest.BeneficiaryId))
              .ForMember(src => src.SendAmount, opt => opt.MapFrom(dest => dest.SendAmount.Amount))
              .ForMember(src => src.SendCurrency, opt => opt.MapFrom(dest => dest.SendAmount.Currency))
              .ForMember(src => src.ReceiveCurrency, opt => opt.MapFrom(dest => dest.ReceiveAmount.Currency))
              .ForMember(src => src.ReceiveAmount, opt => opt.MapFrom(dest => dest.ReceiveAmount.Amount))
              .ForMember(src => src.TotalCharges, opt => opt.MapFrom(dest => dest.FeeAmount))
              .ForMember(src => src.ChargesCurrency, opt => opt.MapFrom(dest => dest.SendAmount.Currency))
              .ForMember(src => src.ReferralCode, opt => opt.MapFrom(dest => !string.IsNullOrEmpty(dest.ReferralCode) ? dest.ReferralCode.ToLower().Trim() : null))
              .ForMember(src => src.ConversionRate, opt => opt.MapFrom(dest => dest.ConversionRate))
              .ForMember(src => src.DisplayedFxRate, opt => opt.MapFrom(dest => dest.LogFxRate));


            // Add Money Transfer Transaction.
            CreateMap<MoneyTransferTransaction, MoneyTransferDto>()
             .ForMember(src => src.Id, opt => opt.MapFrom(dest => dest.Id))
             .ForMember(src => src.ReferralCode, opt => opt.MapFrom(dest => dest.ReferralCode))
             .ForMember(src => src.BeneficiaryFullName, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.FirstName + (string.IsNullOrEmpty(dest.MoneyTransferBeneficiary.MiddleName) ? " " : " " + dest.MoneyTransferBeneficiary.MiddleName + " ") + dest.MoneyTransferBeneficiary.LastName))
             .ForPath(src => src.SendAmount.Amount, opt => opt.MapFrom(dest => dest.SendAmount))
             .ForPath(src => src.SendAmount.Currency, opt => opt.MapFrom(dest => dest.SendCurrency))
             .ForPath(src => src.ReceiveAmount.Currency, opt => opt.MapFrom(dest => dest.ReceiveCurrency))
             .ForPath(src => src.ReceiveAmount.Amount, opt => opt.MapFrom(dest => dest.ReceiveAmount))
             .ForMember(src => src.TransferMethod, opt => opt.MapFrom(dest => MapFromTransferTypeToTransferMethod(dest.MoneyTransferBeneficiary.TransferType)))
             .ForMember(src => src.DateSent, opt => opt.MapFrom(dest => dest.CreatedDate))
             .ForMember(src => src.Remarks, opt => opt.MapFrom(dest => dest.Remarks))
             .ForPath(src => src.BeneficiaryPhoneNumber, opt => opt.MapFrom(dest => string.IsNullOrEmpty(dest.MoneyTransferBeneficiary.AccountNumber) ? dest.MoneyTransferBeneficiary.LinkedUser.PhoneNumber : dest.MoneyTransferBeneficiary.AccountNumber))
             .ForMember(src => src.Status, opt => opt.MapFrom(dest => dest.Status));

            CreateMap<SendMoneyTransferResultDto, MoneyTransferDto>()
             .ForMember(src => src.Id, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.Id))
             .ForMember(src => src.ReferralCode, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.ReferralCode))
             .ForMember(src => src.BeneficiaryFullName, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.MoneyTransferBeneficiary.FirstName + (string.IsNullOrEmpty(dest.MoneyTransferTransaction.MoneyTransferBeneficiary.MiddleName) ? " " : " " + dest.MoneyTransferTransaction.MoneyTransferBeneficiary.MiddleName + " ") + dest.MoneyTransferTransaction.MoneyTransferBeneficiary.LastName))
             .ForPath(src => src.SendAmount.Amount, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.SendAmount))
             .ForPath(src => src.SendAmount.Currency, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.SendCurrency))
             .ForPath(src => src.ReceiveAmount.Currency, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.ReceiveCurrency))
             .ForPath(src => src.ReceiveAmount.Amount, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.ReceiveAmount))
             .ForMember(src => src.TransferMethod, opt => opt.MapFrom(dest => MapFromTransferTypeToTransferMethod(dest.MoneyTransferTransaction.MoneyTransferBeneficiary.TransferType)))
             .ForMember(src => src.DateSent, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.CreatedDate))
             .ForMember(src => src.Remarks, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.Remarks))
             .ForPath(src => src.BeneficiaryPhoneNumber, opt => opt.MapFrom(dest => string.IsNullOrEmpty(dest.MoneyTransferTransaction.MoneyTransferBeneficiary.AccountNumber) && dest.MoneyTransferTransaction.MoneyTransferBeneficiary.TransferType == MoneyTransferType.DirectTransfer ? dest.MoneyTransferTransaction.MoneyTransferBeneficiary.LinkedUser.PhoneNumber : dest.MoneyTransferTransaction.MoneyTransferBeneficiary.AccountNumber))
             .ForMember(src => src.Status, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.Status))
             .ForMember(src => src.DelayType, opt => opt.MapFrom(dest => dest.MoneyTransferDelay.Type))
             .ForMember(src => src.DelayStartDate, opt => opt.MapFrom(dest => dest.MoneyTransferDelay.StartDate))
             .ForMember(src => src.DelayEndDate, opt => opt.MapFrom(dest => dest.MoneyTransferDelay.EndDate))
             .ForMember(src => src.DelayTimeRemainingInHours, opt => opt.MapFrom(dest => dest.MoneyTransferDelay.TimeRemainingInHours))
             .ForMember(src => src.DelayCountryCode, opt => opt.MapFrom(dest => dest.MoneyTransferDelay.CountryCode))
             .ForMember(src => src.DelayCountryName, opt => opt.MapFrom(dest => dest.MoneyTransferDelay.Country.Name))
             .ForPath(src => src.CashPickupTransferDetailsDto.CashPickupPin, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.CashPickupPin))
             .ForPath(src => src.CashPickupTransferDetailsDto.SharePinMessageContent, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.CashPickupPin))
             .ForPath(src => src.FirstName, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.MoneyTransferBeneficiary.FirstName))
             .ForPath(src => src.LastName, opt => opt.MapFrom(dest => dest.MoneyTransferTransaction.MoneyTransferBeneficiary.LastName));


            // Mapping for money transfer transactions.
            CreateMap<MoneyTransferTransaction, MoneyTransferReceiptDto>()
                .ForMember(src => src.BeneficiaryFullName, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.FirstName + " " + dest.MoneyTransferBeneficiary.MiddleName + " " + dest.MoneyTransferBeneficiary.LastName))
                .ForPath(src => src.SendAmount.Amount, opt => opt.MapFrom(dest => dest.SendAmount))
                .ForPath(src => src.SendAmount.Currency, opt => opt.MapFrom(dest => dest.SendCurrency))
                .ForPath(src => src.ReceiveAmount.Currency, opt => opt.MapFrom(dest => dest.ReceiveCurrency))
                .ForMember(src => src.ReferralCode, opt => opt.MapFrom(dest => dest.ReferralCode))
                .ForMember(src => src.ConversionRate, opt => opt.MapFrom(dest => dest.ConversionRate == null || dest.ConversionRate == 0 ? 0 : Math.Round(1 / Convert.ToDecimal(dest.ConversionRate), 7)))
                .ForPath(src => src.ReceiveAmount.Amount, opt => opt.MapFrom(dest => dest.ReceiveAmount))
                .ForPath(src => src.Fee.Amount, opt => opt.MapFrom(dest => dest.TotalCharges))
                .ForPath(src => src.Fee.Currency, opt => opt.MapFrom(dest => dest.ChargesCurrency))
                .ForPath(src => src.BankDetails.BankCode, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.IdentifierCode1))
                .ForPath(src => src.BankDetails.BankBranchCode, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.IdentifierCode2))
                .ForPath(src => src.BankDetails.BankName, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.BankName))
                .ForPath(src => src.BankDetails.BankBranchName, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.BankBranchName))
                .ForPath(src => src.BankDetails.Address1, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.Address1))
                .ForPath(src => src.BankDetails.Address2, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.Address2))
                .ForPath(src => src.BankDetails.IsBranchSelected, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.IsBranchSelected))
                .ForPath(src => src.BankDetails.AccountNumber, opt => opt.MapFrom(dest => string.IsNullOrEmpty(dest.MoneyTransferBeneficiary.AccountNumber) && dest.MoneyTransferBeneficiary.TransferType != MoneyTransferType.Wallet ? dest.MoneyTransferBeneficiary.LinkedUser.PhoneNumber : dest.MoneyTransferBeneficiary.AccountNumber))
                .ForPath(src => src.BeneficiaryDetails.FirstName, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.FirstName))
                .ForPath(src => src.BeneficiaryDetails.MiddleName, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.MiddleName))
                .ForPath(src => src.BeneficiaryDetails.LastName, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.LastName))
                .ForPath(src => src.MoneyTransferPurpose, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.MoneyTransferReason.Reason))
                .ForPath(src => src.BeneficiaryDetails.Country, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.CountryCode))
                .ForPath(src => src.BeneficiaryDetails.CountryName, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.Country.Name))
                .ForMember(src => src.TransferMethod, opt => opt.MapFrom(dest => MapFromTransferTypeToTransferMethod(dest.MoneyTransferBeneficiary.TransferType)))
                .ForMember(src => src.DateSent, opt => opt.MapFrom(dest => dest.CreatedDate))
                .ForMember(src => src.Remarks, opt => opt.MapFrom(dest => dest.Remarks))
                .ForMember(src => src.CashPickUpProvider, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.Country.CashPickUpProvider))
                .ForMember(src => src.Status, opt => opt.MapFrom(dest => CastToMoneyTransferStatus(dest.Status)))
                .ForPath(src => src.BeneficiaryDetails.PhoneNumber, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.PhoneNumber))
                .ForPath(src => src.BeneficiaryDetails.ProviderName, opt => opt.MapFrom(dest => dest.MoneyTransferBeneficiary.WalletProvider))
                .ForPath(src => src.CashPickupTransferDetailsDto.CashPickupPin, opt => opt.MapFrom(dest => dest.CashPickupPin))
                .ForPath(src => src.CashPickupTransferDetailsDto.SharePinMessageContent, opt => opt.MapFrom(dest => dest.CashPickupPin))
                 .AfterMap((src, dest, context) =>
                 {
                     // If the user has the same first name and last name, and the transfer type, then only show 1 name.
                     // This is because the wallet transfer page has only one input (full name).
                     if (dest.TransferMethod == TransferMethod.WALLET.ToString())
                     {
                         var nameParts = dest.BeneficiaryFullName.Split(' ');
                         if (nameParts.First() == nameParts.Last())
                         {
                             dest.BeneficiaryFullName = nameParts.First();
                         }
                     }
                 });


            //mobile recharge country mapping
            CreateMap<C3Pay.Core.Models.Country, C3Pay.API.Resources.Countries.MobileRechageCountryDto>()
            .ForMember(dest => dest.ISDCode, opt => opt.MapFrom(src => src.STDCode))
            .ForMember(dest => dest.IsPopular, opt => opt.MapFrom(src => src.IsPapularCountry));

            CreateMap<C3Pay.Core.Models.MobileRechargeProvider, C3Pay.API.Resources.MobileRecharge.MobileRechargeProviderDto>()
                .ForMember(src => src.RechargeMethodType, opt => opt.MapFrom(dest => SetRechargeMethodType(dest)));

            //Mapping for mobile recharge products.
            CreateMap<C3Pay.Core.Models.MobileRechargeProduct, C3Pay.API.Resources.MobileRecharge.MobileRechargeProductDto>()
             .ForMember(src => src.ProductCode, opt => opt.MapFrom(dest => dest.Code))
             .ForMember(src => src.DisplayName, opt => opt.MapFrom(dest => dest.DefaultDisplayText))
             .ForPath(src => src.ProviderLogoURL, opt => opt.MapFrom(dest => dest.Provider.LogoUrl))
             .ForPath(src => src.Description, opt => opt.MapFrom(dest => dest.AdditionalInformation))
             .ForPath(src => src.ProviderName, opt => opt.MapFrom(dest => dest.Provider.Name))
             .ForPath(src => src.CustomerCareNumber, opt => opt.MapFrom(dest => dest.Provider.CustomerCareNumber))
             .ForPath(src => src.SendAmount.Amount, opt => opt.MapFrom(dest => dest.MaxSendValue))
             .ForPath(src => src.SendAmount.Currency, opt => opt.MapFrom(dest => dest.MaxSendCurrencyIso))
             .ForPath(src => src.SendFee.Amount, opt => opt.MapFrom(dest => dest.CustomFee > 0 ? dest.CustomFee : dest.MaxCustomerFee))
             .ForPath(src => src.SendFee.Currency, opt => opt.MapFrom(dest => dest.MaxSendCurrencyIso))
             .ForPath(src => src.ReceiveAmount.Amount, opt => opt.MapFrom(dest => dest.MaxReceiveValue))
             .ForPath(src => src.ReceiveAmount.Currency, opt => opt.MapFrom(dest => dest.MaxReceiveCurrencyIso))
             .ForPath(src => src.ReceiveAmountExcludingTax.Amount, opt => opt.MapFrom(dest => dest.MaxReceiveValueExcludingTax))
             .ForPath(src => src.ReceiveAmountExcludingTax.Currency, opt => opt.MapFrom(dest => dest.MaxReceiveCurrencyIso))
             .ForPath(src => src.OperatorName, opt => opt.MapFrom(dest => dest.ProviderCode == BaseEnums.CallingCardOperator.FUAE.ToString() ? dest.Provider.Name.Replace(" Intl VOIP Calling Card", "(Etisalat)") :
                                                                                    dest.ProviderCode == BaseEnums.CallingCardOperator.HAAE.ToString() ? dest.Provider.Name.Replace(" Intl VOIP Calling Card", "(Du)") : dest.Provider.Name))
             .ForMember(src => src.ProductType, opt => opt.MapFrom(dest => dest.Benefits))
             .ForMember(src => src.Region, opt => opt.MapFrom(dest => dest.SupportedCountry.Name))
             .ForMember(src => src.IsHighlighted, opt => opt.MapFrom(dest => (dest.IsHighlighted == null || dest.IsHighlighted == false) ? false : true))
             .ForMember(src => src.DescriptiveDisplayName, opt => opt.MapFrom(dest => dest.DescriptiveDisplayName));

            //Mapping for mobile recharge products v2
            CreateMap<C3Pay.Core.Models.MobileRechargeProduct, C3Pay.API.Resources.MobileRecharge.MobileRechargeProductDtoV2>()
                .ForMember(src => src.ProductCode, opt => opt.MapFrom(dest => dest.Code))
                .ForPath(src => src.Description, opt => opt.MapFrom(dest => SetDescription(dest.AdditionalInformation, dest.DefaultDisplayText)))
                .ForPath(src => src.SendAmount.Amount, opt => opt.MapFrom(dest => dest.MaxSendValue))
                .ForPath(src => src.SendAmount.Currency, opt => opt.MapFrom(dest => dest.MaxSendCurrencyIso))
                .ForPath(src => src.ReceiveAmount.Amount, opt => opt.MapFrom(dest => dest.MaxReceiveValue))
                .ForPath(src => src.ReceiveAmount.Currency, opt => opt.MapFrom(dest => SetCurrencySymbol(dest.MaxReceiveCurrencyIso)))
                .ForMember(src => src.Data, opt => opt.MapFrom(dest => ExtractFromProductExtension(dest.ProductDetailsJson, "Data")))
                .ForMember(src => src.Validity, opt => opt.MapFrom(dest => ExtractValidity(dest.ValidityPeriodIso)))
                .ForMember(src => src.TalkTime, opt => opt.MapFrom(dest => ExtractFromProductExtension(dest.ProductDetailsJson, "TalkTime")))
                .ForMember(src => src.IsHighlighted, opt => opt.MapFrom(dest => (dest.IsHighlighted == null || dest.IsHighlighted == false) ? false : true))
                .ForPath(src => src.TargetedDiscountAmount.Amount, opt => opt.MapFrom(dest => SetTargetedDiscountedAmount(dest.IsForTargetedDiscount, dest.MaxSendValue, dest.CustomFee)))
                .ForPath(src => src.TargetedDiscountAmount.Currency, opt => opt.MapFrom(dest => dest.MaxSendCurrencyIso))
                .ForPath(src => src.IsForBestValueHighlight, opt => opt.MapFrom(dest => dest.IsForBestValueExperiment));


            //mapping product estimate price
            CreateMap<Edenred.Common.Core.ProductEstimatePriceDingModel, C3Pay.API.Resources.MobileRecharge.ProductEstimatePriceResponseDto>()
                 .ForMember(src => src.ProductCode, opt => opt.MapFrom(dest => dest.ProductCode))
                 .ForPath(src => src.RechargeAmount.Amount, opt => opt.MapFrom(dest => dest.PricePayment.SendValue - dest.PricePayment.CustomerFee))
                 .ForPath(src => src.TotalSendAmount.Amount, opt => opt.MapFrom(dest => dest.PricePayment.SendValue))
                 .ForPath(src => src.TotalSendAmount.Currency, opt => opt.MapFrom(dest => dest.PricePayment.SendCurrencyIso))
                 .ForPath(src => src.RechargeAmount.Currency, opt => opt.MapFrom(dest => dest.PricePayment.SendCurrencyIso))
                 .ForPath(src => src.CustomerFee.Amount, opt => opt.MapFrom(dest => dest.PricePayment.CustomerFee))
                 .ForPath(src => src.CustomerFee.Currency, opt => opt.MapFrom(dest => dest.PricePayment.SendCurrencyIso))
                 .ForPath(src => src.ReceiveAmount.Amount, opt => opt.MapFrom(dest => dest.PricePayment.ReceiveValue))
                 .ForPath(src => src.ReceiveAmount.Currency, opt => opt.MapFrom(dest => dest.PricePayment.ReceiveCurrencyIso))
                 .ForPath(src => src.TaxAmount.Amount, opt => opt.MapFrom(dest => dest.PricePayment.TaxRate))
                 .ForPath(src => src.TaxAmount.Currency, opt => opt.MapFrom(dest => dest.PricePayment.SendCurrencyIso))
                 .ForPath(src => src.ReceiveAmountExcludingTax.Currency, opt => opt.MapFrom(dest => dest.PricePayment.ReceiveCurrencyIso))
                 .ForPath(src => src.ReceiveAmountExcludingTax.Amount, opt => opt.MapFrom(dest => dest.PricePayment.ReceiveValueExcludingTax));


            //Add Mobile Recharge Beneficiay
            CreateMap<C3Pay.API.Resources.MobileRecharge.PostMobileRechargeBeneficiaryRequestDto, C3Pay.Core.Models.MobileRechargeBeneficiary>()
               .ForMember(src => src.IsDeleted, opt => opt.Ignore())
               .ForMember(src => src.DeletedDate, opt => opt.Ignore())
               .ForMember(src => src.UpdatedDate, opt => opt.Ignore())
               .ForMember(src => src.CountryCode, opt => opt.MapFrom(dest => dest.Beneficiary.CountryCode.ToUpper()))
               .ForMember(src => src.NickName, opt => opt.MapFrom(dest => dest.Beneficiary.FullName))
               .ForMember(dest => dest.AccountNumber, opt => opt.MapFrom(dest => dest.Beneficiary.PhoneNumber.ToZeroPrefixedPhoneNumber()))
               .ForMember(src => src.RechargeType, opt => opt.MapFrom(dest => dest.Beneficiary.RechargeType));

            //Get Mobile Recharge Beneficiary list
            CreateMap<C3Pay.Core.Models.MobileRechargeBeneficiary, C3Pay.API.Resources.MobileRecharge.MobileRechargeBeneficiarieyDto>()
              .ForMember(src => src.BeneficiaryId, opt => opt.MapFrom(dest => dest.Id))
              .ForPath(src => src.CountryCode, opt => opt.MapFrom(dest => dest.CountryCode))
              .ForPath(src => src.CountryName, opt => opt.MapFrom(dest => dest.Country.Name))
              .ForPath(src => src.FullName, opt => opt.MapFrom(dest => dest.NickName))
              .ForPath(src => src.PhoneNumber, opt => opt.MapFrom(dest => dest.AccountNumber.ToZeroPrefixedPhoneNumber()))
              .ForMember(src => src.ProviderName, opt => opt.MapFrom(dest => dest.BeneficiaryProviders != null && dest.BeneficiaryProviders.Count > 0 ? dest.BeneficiaryProviders[0].Provider.Name : string.Empty))
              .ForMember(src => src.ProviderLogo, opt => opt.MapFrom(dest => dest.BeneficiaryProviders != null && dest.BeneficiaryProviders.Count > 0 ? dest.BeneficiaryProviders[0].Provider.LogoUrl : string.Empty))
              .ForPath(src => src.RechargeType, opt => opt.MapFrom(dest => dest.RechargeType))
              .ForMember(src => src.Status, opt => opt.MapFrom(dest => dest.Status))
              .ForMember(src => src.Remarks, opt => opt.MapFrom(dest => dest.Remarks));

            CreateMap<MobileRechargeBeneficiaryDetails, C3Pay.API.Resources.MobileRecharge.MobileRechargeBeneficiarieyDto>()
             .ForPath(src => src.PhoneNumber, opt => opt.MapFrom(dest => dest.PhoneNumber.ToZeroPrefixedPhoneNumber()))
             .ForPath(src => src.RechargeType, opt => opt.MapFrom(dest => dest.RechargeType.ToString()))
             .ForMember(src => src.Status, opt => opt.MapFrom(dest => dest.Status.ToString()));

            //Mobile recharge transaction request
            CreateMap<C3Pay.API.Resources.MobileRecharge.PostMobileRechargeRequestDto, C3Pay.Core.Models.MobileRechargeTransaction>()
              .ForMember(src => src.ProductCode, opt => opt.MapFrom(dest => dest.ProductCode))
              .ForMember(src => src.SendAmount, opt => opt.MapFrom(dest => dest.SendValue.Amount - dest.FeeValue.Amount))
              .ForMember(src => src.TotalAmount, opt => opt.MapFrom(dest => dest.SendValue.Amount))
              .ForMember(src => src.SendCurrency, opt => opt.MapFrom(dest => dest.SendValue.Currency))
              .ForMember(src => src.Fee, opt => opt.MapFrom(dest => dest.FeeValue.Amount))
              .ForPath(src => src.ReceiveAmount, opt => opt.MapFrom(dest => dest.ReceiveValue.Amount))
              .ForMember(src => src.ReceiveCurrency, opt => opt.MapFrom(dest => dest.ReceiveValue.Currency));


            //Mobile recharge transaction response
            CreateMap<C3Pay.Core.Models.CallingCardResponseDingModel, C3Pay.API.Resources.MobileRecharge.PostMobileRechargeResponseDto>();

            CreateMap<C3Pay.API.Resources.MobileRecharge.MobileRechargeRequestDto, C3Pay.Core.Models.MobileRechargeTransaction>();

            CreateMap<C3Pay.Core.Models.MobileRechargeProduct, C3Pay.API.Resources.MobileRecharge.MobileRechargeInlineProductDto>()
                .ForMember(src => src.ProductCode, opt => opt.MapFrom(dest => dest.Code))
                .ForMember(src => src.DisplayName, opt => opt.MapFrom(dest =>
                    !string.IsNullOrEmpty(dest.ValidityPeriodIso) && dest.Benefits != MobileRechargeType.CALLINGCARDS.ToString() ? dest.DefaultDisplayText.Trim() + ExtractDaysFromText(dest.ValidityPeriodIso)
                    : dest.DefaultDisplayText))
                .ForPath(src => src.ProviderLogoURL, opt => opt.MapFrom(dest => dest.Provider.LogoUrl))
                .ForPath(src => src.Description, opt => opt.MapFrom(dest => dest.AdditionalInformation))
                .ForPath(src => src.ProviderName, opt => opt.MapFrom(dest => dest.Provider.Name))
                .ForPath(src => src.CustomerCareNumber, opt => opt.MapFrom(dest => dest.Provider.CustomerCareNumber))
                .ForPath(src => src.OperatorName, opt => opt.MapFrom(dest => dest.ProviderCode == BaseEnums.CallingCardOperator.FUAE.ToString() ? dest.Provider.Name.Replace(" Intl VOIP Calling Card", "(Etisalat)") :
                                                                                       dest.ProviderCode == BaseEnums.CallingCardOperator.HAAE.ToString() ? dest.Provider.Name.Replace(" Intl VOIP Calling Card", "(Du)") : dest.Provider.Name))
                .ForMember(src => src.ProductType, opt => opt.MapFrom(dest => dest.Benefits))
                .ForMember(src => src.Region, opt => opt.MapFrom(dest => dest.SupportedCountry.Name))
                .ForMember(src => src.IsHighlighted, opt => opt.MapFrom(dest => (dest.IsHighlighted == null || dest.IsHighlighted == false) ? false : true))
                .ForMember(src => src.DescriptiveDisplayName, opt => opt.MapFrom(dest => dest.DescriptiveDisplayName));


            // Mobile Recharge Summary Response 
            CreateMap<MobileRechargeTransaction, C3Pay.API.Resources.MobileRecharge.MobileRechargeSummaryDto>()
                .ForPath(src => src.SendValue.Amount, opt => opt.MapFrom(dest => dest.SendAmount))
                .ForPath(src => src.SendValue.Currency, opt => opt.MapFrom(dest => ConstantParam.DefaultCurrency))
                .ForPath(src => src.FeeValue.Amount, opt => opt.MapFrom(dest => dest.Fee))
                .ForPath(src => src.FeeValue.Currency, opt => opt.MapFrom(dest => ConstantParam.DefaultCurrency))
                .ForPath(src => src.ReceiveValue.Amount, opt => opt.MapFrom(dest => dest.ReceiveAmount))
                .ForPath(src => src.ReceiveValue.Currency, opt => opt.MapFrom(dest => dest.ReceiveCurrency))
                .ForMember(dest => dest.TotalValue, opt => opt.MapFrom(dest => new AmountDto()
                {
                    Amount = (double)CalculateInAED(dest) - (double)dest.DiscountAmount,
                    //(dest.DiscountAmount > dest.Fee ? (dest.DiscountAmount != 0 ? (double) dest.DiscountAmount - (double) dest.Fee : 0) : 0) ,
                    Currency = ConstantParam.DefaultCurrency
                }))

                .ForMember(src => src.Discount, opt => opt.MapFrom(dest =>
                   dest.DiscountAmount == 0 ? null : new C3Pay.API.Resources.MobileRecharge.MobileRechargeDiscountDto()
                   {
                       Title = dest.UserDiscount.Discount.Title,
                       Description = dest.UserDiscount.Discount.Description,
                       LogoUrl = dest.UserDiscount.Discount.LogoUrl,
                       DiscountValue = new AmountDto()
                       {
                           Amount = dest.DiscountAmount > dest.Fee ? (double)-dest.DiscountAmount : 0,
                           Currency = ConstantParam.DefaultCurrency
                       },
                       DiscountedFeeValue = new AmountDto()
                       {
                           Amount = dest.DiscountAmount > dest.Fee ? 0 : (double)dest.Fee - (double)dest.DiscountAmount,
                           Currency = ConstantParam.DefaultCurrency
                       }
                   }))
                .ForMember(src => src.RenewalInfo, opt => opt.MapFrom(dest =>
                    dest.Product != null && (dest.Product.IsAutoRenewalEnabled ?? false) && !string.IsNullOrEmpty(dest.Product.ValidityPeriodIso)
                    ? new MobileRechargeRenewalInfo()
                    {
                        IsEligibleForRenewal = true,
                        Validity = ExtractValidity(dest.Product.ValidityPeriodIso),
                        RenewalDate = CalculateRenewalDate(dest.Product.ValidityPeriodIso)
                    }
                    : new MobileRechargeRenewalInfo()
                    {
                        IsEligibleForRenewal = false,
                    }
                    ))
                .ForMember(src => src.TargetedDiscount, opt => opt.MapFrom(dest =>
                dest.TargetedDiscountAmount != 0.0M ?
                new MobileRechargeTargetedDiscountDto()
                {
                    DiscountAmountValue = new AmountDto()
                    {
                        Amount = (double)dest.TargetedDiscountAmount,
                        Currency = ConstantParam.DefaultCurrency
                    },
                    DiscountedTotalValue = new AmountDto()
                    {
                        Amount = (double)dest.TotalAmount - (double)dest.TargetedDiscountAmount,
                        Currency = ConstantParam.DefaultCurrency
                    }
                }
                :
                null
                ));

            CreateMap<C3Pay.Core.Models.MobileRechargeRenewal, C3Pay.API.Resources.MobileRecharge.MobileRechargeRenewalDetailDto>()
                .ForPath(src => src.ProviderLogoUrl, opt => opt.MapFrom(m => m.MobileRechargeProduct.Provider.LogoUrl))
                .ForMember(src => src.BeneficiaryId, opt => opt.MapFrom(m => m.BeneficiaryId))
                .ForPath(src => src.BeneficiaryPhoneNumber, opt => opt.MapFrom(m => m.MobileRechargeBeneficiary.AccountNumber))
                .ForPath(src => src.BeneficiaryFullname, opt => opt.MapFrom(m => m.MobileRechargeBeneficiary.NickName))
                .ForPath(src => src.ProductCode, opt => opt.MapFrom(m => m.MobileRechargeProduct.Code))
                .ForPath(src => src.ProductDescription, opt => opt.MapFrom(m => string.IsNullOrEmpty(m.MobileRechargeProduct.DescriptiveDisplayName) ? "" : m.MobileRechargeProduct.DescriptiveDisplayName))
                .ForMember(src => src.Status, opt => opt.MapFrom(m => m.InactiveCode != null ? ((MobileRecharge_AutoRenewalInactiveCode)m.InactiveCode).ToString() : ""))
                .ForMember(src => src.NextRenewalDate, opt => opt.MapFrom(m => m.RenewalDate))
                .ForPath(src => src.TotalPrice, opt => opt.MapFrom(m => m.MobileRechargeProduct.MaxSendValue ?? 0 + (m.MobileRechargeProduct.CustomFee > 0 ? m.MobileRechargeProduct.CustomFee : m.MobileRechargeProduct.MaxCustomerFee)));



            //Mobile Recharge transactions
            CreateMap<C3Pay.Core.Models.MobileRechargeTransaction, C3Pay.API.Resources.MobileRecharge.MobileRechargeDto>()
                 .ForMember(src => src.Id, opt => opt.MapFrom(dest => dest.Id))
                 //.ForMember(src => src.BeneficiaryFullName, opt => opt.MapFrom(dest => dest.MobileRechargeBeneficiary != null ? dest.MobileRechargeBeneficiary.NickName : dest.User.FirstName + " " + dest.User.LastName))
                 .ForMember(src => src.BeneficiaryFullName, opt => opt.MapFrom(dest => dest.Beneficiary != null ? dest.Beneficiary.NickName : "Myself"))
                 .ForPath(src => src.RechargeAmount.Amount, opt => opt.MapFrom(dest => dest.SendAmount))
                 .ForPath(src => src.RechargeAmount.Currency, opt => opt.MapFrom(dest => dest.SendCurrency))
                 .ForMember(src => src.RechargeType, opt => opt.MapFrom(dest => SetRechargeMethodType(dest.Product.Provider)))
                 .ForPath(src => src.FeeAmount.Currency, opt => opt.MapFrom(dest => dest.SendCurrency))
                 .ForPath(src => src.FeeAmount.Amount, opt => opt.MapFrom(dest => (dest.DiscountAmount ?? 0) > dest.Fee ? 0 : dest.Fee - (dest.DiscountAmount ?? 0)))
                 .ForPath(src => src.TotalAmount.Amount, opt => opt.MapFrom(dest => dest.TotalAmount))
                 .ForPath(src => src.SecretCode, opt => opt.MapFrom(dest => dest.ExternalTransaction.CallingCardPin))
                 .ForPath(src => src.TotalAmount.Currency, opt => opt.MapFrom(dest => dest.SendCurrency))
                 .ForPath(src => src.ReceiveAmount.Amount, opt => opt.MapFrom(dest => dest.ReceiveAmount))
                 .ForPath(src => src.ReceiveAmount.Currency, opt => opt.MapFrom(dest => dest.ReceiveCurrency))
                 .ForPath(src => src.MobileNumber, opt => opt.MapFrom(dest => dest.AccountNumber.ToZeroPrefixedPhoneNumber()))
                 .ForPath(src => src.Operator, opt => opt.MapFrom(dest => dest.Product.Provider.Name))
                 .ForMember(src => src.Date, opt => opt.MapFrom(dest => dest.CreatedDate))
                 .ForMember(src => src.Status, opt => opt.MapFrom(dest => dest.Status));

            CreateMap<MobileRechargeDetails, C3Pay.API.Resources.MobileRecharge.MobileRechargeDto>()
                .ForPath(dest => dest.RechargeAmount.Amount, opt => opt.MapFrom(src => src.RechargeAmount))
                .ForPath(dest => dest.RechargeAmount.Currency, opt => opt.MapFrom(src => src.RechargeCurrency))

                .ForPath(dest => dest.FeeAmount.Amount, opt => opt.MapFrom(src => src.DiscountAmount > src.FeeAmount ? 0 : src.FeeAmount - src.DiscountAmount))
                .ForPath(dest => dest.FeeAmount.Currency, opt => opt.MapFrom(src => src.FeeCurrency))

                .ForPath(dest => dest.TotalAmount.Amount, opt => opt.MapFrom(src => src.TotalAmount))
                .ForPath(dest => dest.TotalAmount.Currency, opt => opt.MapFrom(src => src.TotalCurrency))

                .ForPath(dest => dest.ReceiveAmount.Amount, opt => opt.MapFrom(src => src.ReceiveAmount))
                .ForPath(dest => dest.ReceiveAmount.Currency, opt => opt.MapFrom(src => src.ReceiveCurrency))

                .ForPath(dest => dest.RechargeType, opt => opt.MapFrom(src => src.RechargeType.ToTileCase()))
                .ForPath(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));

            CreateMap<MoneyTransferDetails, MoneyTransferDto>()
               .ForPath(dest => dest.SendAmount.Amount, opt => opt.MapFrom(src => src.SendAmount))
               .ForPath(dest => dest.SendAmount.Currency, opt => opt.MapFrom(src => src.SendCurrency))

               .ForPath(dest => dest.ReceiveAmount.Amount, opt => opt.MapFrom(src => src.ReceiveAmount))
               .ForPath(dest => dest.ReceiveAmount.Currency, opt => opt.MapFrom(src => src.ReceiveCurrency))

               .ForPath(dest => dest.TransferMethod, opt => opt.MapFrom(src => src.TransferMethod))
               .ForPath(dest => dest.Status, opt => opt.MapFrom(dest => CastToMoneyTransferStatus(dest.Status)))
               .ForPath(dest => dest.BeneficiaryPhoneNumber, opt => opt.MapFrom(src => src.BeneficiaryPhoneNumber))
               .ForPath(dest => dest.CashPickupTransferDetailsDto.CashPickupPin, opt => opt.MapFrom(src => src.CashPickupPin))
               .AfterMap((src, dest, context) =>
                {
                    // If the user has the same first name and last name, and the transfer type, then only show 1 name.
                    // This is because the wallet transfer page has only one input (full name).
                    if (dest.TransferMethod == TransferMethod.WALLET.ToString())
                    {
                        var nameParts = dest.BeneficiaryFullName.Split(' ');
                        if (nameParts.First() == nameParts.Last())
                        {
                            dest.BeneficiaryFullName = nameParts.First();
                        }
                    }
                });

            CreateMap<TransactionStatusVideo, StatusVideoDto>();
            CreateMap<MoneyTransferStatusStep, MoneyTransferStatusStepDto>()
                .ForPath(dest => dest.Status, opt => opt.MapFrom(src => CastToMoneyTransferStepStatus(src.Status)));

            //Popups
            CreateMap<Popup, PopupDto>();

            //Mobile Recharge transactions
            CreateMap<CardNumberEligibilityResult, MobileApplicationEligibilityResponseDto>()
                .ForMember(src => src.CardPlasticType, opt => opt.MapFrom(dest => (int)dest.CardPlasticType))
                .ForMember(src => src.CardSerialNumber, opt => opt.MapFrom(dest => dest.CardSerialNumber))
                .ForMember(src => src.Eligibility, opt => opt.MapFrom(dest => dest.CardEligibilityResult.ToString()));

            CreateMap<EmployeeLeaveBalanceDto, LeaveBalanceDto>()
                            .ForPath(dest => dest.Type.Id, opt => opt.MapFrom(src => src.LeaveTypeId))
                            .ForPath(dest => dest.Type.Name, opt => opt.MapFrom(src => src.LeaveTypeName));

            CreateMap<EmployeeLeaveDto, LeaveReadDto>()
                            .ForMember(dest => dest.TypeName, opt => opt.MapFrom(src => src.LeaveTypeName))
                            .ForMember(dest => dest.Duration, opt => opt.MapFrom(src => (src.EndDate - src.StartDate).Days + 1));

            CreateMap<MoneyTransferBank, MoneyTransferBankDto>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name.ToTileCase()))
                .ForMember(src => src.AccountNumberLengthLimitValues, opt => opt.MapFrom(dest => MapToAccountNumberLengthLimitValues(dest.AccountNumberLengthLimitValues)));


            CreateMap<MoneyTransferBranch, MoneyTransferBranchDto>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name.ToTileCase()))
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.PrimaryIdentifierCode))
                .ForMember(dest => dest.Address, opt => opt.MapFrom(src => string.Join(", ", new string[] { src.PrimaryAddress, src.SecondaryAddress }.Where(a => !string.IsNullOrEmpty(a))).ToTileCase()));

            CreateMap<MoneyTransferBranch, BankBranchDto>()
                .ForMember(dest => dest.BankId, opt => opt.MapFrom(src => src.BankId))
                .ForMember(dest => dest.BankCode, opt => opt.MapFrom(src => src.PrimaryIdentifierCode))
                .ForMember(dest => dest.BankName, opt => opt.MapFrom(src => src.Bank.Name))
                .ForMember(dest => dest.BranchId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.BankBranchCode, opt => opt.MapFrom(src => src.SecondaryIdentifierCode))
                .ForMember(dest => dest.BankBranchName, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Address1, opt => opt.MapFrom(src => src.PrimaryAddress))
                .ForMember(dest => dest.Address2, opt => opt.MapFrom(src => src.SecondaryAddress))
                .ForPath(dest => dest.BankLogoUrl, opt => opt.MapFrom(src => src.Bank.LogoUrl))
                .ForPath(dest => dest.AccountNumberLengthLimitType, opt => opt.MapFrom(src => src.Bank.AccountNumberLengthLimitType))
                .ForPath(dest => dest.AccountNumberLengthLimitValues, opt => opt.MapFrom(src => MapToAccountNumberLengthLimitValues(src.Bank.AccountNumberLengthLimitValues)));


            //Languages
            CreateMap<SupportedLanguagesDto, Language>().ReverseMap();

            CreateMap<VerifyLoggedInPinRequestDto, VerifyUserPinCommand>()
                .ForMember(dest => dest.IsLoggedIn, opt => opt.MapFrom(src => true));

            CreateMap<VerifyLoggedOutPinRequestDto, VerifyUserPinCommand>()
                .ForMember(dest => dest.IsLoggedIn, opt => opt.MapFrom(src => false));

            CreateMap<VerifyLoggedOutCardRequestDto, VerifyUserCardCommand>()
                .ForMember(dest => dest.IsLoggedIn, opt => opt.MapFrom(src => false));

            CreateMap<VerifyLoggedInPinRequestDto, VerifyUserCardCommand>()
               .ForMember(dest => dest.IsLoggedIn, opt => opt.MapFrom(src => true));

            CreateMap<VerifyLoggedOutPinRequestDto, VerifyUserCardCommand>()
               .ForMember(dest => dest.IsLoggedIn, opt => opt.MapFrom(src => false));

            CreateMap<VerifyLoggedInPinRequestDto, VerifyUserCardCommand>()
               .ForMember(dest => dest.IsLoggedIn, opt => opt.MapFrom(src => true));

            CreateMap<VerifyLoggedOutPinRequestDto, VerifyUserCardCommand>()
               .ForMember(dest => dest.IsLoggedIn, opt => opt.MapFrom(src => false));

            CreateMap<CardHolderDetailsDto, CardHolder>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.CitizenId))
                .ForMember(dest => dest.CardNumber, opt => opt.MapFrom(src => src.CardNumber.Remove(5, 6).Insert(6, "******")))
                .ForMember(dest => dest.Gender, opt => opt.MapFrom(src => src.Gender == "M" ? BaseEnums.Gender.Male : BaseEnums.Gender.Female));

            CreateMap<CardHolder, CardHoldersDto>()
                .ForMember(dest => dest.CardHolderId, opt => opt.MapFrom(src => src.Id));


            //EmiratesIdLog
            CreateMap<EmiratesIdLogDto, EmiratesIdLog>();
            CreateMap<PassportLogDto, PassportLog>();

            //MoneyTransfer Multimedia
            CreateMap<MultimediaResource, MultimediaDto>();

            //Experiment
            CreateMap<ExperimentUsers, ExperimentDto>()
               .ForMember(dest => dest.FeatureName, opt => opt.MapFrom(src => src.Experiment.Feature.Name))
               .ForMember(dest => dest.Ctas, opt => opt.MapFrom(src => src.Experiment.ExperimentCtas))
               .ForMember(dest => dest.Multimedias, opt => opt.MapFrom(src => src.Experiment.ExperimentGroupMultimedias));

            CreateMap<ExperimentCta, ExperimentCtaDto>();
            CreateMap<ExperimentGroupMultimedia, ExperimentGroupMultimediaDto>();


            //RepeatTransfer
            CreateMap<MoneyTransferTransaction, RepeatTransfer>()
               .ForMember(dest => dest.TransferType, opt => opt.MapFrom(src => (TransferMethod)src.MoneyTransferBeneficiary.TransferType))
               .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.MoneyTransferBeneficiary.FullName))
               .ForMember(dest => dest.CountryCode, opt => opt.MapFrom(src => src.MoneyTransferBeneficiary.CountryCode));

            //SuspiciousInformation
            CreateMap<MoneyTransferSuspiciousInformationDto, MoneyTransferSuspiciousInformation>().ReverseMap();
            CreateMap<SaveMoneyTransferSuspiciousInformationDto, MoneyTransferSuspiciousInformation>();
            CreateMap<UpdateMoneyTransferSuspiciousInformationDto, MoneyTransferSuspiciousInformation>();

            CreateMap<State, LocationDto>()
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Name));

            CreateMap<City, LocationDto>()
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Name));

            CreateMap<C3PayPlusMembershipLifeInsuranceNomineeRequestDto, C3PayPlusMembershipLifeInsuranceNominee>()
                .ForMember(dest => dest.C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeId, opt => opt.MapFrom(src => src.RelationshipTypeId));
            CreateMap<C3PayPlusMembershipLifeInsuranceNominee, C3PayPlusMembershipLifeInsuranceNomineeResponseDto>()
                                .ForMember(dest => dest.RelationshipTypeId, opt => opt.MapFrom(src => src.C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeId));
            CreateMap<C3PayPlusMembershipLifeInsuranceNomineeRelationshipType, C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeDto>();
            CreateMap<C3PayPlusMembershipSupportedCountry, C3PayPlusMembershipSupportedCountryDto>();
            CreateMap<LoginVideo, LoginVideoCached>();
            CreateMap<LoginVideoCached, LoginVideoResponseDto>().ForMember(dest => dest.Videos, opt => opt.MapFrom(src => src.VideoResources));
            CreateMap<LoginVideoResourceCached, LoginVideoResourceDto>();

            //Mapping for Money Transfer Dynamic Screens
            CreateMap<MoneyTransferScreen, MoneyTransferScreenDto>()
                .ForMember(dest => dest.ScreenIdentifier, opt => opt.MapFrom(src => src.Identifier))
                .ForMember(dest => dest.HelpMetaData, opt => opt.MapFrom(src => src.HelpMetaData))
                .ForMember(dest => dest.ApiIdentifier, opt => opt.MapFrom(src => src.Metadata != null ? src.Metadata.ApiIdentifier : null))
                .ForMember(dest => dest.Elements, opt => opt.MapFrom(src => src.Elements))
                .ForMember(dest => dest.ToBePersistedElementIdentifiers, opt => opt.MapFrom(src => src.ToBePersistedElementIdentifiers));

            CreateMap<MoneyTransferScreenElement, MoneyTransferScreenElementDto>()
                .ForMember(dest => dest.ElementIdentifier, opt => opt.MapFrom(src => src.Identifier));

            CreateMap<ElementMetaData, ElementMetaDataDto>();
            CreateMap<TextBoxElement, TextBoxElementDto>()
                .ForMember(dest => dest.AllowedCharsType, opt => opt.MapFrom(src => GetAllowedCharsType(src.AllowedChars)));
            CreateMap<PhoneNumberElement, PhoneNumberElementDto>()
                .ForMember(dest => dest.AllowedCharsType, opt => opt.MapFrom(src => GetAllowedCharsType(src.AllowedChars)));
            CreateMap<InfoBarElement, InfoBarElementDto>();
            CreateMap<ButtonElement, ButtonElementDto>();
            CreateMap<ListItemElement, ListItemElementDto>();
            CreateMap<ListElement, ListElementDto>();
            CreateMap<SearchTextBoxElement, SearchTextBoxElementDto>()
             .ForMember(dest => dest.SearchKeys, opt => opt.MapFrom(src => src.Search));
            CreateMap<LogoWithLabelElement, LogoWithLabelElementDto>()
                .ForMember(dest => dest.LogoUrl, opt => opt.MapFrom(src => src.Logo));
            CreateMap<NavigateTo, NavigateToElementDto>();
            CreateMap<C3Pay.Core.Models.C3Pay.MoneyTransfer.Group, GroupElementDto>();
            CreateMap<Condition, ConditionElementDto>();
            CreateMap<VideoDetails, VideoDetailsElementDto>();
            CreateMap<NavigationCondition, NavigationConditionElementDto>();
            CreateMap<HelpMetaData, HelpMetaDataDto>();


            //Lookups
            CreateMap<Province, ProvinceDto>()
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Name));

            CreateMap<District, LocationDto>()
                  .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Name));

            CreateMap<C3Pay.API.Resources.MobileRecharge.MobileRechargeValidateRequestDto, C3Pay.Core.Models.MobileRechargeTransaction>();
            CreateMap<Common.Core.Models.ValidateSendTransferResponseDto, C3Pay.API.Resources.MobileRecharge.MobileRechargeValidateResponseDto>();


            #region Sign-up
            CreateMap<SignUpCountry, SignUpCountryDto>();
            #endregion
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transferMethod"></param>
        /// <returns></returns>
        private MoneyTransferType MapFromTransferMethodToTransferType(string transferMethod)
        {
            transferMethod = transferMethod.Trim().ToUpper();

            if (transferMethod == TransferMethod.BANKTRANSFER.ToString())
            {
                return MoneyTransferType.OutsideUAE;
            }

            else if (transferMethod == TransferMethod.CASHPICKUP.ToString())
            {
                return MoneyTransferType.RAKMoneyCashPayout;
            }

            else if (transferMethod == TransferMethod.DIRECTTRANSFER.ToString())
            {
                return MoneyTransferType.DirectTransfer;
            }

            else throw new Exception(TransferStatusValidationMessage.TransferMethodNotExists.ToString());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="limitValues"></param>
        /// <returns></returns>
        private List<string> MapToAccountNumberLengthLimitValues(string limitValues)
        {
            return limitValues?.Split(',').ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transferType"></param>
        /// <returns></returns>
        private string MapFromTransferTypeToTransferMethod(MoneyTransferType transferType)
        {
            switch (transferType)
            {
                case MoneyTransferType.RAKMoneyCashPayout:
                    return TransferMethod.CASHPICKUP.ToString();
                case MoneyTransferType.OutsideUAE:
                    return TransferMethod.BANKTRANSFER.ToString();
                case MoneyTransferType.DirectTransfer:
                    return TransferMethod.DIRECTTRANSFER.ToString();
                case MoneyTransferType.Wallet:
                    return TransferMethod.WALLET.ToString();
                default: throw new Exception(TransferStatusValidationMessage.TransferMethodNotExists.ToString());
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        public static string CastToMoneyTransferStatus(Status status)
        {
            // C3 to C3
            if (status == Status.CANCELED || status == Status.AUTOCANCELED)
            {
                return Status.REVERSED.ToTileCase();
            }

            // Auto rev
            if (status == Status.FAILEDTOREVERSE || status == Status.PENDINGREVERSE || status == Status.NEEDSMANUALREVERSAL)
            {
                return Status.FAILED.ToTileCase();
            }

            if (status == Status.REVERSED)
            {
                return Status.REVERSED.ToTileCase();
            }

            if (status == Status.ONHOLD)
            {
                return Status.PENDING.ToTileCase();
            }

            else return status.ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        public static string CastToMoneyTransferStepStatus(Status status)
        {
            var desc = EnumUtility.GetDescriptionFromEnumValue(status);
            return desc.ToUpper();
        }

        private decimal CalculateInAED(MobileRechargeTransaction transaction)
        {
            decimal exchangeRate = (decimal)transaction.ReceiveAmount / (decimal)(transaction.SendAmount + (decimal)transaction.Fee);
            decimal receiveValueInAED = (decimal)transaction.ReceiveAmount / exchangeRate;
            decimal roundedValue = Math.Round(receiveValueInAED / 0.01m, MidpointRounding.AwayFromZero) * 0.01m;
            return roundedValue;
        }
        public static string ExtractDaysFromText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;
            // Remove the 'P' and 'D' characters from the input text to keep only the numeric part
            string numericPart = text.Replace("P", "").Replace("D", "");

            // Parse the numeric part to an integer
            if (int.TryParse(numericPart, out int days))
            {
                return $" | Valid for {days} Days";
            }
            else
            {
                return string.Empty;
            }
        }

        private static string SetRechargeMethodType(MobileRechargeProvider provider)
        {
            if (provider.CountryCode.ToString().ToUpper() == ConstantParam.DefaultCountryCode)
            {
                if (provider.Code == BaseEnums.LocalOperator.E6AE.ToString() || provider.Code == BaseEnums.LocalOperator.ETAE.ToString())
                    return BaseEnums.MobileRechargeType.LOCAL.ToTileCase();
                else if (provider.Code == BaseEnums.CallingCardOperator.VRAE.ToString())
                    return BaseEnums.MobileRechargeType.LOCALCALLINGCARDS.ToTileCase();
                else
                    return BaseEnums.MobileRechargeType.CALLINGCARDS.ToTileCase().Replace("c", "C"); //Return as CallingCards
            }
            else
                return BaseEnums.MobileRechargeType.INTERNATIONAL.ToTileCase();
        }

        private static string ExtractFromProductExtension(string productExtensionJson, string propertyName)
        {
            var extract = JsonConvert.DeserializeObject<MobileRechargeProductExtensionDto>(productExtensionJson);
            var property = extract.GetType().GetProperty(propertyName);
            return property?.GetValue(extract)?.ToString();
        }

        public static string ExtractValidity(string validityPeriodIso)
        {
            //Return Lifetime if Validity is not available
            if (string.IsNullOrEmpty(validityPeriodIso))
                return ConstantParam.DefaultMRProductValidity;

            // Define a regular expression pattern to match "P" followed by digits and "D" or "M"
            string pattern = @"P(\d+)([DM])"; // Added ([DM]) to capture the last character

            // Use Regex.Match to find the first match in the input string
            Match match = Regex.Match(validityPeriodIso, pattern);

            if (match.Success)
            {
                string number = match.Groups[1].Value;
                string lastChar = match.Groups[2].Value;

                if (lastChar.Equals("D"))
                    return string.Format("{0} Days", number);
                else
                    return string.Format("{0} Months", number);
            }
            else
                return ConstantParam.DefaultMRProductValidity;
        }

        private static DateTime CalculateRenewalDate(string validityPeriodIso)
        {
            // Define a regular expression pattern to match "P" followed by digits and "D" or "M"
            string pattern = @"P(\d+)([DM])"; // Added ([DM]) to capture the last character

            // Use Regex.Match to find the first match in the input string
            Match match = Regex.Match(validityPeriodIso, pattern);

            if (match.Success)
            {
                string number = match.Groups[1].Value;
                string lastChar = match.Groups[2].Value;

                if (double.TryParse(number, out double days))
                {
                    if (lastChar.Equals("D"))
                    {
                        return System.DateTime.Now.AddDays(days);
                    }
                    else if (lastChar.Equals("M"))
                    {
                        return System.DateTime.Now.AddMonths((int)days);
                    }
                }
            }
            return DateTime.Now;
        }

        private static string SetDescription(string additionalInformation, string displayName)
        {
            if (!string.IsNullOrEmpty(additionalInformation))
                return additionalInformation;
            else
                return displayName;
        }
        private static string SetCurrencySymbol(string currency)
        {
            switch (currency)
            {
                case "INR":
                    return "₹";

                case "PKR":
                    return "Rs";

                case "LKR":
                    return "Rs";

                case "PHP":
                    return "₱";

                case "BDT":
                    return "৳";

                case "NPR":
                    return "रू";

                default:
                    return currency;
            }
        }

        private static decimal? SetTargetedDiscountedAmount(bool? isTargetedForDiscount, decimal? maxSendValue, decimal? customFee)
        {
            // If the discount is targeted and maxSendValue has a value
            if (isTargetedForDiscount.GetValueOrDefault(true) && maxSendValue.HasValue)
            {
                decimal discountRate = 20.0M / 100.0M; // Discount rate of 20%
                decimal effectiveFee = customFee.GetValueOrDefault(maxSendValue.Value);

                return maxSendValue.Value - ((maxSendValue.Value - effectiveFee) * discountRate);
            }

            return 0.0M;
        }

        private AllowedCharsType GetAllowedCharsType(string allowedChars)
        {
            if (string.IsNullOrEmpty(allowedChars))
                return AllowedCharsType.Any;


            return allowedChars.Trim().ToLowerInvariant() switch
            {
                "latin,space" => AllowedCharsType.AlphaLatin,
                "latin,digit" => AllowedCharsType.AlphaNumeric,
                "digit" => AllowedCharsType.Numeric,
                "latin,digit,space" => AllowedCharsType.AlphaNumeric,
                "latin" => AllowedCharsType.AlphaLatin,
                "space,latin" => AllowedCharsType.AlphaLatin,
                _ => AllowedCharsType.Any
            };
        }

    }
}