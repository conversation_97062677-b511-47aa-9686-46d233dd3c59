﻿using Azure.Monitor.OpenTelemetry.AspNetCore;
using Azure.Monitor.OpenTelemetry.Exporter;
using Edenred.Common.Services.Azure.Extensions;
using Edenred.Common.Services.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using OpenTelemetry;
using OpenTelemetry.Exporter;
using OpenTelemetry.Instrumentation.AspNetCore;
using OpenTelemetry.Instrumentation.Http;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;

namespace C3Pay.API.StartupExtensions
{
    public static class LoggingExtension
    {
        public static void InjectLogging(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure OpenTelemetry with Azure Monitor
            var connectionString = configuration["ApplicationInsights:ConnectionString"];
            var serviceName = configuration["OpenTelemetry:ServiceName"] ?? "C3Pay.API";
            var serviceVersion = configuration["OpenTelemetry:ServiceVersion"] ?? "1.0.0";

            // Only configure Azure Monitor if connection string is provided
            if (!string.IsNullOrEmpty(connectionString))
            {
                // Configure OpenTelemetry with Azure Monitor
                services.AddOpenTelemetry()
                    .ConfigureResource(resource => resource
                        .AddService(serviceName, serviceVersion))
                    .UseAzureMonitor(options =>
                    {
                        options.ConnectionString = connectionString;
                    })
                    .WithTracing(builder =>
                    {
                        builder
                            .AddAspNetCoreInstrumentation(options => AddEnrichOptionAspNetCore(options))
                            .AddHttpClientInstrumentation(options => AddEnrichOptionHttpClient(options))
                            .AddSqlClientInstrumentation(options =>
                            {
                                options.SetDbStatementForText = true;
                                options.SetDbStatementForStoredProcedure = true;
                                options.RecordException = true;
                            });
                    })
                    .WithMetrics(builder =>
                    {
                        builder
                            .AddAspNetCoreInstrumentation()
                            .AddHttpClientInstrumentation();
                    });
            }
            else
            {
                // Configure basic OpenTelemetry without Azure Monitor for local development
                services.AddOpenTelemetry()
                    .ConfigureResource(resource => resource
                        .AddService(serviceName, serviceVersion))
                    .WithTracing(builder =>
                    {
                        builder
                            .AddAspNetCoreInstrumentation(options =>
                            {
                                AddEnrichOptionAspNetCore(options);
                            })
                            .AddHttpClientInstrumentation(options =>
                            {
                                AddEnrichOptionHttpClient(options);
                            })
                            .AddSqlClientInstrumentation(options =>
                            {
                                options.SetDbStatementForText = true;
                                options.SetDbStatementForStoredProcedure = true;
                                options.RecordException = true;
                            })
                            .AddConsoleExporter(); // Add console exporter for local development
                    })
                    .WithMetrics(builder =>
                    {
                        builder
                            .AddAspNetCoreInstrumentation()
                            .AddHttpClientInstrumentation()
                            .AddConsoleExporter(); // Add console exporter for local development
                    });
            }

            // Configure Serilog with OpenTelemetry
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .Enrich.FromLogContext()
                .CreateLogger();

            services.AddLogging(options =>
            {
                options.AddSerilog();
                
                // Only add Azure Monitor log exporter if connection string is available
                if (!string.IsNullOrEmpty(connectionString))
                {
                    options.AddOpenTelemetry(builder =>
                    {
                        builder.SetResourceBuilder(ResourceBuilder.CreateDefault()
                            .AddService(serviceName, serviceVersion));
                        builder.AddAzureMonitorLogExporter(options =>
                        {
                            options.ConnectionString = connectionString;
                        });
                    });
                }
            });

            services.AddScoped<RequestBodyLoggingMiddleware>();
            services.AddScoped<ResponseBodyLoggingMiddleware>();
            services.AddScoped<Middleware.RequestHeaderLoggingMiddleware>();
        }

        private static void AddEnrichOptionAspNetCore(AspNetCoreTraceInstrumentationOptions options)
        {
            options.RecordException = true;

            options.EnrichWithHttpRequest = (activity, httpRequest) =>
            {
                // Only read if it's a POST/PUT etc. and content exists
                if (httpRequest.ContentLength > 0 &&
                    httpRequest.Body.CanSeek)
                {
                    httpRequest.Body.Position = 0;
                    using var reader = new StreamReader(httpRequest.Body, leaveOpen: true);
                    var body = reader.ReadToEnd();
                    httpRequest.Body.Position = 0; // Reset stream for downstream middleware
                    activity.SetTag("http.request.body", body);
                }
            };

            options.EnrichWithHttpResponse = (activity, httpResponse) =>
            {
                if (httpResponse.Body.CanSeek)
                {
                    httpResponse.Body.Position = 0;
                    using var reader = new StreamReader(httpResponse.Body, leaveOpen: true);
                    var body = reader.ReadToEnd();
                    httpResponse.Body.Position = 0;
                    activity.SetTag("http.response.body", body);
                }

                activity.SetTag("http.status_code", httpResponse.StatusCode);
            };
        }

        private static void AddEnrichOptionHttpClient(HttpClientTraceInstrumentationOptions options)
        {
            // Add request metadata
            options.EnrichWithHttpRequestMessage = (activity, request) =>
            {
                activity.SetTag("http.client.method", request.Method.Method);
                activity.SetTag("http.client.url", request.RequestUri?.ToString());

                foreach (var header in request.Headers)
                {
                    var key = $"http.client.request.header.{header.Key.ToLower()}";
                    var value = string.Join(",", header.Value);
                    activity.SetTag(key, value);
                }


                if (request.Content != null)
                {
                    var body = request.Content.ReadAsStringAsync().Result;
                    activity.SetTag("http.client.request.body", body);
                }
            };

            // Add response metadata
            options.EnrichWithHttpResponseMessage = (activity, response) =>
            {
                foreach (var header in response.Headers)
                {
                    var key = $"http.client.response.header.{header.Key.ToLower()}";
                    var value = string.Join(",", header.Value);
                    activity.SetTag(key, value);
                }

                // Include response content headers if needed
                if (response.Content != null)
                {
                    foreach (var header in response.Content.Headers)
                    {
                        var key = $"http.client.response.header.{header.Key.ToLower()}";
                        var value = string.Join(",", header.Value);
                        activity.SetTag(key, value);
                    }
                }

                activity.SetTag("http.client.status_code", (int)response.StatusCode);
                if (response.Content != null)
                {
                    var body = response.Content.ReadAsStringAsync().Result;
                    activity.SetTag("http.client.response.body", body);
                }
            };

            // Log exceptions if they occur
            options.EnrichWithException = (activity, exception) =>
            {
                activity.SetTag("http.client.exception", exception.Message);
            };
        }

        private static string GetRequestBody(HttpRequest request)
        {
            try
            {
                if (request.Body.CanRead && request.ContentLength > 0)
                {
                    request.EnableBuffering();
                    request.Body.Position = 0;
                    using var reader = new System.IO.StreamReader(request.Body, leaveOpen: true);
                    var body = reader.ReadToEndAsync().Result;
                    request.Body.Position = 0;
                    return body;
                }
            }
            catch
            {
                // Ignore errors when reading request body
            }
            return null;
        }

        private static string GetResponseBody(HttpResponse response)
        {
            try
            {
                if (response.Body.CanRead && response.ContentLength > 0)
                {
                    response.Body.Position = 0;
                    using var reader = new System.IO.StreamReader(response.Body, leaveOpen: true);
                    var body = reader.ReadToEndAsync().Result;
                    response.Body.Position = 0;
                    return body;
                }
            }
            catch
            {
                // Ignore errors when reading response body
            }
            return null;
        }
    }
}
