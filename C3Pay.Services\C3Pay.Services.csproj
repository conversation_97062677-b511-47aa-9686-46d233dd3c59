﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Mapping\MoneyTransfer\**" />
	  <Compile Remove="MoneyTransfer\MoneyTransferReasons\**" />
	  <EmbeddedResource Remove="Mapping\MoneyTransfer\**" />
	  <EmbeddedResource Remove="MoneyTransfer\MoneyTransferReasons\**" />
	  <None Remove="Mapping\MoneyTransfer\**" />
	  <None Remove="MoneyTransfer\MoneyTransferReasons\**" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="Helper\Templates\PushNotification\BillPaymentAmountDue.txt" />
		<None Remove="Helper\Templates\PushNotification\BillPaymentBillerUnavailable.txt" />
		<None Remove="Helper\Templates\PushNotification\BillPaymentBillExpired.txt" />
		<None Remove="Helper\Templates\PushNotification\BillPaymentBillPaid.txt" />
		<None Remove="Helper\Templates\PushNotification\BillPaymentCardInsufficientBalance.txt" />
		<None Remove="Helper\Templates\PushNotification\BillPaymentDeleteAndAddAgain.txt" />
		<None Remove="Helper\Templates\PushNotification\BillPaymentInvalidDetails.txt" />
		<None Remove="Helper\Templates\PushNotification\BillPaymentNoBill.txt" />
		<None Remove="Helper\Templates\PushNotification\BillPaymentNoDueAtPayment.txt" />
		<None Remove="Helper\Templates\PushNotification\BillPaymentPaymentFailed.txt" />
		<None Remove="Helper\Templates\PushNotification\C3PayPlusBalanceEnquiryRefund.txt" />
		<None Remove="Helper\Templates\PushNotification\C3PayPlusSmsRefund.txt" />
		<None Remove="Helper\Templates\PushNotification\ClaimedDirectTransferToReceiverTemplate.txt" />
		<None Remove="Helper\Templates\PushNotification\ClaimedDirectTransferToSenderTemplate.txt" />
		<None Remove="Helper\Templates\PushNotification\InstantDirectTransferTemplate.txt" />
		<None Remove="Helper\Templates\PushNotification\ReferralCodeUsed.txt" />
		<None Remove="Helper\Templates\PushNotification\ReferralRewardCredited.txt" />
		<None Remove="Helper\Templates\PushNotification\RegistrationEmiratesIdApproval.txt" />
		<None Remove="Helper\Templates\PushNotification\RegistrationPassportApproval.txt" />
		<None Remove="Helper\Templates\PushNotification\RegistrationRejection.txt" />
		<None Remove="Helper\Templates\PushNotification\RMTProfileCreated.txt" />
		<None Remove="Helper\Templates\PushNotification\SuccessfulInsuranceSubscription.txt" />
		<None Remove="Helper\Templates\SMS\AtmWithdrawalFeeReversal.txt" />
		<None Remove="Helper\Templates\SMS\C3PayPlusLifeInsuranceNomineeChangesToMembershipUser.txt" />
		<None Remove="Helper\Templates\SMS\C3PayPlusLifeInsuranceNomineeChangesToNominee.txt" />
		<None Remove="Helper\Templates\SMS\C3PayPlusSubscriptionSmsTemplate.txt" />
		<None Remove="Helper\Templates\SMS\CancelInsuranceRequest.txt" />
		<None Remove="Helper\Templates\SMS\ClaimedDirectTransferToReceiverTemplate.txt" />
		<None Remove="Helper\Templates\SMS\DailyRmtProfileStats.txt" />
		<None Remove="Helper\Templates\SMS\EHBeneficiaryCreated.txt" />
		<None Remove="Helper\Templates\SMS\FreeTransferExpiry_1_DaysLeft.txt" />
		<None Remove="Helper\Templates\SMS\FreeTransferExpiry_2_DaysLeft.txt" />
		<None Remove="Helper\Templates\SMS\FreeTransferExpiry_4_DaysLeft.txt" />
		<None Remove="Helper\Templates\SMS\FreeTransferExpiry_7_DaysLeft.txt" />
		<None Remove="Helper\Templates\SMS\KycExpiringGracePeriod.txt" />
		<None Remove="Helper\Templates\SMS\KycNotSubmittedGracePeriod.txt" />
		<None Remove="Helper\Templates\SMS\MissingProfilesAttachmentsUploadedStats.txt" />
		<None Remove="Helper\Templates\SMS\MissingRakFile.txt" />
		<None Remove="Helper\Templates\SMS\MRAutoRenewalDeactivationLowBalance.txt" />
		<None Remove="Helper\Templates\SMS\MRAutoRenewalDeactivationPackUnavailable.txt" />
		<None Remove="Helper\Templates\SMS\MRLowBalanceEndSmsTemplate.txt" />
		<None Remove="Helper\Templates\SMS\MRLowBalanceStartSmsTemplate.txt" />
		<None Remove="Helper\Templates\SMS\MTBeneficiaryApproved.txt" />
		<None Remove="Helper\Templates\SMS\MTBeneficiaryCreated.txt" />
		<None Remove="Helper\Templates\SMS\OrderPlacedTemplate.txt" />
		<None Remove="Helper\Templates\SMS\OTPMessage.txt" />
		<None Remove="Helper\Templates\SMS\PendingDirectTransferTemplate.txt" />
		<None Remove="Helper\Templates\SMS\PhoneNumberChanged.txt" />
		<None Remove="Helper\Templates\SMS\ResetPassword.txt" />
		<None Remove="Helper\Templates\SMS\RMTProfileCreated.txt" />
		<None Remove="Helper\Templates\SMS\SuccessfulInsuranceSubscriptionWithBalance.txt" />
		<None Remove="Helper\Templates\SMS\SuccessfulInsuranceSubscriptionWithoutBalance.txt" />
		<None Remove="Helper\Templates\SMS\UnSuccessfulInsuranceSubscriptionWithBalance.txt" />
		<None Remove="Helper\Templates\SMS\UnSuccessfulInsuranceSubscriptionWithoutBalance.txt" />
		<None Remove="Helper\Templates\SMS\VpnSubscriptionSuccess.txt" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="Helper\Templates\PushNotification\BillPaymentBillerUnavailable.txt" />
	  <EmbeddedResource Include="Helper\Templates\PushNotification\BillPaymentBillExpired.txt" />
	  <EmbeddedResource Include="Helper\Templates\PushNotification\BillPaymentBillPaid.txt" />
	  <EmbeddedResource Include="Helper\Templates\PushNotification\BillPaymentDeleteAndAddAgain.txt" />
	  <EmbeddedResource Include="Helper\Templates\PushNotification\BillPaymentNoDueAtPayment.txt" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="Helper\Templates\PushNotification\BillPaymentCardInsufficientBalance.txt" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="Helper\Templates\PushNotification\BillPaymentAmountDue.txt">
	    <CopyToOutputDirectory>Never</CopyToOutputDirectory>
	  </EmbeddedResource>
	  <EmbeddedResource Include="Helper\Templates\PushNotification\BillPaymentInvalidDetails.txt" />
	  <EmbeddedResource Include="Helper\Templates\PushNotification\BillPaymentNoBill.txt" />
	  <EmbeddedResource Include="Helper\Templates\PushNotification\BillPaymentPaymentFailed.txt" />
	  <EmbeddedResource Include="Helper\Templates\PushNotification\C3PayPlusSmsRefund.txt" />
	  <EmbeddedResource Include="Helper\Templates\PushNotification\C3PayPlusBalanceEnquiryRefund.txt" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Helper\Templates\PushNotification\ClaimedDirectTransferToReceiverTemplate.txt" />
		<EmbeddedResource Include="Helper\Templates\PushNotification\ClaimedDirectTransferToSenderTemplate.txt" />
		<EmbeddedResource Include="Helper\Templates\PushNotification\InstantDirectTransferTemplate.txt" />
		<EmbeddedResource Include="Helper\Templates\PushNotification\MobileRechargeDeepLinkStatus.txt" />
		<EmbeddedResource Include="Helper\Templates\PushNotification\ReferralRewardCredited.txt" />
		<EmbeddedResource Include="Helper\Templates\PushNotification\ReferralCodeUsed.txt" />
		<EmbeddedResource Include="Helper\Templates\PushNotification\RegistrationPassportApproval.txt" />
		<EmbeddedResource Include="Helper\Templates\PushNotification\RegistrationRejection.txt" />
		<EmbeddedResource Include="Helper\Templates\PushNotification\RMTProfileCreated.txt" />
		<EmbeddedResource Include="Helper\Templates\PushNotification\RegistrationEmiratesIdApproval.txt" />
		<EmbeddedResource Include="Helper\Templates\PushNotification\SuccessfulInsuranceSubscription.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\AtmWithdrawalFeeReversal.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\C3PayPlusLifeInsuranceNomineeChangesToMembershipUser.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\C3PayPlusLifeInsuranceNomineeChangesToNominee.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\C3PayPlusSubscriptionSmsTemplate.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\MissingProfilesAttachmentsUploadedStats.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\DailyRmtProfileStats.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\FreeTransferExpiry_2_DaysLeft.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\FreeTransferExpiry_1_DaysLeft.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\FreeTransferExpiry_4_DaysLeft.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\KycNotSubmittedGracePeriod.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\KycExpiringGracePeriod.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\ClaimedDirectTransferToReceiverTemplate.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\EHBeneficiaryCreated.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\MissingRakFile.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\MRAutoRenewalDeactivationLowBalance.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\MRAutoRenewalDeactivationPackUnavailable.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\MRLowBalanceEndSmsTemplate.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\MRLowBalanceStartSmsTemplate.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\OrderPlacedTemplate.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\PendingDirectTransferTemplate.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\MTBeneficiaryApproved.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\MTBeneficiaryCreated.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\OTPMessage.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\PhoneNumberChanged.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\ResetPassword.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\RMTProfileCreated.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\FreeTransferExpiry_7_DaysLeft.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\SuccessfulInsuranceSubscriptionWithoutBalance.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\SuccessfulInsuranceSubscriptionWithBalance.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\CancelInsuranceRequest.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\UnSuccessfulInsuranceSubscriptionWithBalance.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\UnSuccessfulInsuranceSubscriptionWithoutBalance.txt" />
		<EmbeddedResource Include="Helper\Templates\SMS\VpnSubscriptionSuccess.txt" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Messaging.ServiceBus" Version="7.20.1" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.22.2" />
		<PackageReference Include="Bogus" Version="35.6.1" />
		<PackageReference Include="FluentValidation" Version="11.10.0" />
		<PackageReference Include="Grpc.AspNetCore" Version="2.66.0" />
		<PackageReference Include="Grpc.AspNetCore.Web" Version="2.66.0" />
		<PackageReference Include="MediatR" Version="12.4.1" />
		<PackageReference Include="OpenTelemetry" Version="1.8.1" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.8.1" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.8.1" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.8.1" />
		<PackageReference Include="OpenTelemetry.Instrumentation.SqlClient" Version="1.8.0-beta.1" />
		<PackageReference Include="OpenTelemetry.Exporter.Jaeger" Version="1.5.1" />
		<PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.2.0" />
		<PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.2.0" />
		<PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.FeatureManagement.AspNetCore" Version="4.0.0" />
		<PackageReference Include="Microsoft.IdentityModel.Clients.ActiveDirectory" Version="5.3.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Polly" Version="8.4.2" />
		<PackageReference Include="Renci.SshNet.Async" Version="1.4.0" />
		<PackageReference Include="SSH.NET" Version="2024.1.0" />
		<PackageReference Include="System.Data.OleDb" Version="8.0.1" />
		<PackageReference Include="System.Resources.ResourceManager" Version="4.3.0" />
		<PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
		<PackageReference Include="System.ServiceModel.Http" Version="8.0.0" />
		<PackageReference Include="System.ServiceModel.NetTcp" Version="8.0.0" />
		<PackageReference Include="System.ServiceModel.Primitives" Version="8.0.0" />
		<PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
		<PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
		<PackageReference Include="Azure.Data.Tables" Version="12.9.1" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.8.1" />
		<PackageReference Include="ClosedXML" Version="0.104.1" />
		<PackageReference Include="FuzzySharp" Version="2.0.2" />
		<PackageReference Include="jose-jwt" Version="5.0.0" />
		<PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.7" />
		<PackageReference Include="Microsoft.AspNetCore.Server.Kestrel.Core" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.2.0" />
		<PackageReference Include="Microsoft.Azure.KeyVault" Version="3.0.5" />
		<PackageReference Include="Microsoft.Azure.Services.AppAuthentication" Version="1.6.1" />
		<PackageReference Include="Microsoft.Azure.Storage.Common" Version="11.2.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
		<PackageReference Include="Microsoft.Extensions.Localization.Abstractions" Version="8.0.10" />
		<PackageReference Include="Renci.SshNet.Async" Version="1.4.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.1.2" />
		<PackageReference Include="System.ServiceModel.Duplex" Version="4.8.1" />
		<PackageReference Include="System.ServiceModel.Http" Version="4.8.1" />
		<PackageReference Include="System.ServiceModel.NetTcp" Version="4.8.1" />
		<PackageReference Include="System.ServiceModel.Security" Version="4.8.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\C3Pay.Data\C3Pay.Data.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Dashboard\Popup\Commands\" />
	  <Folder Include="LoginVideos\Commands\" />
	  <Folder Include="Membership\Modifiers\" />
	</ItemGroup>
</Project>
